FROM registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8 as builder

WORKDIR /application
ADD application.jar /application/
RUN java -Djarmode=layertools -jar application.jar extract

FROM registry.cn-shanghai.aliyuncs.com/jsyuanda/java
WORKDIR "/app"
COPY --from=builder /application/dependencies/ ./
COPY --from=builder /application/spring-boot-loader/ ./
COPY --from=builder /application/snapshot-dependencies/ ./
COPY --from=builder /application/application/ ./
VOLUME ["/app/storage"]
EXPOSE 8080
ENTRYPOINT ["java","-Duser.timezone=GMT+08", "-Xms128m","-Xmx2024m","-XX:CompressedClassSpaceSize=64m","-XX:MetaspaceSize=64m","-XX:MaxMetaspaceSize=1024m","-Djava.security.egd=file:/dev/./urandom", "-Dfile.encoding=utf-8","org.springframework.boot.loader.JarLauncher"]
#CMD ["--spring.profiles.active=${ACTIVE_PROFILES:mysql}"]

