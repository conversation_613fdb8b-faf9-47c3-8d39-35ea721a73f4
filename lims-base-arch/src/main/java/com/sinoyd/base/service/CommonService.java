package com.sinoyd.base.service;

import com.sinoyd.base.vo.DeptDropdownVO;
import com.sinoyd.base.vo.DocumentPathVO;
import com.sinoyd.base.vo.GenericDropdownVO;
import com.sinoyd.base.vo.RoleVO;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.dto.DtoOrg;
import com.sinoyd.frame.dto.DtoUser;
import org.springframework.http.HttpMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 通用服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
public interface CommonService {

    /**
     * 开通账户
     *
     * @param dtoUser               用户
     * @param pwd                   密码
     * @param roleIds               角色ids
     * @param isCheckAccountExisted 是否验证账号已存在
     */
    void openAccount(DtoUser dtoUser, String pwd, List<String> roleIds, boolean isCheckAccountExisted);

    /**
     * 获取机构数据源
     *
     * @return key: 机构id, value: 机构名称
     */
    Map<String, String> findOrgMap();

    /**
     * 根据行政区域id集合查询，并将查询结果映射成Map格式
     *
     * @param areaIds 行政区域id集合
     * @return key: 行政区域id, value: 行政区域名称
     */
    Map<String, String> findAreaMap(Collection<String> areaIds);

    /**
     * 查询所有机构
     *
     * @return 机构集合
     */
    List<DtoOrg> findAllOrg();

    /**
     * 获取所有启用的账号id
     *
     * @param personIds 用户id
     * @return 账号id集合
     */
    List<String> findEnabledAccountIds(List<String> personIds);

    /**
     * 对人员按权限过滤
     *
     * @param personIds     人员id
     * @param permissionIds 权限id集合
     * @return 人员id
     */
    List<String> filterPersonWithPermissionIds(List<String> personIds, List<String> permissionIds);

    /**
     * 获取当前登录用户机构类型
     *
     * @return 机构类型，参考枚举 EnumOrgType
     */
    String findPrincipalOrgType();

    /**
     * 获取部门下拉框
     *
     * @return 部门下拉框VO
     */
    List<DeptDropdownVO> loadDepartmentDropdown();

    /**
     * 查询角色
     *
     * @param orgId 机构id
     * @return 角色列表
     */
    List<RoleVO> findRole(String orgId);

    /**
     * 根据父级id递归查询所有子级区域id
     *
     * @param parentId 父级id
     * @return 所有子级id
     */
    Collection<String> queryAreaIdsByParentId(String parentId);

    /**
     * 文件预览
     *
     * @param vo       文件预览模型
     * @param response 响应流
     */
    void previewDocument(DocumentPreviewVO vo, HttpServletResponse response);

    /**
     * 根据枚举类名获取枚举值和名称
     *
     * @param moduleName 应用模块名称，参考枚举 @{@link com.sinoyd.base.enums.EnumApplicationModule}
     * @param enumName   枚举类名
     * @return 枚举的下拉框展示实体集合
     */
    List<GenericDropdownVO> loadEnumDropdown(String moduleName, String enumName);

    /**
     * 得到文件上传的路径
     *
     * @param code 通过编码锁定相应的反射类
     * @param map  前端传相应的参数集合
     * @return 返回文件上传的路径vo
     */
    DocumentPathVO getDocumentPathFromXml(String code, Map<String, Object> map);

    /**
     * 获取参数设置中参数值
     *
     * @param configParamKey 参数key
     * @return 参数值
     */
    String findConfigValue(String configParamKey);

    /**
     * 生成二维码
     *
     * @param content 二维码内容
     * @param size    二维码尺寸大小
     * @return 二维码base64形式
     */
    String createQrCode(String content, int size);

    /**
     * 获取上传限制信息
     * @return 上传限制信息
     */
    Map<String, String> loadUploadLimitInfo();

    /**
     * 远程同步文件
     *
     * @param gatePath       网关路径
     * @param gateContext    网关上下文
     * @param uri            uri
     * @param requestMethod  远程请求方式
     * @param params         请求参数
     * @param token          目标令牌
     * @param targetPath     目标路径
     * @param targetFileName 目标文件名
     *
     * @return 文件
     */
    File remoteSyncFile(String gatePath,
                        String gateContext,
                        String uri,
                        HttpMethod requestMethod,
                        Map<String, Object> params,
                        String token,
                        String targetPath,
                        String targetFileName);
}
