package com.sinoyd.base.service;

import java.util.Map;

/**
 * 开发工具接口
 * 注意：该接口和业务无任何关系，只是用来提升开发效率
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/11
 */
public interface DevelopToolService {

    /**
     * 生成ApiFox数据模型
     *
     * @param params 参数
     * @return 内容
     */
    String generateApifoxObjectModel(Map<String, String> params);


    /**
     * 生成ApiFox数据模型
     *
     * @param params 参数
     * @return 内容
     */
    String generateApifoxObjectModelByFullName(Map<String, String> params);
}