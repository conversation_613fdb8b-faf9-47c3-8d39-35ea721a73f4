package com.sinoyd.base.service;

import com.sinoyd.frame.dto.DtoCode;

import java.util.Map;

/**
 * 字典服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
public interface DictService {

    /**
     * 将字典转换成Map，方便业务使用
     *
     * @param dictType 字典类型
     * @return key:字典编码, value: 字典名称
     */
    Map<String, DtoCode> loadDictMap(String dictType);

    /**
     * 将字典转换成Map，方便业务使用
     *
     * @param dictType 字典类型
     * @return key:字典ID, value: 字典名称
     */
    Map<String, DtoCode> loadDictIdMap(String dictType);

    /**
     * 将字典转换成Map，方便业务使用
     *
     * @param dictType 字典类型
     * @return key:字典编码, value: 字典名称
     */
    Map<String, String> loadDictNameMap(String dictType);

    /**
     * 将字典转换成Map，方便业务使用
     *
     * @param dictType 字典类型
     * @return key:字典编码, value: 字典值
     */
    Map<String, String> loadDictValueMap(String dictType);

    /**
     * 将部门转换成Map，方便业务使用
     *
     * @return key:部门id, value: 部门名称
     */
    Map<String, String> loadDepartmentMap();
}