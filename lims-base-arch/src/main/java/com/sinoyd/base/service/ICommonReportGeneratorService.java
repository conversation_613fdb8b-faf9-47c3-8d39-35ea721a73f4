package com.sinoyd.base.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 基础报表生成基础服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/16
 */
public interface ICommonReportGeneratorService {

    /**
     * 基础导出报表生成
     *
     * @param reportCode 报表编码
     * @param criteria   查询条件
     * @param response   响应对象
     */
    void exportData(String reportCode, Map<String, Object> criteria, HttpServletResponse response);
}
