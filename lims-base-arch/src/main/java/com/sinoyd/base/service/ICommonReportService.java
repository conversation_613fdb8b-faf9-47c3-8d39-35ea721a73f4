package com.sinoyd.base.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 基础报表导出生成统一接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/16
 */
public interface ICommonReportService {

    /**
     * 生成报表
     *
     * @param reportCode 报表编码
     * @param criteria   查询条件
     * @param response   响应对象
     */
    void generateReport(String reportCode, Map<String, Object> criteria, HttpServletResponse response);
}
