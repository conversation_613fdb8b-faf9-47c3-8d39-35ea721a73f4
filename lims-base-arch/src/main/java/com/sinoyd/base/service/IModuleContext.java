package com.sinoyd.base.service;

import java.util.List;

/**
 * LIMS模块上下文接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/6
 */
public interface IModuleContext {

    /**
     * 所有
     *
     * @param moduleCode 模块编码
     * @return 当前模块所有的状态
     */
    List<Integer> all(String moduleCode);

    /**
     * 所有
     *
     * @param moduleCode 模块编码
     * @return 当前模块已完成的状态
     */
    List<Integer> completed(String moduleCode);

    /**
     * 所有
     *
     * @param moduleCode 模块编码
     * @return 当前模块未完成的状态
     */
    List<Integer> uncompleted(String moduleCode);
}
