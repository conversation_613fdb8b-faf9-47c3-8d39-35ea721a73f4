package com.sinoyd.base.service;

import java.util.List;

/**
 * LIMS模块接口，主要用于解决已处理、未处理查询条件下涵盖的状态
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/6
 */
public interface IModuleService {

    /**
     * 所有
     *
     * @return 当前模块所有的状态
     */
    List<Integer> all();

    /**
     * 所有
     *
     * @return 当前模块已完成的状态
     */
    List<Integer> completed();

    /**
     * 所有
     *
     * @return 当前模块未完成的状态
     */
    List<Integer> uncompleted();
}
