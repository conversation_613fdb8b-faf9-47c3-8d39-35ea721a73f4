package com.sinoyd.base.service;

import com.sinoyd.base.vo.RemoteRequestParamVO;
import com.sinoyd.boot.common.dto.RestResponse;

/**
 * 远程请求服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/12
 */
public interface IRemoteRequestService {

    /**
     * 远程请求
     *
     * @param paramVO 请求参数
     * @return 请求结果
     */
    RestResponse<?> request(RemoteRequestParamVO paramVO);

    /**
     * 获取请求结果数据JSON
     *
     * @param response 远程请求结果
     * @return 请求结果数据JSON
     */
    String getResponseDataJson(RestResponse<?> response);
}
