package com.sinoyd.base.service;

import com.sinoyd.base.enums.EnumTableStrategy;

/**
 * 模版表接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
public interface ITemplateTable {

    /**
     * 创建模版表sql
     *
     * @return 建表语句
     */
    String createTemplateTableSql();

    /**
     * 获取模版表名
     *
     * @return 模版表名
     */
    String getTemplateTableName();

    /**
     * 设置分表表名
     *
     * @param tableName 分表表名
     */
    default void setTableName(String tableName) {
    }

    /**
     * 获取分表表名
     *
     * @return 分表表名
     */
    default String getTableName() {
        return null;
    }

    /**
     * 获取建表策略
     *
     * @return 建表策略枚举
     */
    EnumTableStrategy getTableStrategy();
}
