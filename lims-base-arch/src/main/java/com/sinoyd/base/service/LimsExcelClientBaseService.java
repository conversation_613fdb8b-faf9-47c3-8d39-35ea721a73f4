package com.sinoyd.base.service;

import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.frame.base.criteria.BaseCriteria;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * LIMS表格Fiegen客户端处理基础接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/06
 */
public interface LimsExcelClientBaseService<T, V extends LimsExcelBaseVO, S>{

    /**
     * 查询导出数据
     *
     * @param criteria 查询条件
     */
    List<T> findExportData(BaseCriteria criteria);

    /**
     * 数据导出
     *
     * @param response 响应流
     * @param criteria 查询条件
     */
    void exportData(HttpServletResponse response, BaseCriteria criteria);
}
