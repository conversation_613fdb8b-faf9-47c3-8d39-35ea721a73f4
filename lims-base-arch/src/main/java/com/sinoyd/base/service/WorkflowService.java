package com.sinoyd.base.service;

import com.sinoyd.base.vo.WorkflowParamVO;

import java.util.Map;

/**
 * 工作流通用服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/26
 */
public interface WorkflowService {

    /**
     * 提交工作流的信号信息
     *
     * @param vo 工作流信息
     */
    void submitSign(WorkflowParamVO vo);


    /**
     * 创建工作流的实例
     *
     * @param workflowCode 工作流编号
     * @param objectId     对象id
     * @param params       参数，比如信号值等
     */
    void createInstance(String workflowCode, String objectId, Map<String, Object> params);

    /**
     * 终止实例
     *
     * @param objectId 对象id
     * @param option   原因
     */
    void endInstance(String objectId, String option);
}
