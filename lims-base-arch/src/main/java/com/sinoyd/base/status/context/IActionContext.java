package com.sinoyd.base.status.context;


import com.sinoyd.base.vo.ActionParamVO;

import java.util.List;

/**
 * 动作上下文接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/13
 */
public interface IActionContext<T, E extends Enum<E>> {

    /**
     * 执行动作
     *
     * @param param      动作参数实例
     * @param actionEnum 动作枚举
     * @return 动作结果
     */
    List<T> execute(ActionParamVO<T> param, E actionEnum);
}
