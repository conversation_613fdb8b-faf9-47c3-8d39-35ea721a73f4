2025-09-01 10:20:53.632  WARN 14352 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[lims60-lim.yaml] & group[DEFAULT_GROUP]
2025-09-01 10:20:53.638  WARN 14352 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[lims60-lim-dev.yaml] & group[DEFAULT_GROUP]
2025-09-01 10:20:53.639  INFO 14352 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-lims60-lim-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-lims60-lim.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-lims60-lim,DEFAULT_GROUP'}]
2025-09-01 10:20:53.681  INFO 14352 --- [main] c.s.b.service.impl.CalculateServiceTest  : The following 2 profiles are active: "${activeProfile:dev}", "dev"
2025-09-01 10:20:54.848  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-01 10:20:54.849  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-01 10:20:54.869  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 JPA repository interfaces.
2025-09-01 10:20:55.530  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-01 10:20:55.532  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-01 10:20:55.601  INFO 14352 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-09-01 10:20:56.063  INFO 14352 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=847da7bd-f0a1-3eb4-880e-4cb50bb1d015
2025-09-01 10:20:56.950  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'cacheManagerConfig' of type [com.sinoyd.frame.base.configuration.CacheManagerConfig$$EnhancerBySpringCGLIB$$96d0358a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.309  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.318  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.329  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$793/785240035] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.346  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.359  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.372  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.384  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.388  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSourceConfig' of type [com.sinoyd.configuration.DataSourceConfig$$EnhancerBySpringCGLIB$$894de1c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.405  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:57.437  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.flyway-org.springframework.boot.autoconfigure.flyway.FlywayProperties' of type [org.springframework.boot.autoconfigure.flyway.FlywayProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:58.168  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSource' of type [com.zaxxer.hikari.HikariDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:58.329  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'flyway' of type [org.flywaydb.core.Flyway] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:58.358  INFO 14352 --- [main] o.f.c.internal.license.VersionPrinter    : Flyway Community Edition 8.5.11 by Redgate
2025-09-01 10:20:58.358  INFO 14352 --- [main] o.f.c.internal.license.VersionPrinter    : See what's new here: https://flywaydb.org/documentation/learnmore/releaseNotes#8.5.11
2025-09-01 10:20:58.358  INFO 14352 --- [main] o.f.c.internal.license.VersionPrinter    : 
2025-09-01 10:20:58.868  INFO 14352 --- [main] o.f.c.i.database.base.BaseDatabaseType   : Database: ****************************************** (MySQL 5.7)
2025-09-01 10:20:58.951  INFO 14352 --- [main] o.f.core.internal.command.DbValidate     : Successfully validated 44 migrations (execution time 00:00.040s)
2025-09-01 10:20:58.977  INFO 14352 --- [main] o.f.core.internal.command.DbMigrate      : Current version of schema `lims60lim`: 1.0044.000
2025-09-01 10:20:58.977 ERROR 14352 --- [main] o.f.core.internal.command.DbMigrate      : Schema `lims60lim` has version 1.0044.000, but no migration could be resolved in the configured locations !
2025-09-01 10:20:58.979  INFO 14352 --- [main] o.f.core.internal.command.DbMigrate      : Schema `lims60lim` is up to date. No migration necessary.
2025-09-01 10:20:58.994  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'flywayInitializer' of type [org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:58.999  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.007  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.023  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.060  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.073  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.079  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'hikariPoolDataSourceMetadataProvider' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration$$Lambda$946/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.113  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.160  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.172  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:20:59.269  INFO 14352 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: lims]
2025-09-01 10:20:59.380  INFO 14352 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-01 10:20:59.623  INFO 14352 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-01 10:20:59.847  INFO 14352 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-01 10:20:59.899  INFO 14352 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-01 10:20:59.929  INFO 14352 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-01 10:21:00.429  INFO 14352 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-01 10:21:00.444  INFO 14352 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'lims'
2025-09-01 10:21:00.448  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:21:00.457  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limEntityManagerFactory' of type [com.sun.proxy.$Proxy152] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:21:00.487  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limTransactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:21:00.495  INFO 14352 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultTxnManagerConfig' of type [com.sinoyd.configuration.DefaultTxnManagerConfig$$EnhancerBySpringCGLIB$$55448477] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:21:01.375  WARN 14352 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-01 10:21:02.251  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.180  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.200  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.438  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.464  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.490  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.525  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.537  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.559  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.574  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.594  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.634  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.674  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.691  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:03.724  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:04.095  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:04.203  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:04.211  INFO 14352 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:21:04.535  WARN 14352 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 255c3b33-4ebe-4c97-9df3-63ebf2e79216

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-01 10:21:05.089  INFO 14352 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-09-01 10:21:05.936  INFO 14352 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a7f5c44, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ab1693b, org.springframework.security.web.context.SecurityContextPersistenceFilter@475b796d, org.springframework.security.web.header.HeaderWriterFilter@50915d5, org.springframework.web.filter.CorsFilter@35e0d91e, org.springframework.security.web.authentication.logout.LogoutFilter@6abbdcde, com.sinoyd.frame.filter.JwtServiceAuthFilter@3204146d, com.sinoyd.frame.filter.JwtUserAuthFilter@3299e315, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7bca98d5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3d72abea, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7320bccc, org.springframework.security.web.session.SessionManagementFilter@67544105, org.springframework.security.web.access.ExceptionTranslationFilter@75128cf, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36a59d8]
2025-09-01 10:21:09.317  WARN 14352 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-01 10:21:09.380  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:21:09.380  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:21:09.542  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:21:09.542  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:21:09.931  INFO 14352 --- [main] c.s.b.service.impl.CalculateServiceTest  : Started CalculateServiceTest in 19.211 seconds (JVM running for 20.259)
2025-09-01 10:21:09.935  INFO 14352 --- [main] c.s.b.a.client.runner.AuthClientRunner   : Begin----------------Init Get User PubKey From Auth Server---------------ClientId[lims60-lim]-
2025-09-01 10:21:10.157  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:21:10.158  INFO 14352 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:21:10.518  INFO 14352 --- [main] c.s.b.a.client.runner.AuthClientRunner   : End----------------Init Get User PubKey From Auth Server----------------Success
2025-09-01 10:21:10.518  INFO 14352 --- [main] c.s.b.a.client.runner.AuthClientRunner   : Begin----------------Init Get Service PubKey From Auth Server----------------ClientId[lims60-lim]
2025-09-01 10:21:10.527  INFO 14352 --- [main] c.s.b.a.client.runner.AuthClientRunner   : End----------------Init Get Service PubKey From Auth Server----------------Success
2025-09-01 10:21:10.530  INFO 14352 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim-dev.yaml, group=DEFAULT_GROUP
2025-09-01 10:21:10.532  INFO 14352 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim, group=DEFAULT_GROUP
2025-09-01 10:21:10.533  INFO 14352 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim.yaml, group=DEFAULT_GROUP
2025-09-01 10:21:10.668  INFO 14352 --- [main] c.s.b.service.impl.CalculateServiceTest  : revise value is 0.0
2025-09-01 10:21:11.204  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.sinoyd.common.sort.SortTest], using SpringBootContextLoader
2025-09-01 10:21:11.206  INFO 14352 --- [main] o.s.t.c.support.AbstractContextLoader    : Could not detect default resource locations for test class [com.sinoyd.common.sort.SortTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-09-01 10:21:11.212  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-09-01 10:21:11.212  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@988eae9, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@3e93ee71, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@1cfa5e46, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@4df5b562, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3e4d197, org.springframework.test.context.support.DirtiesContextTestExecutionListener@222557fc, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6c633590, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@c5dc3db, org.springframework.test.context.event.EventPublishingTestExecutionListener@2910c3ae, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@2182ebc7, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@543895d2, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@3699c795, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@193b6f5b, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@2fd16368, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@3fd7f545]
2025-09-01 10:21:12.135  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.sinoyd.poi.POIDynamicColumnTest], using SpringBootContextLoader
2025-09-01 10:21:12.136  INFO 14352 --- [main] o.s.t.c.support.AbstractContextLoader    : Could not detect default resource locations for test class [com.sinoyd.poi.POIDynamicColumnTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-09-01 10:21:12.139  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-09-01 10:21:12.139  INFO 14352 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@24a29d8, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@55af9b48, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@890196b, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@12e1413c, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@41b6ae51, org.springframework.test.context.support.DirtiesContextTestExecutionListener@686a08f4, org.springframework.test.context.transaction.TransactionalTestExecutionListener@60b90fd3, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@78494a59, org.springframework.test.context.event.EventPublishingTestExecutionListener@c79a727, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34ec33f4, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1c83652d, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@2eaaef66, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@6fab250b, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3a8dd899, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@3da068e8]
2025-09-01 10:21:12.153  WARN 14352 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-09-01 10:21:12.153  WARN 14352 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-01 10:21:12.154  WARN 14352 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-09-01 10:21:12.155  WARN 14352 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-09-01 10:21:12.532  INFO 14352 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'lims'
2025-09-01 10:21:12.534  INFO 14352 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-01 10:21:12.580  INFO 14352 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-01 10:22:31.590  WARN 5636 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[lims60-lim.yaml] & group[DEFAULT_GROUP]
2025-09-01 10:22:31.597  WARN 5636 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[lims60-lim-dev.yaml] & group[DEFAULT_GROUP]
2025-09-01 10:22:31.598  INFO 5636 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-lims60-lim-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-lims60-lim.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-lims60-lim,DEFAULT_GROUP'}]
2025-09-01 10:22:31.640  INFO 5636 --- [main] c.s.b.service.impl.CalculateServiceTest  : The following 2 profiles are active: "${activeProfile:dev}", "dev"
2025-09-01 10:22:32.507  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-01 10:22:32.507  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-01 10:22:32.518  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 JPA repository interfaces.
2025-09-01 10:22:32.931  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-01 10:22:32.932  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-01 10:22:32.979  INFO 5636 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-09-01 10:22:33.408  INFO 5636 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=847da7bd-f0a1-3eb4-880e-4cb50bb1d015
2025-09-01 10:22:33.893  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'cacheManagerConfig' of type [com.sinoyd.frame.base.configuration.CacheManagerConfig$$EnhancerBySpringCGLIB$$92a95676] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.052  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.057  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.061  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$793/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.071  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.078  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.085  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.091  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.094  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSourceConfig' of type [com.sinoyd.configuration.DataSourceConfig$$EnhancerBySpringCGLIB$$852702b2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.103  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.125  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.flyway-org.springframework.boot.autoconfigure.flyway.FlywayProperties' of type [org.springframework.boot.autoconfigure.flyway.FlywayProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.477  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSource' of type [com.zaxxer.hikari.HikariDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.569  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'flyway' of type [org.flywaydb.core.Flyway] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:34.590  INFO 5636 --- [main] o.f.c.internal.license.VersionPrinter    : Flyway Community Edition 8.5.11 by Redgate
2025-09-01 10:22:34.590  INFO 5636 --- [main] o.f.c.internal.license.VersionPrinter    : See what's new here: https://flywaydb.org/documentation/learnmore/releaseNotes#8.5.11
2025-09-01 10:22:34.590  INFO 5636 --- [main] o.f.c.internal.license.VersionPrinter    : 
2025-09-01 10:22:34.949  INFO 5636 --- [main] o.f.c.i.database.base.BaseDatabaseType   : Database: ****************************************** (MySQL 5.7)
2025-09-01 10:22:35.019  INFO 5636 --- [main] o.f.core.internal.command.DbValidate     : Successfully validated 44 migrations (execution time 00:00.031s)
2025-09-01 10:22:35.045  INFO 5636 --- [main] o.f.core.internal.command.DbMigrate      : Current version of schema `lims60lim`: 1.0044.000
2025-09-01 10:22:35.045 ERROR 5636 --- [main] o.f.core.internal.command.DbMigrate      : Schema `lims60lim` has version 1.0044.000, but no migration could be resolved in the configured locations !
2025-09-01 10:22:35.047  INFO 5636 --- [main] o.f.core.internal.command.DbMigrate      : Schema `lims60lim` is up to date. No migration necessary.
2025-09-01 10:22:35.058  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'flywayInitializer' of type [org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.063  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.072  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.084  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.115  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.122  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.125  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'hikariPoolDataSourceMetadataProvider' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration$$Lambda$946/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.139  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.159  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.168  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.220  INFO 5636 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: lims]
2025-09-01 10:22:35.285  INFO 5636 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-01 10:22:35.485  INFO 5636 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-01 10:22:35.652  INFO 5636 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-01 10:22:35.702  INFO 5636 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-01 10:22:35.725  INFO 5636 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-01 10:22:35.944  INFO 5636 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-01 10:22:35.956  INFO 5636 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'lims'
2025-09-01 10:22:35.960  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.971  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limEntityManagerFactory' of type [com.sun.proxy.$Proxy152] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:35.997  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'limTransactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:36.005  INFO 5636 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultTxnManagerConfig' of type [com.sinoyd.configuration.DefaultTxnManagerConfig$$EnhancerBySpringCGLIB$$511da563] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-01 10:22:36.586  WARN 5636 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-01 10:22:37.626  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.325  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.346  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.542  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.559  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.587  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.618  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.629  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.652  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.667  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.686  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.725  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.758  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.770  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:38.792  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:39.043  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-frame' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:39.139  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:39.148  INFO 5636 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-activiti' URL not provided. Will try picking an instance via load-balancing.
2025-09-01 10:22:39.387  WARN 5636 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 737ac6e3-a8c2-422a-8038-0047c919f99b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-01 10:22:39.847  INFO 5636 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-09-01 10:22:40.300  INFO 5636 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@136a5572, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@11548363, org.springframework.security.web.context.SecurityContextPersistenceFilter@69c7fb94, org.springframework.security.web.header.HeaderWriterFilter@65130cf2, org.springframework.web.filter.CorsFilter@6be865c1, org.springframework.security.web.authentication.logout.LogoutFilter@5a35efac, com.sinoyd.frame.filter.JwtServiceAuthFilter@4c140b12, com.sinoyd.frame.filter.JwtUserAuthFilter@d140627, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d72abea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7976d382, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@386ec37d, org.springframework.security.web.session.SessionManagementFilter@552b7481, org.springframework.security.web.access.ExceptionTranslationFilter@668ea404, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2ff2a096]
2025-09-01 10:22:42.419  WARN 5636 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-01 10:22:42.469  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:22:42.469  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:22:42.629  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:22:42.629  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:22:42.881  INFO 5636 --- [main] c.s.b.service.impl.CalculateServiceTest  : Started CalculateServiceTest in 13.853 seconds (JVM running for 14.866)
2025-09-01 10:22:42.883  INFO 5636 --- [main] c.s.b.a.client.runner.AuthClientRunner   : Begin----------------Init Get User PubKey From Auth Server---------------ClientId[lims60-lim]-
2025-09-01 10:22:42.991  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-09-01 10:22:42.991  INFO 5636 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-09-01 10:22:43.288  INFO 5636 --- [main] c.s.b.a.client.runner.AuthClientRunner   : End----------------Init Get User PubKey From Auth Server----------------Success
2025-09-01 10:22:43.288  INFO 5636 --- [main] c.s.b.a.client.runner.AuthClientRunner   : Begin----------------Init Get Service PubKey From Auth Server----------------ClientId[lims60-lim]
2025-09-01 10:22:43.297  INFO 5636 --- [main] c.s.b.a.client.runner.AuthClientRunner   : End----------------Init Get Service PubKey From Auth Server----------------Success
2025-09-01 10:22:43.300  INFO 5636 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim-dev.yaml, group=DEFAULT_GROUP
2025-09-01 10:22:43.301  INFO 5636 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim, group=DEFAULT_GROUP
2025-09-01 10:22:43.302  INFO 5636 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=lims60-lim.yaml, group=DEFAULT_GROUP
2025-09-01 10:22:43.403  INFO 5636 --- [main] c.s.b.service.impl.CalculateServiceTest  : revise value is 0.0
2025-09-01 10:22:43.752  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.sinoyd.common.sort.SortTest], using SpringBootContextLoader
2025-09-01 10:22:43.753  INFO 5636 --- [main] o.s.t.c.support.AbstractContextLoader    : Could not detect default resource locations for test class [com.sinoyd.common.sort.SortTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-09-01 10:22:43.758  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-09-01 10:22:43.758  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@3e93ee71, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@1cfa5e46, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@4df5b562, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@3e4d197, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@222557fc, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6c633590, org.springframework.test.context.transaction.TransactionalTestExecutionListener@c5dc3db, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2910c3ae, org.springframework.test.context.event.EventPublishingTestExecutionListener@2182ebc7, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@543895d2, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@3699c795, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@193b6f5b, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@2fd16368, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3fd7f545, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@470a2845]
2025-09-01 10:22:44.429  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.sinoyd.poi.POIDynamicColumnTest], using SpringBootContextLoader
2025-09-01 10:22:44.432  INFO 5636 --- [main] o.s.t.c.support.AbstractContextLoader    : Could not detect default resource locations for test class [com.sinoyd.poi.POIDynamicColumnTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-09-01 10:22:44.440  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-09-01 10:22:44.441  INFO 5636 --- [main] .b.t.c.SpringBootTestContextBootstrapper : Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@55af9b48, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@890196b, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@12e1413c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@41b6ae51, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@686a08f4, org.springframework.test.context.support.DirtiesContextTestExecutionListener@60b90fd3, org.springframework.test.context.transaction.TransactionalTestExecutionListener@78494a59, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@c79a727, org.springframework.test.context.event.EventPublishingTestExecutionListener@34ec33f4, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@1c83652d, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@2eaaef66, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@6fab250b, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@3a8dd899, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3da068e8, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@448861e5]
2025-09-01 10:22:44.458  WARN 5636 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-01 10:22:44.458  WARN 5636 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-09-01 10:22:44.459  WARN 5636 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-09-01 10:22:44.460  WARN 5636 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-09-01 10:22:44.820  INFO 5636 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'lims'
2025-09-01 10:22:44.822  INFO 5636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-01 10:22:44.832  INFO 5636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
