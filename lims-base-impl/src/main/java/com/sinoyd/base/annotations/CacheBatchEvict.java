package com.sinoyd.base.annotations;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 批量清空缓存
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/29
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface CacheBatchEvict {

    @AliasFor("cacheNames")
    String[] value() default {};

    /**
     * 指定缓存组件
     */
    @AliasFor("value")
    String[] cacheNames() default {};

    /**
     * key需要指定一个spEL表达式，通过spEL表达式获取方法参数
     */
    String key() default "";
}