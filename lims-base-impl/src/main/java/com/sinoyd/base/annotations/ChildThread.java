package com.sinoyd.base.annotations;

import org.springframework.core.annotation.AliasFor;
import org.springframework.scheduling.annotation.Async;

import java.lang.annotation.*;

/**
 * 标记子线程注解
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/27
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Async
public @interface ChildThread {

    @AliasFor(
            annotation = Async.class
    )
    String value() default "";
}
