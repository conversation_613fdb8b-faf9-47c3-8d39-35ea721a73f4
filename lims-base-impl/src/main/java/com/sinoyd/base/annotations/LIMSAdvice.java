package com.sinoyd.base.annotations;

import java.lang.annotation.*;

/**
 * LIMS自定义注解，用于对方法进行切面处理，主要用于前置后置处理
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/14
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface LIMSAdvice {

    /**
     * 需要清除的缓存名，默认为空字符，不会清除缓存
     */
    String[] cacheNames() default {};

    /**
     * 处理方式
     * {@link com.sinoyd.base.constants.IBaseConstants.AdviceWay}
     */
    String way() default "";

    /**
     * 事件名
     */
    String eventName() default "";

    /**
     * 事件动作
     * {@link com.sinoyd.base.constants.IEventAction}
     */
    String eventAction() default "";
}
