package com.sinoyd.base.annotations;

import java.lang.annotation.*;

/**
 * 开启多线程事务注解
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/28
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface ThreadTransaction {

    /**
     * 子线程数量，支持spel表达式从方法的入参和返回值中获取子线程数量
     */
    String value() default "1";

    /**
     * 主线程等待最大时间(单位秒)，超过该时间会抛出超时等待异常，默认30秒
     */
    int time() default 30;
}
