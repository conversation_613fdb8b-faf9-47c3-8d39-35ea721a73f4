package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.CacheBatchEvict;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 自定义切面，拦截@CacheBatchEvict
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/29
 */
@Aspect
@Component
@Slf4j
public class CacheBatchEvictAspect {

    private CacheManager cacheManager;

    /**
     * 定义切入点
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.CacheBatchEvict)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取注解参数值 SpEL表达式
        CacheBatchEvict cacheBatchEvict = methodSignature.getMethod().getAnnotation(CacheBatchEvict.class);
        CacheConfig cacheConfig = methodSignature.getMethod().getDeclaringClass().getAnnotation(CacheConfig.class);
        String[] cacheNames = cacheBatchEvict.cacheNames();
        if (cacheNames.length == 0) {
            cacheNames = cacheBatchEvict.value();
        }
        if (cacheNames.length == 0) {
            cacheNames = cacheConfig.cacheNames();
        }
        String spEL = cacheBatchEvict.key();
        if (StringUtils.isEmpty(spEL)) {
            log.warn("@CacheBatchEvict key is null");
            return null;
        }
        // 获取目标方法参数
        Collection<Object> keys = SPELUtil.parseSpel2Collection(spEL, joinPoint);
        if (StringUtils.isEmpty(keys)) {
            log.warn(String.format("unable to find method params by [%s]", spEL));
            return null;
        }
        // 清除缓存
        for (String cacheName : cacheNames) {
            for (Object key : keys) {
                cacheManager.getCache(cacheName).evictIfPresent(key);
            }
        }
        // 执行方法
        return joinPoint.proceed();
    }

    @Autowired
    public void setCacheManager(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }
}