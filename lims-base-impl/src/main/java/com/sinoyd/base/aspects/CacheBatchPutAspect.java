package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.CacheBatchPut;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

/**
 * 自定义切面，拦截@CacheBatchPut
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/06
 */
@Aspect
@Component
@Slf4j
public class CacheBatchPutAspect {

    private CacheManager cacheManager;

    /**
     * 定义切入点
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.CacheBatchPut)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取注解参数值 SpEL表达式
        CacheBatchPut cacheBatchEvict = methodSignature.getMethod().getAnnotation(CacheBatchPut.class);
        CacheConfig cacheConfig = methodSignature.getMethod().getDeclaringClass().getAnnotation(CacheConfig.class);
        String[] cacheNames = cacheBatchEvict.cacheNames();
        if (cacheNames.length == 0) {
            cacheNames = cacheBatchEvict.value();
        }
        if (cacheNames.length == 0) {
            cacheNames = cacheConfig.cacheNames();
        }
        String spEL = cacheBatchEvict.key();
        if (StringUtils.isEmpty(spEL)) {
            log.warn("@CacheBatchPut key is null");
            return null;
        }
        // 获取目标方法参数
        Collection<Object> keys = SPELUtil.parseSpel2Collection(spEL, joinPoint);
        if (StringUtils.isEmpty(keys)) {
            log.warn(String.format("unable to find method params by [%s]", spEL));
            return null;
        }
        // 清除缓存
        for (String cacheName : cacheNames) {
            for (Object key : keys) {
                cacheManager.getCache(cacheName).evictIfPresent(key);
            }
        }
        // 执行方法
        List methodResult = (List) joinPoint.proceed(ArrayUtils.toArray(keys));
        String fieldName = cacheBatchEvict.field();
        //将方法结果放入缓存
        if (StringUtils.isNotEmpty(methodResult)) {
            for (Object obj : methodResult) {
                Class clazz = obj.getClass().getSuperclass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (fieldName.equals(field.getName())) {
                        field.setAccessible(true);
                        for (String cacheName : cacheNames) {
                            cacheManager.getCache(cacheName).put(field.get(obj), obj);
                        }
                    }
                }
            }
        }
        return methodResult;
    }

    @Autowired
    public void setCacheManager(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }
}