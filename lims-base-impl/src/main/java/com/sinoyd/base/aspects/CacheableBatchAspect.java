package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.CacheableBatch;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 自定义切面，拦截@CacheableBatch
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/29
 */
@Aspect
@Component
@Slf4j
public class CacheableBatchAspect {

    private CacheManager cacheManager;

    /**
     * 定义切入点
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.CacheableBatch)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取注解参数值 SpEL表达式
        CacheableBatch cacheableBatch = methodSignature.getMethod().getAnnotation(CacheableBatch.class);
        CacheConfig cacheConfig = methodSignature.getMethod().getDeclaringClass().getAnnotation(CacheConfig.class);
        String[] cacheNames = cacheableBatch.cacheNames();
        String fieldName = cacheableBatch.field();
        if (cacheNames.length == 0) {
            cacheNames = cacheableBatch.value();
        }
        if (cacheNames.length == 0) {
            cacheNames = cacheConfig.cacheNames();
        }
        String spEL = cacheableBatch.key();
        if (StringUtils.isEmpty(spEL)) {
            log.warn("@CacheableBatch key is null");
            return null;
        }
        // 获取目标方法参数
        Collection<Object> keys = SPELUtil.parseSpel2Collection(spEL, joinPoint);
        if (StringUtils.isEmpty(keys)) {
            log.warn(String.format("unable to find method params by [%s]", spEL));
            return null;
        }
        //缓存的数据
        List cacheValueList = new ArrayList<>();
        //未命中缓存的key
        List unCacheKeyList = new ArrayList();
        for (String cacheName : cacheNames) {
            for (Object key : keys) {
                Cache.ValueWrapper valueWrapper = cacheManager.getCache(cacheName).get(key);
                if (valueWrapper != null) {
                    Object value = valueWrapper.get();
                    cacheValueList.add(value);
                } else {
                    unCacheKeyList.add(key);
                }
            }
        }
        if (StringUtils.isNotEmpty(unCacheKeyList)) {
            List unCacheValueList = (List) joinPoint.proceed(ArrayUtils.toArray(unCacheKeyList));
            if (StringUtils.isNotEmpty(unCacheValueList)) {
                for (Object obj : unCacheValueList) {
                    Class clazz = obj.getClass().getSuperclass();
                    Field[] fields = clazz.getDeclaredFields();
                    for (Field field : fields) {
                        if (fieldName.equals(field.getName())) {
                            field.setAccessible(true);
                            for (String cacheName : cacheNames) {
                                cacheManager.getCache(cacheName).put(field.get(obj), obj);
                            }
                        }
                    }
                }
                cacheValueList.addAll(unCacheValueList);
            }
        }
        return cacheValueList;
    }

    @Autowired
    public void setCacheManager(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }
}