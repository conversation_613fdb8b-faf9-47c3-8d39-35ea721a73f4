package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.LIMSAdvice;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.util.CacheUtil;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * LIMS相关方法前置后置处理切面，拦截注解@LIMSAdvice
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/14
 */
@Aspect
@Component
@Slf4j
public class LIMSAdviceAspect {


    /**
     * 定义切入点
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.LIMSAdvice)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Object[] parameters = joinPoint.getArgs();
        LIMSAdvice advice = method.getAnnotation(LIMSAdvice.class);
        String way = advice.way();
        String[] cacheNames = advice.cacheNames();
        String eventName = advice.eventName();
        String eventAction = advice.eventAction();
        Object returnValue;
        if (IBaseConstants.AdviceWay.BEFORE.equals(way)) {
            clearCache(cacheNames);
            publishEvent(parameters, null, eventName, eventAction);
            returnValue = joinPoint.proceed();
        } else if (IBaseConstants.AdviceWay.AFTER.equals(way)) {
            returnValue = joinPoint.proceed();
            clearCache(cacheNames);
            publishEvent(parameters, returnValue, eventName, eventAction);
        } else {
            throw new BaseException("...注解LIMSAdvice属性way的值不合法，参考com.sinoyd.base.constants.IBaseConstants.AdviceWay...");
        }
        return returnValue;
    }

    /**
     * 事件发布处理
     *
     * @param parameters  目标方法的参数
     * @param returnValue 目标方法执行的返回值，如果way是before，该值肯定是null
     * @param eventName   事件名
     * @param eventAction 事件动作
     */
    private void publishEvent(Object[] parameters, Object returnValue, String eventName, String eventAction) {
        if (StringUtils.isNotEmpty(eventName) && StringUtils.isNotEmpty(eventAction)) {
            if (IEventAction.SAVE.equals(eventAction) || IEventAction.UPDATE.equals(eventAction)) {
                SpringContextAware.getApplicationContext()
                        .publishEvent(new LIMSEvent<>(returnValue, eventName, eventAction));
            } else if (IEventAction.DELETE.equals(eventAction) && parameters != null && parameters.length == 1) {
                SpringContextAware.getApplicationContext()
                        .publishEvent(new LIMSEvent<>(parameters[0], eventName, eventAction));
            } else {
                throw new BaseException("未知的事件动作");
            }
        }
    }

    /**
     * 清除缓存
     *
     * @param cacheNames 缓存名数组
     */
    private void clearCache(String... cacheNames) {
        if (cacheNames != null && cacheNames.length > 0) {
            CacheUtil.clearCache(cacheNames);
        }
    }

}