package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.ThreadTransaction;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Vector;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 多线程事务控制切面
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/28
 */
@Aspect
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class ThreadTransactionAspect {

    /**
     * 存储各线程计数器容器以及上下文信息
     */
    private static final Map<String, Object> COUNT_DOWN_LATCH_MAP = new HashMap<>();

    /**
     * 事务管理器
     */
    @Resource
    private PlatformTransactionManager transactionManager;

    /**
     * 定义@ThreadTransaction切入点，该注解用于开启多线程事务控制
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.ThreadTransaction)")
    public void threadTransactionPointcut() {

    }

    /**
     * 定义@ChildThread切入点，该注解用于标记子线程
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.ChildThread)")
    public void childThreadPointcut() {

    }

    /**
     * 主线程处理
     *
     * @param joinPoint 切点
     * @return 主线程执行结果
     * @throws Throwable 异常
     */
    @Around("threadTransactionPointcut()")
    public Object mainProceed(ProceedingJoinPoint joinPoint) throws Throwable {
        //当前线程名称，主线程
        String threadName = Thread.currentThread().getName();
        log.info(".......开始主线程: " + threadName);
        //存放主线程上下文信息
        COUNT_DOWN_LATCH_MAP.put("securityContext", SecurityContextHolder.getContext());
        //存放主线程名称
        COUNT_DOWN_LATCH_MAP.put("mainThreadName", threadName);

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        ThreadTransaction threadTransaction = method.getAnnotation(ThreadTransaction.class);
        //初始化计数器
        CountDownLatch mainDownLatch = new CountDownLatch(1);
        //子线程的数量
        String childThreadCount = Objects.requireNonNull(SPELUtil.parseSpel2Object(threadTransaction.value(), joinPoint)).toString();
        int maxWaitTime = threadTransaction.time();
        CountDownLatch childDownLatch = new CountDownLatch(Integer.parseInt(childThreadCount));
        // 用来记录子线程的运行状态，只要有一个失败就变为true
        AtomicBoolean isRollback = new AtomicBoolean(false);
        // 用来存每个子线程的异常，保障线程安全使用Vector代替List
        Vector<Throwable> exceptionVector = new Vector<>();

        COUNT_DOWN_LATCH_MAP.put(threadName + "-mainDownLatch", mainDownLatch);
        COUNT_DOWN_LATCH_MAP.put(threadName + "-childDownLatch", childDownLatch);
        COUNT_DOWN_LATCH_MAP.put(threadName + "-isRollback", isRollback);
        COUNT_DOWN_LATCH_MAP.put(threadName + "-exceptionVector", exceptionVector);

        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Throwable e) {
            exceptionVector.add(0, e);
            isRollback.set(true);
            //放行所有子线程
            mainDownLatch.countDown();
        }

        if (!isRollback.get()) {
            try {
                //childDownLatch等待，直到所有子线程执行完DB操作(此时还没有提交事务)
                if(!childDownLatch.await(maxWaitTime, TimeUnit.SECONDS)){
                    throw new RuntimeException("...the main thread is waiting for the child thread to execute timeout...");
                }
                // 根据isRollback状态放行子线程的await处，告知是回滚还是提交
                mainDownLatch.countDown();
            } catch (Exception e) {
                isRollback.set(true);
                exceptionVector.add(0, e);
            }
        }
        if (StringUtils.isNotEmpty(exceptionVector)) {
            COUNT_DOWN_LATCH_MAP.remove(threadName + "-mainDownLatch");
            COUNT_DOWN_LATCH_MAP.remove(threadName + "-childDownLatch");
            COUNT_DOWN_LATCH_MAP.remove(threadName + "-isRollback");
            COUNT_DOWN_LATCH_MAP.remove(threadName + "-exceptionVector");
            COUNT_DOWN_LATCH_MAP.remove("securityContext");
            COUNT_DOWN_LATCH_MAP.remove("mainThreadName");
            throw exceptionVector.get(0);
        }
        return result;
    }

    @Around("childThreadPointcut()")
    public Object childProceed(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        SecurityContextHolder.setContext((SecurityContext) COUNT_DOWN_LATCH_MAP.get("securityContext"));

        String threadName = Thread.currentThread().getName();
        String mainThreadName = (String) COUNT_DOWN_LATCH_MAP.get("mainThreadName");
        log.info(".......开始子线程: " + threadName);
        CountDownLatch mainDownLatch = (CountDownLatch) COUNT_DOWN_LATCH_MAP.get(mainThreadName + "-mainDownLatch");
        if (mainDownLatch == null) {
            //主线程未加注解时, 直接执行子事务
            result = joinPoint.proceed();
        } else {
            CountDownLatch childDownLatch = (CountDownLatch) COUNT_DOWN_LATCH_MAP.get(mainThreadName + "-childDownLatch");
            AtomicBoolean rollBackFlag = (AtomicBoolean) COUNT_DOWN_LATCH_MAP.get(mainThreadName + "-isRollback");
            Vector<Throwable> exceptionVector = (Vector<Throwable>) COUNT_DOWN_LATCH_MAP.get(mainThreadName + "-exceptionVector");

            if (rollBackFlag.get()) {
                //如果任何一个子线程出错，那当前线程无需执行
                childDownLatch.countDown();
            } else {
                //对子线程开启新事务
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                TransactionStatus status = transactionManager.getTransaction(def);
                try {
                    result = joinPoint.proceed();
                    childDownLatch.countDown();
                    mainDownLatch.await();
                    // 所有子线程都已经执行完毕判断提交还是回滚
                    if (rollBackFlag.get()) {
                        transactionManager.rollback(status);
                    } else {
                        transactionManager.commit(status);
                    }
                } catch (Throwable e) {
                    exceptionVector.add(0, e);
                    // 回滚
                    transactionManager.rollback(status);
                    // 并把状态设置为true
                    rollBackFlag.set(true);
                    mainDownLatch.countDown();
                    childDownLatch.countDown();
                } finally {
                    // 清理上下文
                    SecurityContextHolder.clearContext();
                }
            }
        }
        return result;
    }
}