package com.sinoyd.base.bizCheck.context;

import com.sinoyd.base.enums.EnumBizCheckItem;
import com.sinoyd.base.service.IBizCheckService;
import com.sinoyd.base.vo.BizCheckParamVO;
import com.sinoyd.base.vo.BizCheckResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 业务检查统一接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/30
 */
@Component
public class BizCheckContext implements IBizCheckContext {

    private Map<String, IBizCheckService> bizCheckMap;

    @Override
    public BizCheckResultVO checkBiz(BizCheckParamVO bizCheckParam) {
        EnumBizCheckItem enumBizCheckItem = EnumBizCheckItem.getEnumItem(bizCheckParam.getBizCheckType());
        BizCheckResultVO checkResultVO = bizCheckMap.get(enumBizCheckItem.getCheckClassName()).check(bizCheckParam.getBizIds());
        checkResultVO.setIsBlock(checkResultVO.getIsBlock())
                .setFailureAmounts(checkResultVO.getCheckItemList().stream().filter(item -> !item.getIsPassed()).count());
        return checkResultVO;
    }


    @Autowired(required = false)
    public void setBizCheckMap(Map<String, IBizCheckService> bizCheckMap) {
        this.bizCheckMap = bizCheckMap;
    }
}