package com.sinoyd.base.cache;

import com.sinoyd.boot.common.exception.BaseException;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;

/**
 * 集合key生成器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/1
 */
@Component
public class CollectionKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        if (params[0] instanceof Collection) {
            return String.join("#", (Collection) params[0]);
        } else {
            throw new BaseException("Cache key is not a collection....");
        }
    }
}