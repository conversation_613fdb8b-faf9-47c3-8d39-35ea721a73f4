package com.sinoyd.base.components;

import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.base.service.CommonService;
import com.sinoyd.base.service.DictService;
import com.sinoyd.boot.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 冗余字段基础组件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/06
 **/
@Component
public abstract class BaseLoadTransientFieldsComponent<T> {

    protected FilePropertyConfig filePropertyConfig;

    protected CommonService commonService;

    protected DictService dictService;

    /**
     * 冗余字段属性赋值
     *
     * @param dataList            数据列表
     * @param transientFieldNames 冗余字段名称数组
     */
    public void loadTransientFields(Collection<T> dataList, String... transientFieldNames) {
        if (StringUtils.isNotEmpty(dataList) && transientFieldNames != null && transientFieldNames.length > 0) {
            loadFieldValues(dataList, transientFieldNames);
        }
    }

    /**
     * 冗余字段属性赋值
     *
     * @param dataList            数据列表
     * @param transientFieldNames 冗余字段名称数组
     */
    public abstract void loadFieldValues(Collection<T> dataList, String... transientFieldNames);

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }

    @Autowired
    public void setCommonService(CommonService commonService) {
        this.commonService = commonService;
    }

    @Autowired
    public void setDictService(DictService dictService) {
        this.dictService = dictService;
    }

}
