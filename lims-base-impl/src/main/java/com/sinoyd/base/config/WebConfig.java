package com.sinoyd.base.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 业务参数配置
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/21
 */
@Configuration
@RefreshScope
@Getter
public class WebConfig {

    /**
     * 网关地址
     */
    @Value("${qcms.gate.path: http://localhost:6101}")
    private String gatePath;
}