package com.sinoyd.base.controller;

import com.sinoyd.base.vo.BizCheckParamVO;
import com.sinoyd.base.vo.BizCheckResultVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.base.bizCheck.context.IBizCheckContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务检查 controller
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/30
 */
@RestController
@RequestMapping("/api/base/biz/check")
public class BizCheckController extends ExceptionHandlerController<IBizCheckContext> {

    /**
     * 业务检查
     *
     * @param param 业务检查参数实例
     * @return 响应结果
     */
    @PostMapping
    public RestResponse<BizCheckResultVO> bizCheck(@RequestBody BizCheckParamVO param) {
        RestResponse<BizCheckResultVO> restResponse = new RestResponse<>();
        restResponse.setData(service.checkBiz(param));
        return restResponse;
    }
}