package com.sinoyd.base.controller;

import com.sinoyd.base.service.CommonService;
import com.sinoyd.base.vo.DeptDropdownVO;
import com.sinoyd.base.vo.DocumentPathVO;
import com.sinoyd.base.vo.GenericDropdownVO;
import com.sinoyd.base.vo.RoleVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/14
 */
@RestController
@RequestMapping("/api/base/common")
public class CommonController extends ExceptionHandlerController<CommonService> {

    /**
     * 获取机构下拉框数据源
     *
     * @return 结果
     */
    @GetMapping("/org")
    public RestResponse<Map<String, String>> findOrg() {
        RestResponse<Map<String, String>> restResp = new RestResponse<>();
        restResp.setData(service.findOrgMap());
        return restResp;
    }

    /**
     * 获取部门下拉框数据源
     *
     * @return 结果
     */
    @GetMapping("/dept")
    public RestResponse<List<DeptDropdownVO>> findDept() {
        RestResponse<List<DeptDropdownVO>> restResp = new RestResponse<>();
        restResp.setData(service.loadDepartmentDropdown());
        return restResp;
    }

    /**
     * 获取角色列表
     *
     * @return 结果
     */
    @GetMapping("/role")
    public RestResponse<List<RoleVO>> findRole(String orgId) {
        RestResponse<List<RoleVO>> restResp = new RestResponse<>();
        restResp.setData(service.findRole(orgId));
        return restResp;
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览实体
     * @param response 输出流
     */
    @PostMapping("/preview")
    public void previewDocument(@RequestBody DocumentPreviewVO vo, HttpServletResponse response) {
        service.previewDocument(vo, response);
    }

    /**
     * 根据枚举类名获取枚举的下拉框展现形式
     *
     * @param moduleName    模块名称
     * @param enumClassName 枚举类名
     * @return 结果
     */
    @GetMapping("/enum")
    public RestResponse<List<GenericDropdownVO>> loadEnumDropdown(@RequestParam(name = "moduleName") String moduleName,
                                                                  @RequestParam(name = "enumClassName") String enumClassName) {
        RestResponse<List<GenericDropdownVO>> restResponse = new RestResponse<>();
        restResponse.setData(service.loadEnumDropdown(moduleName, enumClassName));
        return restResponse;
    }

    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @GetMapping("/path/{code}")
    public RestResponse<DocumentPathVO> getDocumentPath(@PathVariable("code") String code, @RequestParam Map<String, Object> map) {
        RestResponse<DocumentPathVO> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPathFromXml(code, map));
        return restResp;
    }

    /**
     * 获取参数配置中参数值
     *
     * @param paramKey 参数key
     * @return 响应结果
     */
    @GetMapping("/config/param/value")
    public RestResponse<String> getConfigParamValue(@RequestParam("key") String paramKey) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.findConfigValue(paramKey));
        return restResponse;
    }

    /**
     * 获取允许上传的文件类型
     *
     * @return 响应结果
     */
    @GetMapping("/upload-limit-info")
    public RestResponse<Map<String, String>> loadUploadLimitInfo() {
        RestResponse<Map<String, String>> restResponse = new RestResponse<>();
        restResponse.setData(service.loadUploadLimitInfo());
        return restResponse;
    }
}