package com.sinoyd.base.controller;

import com.sinoyd.base.service.ICommonReportGeneratorService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 报表生成基础接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/16
 */
@RestController
@RequestMapping("/api/base/common/report")
public class CommonReportController extends ExceptionHandlerController<ICommonReportGeneratorService> {

    /**
     * 统一基本报表导出
     *
     * @param reportCode 报表编码
     * @param criteria   查询条件
     * @param response   响应体
     * @return 无返回
     */
    @PostMapping("/export/{reportCode}")
    public RestResponse<Void> exportDetail(@PathVariable("reportCode") String reportCode,
                                           @RequestBody Map<String,Object> criteria,
                                           HttpServletResponse response) {
        service.exportData(reportCode, criteria, response);
        return new RestResponse<>();
    }
}
