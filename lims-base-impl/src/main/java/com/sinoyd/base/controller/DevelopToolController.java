package com.sinoyd.base.controller;

import com.sinoyd.base.service.DevelopToolService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 开发工具服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/11
 */
@RestController
@RequestMapping("/api/base/develop/tool")
public class DevelopToolController extends ExceptionHandlerController<DevelopToolService> {

    /**
     * 产生模型
     *
     * @param params 参数
     * @return 结果
     */
    @PostMapping("/model")
    public String generateModel(@RequestBody Map<String, String> params) {
//        RestResponse<Map<String, String>> restResp = new RestResponse<>();
//        restResp.setData(service.generateApifoxObjectModel(params));
        return service.generateApifoxObjectModel(params);
    }

    /**
     * 产生模型
     *
     * @param params 参数
     * @return 结果
     */
    @PostMapping("/model/entity")
    public String generateApifoxObjectModelByFullName(@RequestBody Map<String, String> params) {
        return service.generateApifoxObjectModelByFullName(params);
    }
}