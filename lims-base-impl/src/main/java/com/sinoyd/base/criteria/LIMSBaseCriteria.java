package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;

import java.util.Date;

/**
 * 基础查询条件类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/21
 */
public abstract class LIMSBaseCriteria extends BaseCriteria {

    /**
     * 处理查询条件中结束日期，将结束日期加1天，条件中直接小于该方法返回的日期
     *
     * @param endDateStr 前端传递的结束日期
     * @param dateFormat 日期格式
     * @return 处理后的日期
     */
    protected Date processEndDate(String endDateStr, String dateFormat) {
        Date endDate = DateUtil.stringToDate(endDateStr, dateFormat);
        return DateUtil.dateAdd(endDate, 1, false);
    }

    /**
     * 给查询条件增加百分号，便于模糊检索
     *
     * @param value 前端传递的原始值
     * @return 处理后的查询条件值
     */
    protected String appendPercent(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        return "%" + value.trim() + "%";
    }

}