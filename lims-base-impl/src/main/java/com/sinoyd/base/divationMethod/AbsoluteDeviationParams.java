package com.sinoyd.base.divationMethod;

import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 绝对偏差
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class AbsoluteDeviationParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation       质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(qcValue) && MathUtil.isNumber(avgValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = qcLimit.getCheckItemRange();
                Boolean flag = calculationResult(qcRangeLimit, new BigDecimal(avgValue));
                if (flag) {
                    if (valueList.size() > 3) {
                        List<String> yyPxValList = valueList.stream().skip(3).limit(valueList.size()).collect(Collectors.toList());
                        //多个平行样的情况，相对偏差按照标准差的方式进行计算
                        retStr = multiAbsoluteDeviation(yyPxValList, avgValue, qcLimit.getQcInfo().getSignificantDigit(),
                                qcLimit.getQcInfo().getDecimalDigit());
                    } else {
                        String deviationFormula = deviation.getFormula();
                        if (StringUtils.isNotEmpty(deviationFormula)) {
                            retStr = calculateDeviationFormula(deviationFormula, samValue, qcValue);
                        } else {
                            //|a-b|
                            retStr = (new BigDecimal(samValue).subtract(new BigDecimal(qcValue)).abs()).toString();
                        }
                    }
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
