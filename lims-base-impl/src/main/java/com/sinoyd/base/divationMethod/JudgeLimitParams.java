package com.sinoyd.base.divationMethod;

import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 限值判定
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class JudgeLimitParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        String samValue = valueList.get(0);
        if (MathUtil.isNumber(samValue)) {
            //允许质控限值
            String allowLimit = qcLimit.getValidLimitValue();
            if (calculationResult(allowLimit, new BigDecimal(samValue))) {
                retStr = "合格";
            } else {
                retStr = "不合格";
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
