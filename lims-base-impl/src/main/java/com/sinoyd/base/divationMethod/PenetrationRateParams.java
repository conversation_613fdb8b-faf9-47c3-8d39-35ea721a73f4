package com.sinoyd.base.divationMethod;

import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 穿透率
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class PenetrationRateParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 1) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(qcValue) && StringUtils.isNotEmpty(qcLimit.getFormula())) {
                //判断数据偏差是否使用穿透率 -- 检查项范围
                String qcRangeLimit = qcLimit.getCheckItemRange();
                Boolean flag = true;
                if (StringUtils.isNotEmpty(qcRangeLimit)) {
                    flag = calculationResult(qcRangeLimit, new BigDecimal(samValue));
                }
                if (flag) {
                    Map<String, Object> limitMap = new HashMap<>(2);
                    limitMap.put("a", new BigDecimal(samValue));
                    limitMap.put("b", new BigDecimal(qcValue));
                    try {
                        retStr = calculateService.calculateRule(webParamConfig.getGatePath(), Collections.singletonList(qcLimit.getFormula()), limitMap);
                        if (StringUtils.isEmpty(retStr)) {
                            retStr = "0";
                        }
                    } catch (Exception ex) {
                        throw new BaseException("计算穿透率错误");
                    }
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
