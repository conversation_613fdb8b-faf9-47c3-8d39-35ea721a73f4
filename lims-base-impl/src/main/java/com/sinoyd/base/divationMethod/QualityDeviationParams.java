package com.sinoyd.base.divationMethod;

import com.sinoyd.base.config.WebConfig;
import com.sinoyd.base.enums.EnumCoefficientTable;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.revise.ReviseDataFactory;
import com.sinoyd.common.utils.CalculationUtil;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 质控计算
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/09/06
 */
@Component
public abstract class QualityDeviationParams {

    private static final Pattern PATTERN = Pattern.compile("\\[\\w+\\]");

    protected CalculateService calculateService;

    protected WebConfig webParamConfig;

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    public abstract void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation);

    /**
     * 计算偏差公式
     * 1.处理公式中的100%, 在结果计算出之后再处理百分比数据
     *
     * @param formula 偏差公式
     * @param a       偏差值A
     * @param b       偏差值B
     * @return 计算结果
     */
    public String calculateDeviationFormula(String formula, String a, String b) {
        //公式替换掉百分比符号%
        String replaceFormula = formula.replace("%", "");
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("a", new BigDecimal(a));
        paramMap.put("b", new BigDecimal(b));
        return calculateService.calculateRule(webParamConfig.getGatePath(),
                Collections.singletonList(replaceFormula), paramMap);
    }

    /**
     * 质控限值范围通用计算方法
     *
     * @param range 范围
     * @return 是否通过
     */
    public boolean calculationResult(String range, BigDecimal data) {
        boolean flag = true;
        if (StringUtils.isNotEmpty(range)) {
            Matcher m = PATTERN.matcher(range);
            while (m.find()) {
                String matchWord = m.group(0);
                Map<String, Object> map = new HashMap<>();
                map.put(matchWord.replace("[", "").replace("]", ""), data);
                // 修正之后的范围
                List<String> rangeCorrect = CalculationUtil.parseFormulas(Collections.singletonList(range));
                String result = calculateService.calculateRule(webParamConfig.getGatePath(), rangeCorrect, map);
                if ("false".equals(result)) {
                    flag = false;
                }
            }
        }
        return flag;
    }

    /**
     * 计算标准差S（bigDecimal）
     *
     * @param numberList 数据列表
     * @param number     均值
     * @return 标准差
     */
    protected BigDecimal standardDeviationBigDecimal(List<String> numberList, String number) {
        if (numberList == null || numberList.size() == 0) {
            throw new RuntimeException("标准差计算时接收的原始数据集合是空");
        }
        numberList.forEach(p -> {
            if (!MathUtil.isNumber(p)) {
                throw new RuntimeException("标准差计算时，原始数据集合中存在非数字");
            }
        });
        if (!MathUtil.isNumber(number)) {
            throw new RuntimeException("标准差计算平均值存在非数字或空值");
        }
        BigDecimal avg = new BigDecimal(number);
        List<BigDecimal> values = numberList.parallelStream().map(BigDecimal::new).collect(Collectors.toList());
        int size = values.size();
        BigDecimal s = BigDecimal.ZERO;
        //求方差
        for (BigDecimal value : values) {
            s = s.add((value.subtract(avg)).multiply(value.subtract(avg)));
        }
        s = s.divide(new BigDecimal(size), 10, BigDecimal.ROUND_HALF_EVEN);
        //开根号
        return sqrt(s, 10);
    }

    /**
     * 置信系数计算
     *
     * @param count     个数
     * @param deviation 标准差值
     * @return 置信系数值
     */
    protected BigDecimal confidenceCoefficient(int count, String deviation) {
        String coefficient = BigDecimal.ZERO.toString();
        BigDecimal actionValue = new BigDecimal(deviation);
        //系数值(需要count大于5)
        if (count > BigDecimal.ROUND_HALF_DOWN) {
            coefficient = EnumCoefficientTable.EnumReValue(count - 1);
        }
        //置信系数 = 系数值*标准偏差/根号count
        return new BigDecimal(coefficient).multiply(actionValue)
                .divide(sqrt(new BigDecimal(count), BigDecimal.ROUND_HALF_EVEN), BigDecimal.ROUND_HALF_DOWN);
    }

    /**
     * 多个平行样时，计算绝对偏差(标准差S)
     *
     * @param strValues    数据列表
     * @param avg          平均值
     * @param deviationSig 标准差有效位
     * @param deviationDec 标准差小数位
     * @return 绝对偏差
     */
    protected String multiAbsoluteDeviation(List<String> strValues, String avg, Integer deviationSig, Integer deviationDec) {
        //计算标准差
        BigDecimal s = standardDeviationBigDecimal(strValues, avg);
        //标准差s需要修约，修约方式为：保留三位有效，两位小数。（修约规则常量维护）
        s = new BigDecimal(ReviseDataFactory.revise(s.toPlainString(), deviationSig, deviationDec, false));
        return s.toString();
    }

    /**
     * 开根号(牛顿大数法)
     *
     * @param value 数据
     * @param scale 小数位数
     * @return 开根号结果
     */
    private BigDecimal sqrt(BigDecimal value, int scale) {
        BigDecimal num2 = BigDecimal.valueOf(2);
        int precision = 100;
        MathContext mc = new MathContext(precision, RoundingMode.HALF_UP);
        BigDecimal deviation = value;
        if (value.compareTo(BigDecimal.ZERO) != 0) {
            int cnt = 0;
            while (cnt < precision) {
                deviation = (deviation.add(value.divide(deviation, mc))).divide(num2, mc);
                cnt++;
            }
            deviation = deviation.setScale(scale, BigDecimal.ROUND_HALF_UP);
        }
        return deviation;
    }

    @Autowired
    @Lazy
    public void setCalculateService(CalculateService calculateService) {
        this.calculateService = calculateService;
    }

    @Autowired
    public void setWebParamConfig(WebConfig webParamConfig) {
        this.webParamConfig = webParamConfig;
    }
}
