package com.sinoyd.base.divationMethod;

import com.sinoyd.base.enums.EnumQcLimitCheckItem;
import com.sinoyd.base.enums.EnumQcType;
import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 回收率
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class RecoveryParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation       质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        String qcAddedValue = "";
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcTestValue = valueList.get(1);
            //加入量值
            String qcValue = valueList.get(2);
            //原样的出证结果
            String testValue = samValue;
            if (valueList.size() > 3) {
                if (EnumQcLimitCheckItem.出证结果.getValue().equals(qcLimit.getCheckItem())) {
                    testValue = valueList.get(3);
                }
            }
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(qcTestValue) && MathUtil.isNumber(qcValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = qcLimit.getCheckItemRange();
                Boolean flag = true;
                if (StringUtils.isNotEmpty(qcRangeLimit) && MathUtil.isNumber(testValue)) {
                    flag = calculationResult(qcRangeLimit, new BigDecimal(testValue));
                }
                if (flag) {
                    //增值
                    BigDecimal zz = BigDecimal.ZERO;
                    //加标回收率
                    BigDecimal qcRecoverRate = BigDecimal.ZERO;
                    //倍数1000
                    BigDecimal baseValue1000 = new BigDecimal(1000);
                    //倍数100
                    BigDecimal baseValue100 = new BigDecimal(100);
                    try {
                        BigDecimal realSampleTestValueDecimal = new BigDecimal(samValue);
                        BigDecimal qcTestValueDecimal = new BigDecimal(qcTestValue);
                        BigDecimal qcValueDecimal = new BigDecimal(qcValue);
                        //区分回收率是替代回收，还是加标回收
                        if (EnumQcType.替代样.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                            //加标回收率
                            qcRecoverRate = realSampleTestValueDecimal.divide(qcValueDecimal, 20, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        } else {
                            zz = (qcTestValueDecimal.subtract(realSampleTestValueDecimal)).multiply(baseValue1000).divide(baseValue1000);
                            //加标回收率
                            qcRecoverRate = zz.divide(qcValueDecimal, 20, BigDecimal.ROUND_HALF_UP).multiply(baseValue100);
                        }
                    } catch (Exception ex) {
                        throw new BaseException("加标回收率计算发生错误");
                    }
                    //保留三位有效位数
                    //绝对值
                    retStr = qcRecoverRate.abs().toString();
                    //正好是三位整数
                    if (retStr.indexOf(".") == 3) {
                        retStr = qcRecoverRate.setScale(0, RoundingMode.HALF_EVEN).toString();
                    } else {
                        retStr = String.format("%s", qcRecoverRate.setScale(1, RoundingMode.HALF_EVEN).toString());
                    }
                    qcAddedValue = zz.toString();
                }
            }
        }
        //增值
        deviation.setQcAddedValue(qcAddedValue);
        //回收率
        deviation.setDeivationValue(retStr);
    }
}