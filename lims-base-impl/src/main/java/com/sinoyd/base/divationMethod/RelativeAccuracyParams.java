package com.sinoyd.base.divationMethod;

import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 相对准确度
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class RelativeAccuracyParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 0) {
            //实验室均值
            String syAvg = valueList.get(0);
            //在线集合
            List<String> zxList = deviation.getOnlineDataList();
            //实验室集合
            List<String> syList = deviation.getLaboratoryData();
            //判断数据偏差是否使用绝对偏差 -- 检查项范围
            String qcRangeLimit = qcLimit.getCheckItemRange();
            Boolean flag = true;
            if (qcLimit.getHasCheckItem()) {
                flag = calculationResult(qcRangeLimit, new BigDecimal(syAvg));
            }
            if (flag) {
                //实验室数据个数
                int count = syList.size();
                retStr = "无法计算";
                //实验数据必须大于0
                if (count > 0) {
                    //对差集合
                    List<String> diList = new ArrayList<>();
                    for (int i = 0; i < count; i++) {
                        //存在在线值
                        if (zxList.size() > i) {
                            diList.add(MathUtil.subtract(zxList.get(i), syList.get(i)));
                        }
                    }
                    //对差均值
                    String diAvg = MathUtil.calculateAvg(diList);
                    //标准偏差
                    BigDecimal standardDeviation = standardDeviationBigDecimal(diList, diAvg);
                    BigDecimal zxCoefficient = confidenceCoefficient(count, standardDeviation.toString());
                    //相对准确度 = （|方差均值|+|置信系数|）/实验室均值
                    BigDecimal accuracyRelative = new BigDecimal(diAvg).abs().add(zxCoefficient.abs()).divide(new BigDecimal(syAvg), BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(100));
                    retStr = accuracyRelative.toString();
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
