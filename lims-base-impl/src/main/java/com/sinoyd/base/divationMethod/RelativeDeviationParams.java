package com.sinoyd.base.divationMethod;

import com.sinoyd.base.enums.EnumQcType;
import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 相对偏差
 *
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class RelativeDeviationParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(qcValue) && MathUtil.isNumber(avgValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = qcLimit.getCheckItemRange();
                Boolean flag = true;
                if (qcLimit.getHasCheckItem()) {
                    if (StringUtils.isNotEmpty(qcRangeLimit) && !EnumQcType.曲线校核.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                        flag = calculationResult(qcRangeLimit, new BigDecimal(avgValue));
                    } else if (StringUtils.isNotEmpty(qcRangeLimit) && EnumQcType.曲线校核.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                        //曲线校核用加入量做判断条件
                        flag = calculationResult(qcRangeLimit, new BigDecimal(qcValue));
                    }
                }
                if (flag) {
                    if (valueList.size() > 3) {
                        List<String> yyPxValList = valueList.stream().skip(3).limit(valueList.size()).collect(Collectors.toList());
                        //多个平行样的情况，相对偏差按照标准差的方式进行计算
                        //获取标准差修约规则
                        retStr = multiAbsoluteDeviation(yyPxValList, avgValue, qcLimit.getQcInfo().getSignificantDigit(),
                                qcLimit.getQcInfo().getDecimalDigit());
                    } else {
                        //根据质控类型，质控等级获取配置的偏差公式
                        String deviationFormula = deviation.getFormula();
                        if (StringUtils.isNotEmpty(deviationFormula)) {
                            deviationFormula = deviationFormula.replace("%", "");
                            BigDecimal a = new BigDecimal(samValue);
                            BigDecimal c = new BigDecimal(samValue).subtract(new BigDecimal(qcValue)).abs();
                            //判断分母是否为0，如果分母为0则无法计算
                            if (c.compareTo(BigDecimal.ZERO) != 0) {
                                if (deviationFormula.contains("a+b")) {
                                    BigDecimal sumValue = new BigDecimal(samValue).add(new BigDecimal(qcValue));
                                    if (sumValue.compareTo(BigDecimal.ZERO) == 0) {
                                        retStr = "无法计算，除数为0";
                                    }
                                } else {
                                    if (a.compareTo(BigDecimal.ZERO) == 0) {
                                        retStr = "无法计算，除数为0";
                                    }
                                }
                                if (!retStr.contains("无法计算")) {
                                    retStr = calculateDeviationFormula(deviationFormula, a.toString(), qcValue);
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }

                            if (MathUtil.isNumber(retStr)) {
                                retStr = new BigDecimal(retStr).multiply(new BigDecimal("100")).round(new MathContext(10, RoundingMode.HALF_EVEN)).toString();
                            }
                        } else {
                            BigDecimal subtract = new BigDecimal(samValue).subtract(new BigDecimal(qcValue)).abs();
                            //公式为 (|x-y|)/(x+y)*100
                            BigDecimal sumValue = new BigDecimal(samValue).add(new BigDecimal(qcValue));
                            //两数和与两数差 都不等于 0 不然结果都是0
                            if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
                                //判断分母是否为0，为0则无法计算
                                if (sumValue.compareTo(BigDecimal.ZERO) != 0) {
                                    retStr = (subtract.divide(sumValue, 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
                                } else {
                                    retStr = "无法计算，除数为0";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }
                        }
                    }
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
