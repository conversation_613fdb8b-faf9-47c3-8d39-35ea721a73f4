package com.sinoyd.base.divationMethod;

import com.sinoyd.base.enums.EnumQcType;
import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;

/**
 * 相对误差
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class RelativeErrorParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation       质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(qcValue) && MathUtil.isNumber(avgValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = qcLimit.getCheckItemRange();
                Boolean flag = true;
                if (StringUtils.isNotEmpty(qcRangeLimit) && !EnumQcType.曲线校核.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                    flag = calculationResult(qcRangeLimit, new BigDecimal(avgValue));
                } else if (StringUtils.isNotEmpty(qcRangeLimit) && EnumQcType.曲线校核.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                    //曲线校核用加入量做判断条件
                    flag = calculationResult(qcRangeLimit, new BigDecimal(samValue));
                }
                if (flag) {
                    String deviationFormula = deviation.getFormula();
                    if (StringUtils.isNotEmpty(deviationFormula)) {
                        retStr = getRetStrByFormula(samValue, qcValue, deviationFormula);
                    } else {
                        //相对误差算法 |A-B|/A*100%，在原有的基础上A-B的值取绝对值
                        //曲线校核和校正系数公式为 (|x-y|)/y*100
                        if (EnumQcType.曲线校核.getValue().equals(qcLimit.getQcInfo().getQcType()) ||
                                EnumQcType.校正系数检验.getValue().equals(qcLimit.getQcInfo().getQcType())) {
                            //曲线校核样和校正系数检验样，原样结果传的是理论值和校正点浓度,需要调换samValue和qcValue的值
                            String tmp;
                            tmp = samValue;
                            samValue = qcValue;
                            qcValue = tmp;
                            BigDecimal subtract = new BigDecimal(samValue).subtract(new BigDecimal(qcValue));
                            if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
                                if (new BigDecimal(qcValue).compareTo(BigDecimal.ZERO) != 0) {
                                    retStr = (subtract.divide(new BigDecimal(qcValue), 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
                                } else {
                                    retStr = "无法计算";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }
                        } else {
                            BigDecimal subtract = new BigDecimal(qcValue).subtract(new BigDecimal(samValue));
                            if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                                if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0) {
                                    retStr = (subtract.divide(new BigDecimal(samValue), 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
                                } else {
                                    retStr = "无法计算";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }
                        }
                    }
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }

    /**
     * 根据公式配置获取误差
     *
     * @param samValue         样品数据
     * @param qcValue          质控数据
     * @param deviationFormula 计算公式
     * @return 计算结构
     */
    private String getRetStrByFormula(String samValue, String qcValue, String deviationFormula) {
        String retStr = "";
        BigDecimal a = new BigDecimal(samValue), b = new BigDecimal(qcValue), c = a.subtract(b).abs();
        //判断分母是否为0，如果分母为0则无法计算
        if (c.compareTo(BigDecimal.ZERO) != 0) {
            if (deviationFormula.contains("a+b")) {
                BigDecimal sumValue = a.add(b);
                if (sumValue.compareTo(BigDecimal.ZERO) == 0) {
                    retStr = "无法计算，除数为0";
                }
            } else {
                if (a.compareTo(BigDecimal.ZERO) == 0) {
                    retStr = "无法计算，除数为0";
                }
            }
            if (!retStr.contains("无法计算")) {
                retStr = calculateDeviationFormula(deviationFormula, a.toString(), b.toString());
            }
        } else {
            retStr = BigDecimal.ZERO.toString();
        }
        if (MathUtil.isNumber(retStr)) {
            retStr = new BigDecimal(retStr).multiply(new BigDecimal("100")).round(new MathContext(10, RoundingMode.HALF_EVEN)).toString();
        }
        return retStr;
    }
}
