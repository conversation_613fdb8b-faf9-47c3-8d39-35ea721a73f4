package com.sinoyd.base.divationMethod;

import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 相对误差
 * <AUTHOR>
 * @version V1.0.0 2024/8/30
 * @since V100R001
 */
@Component
public class TestLimitParams extends QualityDeviationParams {

    /**
     * 质控限值计算
     *
     * @param qcLimit   质控限值配置
     * @param valueList 数据结果
     * @param deviation 质控计算公式
     */
    @Override
    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation) {
        String retStr = "";
        if (valueList.size() > 1) {
            //比较值
            String samValue = valueList.get(0);
            //判定值
            String limitValue = valueList.get(1);
            if (MathUtil.isNumber(samValue) && MathUtil.isNumber(limitValue)) {
                //比较 samValue小于 判定值是合格 否则是不合格
                if (new BigDecimal(samValue).compareTo(new BigDecimal(limitValue)) > 0) {
                    retStr = "不合格";
                } else {
                    retStr = "合格";
                }
            }
        }
        deviation.setDeivationValue(retStr);
    }
}
