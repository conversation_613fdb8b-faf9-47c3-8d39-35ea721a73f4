package com.sinoyd.base.divationMethod.context;

import com.sinoyd.base.divationMethod.QualityDeviationParams;
import com.sinoyd.base.enums.EnumEvaluationWay;
import com.sinoyd.base.vo.DeviationVO;
import com.sinoyd.base.vo.QcLimitValueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 质控计算
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/09/06
 */
@Component
public class QualityDeviationContext {

    private Map<String, QualityDeviationParams> paramsMap;

    public void deviationValue(QcLimitValueVO qcLimit, List<String> valueList, DeviationVO deviation, Integer dataType) {
        EnumEvaluationWay evaluationWay = EnumEvaluationWay.getEnumItem(dataType);
        paramsMap.get(evaluationWay.getBeanName()).deviationValue(qcLimit, valueList, deviation);
    }

    /**
     * 判断质控限制是否合格
     * @param range    判断公式
     * @param data     判断数据
     * @return 返回结果
     */
    public boolean calculationResult(String range, BigDecimal data) {
        //判定是否合格计算固定
        return paramsMap.get(EnumEvaluationWay.限值判定.getBeanName()).calculationResult(range, data);
    }

    @Autowired
    public void setParamsMap(Map<String, QualityDeviationParams> paramsMap) {
        this.paramsMap = paramsMap;
    }
}
