package com.sinoyd.base.loader.dict;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.excel.dict.loader.IDictDataLoader;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Code字典加载器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/16
 */
@Component
public class CodeDictLoader implements IDictDataLoader {

    private CodeService codeService;

    @Override
    public Map<String, BiMap<String, Object>> loadDictData(String... dictTypes) {
        Map<String, BiMap<String, Object>> map = new HashMap<>();
        for (String dictType : dictTypes) {
            BiMap<String, Object> biMap = HashBiMap.create();
            if (!map.keySet().contains(dictType)) {
                List<DtoCode> codeList = codeService.findCodes(dictType);
                if (StringUtils.isNotEmpty(codeList)) {
                    for (DtoCode dtoCode : codeList) {
                        biMap.put(dtoCode.getDictCode(), dtoCode.getDictName());
                    }
                    map.put(dictType, biMap);
                }
            }
        }
        return map;
    }

    @Override
    public String getDictType() {
        return null;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}