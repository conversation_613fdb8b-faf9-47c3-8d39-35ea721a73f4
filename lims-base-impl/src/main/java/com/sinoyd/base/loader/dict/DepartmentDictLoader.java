package com.sinoyd.base.loader.dict;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.excel.constants.IExcelConstants;
import com.sinoyd.excel.dict.loader.IDictDataLoader;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门字典加载器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/16
 */
@Component
public class DepartmentDictLoader implements IDictDataLoader {

    private DepartmentService departmentService;

    @Override
    public Map<String, BiMap<String, Object>> loadDictData(String... dictType) {
        Map<String, BiMap<String, Object>> map = new HashMap<>();
        List<DtoDepartment> departmentList = departmentService.findByOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        BiMap<String, Object> biMap = HashBiMap.create();
        for (DtoDepartment dtoDepartment : departmentList) {
            biMap.put(dtoDepartment.getId(), dtoDepartment.getDeptName());
        }
        map.put(getDictType(), biMap);
        return map;
    }

    @Override
    public String getDictType() {
        return IExcelConstants.SimulateDictType.DEPARTMENT;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }
}