package com.sinoyd.base.loader.dict;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.excel.constants.IExcelConstants;
import com.sinoyd.excel.dict.loader.IDictDataLoader;
import com.sinoyd.frame.criteria.OrgCriteria;
import com.sinoyd.frame.dto.DtoOrg;
import com.sinoyd.frame.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机构字典加载器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/16
 */
@Component
public class OrgDictLoader implements IDictDataLoader {

    private OrgService orgService;

    @Override
    public Map<String, BiMap<String, Object>> loadDictData(String... dictType) {
        Map<String, BiMap<String, Object>> map = new HashMap<>();
        List<DtoOrg> orgList = orgService.findList(new OrgCriteria());
        BiMap<String, Object> biMap = HashBiMap.create();
        for (DtoOrg dtoOrg : orgList) {
            biMap.put(dtoOrg.getId(), dtoOrg.getOrgName());
        }
        map.put(getDictType(), biMap);
        return map;
    }

    @Override
    public String getDictType() {
        return IExcelConstants.SimulateDictType.ORG;
    }

    @Autowired
    @Lazy
    public void setOrgService(OrgService orgService) {
        this.orgService = orgService;
    }
}