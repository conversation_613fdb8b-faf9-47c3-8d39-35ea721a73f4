package com.sinoyd.base.loader.dict;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.excel.constants.IExcelConstants;
import com.sinoyd.excel.dict.loader.IDictDataLoader;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.frame.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色字典加载器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/19
 */
@Component
public class RoleDictLoader implements IDictDataLoader {

   private RoleService roleService;

    @Override
    public Map<String, BiMap<String, Object>> loadDictData(String... dictType) {
        Map<String, BiMap<String, Object>> map = new HashMap<>();
        List<DtoRole> roleList = roleService.findRoleByOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        BiMap<String, Object> biMap = HashBiMap.create();
        for (DtoRole role : roleList) {
            biMap.put(role.getRoleId(), role.getRoleName());
        }
        map.put(getDictType(), biMap);
        return map;
    }

    @Override
    public String getDictType() {
        return IExcelConstants.SimulateDictType.ROLE;
    }

    @Autowired(required = false)
    @Lazy
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }
}