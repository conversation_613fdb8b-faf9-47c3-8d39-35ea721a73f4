package com.sinoyd.base.loader.dict;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.excel.constants.IExcelConstants;
import com.sinoyd.excel.dict.loader.IDictDataLoader;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 框架用户字典加载器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/16
 */
@Component
public class UserDictLoader implements IDictDataLoader {

    private UserService userService;

    @Override
    public Map<String, BiMap<String, Object>> loadDictData(String... dictType) {
        Map<String, BiMap<String, Object>> map = new HashMap<>();
        List<DtoUser> userList = userService.findAll();
        BiMap<String, Object> biMap = HashBiMap.create();
        for (DtoUser dtoUser : userList) {
            biMap.put(dtoUser.getId(), dtoUser.getUserName());
        }
        map.put(getDictType(), biMap);
        return map;
    }

    @Override
    public String getDictType() {
        return IExcelConstants.SimulateDictType.USER;
    }

    @Autowired
    @Lazy
    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}