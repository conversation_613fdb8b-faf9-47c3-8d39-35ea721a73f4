package com.sinoyd.base.reflection;

import lombok.extern.slf4j.Slf4j;

/**
 * 反射工具类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Slf4j
public class ReflectUtil {


    /**
     * 获取扩展后的目标对象
     *
     * @return 扩展后的目标对象
     */
    public static Object getTarget(DynamicBean dynamicBean) {
        return dynamicBean.getTarget();
    }

    /**
     * 获取属性的值
     *
     * @param dynamicBean 实例
     * @param property    属性名
     * @return 属性值
     */
    public static Object getExtendPropertyValue(DynamicBean dynamicBean, String property) {
        return dynamicBean.getValue(property);
    }

}