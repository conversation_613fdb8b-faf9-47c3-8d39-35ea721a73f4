package com.sinoyd.base.repository;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * LIMS 默认Repository接口
 *
 * @param <T>  实体
 * @param <ID> 主键
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@NoRepositoryBean
public interface LimsRepository<T, ID extends Serializable> extends CrudRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 实体
     */
    T findOne(ID id);

    /**
     * 批量更新
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    <S extends T> List<S> batchSave(Iterable<S> entities);

    /**
     * 批量更新
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    <S extends T> List<S> batchUpdate(Iterable<S> entities);

    /**
     * 实时更新，且支持null值的更新
     *
     * @param paramMap 实体Map形式 Map<属性名, 值>
     * @return 实体
     */
    T realtimeUpdate(Map<String, Object> paramMap);


    /**
     * 实时更新，且支持null值的更新
     *
     * @param params 实体Map格式的集合
     * @return 实体集合
     */
    List<T> realtimeUpdate(Collection<Map<String, Object>> params);

}
