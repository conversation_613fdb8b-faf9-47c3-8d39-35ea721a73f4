package com.sinoyd.base.repository;

import com.sinoyd.base.service.ITemplateTable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 分表数据访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
public interface TemplateTableRepository<T extends ITemplateTable> {

    /**
     * 查询单条数据
     *
     * @param tableName 表名
     * @param id        主键
     * @param tClass    实体类型
     * @return 实体
     */
    T findOne(String tableName, String id, Class<T> tClass);

    /**
     * 保存数据
     *
     * @param t 实体
     * @return 实体
     */
    T save(T t);

    /**
     * 批量保存
     *
     * @param entities 实体集合
     * @return 实体集合
     */
    List<T> saveAll(Collection<T> entities);

    /**
     * 更新数据
     *
     * @param tableName 表名
     * @param t         实体
     * @return 实体
     */
    T update(String tableName, T t);

    /**
     * 批量更新
     *
     * @param tableName 表名
     * @param entities  实体集合
     * @return 实体集合
     */
    List<T> updateAll(String tableName, Collection<T> entities);

    /**
     * 逻辑删除
     *
     * @param tableName 表名
     * @param ids       主键集合
     * @return 删除的记录数
     */
    Integer logicDeleteById(String tableName, Collection<String> ids);

    /**
     * 根据条件查询
     *
     * @param tableName 表名
     * @param condition 查询条件参数
     * @param tClass    实体类型
     * @return 实体集合
     */
    List<T> queryList(String tableName, Map<String, Object> condition, Class<T> tClass);

    /**
     * 根据表名和查询条件sql查询
     *
     * @param tableName    表名
     * @param conditionSql 条件sql
     * @param condition    查询条件参数
     * @param tClass       实体类型
     * @return 实体集合
     */
    List<T> queryList(String tableName, String conditionSql, Map<String, Object> condition, Class<T> tClass);

    /**
     * 获取数据库表名
     *
     * @return 表名
     */
    String getTableName();
}
