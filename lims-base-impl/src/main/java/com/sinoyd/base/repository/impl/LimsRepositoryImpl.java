package com.sinoyd.base.repository.impl;

import cn.hutool.json.JSONUtil;
import com.sinoyd.base.annotations.UnIgnoreNull;
import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.boot.common.exception.BaseException;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;

/**
 * LIMS 默认Repository实现
 *
 * @param <T>  实体
 * @param <ID> 主键
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Slf4j
@NoRepositoryBean
@SuppressWarnings("unchecked")
public class LimsRepositoryImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements LimsRepository<T, ID> {

    private final int BATCH_SIZE = 50;
    private final EntityManager em;
    private final JpaEntityInformation<T, ?> entityInformation;

    public LimsRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.em = entityManager;
        this.entityInformation = entityInformation;
    }

    public LimsRepositoryImpl(Class<T> domainClass, EntityManager em) {
        super(domainClass, em);
        this.em = em;
        this.entityInformation = JpaEntityInformationSupport.getEntityInformation(domainClass, em);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> S save(S s) {
        Assert.notNull(s, "parameter can not be null");
        ID id = (ID) entityInformation.getId(s);
        Optional<T> target = findById(id);
        if (!target.isPresent()) {
            em.persist(s);
            return s;
        } else {
            String[] nullProperties = getNullProperties(s);
            BeanUtils.copyProperties(s, target.get(), nullProperties);
            em.merge(target.get());
            return (S) em.merge(target.get());
        }
    }

    @Override
    public <S extends T> List<S> batchUpdate(Iterable<S> entities) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            S entity = em.merge(s);
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
            list.add(entity);
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        entities.forEach(list::add);
        return list;
    }

    @Override
    public T realtimeUpdate(Map<String, Object> paramMap) {
        ID id = (ID) paramMap.get("id");
        Optional<T> target = findById(id);
        T source = (T) JSONUtil.toBean(JSONUtil.toJsonStr(paramMap), target.get().getClass());
        try {
            Field[] fields = getClassFields(target.get().getClass());
            for (Map.Entry<String, Object> map : paramMap.entrySet()) {
                Optional<Field> fieldOptional = Arrays.stream(fields).filter(p -> p.getName().equals(map.getKey()))
                        .findFirst();
                if (fieldOptional.isPresent()) {
                    fieldOptional.get().setAccessible(true);
                    fieldOptional.get().set(target.get(), fieldOptional.get().get(source));
                }
            }
            return em.merge(target.get());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<T> realtimeUpdate(Collection<Map<String, Object>> params) {
        List<ID> ids = params.stream().map(m -> (ID) m.get("id")).collect(Collectors.toList());
        List<T> targets = findAllById(ids);
        List<T> sources = params.stream().map(m -> (T) JSONUtil.toBean(JSONUtil.toJsonStr(
                m), targets.get(0).getClass())).collect(Collectors.toList());
        Map<ID, T> targetMap = convert2Map(targets);
        Map<ID, T> sourceMap = convert2Map(sources);

        List<T> result = new ArrayList<>();
        try {
            Field[] fields = getClassFields(getTClass());
            for (Map<String, Object> param : params) {
                ID id = (ID) param.get("id");
                T source = sourceMap.get(id);
                T target = targetMap.get(id);
                for (Map.Entry<String, Object> map : param.entrySet()) {
                    Optional<Field> fieldOptional = Arrays.stream(fields).filter(p -> p.getName().equals(map.getKey()))
                            .findFirst();
                    if (fieldOptional.isPresent()) {
                        fieldOptional.get().setAccessible(true);
                        fieldOptional.get().set(target, fieldOptional.get().get(source));
                    }
                }
                result.add(em.merge(target));
                ;
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * 该方法会批量处理保存和新增的数据，会自行判断是新增还是修改数据，但是效率没有单一的批量插入和修改好
     * 数据量不大的情况下可以使用
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <S extends T> List<S> saveAll(Iterable<S> entities) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            ID id = (ID) entityInformation.getId(s);
            Optional<T> target = findById(id);
            if (!target.isPresent()) {
                em.persist(s);
                list.add(s);
            } else {
                String[] nullProperties = getNullProperties(s);
                BeanUtils.copyProperties(s, target.get(), nullProperties);
                list.add((S) em.merge(target.get()));
            }
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> List<S> batchSave(Iterable<S> entities) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            ID id = (ID) entityInformation.getId(s);
            em.persist(s);
            list.add(s);
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        return list;
    }

    @Override
    public T findOne(ID id) {
        Optional<T> optional = super.findById(id);
        if (optional.isPresent()) {
            return optional.get();
        }
        throw new BaseException("记录不存在[Entity: " + super.getDomainClass() + "; id: " + id + "]");
    }

    /**
     * 获取对象的空属性
     *
     * @param src 对象
     * @return 空属性数组
     */
    private String[] getNullProperties(Object src) {
        //获取Bean的空属性
        Set<String> properties = new HashSet<>();
        try {
            Field[] fields = getClassFields(src.getClass());
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(src);
                UnIgnoreNull unIgnoreNull = field.getAnnotation(UnIgnoreNull.class);
                if ((unIgnoreNull == null || !unIgnoreNull.value()) && value == null) {
                    properties.add(field.getName());
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return properties.toArray(new String[0]);
    }

    /**
     * 根据class实例获取字段（包含父类的）
     *
     * @param clazz class实例
     * @return 属性实例集合
     */
    private Field[] getClassFields(Class<?> clazz) {
        List<Field> list = new ArrayList();

        Field[] fields;
        do {
            fields = clazz.getDeclaredFields();

            for (int i = 0; i < fields.length; ++i) {
                list.add(fields[i]);
            }

            clazz = clazz.getSuperclass();
        } while (clazz != Object.class && clazz != null);

        return list.toArray(fields);
    }

    /**
     * 获取泛型T的Class类型
     *
     * @return T的Class类型
     */
    private Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 将给定的T类型列表转换为ID到T的映射表
     *
     * @param tList 给定的T类型列表
     * @return 转换后的ID到T的映射表
     */
    private Map<ID, T> convert2Map(List<T> tList) {
        Map<ID, T> map = new HashMap<>();
        for (T t : tList) {
            ID id = (ID) entityInformation.getId(t);
            map.put(id, t);
        }
        return map;
    }
}