package com.sinoyd.base.repository.impl;

import cn.afterturn.easypoi.util.PoiPublicUtil;
import com.sinoyd.base.repository.TemplateTableRepository;
import com.sinoyd.base.service.ISplitTableService;
import com.sinoyd.base.service.ITemplateTable;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.persistence.Transient;
import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 分表数据访问实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
public class TableRepositoryImpl<T extends ITemplateTable> implements TemplateTableRepository<T> {

    private String tableName;
    private JdbcTemplate jdbcTemplate;
    private ISplitTableService splitTableService;

    @Override
    public T findOne(String tableName, String id, Class<T> tClass) {
        String sql = String.format("select * from %s where id = :id and isDeleted = 0", tableName);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        return new NamedParameterJdbcTemplate(jdbcTemplate).queryForObject(sql, params, getRowMapper(tClass));
    }

    @Override
    public T save(T t) {
        return saveAll(Collections.singletonList(t)).get(0);
    }

    @Override
    public List<T> saveAll(Collection<T> entities) {
        try {
            List<T> entityList = new ArrayList<>(entities);
            String tableName = splitTableService.autoCreateTable(entityList.get(0), entityList.get(0).getTableStrategy());
            this.setTableName(tableName);
            String sql = "insert into %s(%s) values(%s) ";
            StringBuilder fieldSql = new StringBuilder();
            StringBuilder valueSql = new StringBuilder();
            T t = entityList.get(0);
            Field[] fields = PoiPublicUtil.getClassFields(t.getClass());
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                Transient tran = field.getAnnotation(Transient.class);
                if (tran == null) {
                    field.setAccessible(true);
                    fieldSql.append(field.getName());
                    valueSql.append("?");
                    fieldSql.append(",");
                    valueSql.append(",");
                }
            }
            fieldSql.deleteCharAt(fieldSql.length() - 1);
            valueSql.deleteCharAt(valueSql.length() - 1);
            sql = String.format(sql, tableName, fieldSql, valueSql);

            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) {
                    T t = entityList.get(i);
                    Field[] fields = PoiPublicUtil.getClassFields(t.getClass());
                    List<Field> fieldList = new ArrayList<>();
                    for (int j = 0; j < fields.length; j++) {
                        Field field = fields[j];
                        Transient tran = field.getAnnotation(Transient.class);
                        if (tran == null) {
                            field.setAccessible(true);
                            fieldList.add(field);
                        }
                    }
                    for (int j = 0; j < fieldList.size(); j++) {
                        Field field = fieldList.get(j);
                        try {
                            ps.setObject(j + 1, field.get(t));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }

                @Override
                public int getBatchSize() {
                    return entityList.size();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return new ArrayList<>(entities);
    }

    @Override
    public T update(String tableName, T t) {
        return updateAll(tableName, Collections.singletonList(t)).get(0);
    }

    @Override
    public List<T> updateAll(String tableName, Collection<T> entities) {
        try {
            List<T> entityList = new ArrayList<>(entities);
            String sql = "update %s set %s where %s";
            StringBuilder fieldSql = new StringBuilder();
            String conditionSql = " id = :id";
            T t = entityList.get(0);
            Field[] fields = PoiPublicUtil.getClassFields(t.getClass());
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                Transient tran = field.getAnnotation(Transient.class);
                if (tran == null) {
                    field.setAccessible(true);
                    fieldSql.append(field.getName()).append(" = :").append(field.getName());
                    fieldSql.append(",");
                }
            }
            fieldSql.deleteCharAt(fieldSql.length() - 1);
            sql = String.format(sql, tableName, fieldSql, conditionSql);

            List<Map<String, Object>> paramList = new ArrayList<>();
            for (T entity : entities) {
                Map<String, Object> param = new HashMap<>();
                for (Field field : fields) {
                    Transient tran = field.getAnnotation(Transient.class);
                    if (tran == null) {
                        field.setAccessible(true);
                        try {
                            if (field.getName().equals("modifier")) {
                                param.put(field.getName(), PrincipalContextUser.getPrincipal().getUserId());
                            } else if (field.getName().equals("modifyDate")) {
                                param.put(field.getName(), new Date());
                            } else {
                                param.put(field.getName(), field.get(entity));
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                paramList.add(param);
            }

            new NamedParameterJdbcTemplate(jdbcTemplate).batchUpdate(sql, paramList.toArray(new Map[paramList.size()]));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return new ArrayList<>(entities);
    }

    @Override
    public Integer logicDeleteById(String tableName, Collection<String> ids) {
        String sql = String.format("update %s set isDeleted = 1 where id in (:ids) ", tableName);
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        namedParameterJdbcTemplate.update(sql, param);
        return ids.size();
    }

    @Override
    public List<T> queryList(String tableName, Map<String, Object> condition, Class<T> tClass) {
        String sql = String.format("select * from %s where isDeleted = 0", tableName);
        StringBuilder conditionSql = new StringBuilder();
        for (String key : condition.keySet()) {
            conditionSql.append(String.format(" and %s = :%s ", key, key));
        }
        sql += conditionSql;
        return new NamedParameterJdbcTemplate(jdbcTemplate).query(sql, condition, getRowMapper(tClass));
    }

    @Override
    public List<T> queryList(String tableName, String conditionSql, Map<String, Object> condition, Class<T> tClass) {
        String sql = String.format("select * from %s where isDeleted = 0", tableName) + conditionSql;
        return new NamedParameterJdbcTemplate(jdbcTemplate).query(sql, condition, getRowMapper(tClass));
    }

    @Override
    public String getTableName() {
        return tableName;
    }

    protected void setTableName(String tableName) {
        this.tableName = tableName;
    }

    private RowMapper<T> getRowMapper(Class<T> tClass) {
        Field[] fields = PoiPublicUtil.getClassFields(tClass);
        return new RowMapper<T>() {
            @Override
            public T mapRow(ResultSet rs, int rowNum) throws SQLException {
                try {
                    T t = tClass.newInstance();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        Transient tran = field.getAnnotation(Transient.class);
                        if (tran == null) {
                            String fieldName = field.getName();
                            Object value = rs.getObject(fieldName);
                            //数据null的情况
                            if (StringUtils.isNotNull(value)) {
                                if ("LocalDateTime".equals(value.getClass().getSimpleName())) {
                                    value = Timestamp.valueOf((LocalDateTime) value);
                                }
                            }
                            field.set(t, value);
                        }
                    }
                    return t;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        };
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    public void setSplitTableService(ISplitTableService splitTableService) {
        this.splitTableService = splitTableService;
    }
}
