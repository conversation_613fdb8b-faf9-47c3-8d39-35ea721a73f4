package com.sinoyd.base.repository.impl;

import com.sinoyd.base.service.ITemplateTable;
import org.springframework.stereotype.Repository;

/**
 * 分表数据访问实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
@Repository
@SuppressWarnings("all")
public class TemplateTableRepositoryImpl<T extends ITemplateTable> extends TableRepositoryImpl {

    @Override
    public String getTableName() {
        return super.getTableName();
    }
}