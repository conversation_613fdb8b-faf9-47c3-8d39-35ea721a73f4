package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.CalculationUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 计算相关实现类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Service
@Slf4j
@Primary
public class CalculateServiceImpl implements CalculateService {


    @Override
    public final Object calculationExpression(String gateUrl, List<String> formulas, Map<String, Object> params) {
        return this.calculation(gateUrl, formulas, params, false);
    }

    @Override
    public final String calculateRule(String gateUrl, List<String> formulas, Map<String, Object> params) {
        String retValue = CalculationUtil.calculationExpression(gateUrl, PrincipalContextUser.getPrincipal().getToken(), formulas, params, true);
        if (StringUtils.isNotEmpty(retValue)) {
            if (retValue.startsWith("\"")) {
                retValue = retValue.replaceFirst("\"", "");
            }
            if (retValue.endsWith("\"")) {
                retValue = retValue.replaceFirst("\"", "");
            }
        }
        return retValue;
    }

    @Override
    public final Boolean calculateJudge(String gateUrl, List<String> formulas, Map<String, Object> params) {
        return Boolean.valueOf(this.calculation(gateUrl, formulas, params, true).toString());
    }

    private Object calculation(String gateUrl, List<String> formulas, Map<String, Object> params, Boolean ignore) {
        return CalculationUtil.calculationExpression(gateUrl, PrincipalContextUser.getPrincipal().getToken(), formulas, params, ignore);
    }
}