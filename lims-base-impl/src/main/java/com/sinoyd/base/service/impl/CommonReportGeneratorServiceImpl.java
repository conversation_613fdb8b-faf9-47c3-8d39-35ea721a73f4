package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.ICommonReportGeneratorService;
import com.sinoyd.base.service.ICommonReportService;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 基础报表生成基础服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/16
 */
@Service
@Slf4j
public class CommonReportGeneratorServiceImpl implements ICommonReportGeneratorService {

    private Map<String, ICommonReportService> reportServiceMap;

    @Override
    public void exportData(String reportCode, Map<String, Object> criteria, HttpServletResponse response) {
        if (!reportServiceMap.containsKey(reportCode)) {
            throw new BaseException("报表编码生成服务不存在，请检查报表编码是否正确!");
        }
        ICommonReportService service = reportServiceMap.get(reportCode);
        service.generateReport(reportCode, criteria, response);
    }

    @Autowired(required = false)
    public void setReportServiceMap(Map<String, ICommonReportService> reportServiceMap) {
        this.reportServiceMap = reportServiceMap;
    }
}
