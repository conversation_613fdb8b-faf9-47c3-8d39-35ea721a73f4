package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.DevelopToolService;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.Table;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开发工具实现
 * 注意：该接口和业务无任何关系，只是用来提升开发效率
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/11
 */
@Service
@Slf4j
public class DevelopToolServiceImpl implements DevelopToolService {

    private JdbcTemplate jdbcTemplate;

    @Override
    public String generateApifoxObjectModel(Map<String, String> params) {
        return generateJson(params.get("modelName"), getTableFieldVoList(params.get("schemaName"),params.get("tableName")));
    }


    @Override
    public String generateApifoxObjectModelByFullName(Map<String, String> params) {
        String modelName = params.get("modelName");
        String className = params.get("className");
        String schemaName = params.get("schemaName");
        try {
            Class<?> clazz = Class.forName(className);
            //获取表中已有属性
            String tableName = clazz.getAnnotation(Table.class).name();
            List<FieldVO> voList = getTableFieldVoList(schemaName,tableName);
            List<String> tableFieldNames = voList.stream().map(FieldVO::getFieldName).collect(Collectors.toList());
            //获取对象全部属性
            List<Field> fields = new ArrayList<>();
            getAllFields(fields,clazz);
            //过滤附加属性
            fields.removeIf(f->tableFieldNames.contains(f.getName()));
            //附加属性统计并添加
            for (Field field:fields) {
                FieldVO vo= generateFieldObjectForEntity(field.getName(),field.getAnnotatedType().getType().getTypeName());
                if(!"object".endsWith(vo.getFieldType())){
                    voList.add(vo);
                }
            }
            return generateJson(modelName, voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("生成Apifox数据模型出错");
        }
    }

    /**
     * 生成模型说明集合
     * @param schemaName  模式名
     * @param tableName   表名
     */
    private List<FieldVO> getTableFieldVoList(String schemaName,String tableName){
        List<FieldVO> voList = new ArrayList<>();
        Connection conn = null;
        try {
            conn = Objects.requireNonNull(jdbcTemplate.getDataSource()).getConnection();
            DatabaseMetaData dbMetaData = conn.getMetaData();
            String[] types = {"TABLE"};
            ResultSet tableRs = dbMetaData.getTables(null, schemaName, tableName, types);
            while (tableRs.next()) {
                tableName = tableRs.getString("TABLE_NAME");
                ResultSet columnRs = dbMetaData.getColumns(null, null, tableName, null);
                while (columnRs.next()) {
                    String columnName = columnRs.getString("COLUMN_NAME");
                    String columnType = columnRs.getString("TYPE_NAME");
                    String columnRemarks = columnRs.getString("REMARKS");
                    if (!voList.stream().map(FieldVO::getFieldName).collect(Collectors.toList()).contains(columnName)) {
                        voList.add(generateFieldObject(columnName, columnType, columnRemarks));
                    }
                }
            }
            return voList;
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("生成Apifox数据模型出错");
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 产生模型语句
     *
     * @param modelName 模型名称
     * @param voList    模型属性集合
     * @return 模型语句
     */
    private String generateJson(String modelName, List<FieldVO> voList) {
        StringBuilder sb = new StringBuilder();
        sb.append("{")
                .append("\"type\": \"object\",");

        sb.append("\"properties\": {");
        List<String> columnJsonList = voList.stream().map(FieldVO::toString).collect(Collectors.toList());
        sb.append(String.join(",", columnJsonList));
        sb.append("},");

        List<String> columnNameList = voList.stream().map(FieldVO::fieldWithDoubleQuote).collect(Collectors.toList());
        sb.append("\"x-apifox-orders\": [")
                .append(String.join(",", columnNameList))
                .append("],");

        sb.append("\"title\": \"").append(modelName).append("\",");

        sb.append(" \"required\": [")
                .append(String.join(",", columnNameList))
                .append("]");

        sb.append("}");
        return sb.toString();
    }

    /**
     * 将数据库字段类型转换成Apifox中的接口属性类型
     *
     * @param columnType 数据库字段类型
     * @return 接口属性类型
     */
    private FieldVO generateFieldObject(String columnName, String columnType, String columnRemarks) {
        FieldVO vo = new FieldVO();
        vo.setFieldName(columnName);
        vo.setFieldDesc(columnRemarks);
        if (columnType.startsWith("VARCHAR") || columnType.startsWith("NVARCHAR")) {
            vo.setFieldType("string");
            if (columnName.endsWith("id")) {
                vo.setMockValue("@guid");
            } else {
                vo.setMockValue("@word");
            }
        } else if (columnType.startsWith("INT") || columnType.endsWith("INT")) {
            vo.setFieldType("integer");
            vo.setMockValue("@integer(60, 100)");
        } else if (columnType.startsWith("BIT")) {
            vo.setFieldType("boolean");
            vo.setMockValue("@boolean");
        } else if (columnType.startsWith("DECIMAL")) {
            vo.setFieldType("number");
            vo.setMockValue("@float(60, 100, 3,2)");
        } else if ("DATE".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@date");
        } else if ("DATETIME".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@datetime");
        } else if (columnType.endsWith("TEXT")) {
            vo.setFieldType("string");
            vo.setMockValue("@csentence");
        } else {
            throw new BaseException("未知类型");
        }
        return vo;
    }

    /**
     * 将数据库字段类型转换成Apifox中的接口属性类型
     *
     * @param columnType 数据库字段类型
     * @return 接口属性类型
     */
    private FieldVO generateFieldObjectForEntity(String columnName, String columnType) {
        FieldVO vo = new FieldVO();
        vo.setFieldName(columnName);
        vo.setFieldDesc("");
        String shortType = columnType;
        if(columnType.contains(".")&&!columnType.endsWith(".")){
            shortType = columnType.substring(columnType.lastIndexOf(".")+1);
        }
        switch (shortType) {
            case "String":
                vo.setFieldType("string");
                vo.setMockValue("@word");
                break;
            case "Integer":
                vo.setFieldType("integer");
                vo.setMockValue("@integer(60, 100)");
                break;
            case "Boolean":
                vo.setFieldType("boolean");
                vo.setMockValue("@boolean");
                break;
            case "BigDecimal":
                vo.setFieldType("number");
                vo.setMockValue("@float(60, 100, 3,2)");
                break;
            case "Date":
                vo.setFieldType("string");
                vo.setMockValue("@date");
                break;
            default:
                vo.setFieldType("object");
                break;
        }
        return vo;
    }

    /**
     * 递归获取所有属性
     * @param fields 属性容器
     * @param clazz  类
     */
    private void getAllFields(List<Field> fields,Class<?> clazz){
        if (clazz == null || clazz.equals(Object.class)) {
            return;
        }
        fields.addAll(Arrays.asList(clazz.getDeclaredFields())) ;
        getAllFields(fields,clazz.getSuperclass());
    }

    @Data
    static class FieldVO {
        /**
         * 模型属性类型
         */
        private String fieldType;

        /**
         * 属性名称
         */
        private String fieldName;

        /**
         * 属性模拟值
         */
        private String mockValue;

        /**
         * 属性描述
         */
        private String fieldDesc;

        public String toString() {
            return "\"" + this.fieldName + "\": {"
                    + "\"type\": \"" + this.fieldType + "\","
                    + "\"mock\": {\"mock\":\"" + this.mockValue + "\"},"
                    + "\"description\": \"" + this.fieldDesc + "\"}";
        }

        public String fieldWithDoubleQuote() {
            return "\"" + this.fieldName + "\"";
        }

    }


    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}