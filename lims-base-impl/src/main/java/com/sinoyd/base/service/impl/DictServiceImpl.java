package com.sinoyd.base.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.service.DictService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.annotation.CacheExpire;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
@Service
@Slf4j
public class DictServiceImpl implements DictService {

    private CodeService codeService;

    private DepartmentService departmentService;


    @Override
    public Map<String, String> loadDictNameMap(String dictType) {
        Map<String, DtoCode> map = loadDictMap(dictType);
        Map<String, String> result = new LinkedHashMap<>();
        for (Map.Entry<String, DtoCode> m : map.entrySet()) {
            result.put(m.getKey(), m.getValue().getDictName());
        }
        return result;
    }

    @Override
    public Map<String, String> loadDictValueMap(String dictType) {
        Map<String, DtoCode> map = loadDictMap(dictType);
        Map<String, String> result = new LinkedHashMap<>();
        for (Map.Entry<String, DtoCode> m : map.entrySet()) {
            result.put(m.getKey(), m.getValue().getDictValue());
        }
        return result;
    }

    @Override
    @Cacheable(value = IBaseConstants.CacheName.DEPARTMENT, key = "':name'")
    @CacheExpire(IBaseConstants.CacheExpireTime.COMMON_EXPIRE_TIME)
    public Map<String, String> loadDepartmentMap() {
        List<DtoDepartment> departmentList = departmentService.findByOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        Map<String, String> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(departmentList)) {
            departmentList.forEach(d -> map.put(d.getId(), d.getDeptName()));
        }
        return map;
    }

    @Override
    public Map<String, DtoCode> loadDictMap(String dictType) {
        Map<String, DtoCode> map = new LinkedHashMap<>();
        List<DtoCode> codeList = codeService.findCodes(dictType);
        if (StringUtils.isNotEmpty(codeList)) {
            codeList.sort(Comparator.comparing(DtoCode::getSortNum));
            codeList.forEach(p -> map.put(p.getDictCode(), p));
        } else {
            throw new BaseException("字典数据未配置，字典类型: " + dictType);
        }
        return map;
    }

    @Override
    public Map<String, DtoCode> loadDictIdMap(String dictType) {
        Map<String, DtoCode> map = new LinkedHashMap<>();
        List<DtoCode> codeList = codeService.findCodes(dictType);
        if (StringUtils.isNotEmpty(codeList)) {
            codeList.sort(Comparator.comparing(DtoCode::getSortNum));
            codeList.forEach(p -> map.put(p.getId(), p));
        } else {
            throw new BaseException("字典数据未配置，字典类型: " + dictType);
        }
        return map;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }
}