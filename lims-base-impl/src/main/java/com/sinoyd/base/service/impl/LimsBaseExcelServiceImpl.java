package com.sinoyd.base.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.base.service.LimsExcelBaseService;
import com.sinoyd.base.util.BeanUtil;
import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.excel.dict.builders.IGenericDictHandlerBuilder;
import com.sinoyd.excel.service.IPOJOExportService;
import com.sinoyd.excel.service.IPOJOImportService;
import com.sinoyd.excel.style.GenericExcelStyle;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * LIMS表格处理基础实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/7/19
 */
@Service
public abstract class LimsBaseExcelServiceImpl<T, V extends LimsExcelBaseVO, ID extends Serializable, S extends LimsBaseService<T, ID>>
        implements LimsExcelBaseService<T, V, ID, S> {

    protected S service;

    protected IPOJOExportService pojoExportService;

    protected IGenericDictHandlerBuilder dictHandlerBuilder;

    protected IPOJOImportService pojoImportService;

    @Override
    public List<T> findExportData(BaseCriteria criteria) {
        PageBean<T> pageBean = new PageBean<>();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        service.findByPage(pageBean, criteria);
        return pageBean.getData();
    }

    @Override
    public void exportData(HttpServletResponse response, BaseCriteria criteria) {
        List<T> dataList = findExportData(criteria);
        List<V> voList = convertT2V(dataList);
        exportData(response, voList);
    }

    /**
     * 导出
     *
     * @param response    响应流
     * @param vCollection 数据集合
     */
    protected void exportData(HttpServletResponse response, List<V> vCollection) {
        GlobalExport globalExport = getVClass().getAnnotation(GlobalExport.class);
        if (globalExport == null) {
            throw new BaseException("尚未配置导出文件名");
        }
        ExportParams exportParams = new ExportParams(null, globalExport.name());
        exportParams.setStyle(GenericExcelStyle.class);
        exportParams.setDictHandler(dictHandlerBuilder.buildPOJODictHandler(getVClass()));
        exportParams.setExclusions(globalExport.exclusions());
        exportParams.setPojoClass(getVClass());
        if (globalExport.freezeCol() > 0) {
            exportParams.setFreezeCol(globalExport.freezeCol());
        }
        pojoExportService.exportData(vCollection, exportParams, globalExport.name(), response);
    }

    @Override
    @Transactional
    public void importData(HttpServletResponse response, MultipartFile file) {
    }

    @Override
    public void downloadImportTemplate(HttpServletResponse response) {
        pojoImportService.exportTemplate(getVClass(), null, null, response);
    }

    /**
     * 将DTO转成VO，用于导出
     *
     * @param dtoCollection DTO集合
     * @return VO集合
     */
    protected List<V> convertT2V(Collection<T> dtoCollection) {
        return BeanUtil.convertT2V(getVClass(), dtoCollection);
    }

    /**
     * 将DTO转成VO，用于导出
     *
     * @param t DTO实体
     * @return VO实体
     */
    protected V convertT2V(T t) {
        return BeanUtil.convertT2V(getVClass(), Collections.singletonList(t)).get(0);
    }

    /**
     * 将VO转成DTO，用于导入
     *
     * @param voCollection vo集合
     * @return dto集合
     */
    protected List<T> convertV2T(Collection<V> voCollection) {
        return BeanUtil.convertV2T(getTClass(), voCollection);
    }

    /**
     * 获取泛型T的Class类型
     *
     * @return T的Class类型
     */
    protected Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 获取泛型V的Class类型
     *
     * @return V的Class类型
     */
    protected Class<V> getVClass() {
        return (Class<V>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }

    @Autowired
    @Lazy
    public void setDictHandlerBuilder(IGenericDictHandlerBuilder dictHandlerBuilder) {
        this.dictHandlerBuilder = dictHandlerBuilder;
    }

    @Autowired
    @Lazy
    public void setPojoExportService(IPOJOExportService pojoExportService) {
        this.pojoExportService = pojoExportService;
    }

    @Autowired
    public void setService(S service) {
        this.service = service;
    }

    @Autowired
    @Lazy
    public void setPojoImportService(IPOJOImportService pojoImportService) {
        this.pojoImportService = pojoImportService;
    }
}