package com.sinoyd.base.service.impl;

import cn.afterturn.easypoi.util.PoiPublicUtil;
import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.base.util.CacheUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;

/**
 * 扩展的LIMS Base Service实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/9
 */
@Service
public abstract class LimsBaseServiceImpl<T, ID extends Serializable, R extends IBaseJpaRepository<T, ID>> extends BaseJpaServiceImpl<T, ID, R>
        implements LimsBaseService<T, ID> {

    @Autowired(required = false)
    private LimsRepository<T, ID> limsRepository;

    @PersistenceContext(
            unitName = "limEntityManagerFactory"
    )
    private EntityManager entityManager;

    @Override
    public void findByPage(PageBean<T> pageBean, BaseCriteria criteria) {
        findByPage(pageBean, criteria, true);
    }

    @Override
    public void findByPage(PageBean<T> pageBean, BaseCriteria criteria, boolean isLoadTransientField) {
        if (StringUtils.isEmpty(pageBean.getEntityName()) || StringUtils.isEmpty(pageBean.getSelect())) {
            String entityName = getTClass().getSimpleName();
            pageBean.setEntityName(entityName);
            super.findByPage(pageBean, criteria);
        } else {
            super.findByPage(pageBean, criteria);
        }
        if (isLoadTransientField) {
            loadTransientFields(pageBean.getData());
        }
    }

    @Override
    @Transactional
    public T save(T entity) {
        return super.save(entity);
    }

    @Override
    @Transactional
    public List<T> save(Collection<T> entities) {
        return super.save(entities);
    }

    @Override
    @Transactional
    public List<T> batchSave(Collection<T> tList) {
            return limsRepository.batchSave(tList);
    }

    @Override
    @Transactional
    public T update(T entity) {
        return repository.save(entity);
    }

    @Override
    @Transactional
    public List<T> update(Collection<T> entities) {
        return repository.saveAll(entities);
    }

    @Override
    @Transactional
    public List<T> batchUpdate(Collection<T> tList) {
        return limsRepository.batchUpdate(tList);
    }

    @Override
    public Map<String, T> findAllMap(Collection<ID> ids, boolean isLoadUnDBFiledValue) {
        Map<String, T> map = new HashMap<>();
        List<T> list = super.findAll(ids);
        if (isLoadUnDBFiledValue) {
            loadTransientFields(list);
        }
        for (T t : list) {
            BaseEntity entity = (BaseEntity) t;
            map.put(entity.getId().toString(), t);
        }
        return map;
    }

    @Override
    public Map<String, T> findAllMap(Collection<ID> ids) {
        return this.findAllMap(ids, true);
    }

    @Override
    public List<T> findAll(Collection<ID> ids, boolean isLoadUnDBFiledValue) {
        if (isLoadUnDBFiledValue) {
            return findAll(ids);
        }
        return super.findAll(ids);
    }

    @Override
    public List<T> findAll(Collection<ID> ids) {
        List<T> list = super.findAll(ids);
        if (StringUtils.isNotEmpty(list)) {
            loadTransientFields(list);
        }
        return list;
    }

    @Override
    public T findOne(ID key) {
        T t = super.findOne(key);
        if (t != null) {
            loadTransientFields(Collections.singleton(t));
        } else {
            throw new BaseException("当前记录已经被其他用户删除，请确认");
        }
        return t;
    }

    @Override
    public List<T> findAll() {
        List<T> dataList = super.findAll();
        loadTransientFields(dataList);
        return dataList;
    }

    @Override
    public T findOne(ID id, boolean isLoadUnDBFiledValue) {
        T t = super.findOne(id);
        if (t == null) {
            throw new BaseException("当前记录已经被其他用户删除，请确认");
        }
        if (isLoadUnDBFiledValue) {
            return findOne(id);
        }
        return t;
    }

    @Override
    public T findAttachmentPath(ID id) {
        return findOne(id);
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        return super.logicDeleteById(id);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<ID> ids) {
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public void deleteAllByIds(Collection<ID> ids) {
        repository.deleteAllById(ids);
    }

    @Override
    @Transactional
    public List<T> dragOrderNo(Collection<T> entities) {
        return save(entities);
    }

    @Override
    public void detach(T t) {
        entityManager.detach(t);
    }

    @Override
    public void detach(Collection<T> tList) {
        tList.forEach(this::detach);
    }

    /**
     * 获取泛型T的Class类型
     *
     * @return T的Class类型
     */
    protected Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 加载冗余属性
     *
     * @param collection 记录集合
     */
    @Override
    public void loadTransientFields(Collection<T> collection) {
    }

    @Override
    public List<?> loadCacheField(String cacheName, String key, String fieldName) {
        List cacheValueList = CacheUtil.queryCacheFieldValues(cacheName, key);
        if (StringUtils.isEmpty(cacheValueList)) {
            cacheValueList = new ArrayList();
            try {
                List<T> entityList = this.findAll();
                if (StringUtils.isNotEmpty(entityList)) {
                    Class clazz = getTClass();
                    Field[] fields = PoiPublicUtil.getClassFields(clazz);
                    Field field = Arrays.stream(fields).filter(p -> fieldName.equals(p.getName())).findFirst()
                            .orElseThrow(() -> new RuntimeException("Have not found a field named " + fieldName + " for class "
                                    + clazz.getSimpleName()));
                    field.setAccessible(true);
                    for (T t : entityList) {
                        Object value = field.get(t);
                        cacheValueList.add(value);
                    }
                    CacheUtil.addAllCacheFieldValues(cacheName, key, cacheValueList);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return cacheValueList;
    }

}