package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.IModuleContext;
import com.sinoyd.base.service.IModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * LIMS模块上下文实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/6
 */
@Service
public class ModuleStatusContext implements IModuleContext {

    private Map<String, IModuleService> moduleServiceMap;


    @Override
    public List<Integer> all(String moduleCode) {
        return moduleServiceMap.get(moduleCode).all();
    }

    @Override
    public List<Integer> completed(String moduleCode) {
        return moduleServiceMap.get(moduleCode).completed();
    }

    @Override
    public List<Integer> uncompleted(String moduleCode) {
        return moduleServiceMap.get(moduleCode).uncompleted();
    }

    @Autowired(required = false)
    @Lazy
    public void setModuleServiceMap(Map<String, IModuleService> moduleServiceMap) {
        this.moduleServiceMap = moduleServiceMap;
    }
}