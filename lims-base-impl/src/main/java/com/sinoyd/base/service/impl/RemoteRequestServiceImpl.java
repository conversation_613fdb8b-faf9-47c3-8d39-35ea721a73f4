package com.sinoyd.base.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.service.IRemoteRequestService;
import com.sinoyd.base.vo.RemoteRequestParamVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 远程请求服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/12
 */
@Service
@SuppressWarnings("unchecked")
public class RemoteRequestServiceImpl implements IRemoteRequestService {


    @Override
    public RestResponse<?> request(RemoteRequestParamVO paramVO) {
        List<NameValuePair> paramList = new ArrayList<>();
        paramVO.getRequestParams().forEach((k, v) -> paramList.add(new NameValuePair(k, v)));
        return requestResult(paramVO, paramList).toJavaObject(RestResponse.class);
    }

    @Override
    public String getResponseDataJson(RestResponse<?> response) {
        if (response.isSuccess()) {
            if (response.getData() instanceof JSONArray) {
                return ((JSONArray) response.getData()).toJSONString();
            }
            if (response.getData() instanceof JSONObject) {
                return ((JSONObject) response.getData()).toJSONString();
            }
        }
        return IBaseConstants.EmptyValueDisplay.EMPTY;
    }

    /**
     * 调用接口获取结果
     *
     * @param paramVO   远程请求参数
     * @param paramList 请求参数
     * @return 结构结果
     */
    protected JSONObject requestResult(RemoteRequestParamVO paramVO, List<NameValuePair> paramList) {
        //目前远程调用场景
        if (HttpMethod.GET.name().equals(paramVO.getHttpMethod())) {
            return HTTPCaller.getInstance().getOne(paramVO.getGateUrl(),
                    replaceGateContext(paramVO.getUri(), paramVO.getGateContext()),
                    paramVO.getToken(), paramList);
        } else if (HttpMethod.POST.name().equals(paramVO.getHttpMethod())) {
            return HTTPCaller.getInstance().post(paramVO.getGateUrl(),
                    replaceGateContext(paramVO.getUri(), paramVO.getGateContext()),
                    paramVO.getToken(), paramList);
        } else if (HttpMethod.PUT.name().equals(paramVO.getHttpMethod())) {
            return HTTPCaller.getInstance().put(paramVO.getGateUrl(),
                    replaceGateContext(paramVO.getUri(), paramVO.getGateContext()),
                    paramVO.getToken(), paramList);
        } else {
            return HTTPCaller.getInstance().delete(paramVO.getGateUrl(),
                    replaceGateContext(paramVO.getUri(), paramVO.getGateContext()),
                    paramVO.getToken(), paramList);
        }
    }

    /**
     * 替换网关地址
     *
     * @param url         请求地址
     * @param gateContext 网关请求代理地址
     * @return 替换后的地址
     */
    private String replaceGateContext(String url, String gateContext) {
        return url.replaceFirst("^/api", "/api/" + gateContext);
    }
}
