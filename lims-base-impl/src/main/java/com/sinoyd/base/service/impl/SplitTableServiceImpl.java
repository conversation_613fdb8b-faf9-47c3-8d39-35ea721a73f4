package com.sinoyd.base.service.impl;

import com.sinoyd.base.enums.EnumTableStrategy;
import com.sinoyd.base.service.ISplitTableService;
import com.sinoyd.base.service.ITemplateTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 分表实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
@Service
public class SplitTableServiceImpl implements ISplitTableService {

    private JdbcTemplate jdbcTemplate;

    @Override
    public String autoCreateTable(ITemplateTable templateTable, EnumTableStrategy tableStrategy) {
        String tableName = tableStrategy.getTableName().getTableName(templateTable.getTemplateTableName());
        if (!isTableExist(tableName)) {
            String sql = String.format(templateTable.createTemplateTableSql(), tableName);
            jdbcTemplate.execute(sql);
        }
        return tableName;
    }

    /**
     * 判断分表是否存在
     *
     * @param tableName 表名
     * @return 存在返回true，否则false
     */
    private boolean isTableExist(String tableName) {
        try {
            String sql = String.format("select 1 from %s", tableName);
            jdbcTemplate.execute(sql);
        } catch (DataAccessException e) {
            return false;
        }
        return true;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}