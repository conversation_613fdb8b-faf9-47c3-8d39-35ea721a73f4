package com.sinoyd.base.service.impl;

import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.base.service.IWebOfficeBaseService;
import com.sinoyd.base.vo.WebOfficeVO;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;

/**
 * webOffice(在线编辑) 基础服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/21
 */
@Service
@Slf4j
public class WebOfficeBaseServiceImpl implements IWebOfficeBaseService {

    private FilePropertyConfig filePropertyConfig;

    @Override
    public WebOfficeVO getFileWebOffice(String absFilePath) {
        WebOfficeVO dtoWebOffice = new WebOfficeVO();
        File file = new File(absFilePath);
        //输出路径
        String outputPath = filePropertyConfig.getOutputPath();
        //如果文件存在，将文件移值输出路径下面
        if (file.exists()) {
            File outputFile = new File(outputPath);
            if (!outputFile.exists()) {
                boolean mkdirs = outputFile.mkdirs();
                if (mkdirs){
                    log.info("创建临时目录成功:" + outputPath);
                }
            }
            String targetPath = outputPath + "/" + file.getName();
            File targetFile = new File(targetPath);
            //已创建的目录作为应用程序的名称（需要将这个路径放置在网关同级的目录下面）
            String webName = outputFile.getName();
            String webUrl = webName + "/" + file.getName();
            //将文件复制到输出目录下面
            if (targetFile.exists()) {
                boolean delete = targetFile.delete();
                if (delete) {
                    log.info("删除临时文件成功:" + targetPath);
                }
            }
            try {
                Files.copy(file.toPath(), targetFile.toPath());
            } catch (Exception e) {
                log.error("文件打开失败：复制临时文件失败," + e.getMessage(), e);
                throw new BaseException("文件打开失败!");
            }
            String fileType = file.getName().substring(file.getName().lastIndexOf("."));
            dtoWebOffice.setFileType(fileType);
            dtoWebOffice.setFileName(file.getName());
            dtoWebOffice.setWebUrl(webUrl);
        }
        return dtoWebOffice;
    }

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }
}
