package com.sinoyd.base.service.impl;

import com.google.common.collect.Maps;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.base.vo.WorkflowParamVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.boot.workflow.activiti.dto.DtoActProcessInstance;
import com.sinoyd.boot.workflow.activiti.dto.DtoActTask;
import com.sinoyd.boot.workflow.activiti.dto.DtoActTaskInfo;
import com.sinoyd.boot.workflow.activiti.service.IActProcessService;
import com.sinoyd.boot.workflow.activiti.service.IActTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 工作流通用服务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/26
 */
@Service
@Slf4j
public class WorkflowServiceImpl implements WorkflowService {

    private IActProcessService actProcessService;

    private IActTaskService actTaskService;

    @Override
    @Transactional
    public void submitSign(WorkflowParamVO vo) {
        for (String objectId : vo.getObjectIds()) {
            DtoActProcessInstance processInstance = actProcessService.getProcessInstance(objectId);
            DtoActTask actTask = new DtoActTask();
            DtoActTaskInfo taskInfo = actTaskService.getCurrentTask(processInstance.getId());
            actTask.setTaskId(taskInfo.getId());
            actTask.setComment(vo.getOpinion());
            actTask.setProcInsId(processInstance.getId());
            Map<String, Object> vars = StringUtils.isEmpty(actTask.getVars()) ? Maps.newHashMap() : actTask.getVars();

            //指定信号值
            if (StringUtils.isNotEmpty(vo.getSignal())) {
                vars.put("signal", vo.getSignal());
            }
            actTask.setVars(vars);
            actTaskService.complete(actTask, false);
        }
    }

    @Override
    @Transactional
    public void createInstance(String workflowCode, String objectId, Map<String, Object> params) {
        // 启动业务流程
        DtoActProcessInstance procInst = actProcessService.startProcess(workflowCode, objectId);
        // 提交业务流程到新建状态
        actTaskService.completeCurrentTask(procInst.getId(), "", params, false);
    }

    @Override
    @Transactional
    public void endInstance(String objectId, String option) {
        DtoActProcessInstance processInstance = actProcessService.getProcessInstance(objectId);
        if (processInstance != null) {
            actProcessService.deleteProcIns(processInstance.getId(), option);
        }
    }

    @Autowired
    public void setActProcessService(IActProcessService actProcessService) {
        this.actProcessService = actProcessService;
    }

    @Autowired
    public void setActTaskService(IActTaskService actTaskService) {
        this.actTaskService = actTaskService;
    }
}