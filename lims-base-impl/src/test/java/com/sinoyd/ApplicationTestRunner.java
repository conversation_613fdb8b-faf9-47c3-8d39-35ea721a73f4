package com.sinoyd;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.frame.client.annotation.EnableFrameClient;
import com.sinoyd.frame.base.configuration.JdkDateSupport;
import com.sinoyd.frame.base.configuration.JdkTimestampSupport;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.web.WebApplicationInitializer;

/**
 * 资源配置应用入口
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-11-08
 */
@SpringBootApplication(scanBasePackages = "com.sinoyd")
@EnableFrameClient
@EnableDiscoveryClient
@EnableJpaAuditing(auditorAwareRef = "principalContextUser")
@EnableCaching
@EnableFeignClients(basePackages = "com.sinoyd")
public class ApplicationTestRunner extends SpringBootServletInitializer implements WebApplicationInitializer {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationTestRunner.class, args);
        //启用json时间序列化
        JdkTimestampSupport.enable(DateUtil.FULL);
        //启用json时间序列化格式
        JdkDateSupport.enable(DateUtil.FULL);
    }

}
