package com.sinoyd.base.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.base.vo.ReviseDataVO;
import com.sinoyd.common.revise.ReviseDataFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class CalculateServiceTest {

    @Test
    public void revise() {
        try {
            ImportParams importParams = new ImportParams();
            List<ReviseDataVO> dataList = ExcelImportUtil.importExcel(this.getClass().getResourceAsStream("/doc/数据修约测试用例_4.25.xls"), ReviseDataVO.class, importParams);
            int rowNo = 2;
            for (ReviseDataVO vo : dataList) {
                Boolean isSci = "是".equals(vo.getSciFlag());
                String value = ReviseDataFactory.revise(vo.getValue(), vo.getSignificantDigits(), vo.getDecimalDigits(), isSci);
                vo.setCalculateValue(value);
                vo.setRowNo(rowNo++);
                if (!value.equals(vo.getExpectedValue())) {
                    log.info("......与期盼值不符: " + vo + "......");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void reviseSingleData(){
        String value = ReviseDataFactory.revise("0.000000",
                3, 1, false);
        log.info("revise value is " + value);
        Assertions.assertEquals("0.0", value);
    }
}