package com.sinoyd.common.sort;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.excel.service.IPOJOExportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 编号排序测试类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/23
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class SortTest {

    @Autowired
    private IPOJOExportService exportExcel;

    @Test
    public void codesSortTest() throws IOException {
        File file = new File(this.getClass().getResource("/doc").getPath() + "/样品编号排序测试用例_2024.1.23.xlsx");
        ImportParams importParams = new ImportParams();
        List<CodeSortModelVO> objects = ExcelImportUtil.importExcel(file, CodeSortModelVO.class, importParams);
        List<String> codes = objects.stream().map(CodeSortModelVO::getCode).collect(Collectors.toList());
        List<String> sortCodes = SortUtil.compareBusinessCode(codes);
        for (int i = 0; i < objects.size(); i++) {
            CodeSortModelVO codeSortModelVO = objects.get(i);
            codeSortModelVO.setSortCode(sortCodes.get(i));
        }
        OutputStream os = new FileOutputStream(this.getClass().getResource("/doc").getPath() + "/样品编号排序测试用例_2024.1.23_输出.xlsx");
        ExportParams exportParams = new ExportParams();
        exportParams.setPojoClass(CodeSortModelVO.class);
        exportExcel.exportData(objects, exportParams, os);
        os.flush();
        os.close();
    }
}
