package com.sinoyd.configuration;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;

/**
 * 默认的事务管理器
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-11-08
 */
@Configuration
@AutoConfigureAfter(DataSourceConfig.class)
public class DefaultTxnManagerConfig implements TransactionManagementConfigurer{

    @Resource(name = "limTransactionManager")
    private PlatformTransactionManager defaultTxnManager;

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return defaultTxnManager;
    }
}
