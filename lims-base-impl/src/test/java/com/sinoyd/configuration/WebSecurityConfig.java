package com.sinoyd.configuration;


import com.sinoyd.boot.auth.client.config.ServiceAuthConfig;
import com.sinoyd.boot.auth.client.config.UserAuthConfig;
import com.sinoyd.boot.auth.client.feign.IAuthService;
import com.sinoyd.frame.filter.JwtServiceAuthFilter;
import com.sinoyd.frame.filter.JwtUserAuthFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

/**
 * SpringSecurity的配置
 * 通过SpringSecurity的配置，将JWTLoginFilter，JWTAuthenticationFilter组合在一起
 * <AUTHOR> on 2017/9/13.
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig {

    @Autowired
    private ServiceAuthConfig serviceAuthConfig;

    @Autowired
    private UserAuthConfig userAuthConfig;

    @Autowired
    private IAuthService authService;


    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;


    /**
     * 设置 HTTP 验证规则
     *
     * @param http 请求
     * @throws Exception 异常信息
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http.cors().and().csrf().disable().authorizeRequests()
                //允许get根目录直属资源，html、css和js下所有资源（含子目录）
                .anyRequest().authenticated()  // 所有请求需要身份认证
                .and().headers().frameOptions().disable()// 禁用x-frame 、
                .and()
               .addFilter(new JwtServiceAuthFilter(authenticationConfiguration.getAuthenticationManager(), serviceAuthConfig))
                .addFilter(new JwtUserAuthFilter(authenticationConfiguration.getAuthenticationManager(), userAuthConfig, authService))
                .build();
    }
}