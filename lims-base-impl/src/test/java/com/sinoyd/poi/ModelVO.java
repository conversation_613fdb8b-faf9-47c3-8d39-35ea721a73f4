package com.sinoyd.poi;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/3
 */
@Data
@Accessors(chain = true)
public class ModelVO {

    private String displayAreaName;

    private String attentionDegreeName;

    private String areaName;

    private String psName;

    private String outputName;

    private String portName;

    private String tstamp;

    private String creatorName;

    private String reason;

    private String auditStatusName;

    private String auditorName;

    private String field1;
}