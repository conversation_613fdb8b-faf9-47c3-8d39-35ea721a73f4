package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.style.CellContentStyleBuilder;
import com.sinoyd.excel.style.RichTextBuilder;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.*;

/**
 * POI基于模板动态列导出
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class POIDynamicColumnTest {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
//        FileOutputStream fos = new FileOutputStream("F:\\temp\\" + UUIDHelper.newId() + "_DynamicColumn.xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
//        templateExportService.exportData("/doc/template/poi/DynamicColumns.xlsx", prepareData(), fos);
    }

    private List<ExcelSheetVO> prepareData() {
        Map<String, Object> infoDataMap = new HashMap<>();
        infoDataMap.put("title", "2023年7月废水报表");
        infoDataMap.put("psName", "张家港华芳集团");
        infoDataMap.put("portName", "ZJG-HF");
        infoDataMap.put("date", "2023-08-03");

        String unit = "烟气月排放总量单位：×104m3/月";
        RichTextBuilder unitBuilder = RichTextBuilder.create(unit)
                .richSup("104", 2, 2)
                .richSup("m3/月", 1, 1);
        unit = CellContentStyleBuilder.create(String.valueOf(unitBuilder.getText()))
                .getCellValue();
        infoDataMap.put("unit", unit);

        LinkedHashMap<String, String> expandColumnMap = new LinkedHashMap<>();
        expandColumnMap.put("s34001", "氨氮");
        expandColumnMap.put("s34002", "甲苯");
        expandColumnMap.put("s34003", "乙苯");
        expandColumnMap.put("s34004", "PH");
        expandColumnMap.put("s34005", "溶解氧");
        infoDataMap.put("factorName", expandColumnMap);

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("time", "0" + i);
            map.put("s34001-nd", (int) (Math.random() * 10));
            map.put("s34001-pfl", (int) (Math.random() * 10));
            map.put("s34002-nd", (int) (Math.random() * 10));
            map.put("s34002-pfl", (int) (Math.random() * 10));
            map.put("s34003-nd", (int) (Math.random() * 10));
            map.put("s34003-pfl", (int) (Math.random() * 10));
            map.put("s34004-nd", (int) (Math.random() * 10));
            map.put("s34004-pfl", (int) (Math.random() * 10));
            map.put("s34005-nd", (int) (Math.random() * 10));
            map.put("s34005-pfl", (int) (Math.random() * 10));

            map.put("water", (int) (Math.random() * 10));
            map.put("ph", (int) (Math.random() * 10));

            String remark = "111222333444555666777888999sci2*103sci注sub";
            RichTextBuilder richTextBuilder = RichTextBuilder.create(remark)
                    .richFontColor("222", "#CCFFFF").richBlob("222", true).richFontSize("222", 16)
                    .richFontColor("999", "#FF9B00").richFontSize("999", 18)
                    .richSup("2*103", 4, 4)
                    .richSub("注sub", 0, 0);
            remark = CellContentStyleBuilder.create(String.valueOf(richTextBuilder.getText()))
                    .getCellValue();
            map.put("remark", remark);

            dataList.add(map);
        }

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("废水报表")
                .setRenamedSheetName("废水报表")
                .setDataRowIdx(6)
                .setInfoDataMap(infoDataMap)
                .setRowsDataList(dataList);
        return Collections.singletonList(excelSheetVO);
    }
}