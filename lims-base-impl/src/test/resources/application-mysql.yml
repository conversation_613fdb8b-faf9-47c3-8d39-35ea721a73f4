server:
  port: ${PORT:6100}
  tomcat:
    uri-encoding: utf-8

spring:
  cache:
    type: redis
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:sinoyd}
    timeout: 10000
    database: 7
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

  # Mysql DATABASE CONFIG
  datasource:
    # sql连接配置
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:*************}:${DB_PORT:3306}/${DB_NAME:lims60lim}?useUnicode=true&allowMultiQueries=true
    username: ${DB_USER:user} #oracle #devuser
    password: ${DB_PWD:11111} #manager1 #qweAsd#21 #123qwe!@#
    initialSize: 10
    minIdle: 5
    maxActive: 50
    maxWait: 60000
    testWhileIdle: true

  jpa:
    database-platform: com.sinoyd.frame.base.configuration.CustomMySQLDialect
    hibernate:
      naming:
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true

  flyway:
    url: jdbc:mysql://${DB_HOST:*************}:${DB_PORT:3306}/${DB_NAME:lims60lim}?useUnicode=true&allowMultiQueries=true
    user: ${DB_USER:user}
    password: ${DB_PWD:11111}
    # 是否启用flyway
    enabled:  ${FLYWAY_ENABLED:true}
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 生产务必禁掉。这个默认值是 false 理论上作为默认配置是不科学的。
    clean-disabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: ${FLYWAY_LOCATIONS:classpath:db/migration/mysql}
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true


#设置feign客户端超时时间(OpenFeign默认支持ribbon) spring-cloud-netflix-ribbon下面的配置
ribbon:
  #指的是建立连接后从服务器读取到可用资源所用的时间
  ReadTimeout: 5000
  #指的是建立连接所用的时间，适用于网络状况正常的情况下, 两端连接所用的时间
  ConnectTimeout: 5000

feign:
  client:
    config:
      default: #feginClient的配置信息生效
        connectTimeout: 50000
        readTimeout: 50000

#logging
logging:
  level:
    root: info
  file:
    path: ${LOG_PATH:E:/LIMS60/source/log}
    name: ${LOG_NAME:LIMS60-lim.log}

lims-file:
  # 文件上传路径
  filePath: E:/LIMS60/source/lims-lim/proxy/files
  # 临时目录（生成的报表会先放临时目录）
  outputPath: E:/LIMS60/source/lims-lim/proxy/files/outputs
  # 报表、采样单、原始记录单等模板
  templatePath: E:/LIMS60/source/lims-lim/proxy/files/report_templates
  # 允许上传的文件类型
  fileSuffix: jpg,doc,docx,xls,xlsx,jpeg,png,txt,mp3,flac,avi,mp4

# APP下载地址
mobile:
  client:
    download:
      path: www.baidu.com