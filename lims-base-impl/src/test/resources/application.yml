server:
  tomcat:
    uri-encoding: utf-8

spring:
  main:
    allow-circular-references: true
  profiles:
    include: ${activeProfile:dev}

  jpa:
    database-platform: com.sinoyd.frame.base.configuration.CustomMySQLDialect
    hibernate:
      naming:
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true

  servlet:
    multipart:
      #文件上传请求最大大小限制
      max-request-size: 1000MB
      #文件最大大小限制
      max-file-size: 1000MB
      #启用大小限制
      enabled: true

#设置feign客户端超时时间(OpenFeign默认支持ribbon) spring-cloud-netflix-ribbon下面的配置
ribbon:
  #指的是建立连接后从服务器读取到可用资源所用的时间
  ReadTimeout: 5000
  #指的是建立连接所用的时间，适用于网络状况正常的情况下, 两端连接所用的时间
  ConnectTimeout: 5000

feign:
  client:
    config:
      default: #feginClient的配置信息生效
        connectTimeout: 50000
        readTimeout: 50000
