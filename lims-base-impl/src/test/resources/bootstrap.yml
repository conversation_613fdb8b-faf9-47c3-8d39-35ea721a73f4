spring:
  application:
    name: ${APP_CODE:lims60-lim}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:**************:8848}
        ip: ${NACOS_SERVER_IP:**************}
        port: ${NACOS_SERVER_PORT:6100}
        group: ${NACOS_GROUP:DEFAULT_GROUP} #服务分组名
        weight: ${NACOS_WEIGHT:1} # 负载均衡的权重
        namespace: ${NACOS_NAMESPACE:a27fea7b-e4d1-40c1-9c99-bb2ad9917383} #命名空间
      config:
        server-addr: ${NACOS_SERVER_ADDR:**************:8848}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        group: ${NACOS_GROUP:DEFAULT_GROUP} #服务分组名
        namespace: ${NACOS_NAMESPACE:a27fea7b-e4d1-40c1-9c99-bb2ad9917383} #命名空间
      username: ${NACOS_USER:sinoyd} # nacos的用户名
      password: ${NACOS_PWD:SinoydQazXC^&$%123}  #nacos的密码
    sentinel:
      transport:
        dashboard: ${SENTINEL_SERVER_ADDR:**************:18080}
        port: ${SENTINEL_API_PORT:8719}
      datasource:
        flow:
          nacos:
            server-addr: ${NACOS_SERVER_ADDR:**************:8848}
            data-id: ${spring.application.name}-flow-rules
            group-id: SENTINEL_GROUP
            rule-type: flow
        degrade:
          nacos:
            server-addr: ${NACOS_SERVER_ADDR:**************:8848}
            data-id: ${spring.application.name}-degrade-rules
            group-id: SENTINEL_GROUP
            rule-type: degrade
  main:
    allow-bean-definition-overriding: true # 允许覆盖实例名
auth:
  serviceId: sinoyd-auth #认证服务中心实例名
  user:
    token-header: Authorization
    server-validate-token: false #是否启用认证中心端校验用户token，默认为false
  client:
    token-header: client-token
    secret: ${APP_SECRET:12345678}