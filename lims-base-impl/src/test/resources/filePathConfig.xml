<configs>
    <config>
        <code>personPhotos</code>
        <name>人员照片</name>
        <className>com.sinoyd.lims.rms.PersonService</className>
        <method>findAttachmentPath</method>
        <placeholder>chineseName,id</placeholder>
        <path>LIM/Resource/Person/{chineseName}{id}/Photos</path>
    </config>
    <config>
        <code>personSign</code>
        <name>人员签名</name>
        <className>com.sinoyd.lims.rms.PersonService</className>
        <method>findAttachmentPath</method>
        <placeholder>chineseName,id</placeholder>
        <path>LIM/Resource/Person/{chineseName}{id}/Signature</path>
    </config>
    <config>
        <code>personAwards</code>
        <name>人员获奖信息</name>
        <className>com.sinoyd.lims.rms.PersonService</className>
        <method>findAttachmentPath</method>
        <placeholder>chineseName,id</placeholder>
        <path>LIM/Resource/Person/{chineseName}{id}/Awards</path>
    </config>
    <config>
        <code>personAttachment</code>
        <name>人员附件</name>
        <className>com.sinoyd.lims.rms.PersonService</className>
        <method>findAttachmentPath</method>
        <placeholder>chineseName,id</placeholder>
        <path>LIM/Resource/Person/{chineseName}{id}/Attachment</path>
    </config>
    <config>
        <code>analyzeMethod</code>
        <name>分析方法附件</name>
        <className>com.sinoyd.lims.rms.AnalyzeMethodService</className>
        <method>findAttachmentPath</method>
        <placeholder>methodName</placeholder>
        <path>LIM/Resource/Test/AnalyzeMethod/{methodName}</path>
    </config>

    <config>
        <code>instrumentPhotos</code>
        <name>仪器照片附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Photos</path>
    </config>
    <config>
        <code>instrument</code>
        <name>仪器附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/Resource/Instrument/{id}/Attachment</path>
    </config>
    <config>
        <code>instrumentUseRecord</code>
        <name>仪器使用附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/UseRecord</path>
    </config>
    <config>
        <code>instrumentMaintenance</code>
        <name>仪器维护附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Maintenance</path>
    </config>
    <config>
        <code>instrumentRepair</code>
        <name>仪器维修附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Repair</path>
    </config>
    <config>
        <code>instrumentScrapped</code>
        <name>仪器报废附件</name>
        <className>com.sinoyd.lims.rms.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Scrapped</path>
    </config>
    <config>
        <code>consumable</code>
        <name>消耗品附件</name>
        <className>com.sinoyd.lims.rms.service.ConsumableService</className>
        <method>findAttachmentPath</method>
        <placeholder>consumableName,id</placeholder>
        <path>LIM/Resource/Consumable/ConsumableDoc/{consumableName}{id}</path>
    </config>
    <config>
        <code>folder</code>
        <name>文档管理</name>
        <className>com.sinoyd.lims.rms.FolderService</className>
        <method>findAttachmentPath</method>
        <placeholder>path</placeholder>
        <path>LIM/Resource/Document/{path}</path>
    </config>
    <config>
        <code>environmental</code>
        <name>环境管理</name>
        <className>com.sinoyd.lims.rms.EnvironmentService</className>
        <method>findAttachmentPath</method>
        <placeholder>path</placeholder>
        <path>LIM/Resource/{labCode}{id}</path>
    </config>
    <config>
        <code>carManage</code>
        <name>车辆管理附件</name>
        <className>com.sinoyd.lims.rms.CarManageService</className>
        <method>findAttachmentPath</method>
        <placeholder>carCode</placeholder>
        <path>LIM/Resource/Car/{carCode}/Attachment</path>
    </config>
    <config>
        <code>evaluation</code>
        <name>评价标准附件</name>
        <className>com.sinoyd.lims.rms.EvaluationCriteriaService</className>
        <method>findAttachmentPath</method>
        <placeholder>name,ye</placeholder>
        <path>LIM/Evaluation/{name}{ye}</path>
    </config>
    <config>
        <code>shareFolder</code>
        <name>共享文档</name>
        <className>com.sinoyd.lims.rms.FolderService</className>
        <method>findAttachmentPath</method>
        <placeholder>path</placeholder>
        <path>DLY/ShareDocument/{path}</path>
    </config>
    <config>
        <code>notice</code>
        <name>公告附件</name>
        <className>com.sinoyd.lims.rms.NoticeService</className>
        <method>findAttachmentPath</method>
        <placeholder>categoryName,releaseDateStr,id</placeholder>
        <path>DLY/NoticeDoc/{categoryName}/{releaseDateStr}{id}</path>
    </config>
    <config>
        <code>appPackage</code>
        <name>移动端应用安装包</name>
        <className>com.sinoyd.lims.rms.AppVersionService</className>
        <method>findAttachmentPath</method>
        <placeholder>appTypeCode,appVersion</placeholder>
        <path>LIM/appVersion/{appTypeCode}/{appVersion}/attachment</path>
    </config>
    <config>
        <code>appDownloadQRCode</code>
        <name>移动端下载链接二维码</name>
        <className>com.sinoyd.lims.rms.AppVersionService</className>
        <method>findAttachmentPath</method>
        <placeholder>appType,appVersion</placeholder>
        <path>LIM/appVersion/{appTypeCode}/{appVersion}/qrCode</path>
    </config>
    <config>
        <code>enterprise</code>
        <name>客户管理附件</name>
        <className>com.sinoyd.lims.rms.service.EnterpriseService</className>
        <method>findAttachmentPath</method>
        <placeholder>id</placeholder>
        <path>LIM/enterprise/{id}</path>
    </config>
    <config>
        <code>subcontractor</code>
        <name>分包商管理附件</name>
        <className>com.sinoyd.lims.rms.service.SubcontractorService</className>
        <method>findAttachmentPath</method>
        <placeholder>id</placeholder>
        <path>BASE/Subcontractor/{id}</path>
    </config>
    <config>
        <code>supplier</code>
        <name>供应商管理附件</name>
        <className>com.sinoyd.lims.rms.service.SupplierService</className>
        <method>findAttachmentPath</method>
        <placeholder>id</placeholder>
        <path>BASE/Supplier/{id}</path>
    </config>
    <config>
        <code>platformVersion</code>
        <name>平台版本信息</name>
        <className>com.sinoyd.lims.rms.PlatformVersionService</className>
        <method>findAttachmentPath</method>
        <placeholder>platformVersion</placeholder>
        <path>LIM/PlatformVersion/{platformVersion}</path>
    </config>
    <config>
        <code>training</code>
        <name>培训管理</name>
        <className>com.sinoyd.lims.rms.TrainingService</className>
        <method>findAttachmentPath</method>
        <placeholder>trainingName,trainingDate</placeholder>
        <path>LIM/Training/{trainingName}/{trainingDate}</path>
    </config>
    <config>
        <code>personCert</code>
        <name>上岗证</name>
        <className>com.sinoyd.lims.rms.PersonCertificateService</className>
        <method>findAttachmentPath</method>
        <placeholder>name,certificationNo</placeholder>
        <path>LIM/Training/{certificationNo}/{name}</path>
    </config>
</configs>