package com.sinoyd.base.annotations;

import com.sinoyd.base.enums.EnumBusinessType;

import java.lang.annotation.*;

/**
 * 业务日志注解，用于controller入口，标记本次操作相关日志
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/17
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface BizLog {

    /**
     * 模块编码，前端放在参数中传递
     */
    String moduleCode();

    /**
     * 业务类型，参考 {@link com.sinoyd.base.enums.EnumBusinessType}
     */
    EnumBusinessType businessType();

    /**
     * 业务id
     */
    String businessIds();

    /**
     * 操作类型
     */
    String operationType();

    /**
     * 意见
     */
    String opinion() default "";

    /**
     * 日志内容
     */
    String content();

    /**
     * 审核信息实体类型
     */
    Class<?> approvalInfoClass() default Object.class;

    /**
     * 返回结果中实体类型
     */
    Class<?> resultBeanClass() default Object.class;
}
