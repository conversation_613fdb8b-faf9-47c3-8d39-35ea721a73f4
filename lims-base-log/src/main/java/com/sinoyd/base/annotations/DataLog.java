package com.sinoyd.base.annotations;

import com.sinoyd.base.enums.EnumDataOperateType;

import java.lang.annotation.*;

/**
 * 数据日志注解，用于service等方法上，记录本次操作对数据的相关变动
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface DataLog {

    /**
     * 资源名称
     */
    String resourceName() default "";

    /**
     * 资源
     */
    String resources();

    /**
     * 数据操作类型
     */
    EnumDataOperateType operateType();

    /**
     * 数据实体类型
     */
    Class<?> dataClass();
}
