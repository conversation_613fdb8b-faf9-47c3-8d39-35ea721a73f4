package com.sinoyd.base.annotations;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * LIMS日志注解，作用于实体属性上，用于标记该属性的页面显示名称
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/19
 */
@Documented
@Target({FIELD})
@Retention(RUNTIME)
public @interface LogField {

    /**
     * 页面选项卡名称
     */
    String tabName() default "";

    /**
     * 页面区域名称
     */
    String sectionName() default "";

    /**
     * 属性名称
     */
    String name();

    /**
     * 属性用到的枚举类型
     */
    Class<?> enumClass() default Enum.class;

    /**
     * 获取枚举显示名称方法名
     */
    String enumName() default "name";

    /**
     * 字段转换类名称，这里必须是spring托管的bean的名称
     */
    String convertBean() default "";

    /**
     * 字段转换方法名称
     */
    String convertMethod() default "findMapByIds";

    /**
     * 属性用到的字典加载器类型
     */
    String dictType() default "";

    /**
     * 属性用到的日期格式
     */
    String dateFormat() default "";

    /**
     * 属性值替换，如属性是Boolean类型，那么配置 replace ={"启用_true","禁用_false"}， 则会在日志记录中吧true转换成启用，false转换成禁用
     */
    String[] replace() default {"是_true", "否_false"};
}
