package com.sinoyd.base.aspects;

import com.sinoyd.base.service.IBizLogService;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务日志切面
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/17
 */
@Component
@Slf4j
@Aspect
public class BizLogAspect {

    /**
     * 存放当前线程的信息, Map<线程名称, Map<key, 内容>>
     */
    public static final Map<String, Map<String, Object>> threadCache = new ConcurrentHashMap<>();

    public static final String THREAD_CONTENT_KEY = "TRACK_CODE";

    private IBizLogService bizLogService;

    /**
     * 定义切入点
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.BizLog)")
    public void pointcut() {
    }

    /**
     * 处理方法
     *
     * @param joinPoint 切入点
     * @return 处理结果
     */
    @Around("pointcut()")
    @Transactional
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result;
        try {
            initThreadMap();
            result = joinPoint.proceed();
            bizLogService.saveBizLog(Thread.currentThread().getName(), SecurityContextHolder.getContext(), joinPoint, result);
        } catch (Throwable e) {
            log.error("处理业务日志发生错误", e);
            throw e;
        } finally {
            bizLogService.clearThreadCache(Thread.currentThread().getName());
        }
        return result;
    }

    /**
     * 初始化 threadMap
     */
    private void initThreadMap() {
        String trackCode = UUIDHelper.newId();
        Map<String, Object> threadContentMap = new HashMap<>();
        threadContentMap.put(THREAD_CONTENT_KEY, trackCode);
        threadCache.put(Thread.currentThread().getName(), threadContentMap);
    }


    @Data
    @Accessors(chain = true)
    public static class ApprovalInfoVO {

        /**
         * 业务id
         */
        private String businessId;

        /**
         * 审核人员id
         */
        private String auditorId;

        /**
         * 审核人名称
         */
        private String auditorName;

        /**
         * 审核步数
         */
        private Integer approveStep;
    }

    @Autowired
    public void setBizLogService(IBizLogService bizLogService) {
        this.bizLogService = bizLogService;
    }
}