package com.sinoyd.base.aspects;

import com.sinoyd.base.annotations.DataLog;
import com.sinoyd.base.enums.EnumDataOperateType;
import com.sinoyd.base.service.IDataLogService;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Table;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;

/**
 * 数据日志切面
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
@Component
@Slf4j
@Aspect
public class DataLogAspect {


    private IDataLogService dataLogService;

    /**
     * 定义切入点，用于拦截所有被@DataLog注解的方法。
     */
    @Pointcut("@annotation(com.sinoyd.base.annotations.DataLog)")
    public void pointcut() {
    }

    /**
     * 处理方法
     *
     * @param joinPoint 切入点
     * @return 处理结果
     */
    @Around("pointcut()")
    @Transactional
    public Object proceed(ProceedingJoinPoint joinPoint) throws Throwable {
        if (BizLogAspect.threadCache.get(Thread.currentThread().getName()) == null) {
            return joinPoint.proceed();
        }

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        DataLog dataLog = method.getAnnotation(DataLog.class);
        Object resourceName = loadResourceName(dataLog, joinPoint);
        String resourceExp = dataLog.resources();
        Collection<Object> resources = resourceExp.startsWith("#result") ? null : SPELUtil.parseSpel2Collection(resourceExp, joinPoint);
        EnumDataOperateType enumDataOperateType = dataLog.operateType();
        Object operateType = enumDataOperateType.name();
        Class<?> dataClass = dataLog.dataClass();

        List<?> beforeDataList = null, afterDataList = null;
        if (EnumDataOperateType.修改.name().equals(operateType) || EnumDataOperateType.删除.name().equals(operateType)) {
            beforeDataList = dataLogService.queryDataByIds(resources, dataClass);
        }

        //处理业务逻辑
        Object result = joinPoint.proceed();

        //插入更新操作的数据日志
        if (EnumDataOperateType.修改.name().equals(operateType) || EnumDataOperateType.新增.name().equals(operateType)) {
            if (StringUtils.isEmpty(resources)) {
                resources = SPELUtil.getResourceFromMethodResult(result, method, resourceExp);
            }
            afterDataList = dataLogService.queryDataByIds(resources, dataClass);
        }

        //处理数据日志
        dataLogService.saveDataLog(Thread.currentThread().getName(), SecurityContextHolder.getContext(), methodSignature, dataClass, operateType, result, method,
                resourceName, resources, resourceExp, beforeDataList, afterDataList);

        return result;
    }

    /**
     * 获取资源名称，如果注解指定了资源名称，以指定的为主，否则获取 dataClass上的表名作为资源名称
     *
     * @param dataLog   数据日志注解
     * @param joinPoint 切入点
     * @return 资源名称
     */
    private Object loadResourceName(DataLog dataLog, ProceedingJoinPoint joinPoint) {
        Object resourceNameAsObject = SPELUtil.parseSpel2Object(dataLog.resourceName(), joinPoint);
        String resourceName = "";
        if (resourceNameAsObject != null) {
            resourceName = resourceNameAsObject.toString();
        }
        if (StringUtils.isEmpty(resourceName)) {
            Class<?> clazz = dataLog.dataClass();
            Table table = clazz.getAnnotation(Table.class);
            return table.name();
        }
        return resourceName;
    }

    @Autowired
    public void setDataLogService(IDataLogService dataLogService) {
        this.dataLogService = dataLogService;
    }
}