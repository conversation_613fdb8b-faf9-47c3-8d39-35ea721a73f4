package com.sinoyd.base.controller;

import com.sinoyd.base.service.ILogService;
import com.sinoyd.base.vo.LogConditionVO;
import com.sinoyd.base.vo.LogVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 日志 controller
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/22
 */
@RestController
@RequestMapping("/api/base/log")
public class LogController extends ExceptionHandlerController<ILogService> {

    /**
     * 根据日志查询条件获取日志
     *
     * @param logConditionVO 日志查询条件
     * @return 包含日志列表的RestResponse对象
     */
    @PostMapping("/details")
    public RestResponse<List<LogVO>> findLogs(@RequestBody LogConditionVO logConditionVO) {
        RestResponse<List<LogVO>> restResponse = new RestResponse<>();
        restResponse.setData(service.findLog(logConditionVO));
        return restResponse;
    }
}