package com.sinoyd.base.service;

import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.security.core.context.SecurityContext;

/**
 * 对 @BizLog 注解的日志处理接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/28
 */
public interface IBizLogService {


    /**
     * 保存 @BizLog 相关的业务日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring security 上下文
     * @param joinPoint       切入点对象
     * @param result          方法执行结果
     */
    void saveBizLog(String mainThreadName, SecurityContext securityContext, ProceedingJoinPoint joinPoint, Object result);

    /**
     * 清除线程缓存
     *
     * @param mainThreadName 主线程名称
     */
    void clearThreadCache(String mainThreadName);
}
