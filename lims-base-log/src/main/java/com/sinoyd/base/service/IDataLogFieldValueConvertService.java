package com.sinoyd.base.service;

import com.sinoyd.base.annotations.LogField;

/**
 * 数据日字段内容转换接口，主要用于将布尔、枚举、字典、id等字段内容映射成名称或者描述
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/19
 */
public interface IDataLogFieldValueConvertService {


    /**
     * 将给定对象转换为字符串表示形式，并返回转换后的字符串。
     *
     * @param mainThreadName 主线程名称
     * @param value    需要转换的对象
     * @param logField 日志字段注解对象
     * @return 转换后的字符串
     */
    String convert(String mainThreadName, Object value, LogField logField);
}
