package com.sinoyd.base.service;

import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.context.SecurityContext;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;

/**
 * 对 @DataLog 注解的日志处理接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/28
 */
public interface IDataLogService {

    /**
     * 保存数据日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring security上下文
     * @param methodSignature 环绕方法签名
     * @param dataClass       数据实体类型
     * @param operateType     数据操作类型
     * @param result          环绕方法执行结果
     * @param method          环绕方法
     * @param resourceName    资源名称
     * @param resources       资源集合
     * @param resourceExp     资源表达式
     * @param beforeDataList  修改前的数据列表
     * @param afterDataList   修改后的数据列表
     */
    void saveDataLog(String mainThreadName,
                     SecurityContext securityContext,
                     MethodSignature methodSignature,
                     Class<?> dataClass,
                     Object operateType,
                     Object result,
                     Method method,
                     Object resourceName,
                     Collection<Object> resources,
                     String resourceExp,
                     List<?> beforeDataList,
                     List<?> afterDataList);

    /**
     * 根据给定的资源集合和数据类，查询数据库中的数据。
     *
     * @param resources 资源集合
     * @param dataClass 数据类
     * @return 查询结果
     */
    List<?> queryDataByIds(Collection<Object> resources,
                           Class<?> dataClass);
}
