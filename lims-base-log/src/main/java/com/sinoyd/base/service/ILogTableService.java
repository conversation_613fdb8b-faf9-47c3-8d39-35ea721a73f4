package com.sinoyd.base.service;

import org.springframework.security.core.context.SecurityContext;

/**
 * 日志表业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
public interface ILogTableService {

    /**
     * 插入日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param params          参数
     */
    void insertLogs(String mainThreadName, SecurityContext securityContext, Object... params);
}
