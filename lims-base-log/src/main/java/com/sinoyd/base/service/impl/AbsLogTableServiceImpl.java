package com.sinoyd.base.service.impl;

import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.boot.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 日志表业务实现抽象类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
@Service
@Slf4j
public abstract class AbsLogTableServiceImpl implements ILogTableService {

    protected JdbcTemplate jdbcTemplate;

    /**
     * 判断日志表是否存在
     *
     * @param mainThreadName 主线程名
     * @param tableName      日志表名
     * @return 判断结果
     */
    protected boolean isLogTableExists(String mainThreadName, String tableName) {
        Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
        if (threadMap.containsKey(tableName)) {
            return (boolean) threadMap.get(tableName);
        }
        try {
            String sql = String.format("select 1 from %s", tableName);
            jdbcTemplate.execute(sql);
        } catch (DataAccessException e) {
            return false;
        }
        return true;
    }

    /**
     * 创建日志表
     *
     * @param mainThreadName 主线程名
     * @param tableName 表名
     */
    protected void createLogTable(String mainThreadName, String tableName) {
        String createTableSql = createTableSql(tableName);
        jdbcTemplate.execute(createTableSql);
        BizLogAspect.threadCache.get(mainThreadName).put(tableName, true);
    }

    /**
     * 获取日志表后缀
     *
     * @return 日志表后缀
     */
    protected String loadTableSuffix() {
        return DateUtil.dateToString(new Date(), DateUtil.YEAR_MM);
    }

    /**
     * 获取建表语句
     *
     * @param tableName 表名
     * @return 建表语句
     */
    public abstract String createTableSql(String tableName);

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}