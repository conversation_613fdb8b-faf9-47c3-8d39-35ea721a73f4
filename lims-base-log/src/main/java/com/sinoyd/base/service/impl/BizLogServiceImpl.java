package com.sinoyd.base.service.impl;

import com.sinoyd.base.annotations.BizLog;
import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.enums.EnumBusinessType;
import com.sinoyd.base.service.IBizLogService;
import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.repository.CommonRepository;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对 @BizLog 注解的日志处理实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/28
 */
@Service
@Slf4j
@SuppressWarnings({"Duplicates", "unchecked"})
public class BizLogServiceImpl implements IBizLogService {

    @Resource
    private ILogTableService bizLogTableService;

    private CommonRepository commonRepository;


    @Override
    @Transactional
    @Async
    public void saveBizLog(String mainThreadName, SecurityContext securityContext, ProceedingJoinPoint joinPoint, Object result) {
        try {
            insertLog(joinPoint, securityContext, result, mainThreadName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            BizLogAspect.threadCache.get(mainThreadName).put("BIZ_LOG_FLAG", true);
        }
    }

    @Override
    @Async
    public void clearThreadCache(String mainThreadName) {
        Date startTime = new Date();
        Map<String, Map<String, Object>> threadCache = BizLogAspect.threadCache;
        while (true) {
            Map<String, Object> threadCacheMap = threadCache.get(mainThreadName);
            if (StringUtils.isNotEmpty(threadCacheMap)) {
                Object bizLogFlag = threadCacheMap.get("BIZ_LOG_FLAG");
                Object dataLogFlag = threadCacheMap.get("DATA_LOG_FLAG");
                boolean isBizLogCompleted = bizLogFlag != null && (boolean) bizLogFlag;
                boolean isDataLogCompleted = dataLogFlag != null && (boolean) dataLogFlag;
                if (isBizLogCompleted && isDataLogCompleted) {
                    BizLogAspect.threadCache.remove(Thread.currentThread().getName());
                    log.info("...Clear thread cache successfully!...");
                    break;
                }
            } else {
                break;
            }
            // 超过30秒则认为线程已经挂起，需要清理
            Date endTime = new Date();
            if ((endTime.getTime() - startTime.getTime()) > 30000) {
                log.error(Thread.currentThread().getName() + " has been suspended for 30 seconds, clear it!");
                break;
            }
        }
    }

    /**
     * 插入日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param joinPoint       切入点
     * @param result          业务方法执行结果
     */
    private void insertLog(ProceedingJoinPoint joinPoint, SecurityContext securityContext, Object result, String mainThreadName) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        BizLog bizLog = method.getAnnotation(BizLog.class);

        Object moduleCode = SPELUtil.parseSpel2Object(bizLog.moduleCode(), joinPoint);
        EnumBusinessType businessType = bizLog.businessType();
        String businessIdsExp = bizLog.businessIds();
        Collection<Object> businessIds = businessIdsExp.startsWith("#result")
                ? getResourceFromMethodResult(result, businessIdsExp, bizLog) : SPELUtil.parseSpel2Collection(bizLog.businessIds(), joinPoint);
        String operateTypeExp = bizLog.operationType();
        if (StringUtils.isNotEmpty(operateTypeExp) && !operateTypeExp.startsWith("'")) {
            operateTypeExp = "'" + operateTypeExp + "'";
        }
        Object operateType = SPELUtil.parseSpel2Object(operateTypeExp, joinPoint);
        Object content = SPELUtil.parseSpel2Object(bizLog.content(), joinPoint);
        Object opinion = SPELUtil.parseSpel2Object(bizLog.opinion(), joinPoint);
        Map<String, BizLogAspect.ApprovalInfoVO> approvalInfoVOMap = loadNextOperatorName(bizLog, businessIds);
        bizLogTableService.insertLogs(mainThreadName, securityContext, moduleCode, businessType.name(), businessIds,
                operateType, content, opinion, SPELUtil.getTriggerMethodSignature(methodSignature), approvalInfoVOMap);
    }

    /**
     * 从方法执行结果中获取资源对象列表
     *
     * @param methodResult 方法执行结果
     * @param resourceExp  资源表达式
     * @return 资源对象列表
     * @throws RuntimeException 如果在获取资源列表过程中出现异常，则抛出该异常
     */
    private List<Object> getResourceFromMethodResult(Object methodResult, String resourceExp, BizLog bizLog) {
        if (methodResult instanceof RestResponse) {
            RestResponse<?> restResponse = (RestResponse<?>) methodResult;
            Object data = restResponse.getData();

            if (!bizLog.resultBeanClass().getSimpleName().equals("Object")) {
                return SPELUtil.getResourceFromMethodResult(data, bizLog.resultBeanClass(), resourceExp);
            } else {
                List<Object> list = new ArrayList<>();
                list.add(data);
                return SPELUtil.getResourceFromMethodResult(list, data.getClass(), resourceExp);
            }

        }
        throw new BaseException("... @BizLog only can be added on the method which returned RestResponse ...");
    }

    /**
     * 获取下一步操作人
     *
     * @param bizLog      日志注解
     * @param businessIds 业务id集合
     * @return Map<业务id, 审核信息VO实例>
     */
    private Map<String, BizLogAspect.ApprovalInfoVO> loadNextOperatorName(BizLog bizLog, Collection<Object> businessIds) {
        Class<?> clazz = bizLog.approvalInfoClass();
        Map<String, BizLogAspect.ApprovalInfoVO> resultMap = new HashMap<>();
        if (!clazz.getSimpleName().equals("Object")) {
            EnumBusinessType enumBusinessType = bizLog.businessType();
            String jpql = String.format("select a.businessId, a.auditorId, a.auditorName, a.approveStep from %s a " +
                    "where a.businessId in :businessIds and a.businessType = :businessType " +
                    "and a.isDeleted = 0 and a.auditorResult is null order by a.approveStep ", clazz.getSimpleName());
            Map<String, Object> jpqlParams = new HashMap<>();
            jpqlParams.put("businessIds", businessIds);
            jpqlParams.put("businessType", enumBusinessType.name());
            List<Object[]> queryResult = commonRepository.find(jpql, jpqlParams);

            if (StringUtils.isNotEmpty(queryResult)) {
                List<BizLogAspect.ApprovalInfoVO> approvalInfoVOList = new ArrayList<>();
                for (Object[] obj : queryResult) {
                    BizLogAspect.ApprovalInfoVO vo = new BizLogAspect.ApprovalInfoVO()
                            .setBusinessId(obj[0].toString())
                            .setAuditorId(obj[1].toString())
                            .setAuditorName(obj[2].toString())
                            .setApproveStep((Integer) obj[3]);
                    approvalInfoVOList.add(vo);
                }
                Map<String, List<BizLogAspect.ApprovalInfoVO>> approveInfoMap = approvalInfoVOList.stream().collect(Collectors.groupingBy(BizLogAspect.ApprovalInfoVO::getBusinessId));
                for (Map.Entry<String, List<BizLogAspect.ApprovalInfoVO>> map : approveInfoMap.entrySet()) {
                    if (StringUtils.isNotEmpty(map.getValue())) {
                        map.getValue().sort(Comparator.comparing(BizLogAspect.ApprovalInfoVO::getApproveStep));
                        resultMap.put(map.getKey(), map.getValue().get(0));
                    }
                }
            }
        }
        return resultMap;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setBizLogTableService(ILogTableService bizLogTableService) {
        this.bizLogTableService = bizLogTableService;
    }
}