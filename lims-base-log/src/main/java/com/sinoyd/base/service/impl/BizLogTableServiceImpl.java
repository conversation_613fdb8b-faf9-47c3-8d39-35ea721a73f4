package com.sinoyd.base.service.impl;

import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;

/**
 * 日志表业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
@Service("bizLogTableService")
@Slf4j
@SuppressWarnings("unchecked")
public class BizLogTableServiceImpl extends AbsLogTableServiceImpl implements ILogTableService {

    public final static String BIZ_LOG_TABLE_PREFIX = "TB_BIZ_Log_";


    @Override
    @Transactional
    public void insertLogs(String mainThreadName, SecurityContext securityContext,  Object... params) {
        SecurityContextHolder.setContext(securityContext);
        List<Object[]> sqlParams = new ArrayList<>();
        CurrentPrincipalUser principal = PrincipalContextUser.getPrincipal();
        Timestamp timestamp = new Timestamp(new Date().getTime());

        Object moduleCode = params[0];
        Object businessType = params[1];
        Collection<Object> businessIds = (Collection<Object>) params[2];
        Object operateType = params[3];
        Object content = params[4];
        Object opinion = params[5];
        Object triggerPoint = params[6];
        Map<String, BizLogAspect.ApprovalInfoVO> approvalInfoVOMap = (Map<String, BizLogAspect.ApprovalInfoVO>) params[7];

        Object trackCode = BizLogAspect.threadCache.get(mainThreadName).get(BizLogAspect.THREAD_CONTENT_KEY);
        for (Object businessId : businessIds) {
            String nextOperatorId = approvalInfoVOMap.get(businessId.toString()) == null ? null : approvalInfoVOMap.get(businessId.toString()).getAuditorId();
            String nextOperatorName = approvalInfoVOMap.get(businessId.toString()) == null ? null : approvalInfoVOMap.get(businessId.toString()).getAuditorName();
            Object[] objects = new Object[]{UUIDHelper.newId(), moduleCode, businessType, businessId, operateType,
                    trackCode, principal.getUserId(), principal.getUserName(), timestamp, content,
                    nextOperatorId, nextOperatorName, opinion, triggerPoint, principal.getOrgId(), principal.getOrgId()};
            sqlParams.add(objects);
        }

        String tableName = BIZ_LOG_TABLE_PREFIX + loadTableSuffix();
        boolean isTableExists = isLogTableExists(mainThreadName, tableName);
        if (!isTableExists) {
            createLogTable(mainThreadName, tableName);
        }

        insertData(tableName, sqlParams);
    }


    @Override
    public String createTableSql(String tableName) {
        return "CREATE TABLE " + tableName + "(" +
                " id VARCHAR(50) NOT NULL   COMMENT '主键' ," +
                " moduleCode VARCHAR(50) NOT NULL   COMMENT '模块编码' ," +
                " businessType VARCHAR(50) NOT NULL   COMMENT '业务类型，比如报价单、项目、方案等，枚举管理' ," +
                " businessId VARCHAR(50) NOT NULL   COMMENT '业务标识(一般用id标识)' ," +
                " operateType VARCHAR(50) NOT NULL   COMMENT '操作类型，比如新增点位、修改定位等' ," +
                " trackCode VARCHAR(50) NOT NULL   COMMENT '追踪码' ," +
                " operatorId VARCHAR(50) NOT NULL   COMMENT '操作人id' ," +
                " operatorName VARCHAR(50) NOT NULL   COMMENT '操作人名称' ," +
                " operateTime DATETIME NOT NULL   COMMENT '操作时间' ," +
                " content VARCHAR(900) NOT NULL   COMMENT '日志内容' ," +
                " nextOperatorId VARCHAR(50)    COMMENT '下一步操作人id' ," +
                " nextOperatorName VARCHAR(50)    COMMENT '下一步操作人名称' ," +
                " opinion VARCHAR(255)    COMMENT '意见' ," +
                " triggerPoint VARCHAR(900)    COMMENT '触发点，一般存储日志触发的方法的类+方法名称' ," +
                " orgId VARCHAR(50) NOT NULL   COMMENT '所属机构ID' ," +
                " domainId VARCHAR(50) NOT NULL   COMMENT '所属实验室ID' ," +
                " PRIMARY KEY (id)" +
                ") COMMENT = '业务日志表';";
    }

    /**
     * 将数据插入到日志表中
     *
     * @param tableName 表名
     * @param sqlParams 要插入的数据列表，每个元素为一个Object数组，对应日志表中的一列
     */
    private void insertData(String tableName, List<Object[]> sqlParams) {
        String insertLogSql = String.format("INSERT INTO %s(id, moduleCode, businessType, businessId, operateType, " +
                "trackCode, operatorId, operatorName, operateTime, content, " +
                "nextOperatorId, nextOperatorName, opinion, triggerPoint, orgId, domainId) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", tableName);
        jdbcTemplate.batchUpdate(insertLogSql, sqlParams);
    }

}