package com.sinoyd.base.service.impl;

import com.sinoyd.base.annotations.LogField;
import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.service.IDataLogFieldValueConvertService;
import com.sinoyd.base.vo.FieldConvertVO;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数据日字段内容转换实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/19
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class DataLogFieldValueConvertServiceImpl implements IDataLogFieldValueConvertService {

    @Override
    public String convert(String mainThreadName, Object value, LogField logField) {
        if (value == null) {
            return "";
        }
        String dateFormat = logField.dateFormat();
        Class<?> enumClass = logField.enumClass();
        String enumName = logField.enumName();
        String dictType = logField.dictType();
        String[] replace = logField.replace();
        String convertBean = logField.convertBean();
        if (StringUtils.isNotEmpty(dateFormat) && value instanceof Date) {
            return convertDateField(value, dateFormat);
        } else if (value instanceof Boolean) {
            return convertBooleanField(value, replace);
        } else if (enumClass != null && !Enum.class.getName().equals(enumClass.getName())) {
            return convertEnumField(value, enumClass, enumName);
        } else if (StringUtils.isNotEmpty(dictType)) {
            return convertCodeField(mainThreadName, value, dictType);
        } else if (StringUtils.isNotEmpty(convertBean)) {
            FieldConvertVO convertVO = new FieldConvertVO(logField);
            return convertSpecialField(mainThreadName, value, convertVO);
        }
        return value.toString();
    }

    /**
     * 将日期对象转换为指定格式的字符串
     *
     * @param value      日期对象
     * @param dateFormat 日期格式
     * @return 转换后的字符串
     */
    private String convertDateField(Object value, String dateFormat) {
        return DateUtil.dateToString((Date) value, dateFormat);
    }


    /**
     * 将布尔类型的数据转换为指定字符串
     *
     * @param value   需要转换的布尔值
     * @param replace 用于替换的字符串数组
     * @return 转换后的字符串
     */
    private String convertBooleanField(Object value, String[] replace) {
        for (String str : replace) {
            if (str.endsWith(value.toString())) {
                return str.split("_")[0];
            }
        }
        return value.toString();
    }

    /**
     * 将枚举类型的数据转换为指定字符串
     *
     * @param value     需要转换的枚举值
     * @param enumClass 枚举类型对应的Class对象
     * @param enumName  枚举类型中获取名称的方法名
     * @return 转换后的字符串
     */
    private String convertEnumField(Object value, Class<?> enumClass, String enumName) {
        try {
            Object[] enumConstants = enumClass.getEnumConstants();
            Method getValue = enumClass.getMethod("getValue");
            if (!"name".equals(enumName)) {
                enumName = "get" + Character.toUpperCase(enumName.charAt(0)) + enumName.substring(1);
            }
            Method name = enumClass.getMethod(enumName);
            for (Object con : enumConstants) {
                Object val = getValue.invoke(con);
                if (value.equals(val)) {
                    return name.invoke(con).toString();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return value.toString();
    }

    /**
     * 将字典编码类型的字段值转换为对应的字典名称
     *
     * @param value    字段值
     * @param dictType 字典类型
     * @return 返回与字典编码对应的字典名称
     */
    private String convertCodeField(String mainThreadName, Object value, String dictType) {
        Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
        if (threadMap.containsKey(dictType)) {
            List<DtoCode> codeList = (List<DtoCode>) threadMap.get(dictType);
            Optional<DtoCode> codeOptional = codeList.stream().filter(dtoCode -> dtoCode.getDictCode().equals(value)).findFirst();
            if (codeOptional.isPresent()) {
                return codeOptional.get().getDictName();
            }
        }
        return value.toString();
    }

    /**
     * 将特殊字段进行转换
     *
     * @param mainThreadName 当前线程名称
     * @param value          字段值
     * @param fieldConvertVO 字段转换VO
     * @return 转换后的值
     */
    private String convertSpecialField(String mainThreadName, Object value, FieldConvertVO fieldConvertVO) {
        Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
        if (threadMap.containsKey(FieldConvertVO.getConvertKey(fieldConvertVO))) {
            Map<Object, Object> map = (Map<Object, Object>) threadMap.get(FieldConvertVO.getConvertKey(fieldConvertVO));
            return map.get(value).toString();
        }
        return value.toString();
    }
}