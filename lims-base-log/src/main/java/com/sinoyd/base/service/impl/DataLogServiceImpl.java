package com.sinoyd.base.service.impl;

import cn.afterturn.easypoi.util.PoiPublicUtil;
import com.sinoyd.base.annotations.LogField;
import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.enums.EnumDataOperateType;
import com.sinoyd.base.service.IDataLogFieldValueConvertService;
import com.sinoyd.base.service.IDataLogService;
import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.base.util.SPELUtil;
import com.sinoyd.base.vo.FieldConvertVO;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.service.CodeService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 对 @DataLog 注解的日志处理实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/28
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class DataLogServiceImpl implements IDataLogService {

    @Resource
    private ILogTableService resourceLogTableService;

    @Resource
    private ILogTableService dataLogTableService;

    private CodeService codeService;

    private IDataLogFieldValueConvertService fieldValueConvertService;

    private CommonRepository commonRepository;

    @PersistenceContext(
            unitName = "limEntityManagerFactory"
    )
    private EntityManager entityManager;

    @Override
    @Transactional
    @Async
    public void saveDataLog(String mainThreadName,
                            SecurityContext securityContext,
                            MethodSignature methodSignature,
                            Class<?> dataClass,
                            Object operateType,
                            Object result,
                            Method method,
                            Object resourceName,
                            Collection<Object> resources,
                            String resourceExp,
                            List<?> beforeDataList,
                            List<?> afterDataList) {
        try {
            //插入资源日志
            insertResourceLog(mainThreadName, securityContext, resourceName, resources, operateType, methodSignature);

            //插入删除操作的数据日志
            if (EnumDataOperateType.删除.name().equals(operateType)) {
                insertDeleteDataLog(mainThreadName, securityContext, dataClass, beforeDataList);
            }

            //插入更新操作的数据日志
            if (EnumDataOperateType.修改.name().equals(operateType)) {
                insertUpdateDataLog(mainThreadName, securityContext, dataClass, beforeDataList, afterDataList);
            }

            //插入新增操作的数据日志
            if (EnumDataOperateType.新增.name().equals(operateType)) {
                insertCreateDataLog(mainThreadName, securityContext, dataClass, afterDataList);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            BizLogAspect.threadCache.get(mainThreadName).put("DATA_LOG_FLAG", true);
        }
    }

    @Override
    public List<?> queryDataByIds(Collection<Object> resources,
                                  Class<?> dataClass) {
        String entityName = dataClass.getSimpleName();
        String jpql = String.format("select a from %s a where a.id in :ids ", entityName);
        Map<String, Object> jpqlParams = new HashMap<>();
        jpqlParams.put("ids", resources);
        List<?> queryResult = commonRepository.find(jpql, jpqlParams);
        entityManager.clear();
        return queryResult;
    }

    /**
     * 将资源日志插入数据库
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param resourceName    资源名称
     * @param resources       资源集合
     * @param operateType     操作类型
     * @param methodSignature 触发方法签名
     */
    private void insertResourceLog(String mainThreadName,
                                   SecurityContext securityContext,
                                   Object resourceName,
                                   Collection<Object> resources,
                                   Object operateType,
                                   MethodSignature methodSignature) {
        resourceLogTableService.insertLogs(mainThreadName, securityContext, resourceName, resources, operateType,
                SPELUtil.getTriggerMethodSignature(methodSignature));
    }

    /**
     * 插入创建数据的日志
     *
     * @param mainThreadName  线程名称
     * @param securityContext 上下文信息
     * @param dataClass       数据类
     * @param afterList       操作后数据集合
     */
    private void insertCreateDataLog(String mainThreadName, SecurityContext securityContext, Class<?> dataClass, List<?> afterList) {
        insertDataLog(mainThreadName, securityContext, dataClass, null, afterList);
    }

    /**
     * 插入删除数据日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param dataClass       数据类
     * @param beforeList      操作前数据集合
     */
    private void insertDeleteDataLog(String mainThreadName, SecurityContext securityContext, Class<?> dataClass, List<?> beforeList) {
        insertDataLog(mainThreadName, securityContext, dataClass, beforeList, null);
    }

    /**
     * 插入更新数据日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param dataClass       数据类
     * @param beforeList      操作前数据集合
     * @param afterList       操作后数据集合
     */
    private void insertUpdateDataLog(String mainThreadName, SecurityContext securityContext, Class<?> dataClass, List<?> beforeList, List<?> afterList) {
        insertDataLog(mainThreadName, securityContext, dataClass, beforeList, afterList);
    }

    /**
     * 插入创建或删除的数据日志
     *
     * @param mainThreadName  主线程名称
     * @param securityContext spring上下文信息
     * @param dataClass       数据类
     * @param beforeList      操作前数据集合
     * @param afterList       操作后数据集合
     */
    private void insertDataLog(String mainThreadName, SecurityContext securityContext, Class<?> dataClass, List<?> beforeList, List<?> afterList) {
        try {
            Field[] fields = PoiPublicUtil.getClassFields(dataClass);
            Field idField = Arrays.stream(fields).filter(f -> "id".equals(f.getName())).findFirst()
                    .orElseThrow(() -> new BaseException(String.format("实体[%s]不存在id属性", dataClass.getName())));
            idField.setAccessible(true);
            List<?> dataList = beforeList == null ? afterList : beforeList;

            List<Object[]> params = new ArrayList<>();
            Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
            for (int i = 0; i < dataList.size(); i++) {
                Object beforeData = null, afterData = null;
                if (StringUtils.isNotEmpty(beforeList)) {
                    beforeData = beforeList.get(i);
                }
                if (StringUtils.isNotEmpty(afterList)) {
                    afterData = afterList.get(i);
                }
                Object data = dataList.get(i);
                Object id = idField.get(data);
                Object resourceId = threadMap.get(id.toString());
                for (Field field : fields) {
                    LogField logField = field.getAnnotation(LogField.class);
                    if (logField != null) {
                        field.setAccessible(true);
                        String tabName = logField.tabName();
                        String sectionName = logField.sectionName();
                        String itemName = logField.name();
                        Object beforeValue = beforeData == null ? null : field.get(beforeData);
                        Object afterValue = afterData == null ? null : field.get(afterData);
                        Object[] objArray = new Object[]{resourceId, tabName, sectionName, itemName, new Object[]{beforeValue, logField}, new Object[]{afterValue, logField}};

                        if (beforeValue instanceof Date) {
                            beforeValue = DateUtil.dateToString((Date) beforeValue, logField.dateFormat());
                        }
                        if (afterValue instanceof Date) {
                            afterValue = DateUtil.dateToString((Date) afterValue, logField.dateFormat());
                        }

                        //判断是否需要记录日志
                        boolean isLog = (beforeValue != null && !beforeValue.equals(afterValue)) || (afterValue != null && !afterValue.equals(beforeValue));
                        if (isLog) {
                            params.add(objArray);
                        }
                        putThreadCache(mainThreadName, logField, beforeValue, afterValue);
                    }
                }
            }
            if (params.size() > 0) {
                processSpecialFieldThreadCache(mainThreadName, params);

                List<Object[]> newParams = new ArrayList<>();
                for (Object[] param : params) {
                    Object[] newParam = new Object[6];
                    newParam[0] = param[0];
                    newParam[1] = param[1];
                    newParam[2] = param[2];
                    newParam[3] = param[3];
                    Object[] beforeValue = (Object[]) param[4];
                    Object[] afterValue = (Object[]) param[5];
                    LogField logField = (LogField) beforeValue[1];
                    newParam[4] = fieldValueConvertService.convert(mainThreadName, beforeValue[0], logField);
                    newParam[5] = fieldValueConvertService.convert(mainThreadName, afterValue[0], logField);
                    newParams.add(newParam);
                }
                dataLogTableService.insertLogs(mainThreadName, securityContext, newParams.toArray());
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将字典缓存到线程中
     *
     * @param mainThreadName 主线程名称
     * @param logField       字段注解
     * @param beforeValue    操作前数据
     * @param afterValue     操作后数据
     */
    private void putThreadCache(String mainThreadName, LogField logField, Object beforeValue, Object afterValue) {
        Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
        //缓存字典字段信息
        String dictType = logField.dictType();
        if (StringUtils.isNotEmpty(dictType) && !threadMap.containsKey(dictType)) {
            threadMap.put(logField.dictType(), codeService.findCodes(logField.dictType()));
        }
        //缓存需要特殊处理的字段信息
        FieldConvertVO convertVO = threadMap.containsKey(FieldConvertVO.getConvertKey(new FieldConvertVO(logField)))
                ? (FieldConvertVO) threadMap.get(logField.convertBean() + "##" + logField.convertMethod()) : new FieldConvertVO(logField);
        if (convertVO.getIsConvert()) {
            if (beforeValue != null) {
                convertVO.getMethodParams().add(beforeValue);
            }
            if (afterValue != null) {
                convertVO.getMethodParams().add(afterValue);
            }
            threadMap.put(FieldConvertVO.getConvertKey(convertVO), convertVO);
        }
    }

    /**
     * 处理特殊字段的线程缓存
     *
     * @param mainThreadName 线程名称
     * @param params         参数列表
     * @throws RuntimeException 如果处理过程中发生异常，则抛出运行时异常
     */
    private void processSpecialFieldThreadCache(String mainThreadName, List<Object[]> params) {
        Map<String, Object> threadMap = BizLogAspect.threadCache.get(mainThreadName);
        List<FieldConvertVO> convertVOList = new ArrayList<>();
        for (Object[] param : params) {
            Object[] beforeValue = (Object[]) param[4];
            LogField logField = (LogField) beforeValue[1];
            FieldConvertVO vo = (FieldConvertVO) threadMap.get(FieldConvertVO.getConvertKey(new FieldConvertVO(logField)));
            if (vo != null) {
                convertVOList.add(vo);
            }
        }
        try {
            for (FieldConvertVO convertVO : convertVOList) {
                Class<?> beanClass = SpringContextAware.getBean(convertVO.getConvertBean()).getClass();
                Method method = beanClass.getMethod(convertVO.getConvertMethod(), Collection.class);
                Map<Object, Object> resultMap = (Map<Object, Object>) method.invoke(SpringContextAware.getBean(convertVO.getConvertBean()), convertVO.getMethodParams());
                threadMap.put(FieldConvertVO.getConvertKey(convertVO), resultMap);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setFieldValueConvertService(IDataLogFieldValueConvertService fieldValueConvertService) {
        this.fieldValueConvertService = fieldValueConvertService;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }
}