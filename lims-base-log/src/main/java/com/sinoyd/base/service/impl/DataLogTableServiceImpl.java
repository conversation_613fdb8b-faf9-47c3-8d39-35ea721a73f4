package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/19
 */
@Service("dataLogTableService")
@Slf4j
public class DataLogTableServiceImpl extends AbsLogTableServiceImpl implements ILogTableService {

    public final static String DATA_LOG_TABLE_PREFIX = "TB_BIZ_Log_Data_";

    @Override
    @Transactional
    public void insertLogs(String mainThreadName, SecurityContext securityContext, Object... params) {
        SecurityContextHolder.setContext(securityContext);
        List<Object[]> sqlParams = new ArrayList<>();

        for (Object param : params) {
            Object[] objects = (Object[]) param;
            Object resourceId = objects[0];
            Object tabName = objects[1];
            Object sectionName = objects[2];
            Object itemName = objects[3];
            Object beforeValue = objects[4];
            Object afterValue = objects[5];
            Object[] sqlParam = new Object[]{UUIDHelper.newId(), resourceId, tabName, sectionName, itemName, beforeValue, afterValue};
            sqlParams.add(sqlParam);
        }

        String tableName = DATA_LOG_TABLE_PREFIX + loadTableSuffix();
        boolean isTableExists = isLogTableExists(mainThreadName, tableName);
        if (!isTableExists) {
            createLogTable(mainThreadName, tableName);
        }

        insertData(tableName, sqlParams);
    }

    @Override
    public String createTableSql(String tableName) {
        return "CREATE TABLE " + tableName + "(" +
                " id VARCHAR(50) NOT NULL   COMMENT '主键' ," +
                " resourceId VARCHAR(50) NOT NULL   COMMENT '资源id' ," +
                " tabName VARCHAR(100)    COMMENT '页面选项卡名称' ," +
                " sectionName VARCHAR(100)    COMMENT '页面区域名称' ," +
                " itemName VARCHAR(100) NOT NULL   COMMENT '数据项名称' ," +
                " beforeValue VARCHAR(900)    COMMENT '操作前内容', " +
                " afterValue VARCHAR(900)    COMMENT '操作后内容', " +
                " PRIMARY KEY (id)" +
                ") COMMENT = '数据日志表';";
    }

    /**
     * 将数据插入到日志表中
     *
     * @param tableName 表名
     * @param sqlParams 要插入的数据列表，每个元素为一个Object数组，对应日志表中的一列
     */
    private void insertData(String tableName, List<Object[]> sqlParams) {
        String insertLogSql = String.format("INSERT INTO %s(id, resourceId, tabName, sectionName, itemName, beforeValue, afterValue) " +
                "values(?,?,?,?,?,?,?) ", tableName);
        jdbcTemplate.batchUpdate(insertLogSql, sqlParams);
    }
}