package com.sinoyd.base.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.sinoyd.base.service.ILogService;
import com.sinoyd.base.vo.LogConditionVO;
import com.sinoyd.base.vo.LogVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.boot.frame.client.enums.EnumOrgType;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceContext;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志业务实现，主要用于日志查询
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/22
 */
@Service
@Slf4j
public class LogServiceImpl implements ILogService {

    @PersistenceContext(
            unitName = "limEntityManagerFactory"
    )
    private EntityManager entityManager;

    private JdbcTemplate jdbcTemplate;


    @Override
    public List<LogVO> findLog(LogConditionVO conditionVO) {
        List<LogVO> logVOList = new ArrayList<>();
        List<String> allLogTableNames = getAllLogTableNames(BizLogTableServiceImpl.BIZ_LOG_TABLE_PREFIX + "2");
        if (StringUtils.isNotEmpty(allLogTableNames)) {
            boolean isSystemOrg = EnumOrgType.系统机构.getValue().equals(PrincipalContextUser.getPrincipal().getUserType());

            String sqlTemplate = "select id, moduleCode, businessType, businessId, operateType, trackCode, operatorId, "
                    + "operatorName, operateTime, content, nextOperatorId, nextOperatorName, opinion, triggerPoint, orgId, domainId "
                    + "from %s "
                    + "where 1 = 1 ";

            if (StringUtils.isNotEmpty(conditionVO.getBusinessType())) {
                sqlTemplate += "and businessType = :businessType ";
            }
            if (StringUtils.isNotEmpty(conditionVO.getBusinessId())) {
                sqlTemplate += "and businessId = :businessId ";
            }
            if (StringUtils.isNotEmpty(conditionVO.getOperationType())) {
                sqlTemplate += "and operateType like :operateType ";
            }
            if (StringUtils.isNotEmpty(conditionVO.getOperatorId())) {
                sqlTemplate += "and operatorId = :operatorId ";
            }
            if (StringUtils.isNotEmpty(conditionVO.getTrackCode())) {
                sqlTemplate += "and trackCode like :trackCode ";
            }

            StringBuilder sql = new StringBuilder();
            for (int i = 0; i < allLogTableNames.size(); i++) {
                if (i > 0) {
                    sql.append(" UNION ALL ");
                }
                sql.append(String.format(sqlTemplate, allLogTableNames.get(i)));
            }

            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
            Map<String, Object> sqlParamMap = new HashMap<>();
            if (StringUtils.isNotEmpty(conditionVO.getBusinessType())) {
                sqlParamMap.put("businessType", conditionVO.getBusinessType());
            }
            if (StringUtils.isNotEmpty(conditionVO.getBusinessId())) {
                sqlParamMap.put("businessId", conditionVO.getBusinessId());
            }
            if (StringUtils.isNotEmpty(conditionVO.getOperationType())) {
                sqlParamMap.put("operateType", "%" + conditionVO.getOperationType() + "%");
            }
            if (StringUtils.isNotEmpty(conditionVO.getOperatorId())) {
                sqlParamMap.put("operatorId", conditionVO.getOperatorId());
            }
            if (StringUtils.isNotEmpty(conditionVO.getTrackCode())) {
                sqlParamMap.put("trackCode", "%" + conditionVO.getTrackCode() + "%");
            }

            logVOList = namedParameterJdbcTemplate.query(sql.toString(), sqlParamMap, new BeanPropertyRowMapper<>(LogVO.class));
            logVOList.sort(Comparator.comparing(LogVO::getOperateTime, Comparator.reverseOrder()));
            loadLogResource(logVOList, isSystemOrg);
        }
        return logVOList;
    }

    /**
     * 加载日志资源信息
     *
     * @param logVOList   日志基本信息集合
     * @param isSystemOrg 是否系统机构账号
     */
    private void loadLogResource(Collection<LogVO> logVOList, boolean isSystemOrg) {
        if (StringUtils.isNotEmpty(logVOList)) {
            Set<String> trackCodes = logVOList.stream().map(LogVO::getTrackCode).collect(Collectors.toSet());
            String sqlTemplate = "select id, trackCode, resourceName, resource, actionType, triggerPoint "
                    + " from %s "
                    + "where trackCode in (:trackCodes) ";
            StringBuilder sql = new StringBuilder();
            List<String> allResourceTableNames = getAllLogTableNames(ResourceLogTableServiceImpl.DATA_LOG_TABLE_PREFIX);
            for (int i = 0; i < allResourceTableNames.size(); i++) {
                if (i > 0) {
                    sql.append(" UNION ALL ");
                }
                sql.append(String.format(sqlTemplate, allResourceTableNames.get(i)));
            }
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
            Map<String, Object> sqlParamMap = new HashMap<>();
            sqlParamMap.put("trackCodes", trackCodes);
            List<LogVO.LogResourceVO> resourceVOList = namedParameterJdbcTemplate.query(sql.toString(), sqlParamMap,
                    new BeanPropertyRowMapper<>(LogVO.LogResourceVO.class));
            if (StringUtils.isNotEmpty(resourceVOList)) {
                loadLogData(resourceVOList);
                Map<String, List<LogVO.LogResourceVO>> resourceMap = resourceVOList.stream()
                        .collect(Collectors.groupingBy(LogVO.LogResourceVO::getTrackCode));
                for (LogVO logVO : logVOList) {
                    List<LogVO.LogResourceVO> resourceList = resourceMap.get(logVO.getTrackCode());
                    if (StringUtils.isNotEmpty(resourceList)) {
                        //处理数据: 防止log_resource表有trackCode、resourceName、resource三者相同的情况，三者相同时对记录进行合并
                        Map<String, LogVO.LogResourceVO> newResourceMap = new HashMap<>();
                        for (LogVO.LogResourceVO resource : resourceList) {
                            List<LogVO.LogResourceVO.LogDataVO> logDataList = resource.getDataList();
                            String key = resource.getTrackCode() + "##" + resource.getResourceName() + "##" + resource.getResource();
                            //对triggerPoint根据权限进行脱敏处理，如果是系统账号，则不脱敏，否则需要脱敏处理
                            if (!isSystemOrg) {
                                resource.setTriggerPoint(DesensitizedUtil.password(resource.getTriggerPoint()));
                            }
                            if (newResourceMap.containsKey(key)) {
                                LogVO.LogResourceVO vo = newResourceMap.get(key);
                                if (StringUtils.isEmpty(vo.getDataList())) {
                                    vo.setDataList(logDataList);
                                } else {
                                    vo.getDataList().addAll(logDataList);
                                }
                            } else {
                                resource.setId(null);
                                newResourceMap.put(key, resource);
                            }
                        }
                        List<LogVO.LogResourceVO> list = new ArrayList<>(newResourceMap.values());
                        list.sort(Comparator.comparing(LogVO.LogResourceVO::getTrackCode)
                                .thenComparing(LogVO.LogResourceVO::getResourceName)
                                .thenComparing(LogVO.LogResourceVO::getResource)
                                .thenComparing(LogVO.LogResourceVO::getActionType));
                        logVO.setResourceList(list);

                        //对triggerPoint根据权限进行脱敏处理，如果是系统账号，则不脱敏，否则需要脱敏处理
                        if (!isSystemOrg) {
                            logVO.setTriggerPoint(DesensitizedUtil.password(logVO.getTriggerPoint()));
                        }
                    }
                }
            }
        }
    }

    /**
     * 加载日志数据
     *
     * @param resources 日志资源集合
     */
    private void loadLogData(Collection<LogVO.LogResourceVO> resources) {
        if (StringUtils.isNotEmpty(resources)) {
            Set<String> resourceIds = resources.stream().map(LogVO.LogResourceVO::getId).collect(Collectors.toSet());
            String sqlTemplate = "select resourceId, tabName, sectionName, itemName, beforeValue, afterValue "
                    + " from %s "
                    + "where resourceId in (:resourceIds) ";
            StringBuilder sql = new StringBuilder();
            List<String> allResourceTableNames = getAllLogTableNames(DataLogTableServiceImpl.DATA_LOG_TABLE_PREFIX);
            for (int i = 0; i < allResourceTableNames.size(); i++) {
                if (i > 0) {
                    sql.append(" UNION ALL ");
                }
                sql.append(String.format(sqlTemplate, allResourceTableNames.get(i)));
            }
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
            Map<String, Object> sqlParamMap = new HashMap<>();
            sqlParamMap.put("resourceIds", resourceIds);
            List<LogVO.LogResourceVO.LogDataVO> logDataList = namedParameterJdbcTemplate.query(sql.toString(), sqlParamMap,
                    new BeanPropertyRowMapper<>(LogVO.LogResourceVO.LogDataVO.class));
            if (StringUtils.isNotEmpty(logDataList)) {
                Map<String, List<LogVO.LogResourceVO.LogDataVO>> logDataMap = logDataList.stream()
                        .collect(Collectors.groupingBy(LogVO.LogResourceVO.LogDataVO::getResourceId));
                for (LogVO.LogResourceVO resource : resources) {
                    List<LogVO.LogResourceVO.LogDataVO> dataList = logDataMap.get(resource.getId());
                    if (StringUtils.isNotEmpty(dataList)) {
                        dataList.sort(Comparator.comparing(LogVO.LogResourceVO.LogDataVO::getTabName)
                                .thenComparing(LogVO.LogResourceVO.LogDataVO::getSectionName)
                                .thenComparing(LogVO.LogResourceVO.LogDataVO::getItemName));
                        resource.setDataList(dataList);
                    }
                }
            }
        }
    }

    /**
     * 获取所有日志表名列表
     *
     * @param tablePrefix 表名前缀
     * @return 日志表名列表
     */
    private List<String> getAllLogTableNames(String tablePrefix) {
        List<String> resultList = new ArrayList<>();
        EntityManagerFactory entityManagerFactory = entityManager.getEntityManagerFactory();
        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        Session session = sessionFactory.openSession();
        if (session != null) {
            try {
                resultList = session.doReturningWork(connection -> {
                    List<String> tableList = new ArrayList<>();
                    String userName = connection.getMetaData().getUserName();
                    try (ResultSet rs = connection.getMetaData().getTables(connection.getCatalog(), userName, null, null)) {
                        while (rs.next()) {
                            String tableName = rs.getString("TABLE_NAME");
                            if (tableName.toUpperCase().startsWith(tablePrefix.toUpperCase())) {
                                tableList.add(tableName);
                            }
                        }
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                    return tableList;
                });
            } catch (HibernateException e) {
                throw new RuntimeException(e);
            } finally {
                session.close();
            }
        }
        return resultList;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

}