package com.sinoyd.base.service.impl;

import com.sinoyd.base.aspects.BizLogAspect;
import com.sinoyd.base.service.ILogTableService;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 资源日志表业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/18
 */
@Service("resourceLogTableService")
@Slf4j
@SuppressWarnings("unchecked")
public class ResourceLogTableServiceImpl extends AbsLogTableServiceImpl implements ILogTableService {

    public final static String DATA_LOG_TABLE_PREFIX = "TB_BIZ_Log_Resource_";

    @Override
    @Transactional
    public void insertLogs(String mainThreadName, SecurityContext securityContext, Object... params) {
        SecurityContextHolder.setContext(securityContext);
        List<Object[]> sqlParams = new ArrayList<>();

        Object resourceName = params[0];
        Collection<Object> resources = (Collection<Object>) params[1];
        Object actionType = params[2];
        Object triggerPoint = params[3];

        Object trackCode = BizLogAspect.threadCache.get(mainThreadName).get(BizLogAspect.THREAD_CONTENT_KEY);
        for (Object resource : resources) {
            String id = UUIDHelper.newId();
            Object[] objects = new Object[]{id, trackCode, resourceName, resource, actionType, triggerPoint};
            sqlParams.add(objects);
            BizLogAspect.threadCache.get(mainThreadName)
                    .put(resource.toString(), id);
        }

        String tableName = DATA_LOG_TABLE_PREFIX + loadTableSuffix();
        boolean isTableExists = isLogTableExists(mainThreadName, tableName);
        if (!isTableExists) {
            createLogTable(mainThreadName, tableName);
        }
        insertData(tableName, sqlParams);
    }

    @Override
    public String createTableSql(String tableName) {
        return "CREATE TABLE " + tableName + "(" +
                " id VARCHAR(50) NOT NULL   COMMENT '主键' ," +
                " trackCode VARCHAR(50) NOT NULL   COMMENT '追踪码' ," +
                " resourceName VARCHAR(100) NOT NULL   COMMENT '资源名称' ," +
                " resource VARCHAR(100) NOT NULL   COMMENT '资源' ," +
                " actionType VARCHAR(20) NOT NULL   COMMENT '动作类型，新增/修改/删除', " +
                " triggerPoint VARCHAR(900)    COMMENT '触发点，一般存储日志触发的方法的类+方法名称' ," +
                " PRIMARY KEY (id)" +
                ") COMMENT = '资源日志表';";
    }

    /**
     * 将数据插入到日志表中
     *
     * @param tableName 表名
     * @param sqlParams 要插入的数据列表，每个元素为一个Object数组，对应日志表中的一列
     */
    private void insertData(String tableName, List<Object[]> sqlParams) {
        log.info("insert data to table: {}, thread name is {}", tableName, Thread.currentThread().getName());
        String insertLogSql = String.format("INSERT INTO %s(id, trackCode, resourceName, resource, actionType, triggerPoint) " +
                "values(?,?,?,?,?,?) ", tableName);
        jdbcTemplate.batchUpdate(insertLogSql, sqlParams);
    }
}