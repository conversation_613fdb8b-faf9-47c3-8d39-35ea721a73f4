package com.sinoyd.base.vo;

import com.sinoyd.base.annotations.LogField;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.Set;

/**
 * 字段转换VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/20
 */
@Data
@Accessors(chain = true)
public class FieldConvertVO {

    /**
     * 字段转换类名称，这里必须是spring托管的bean的名称
     */
    private String convertBean;

    /**
     * 字段转换方法名称
     */
    private String convertMethod;

    /**
     * 是否转换
     */
    private Boolean isConvert = false;

    /**
     * 转换方法参数集合
     */
    private Set<Object> methodParams = new HashSet<>();

    /**
     * 默认构造器
     */
    public FieldConvertVO() {
        super();
    }

    /**
     * FieldConvertVO构造函数
     *
     * @param logField LogField对象
     */
    public FieldConvertVO(LogField logField) {
        this();
        this.convertBean = logField.convertBean();
        this.convertMethod = logField.convertMethod();
        this.isConvert = StringUtils.isNotEmpty(this.convertBean)
                && StringUtils.isNotEmpty(this.convertMethod);
    }


    /**
     * 获取转换关键字
     *
     * @param vo FieldConvertVO对象
     * @return 转换关键字，格式为：{convertBean}##{convertMethod}
     */
    public static String getConvertKey(FieldConvertVO vo) {
        return vo.getConvertBean() + "##" + vo.getConvertMethod();
    }
}