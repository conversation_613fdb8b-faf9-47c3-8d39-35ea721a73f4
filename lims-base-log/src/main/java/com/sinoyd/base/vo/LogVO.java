package com.sinoyd.base.vo;

import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 日志模型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/22
 */
@Data
@Accessors(chain = true)
public class LogVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 业务类型，比如报价单、项目、方案等，枚举管理
     */
    private String businessType;

    /**
     * 业务标识(一般用id标识)
     */
    private String businessId;

    /**
     * 操作类型，比如新增点位、修改定位等，业务自己定义，建议业务上使用统一枚举
     */
    private String operateType;

    /**
     * 追踪码
     */
    private String trackCode;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 下一步操作人id
     */
    private String nextOperatorId;

    /**
     * 下一步操作人名称
     */
    private String nextOperatorName;

    /**
     * 意见
     */
    private String opinion;

    /**
     * 触发点
     */
    private String triggerPoint;

    /**
     * 所属机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 日志资源集合
     */
    private List<LogResourceVO> resourceList;

    /**
     * 获取内容
     *
     * @return 返回拼接后的字符串
     */
    public String getContent() {
        if (StringUtils.isNotEmpty(this.opinion)) {
            content = content + ", 意见为: " + this.opinion;
        }
        if (StringUtils.isNotEmpty(this.nextOperatorName)) {
            content = content + ", 下一步操作人为: " + this.nextOperatorName;
        }
        return content;
    }

    /**
     * 日志资源VO
     */
    @Data
    public static class LogResourceVO {

        /**
         * 主键
         */
        private String id;

        /**
         * 追踪码
         */
        private String trackCode;

        /**
         * 资源名称
         */
        private String resourceName;

        /**
         * 资源，比如表id等
         */
        private String resource;

        /**
         * 动作类型，参考 {@link com.sinoyd.base.enums.EnumDataOperateType}
         */
        private String actionType;

        /**
         * 触发点
         */
        private String triggerPoint;

        /**
         * 数据日志集合
         */
        private List<LogDataVO> dataList;

        /**
         * 日志数据VO
         */
        @Data
        public static class LogDataVO {

            /**
             * 日志资源表id
             */
            private String resourceId;

            /**
             * 页面选项卡名称
             */
            private String tabName = "";

            /**
             * 页面区域名称
             */
            private String sectionName = "";

            /**
             * 页面数据项名称
             */
            private String itemName;

            /**
             * 修改前的值
             */
            private String beforeValue;

            /**
             * 修改后的值
             */
            private String afterValue;

        }
    }

}