<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sinoyd.lims</groupId>
        <artifactId>lims-base</artifactId>
        <version>${base.version}-MS-SNAPSHOT</version>
    </parent>

    <artifactId>lims-base-public</artifactId>
    <name>lims-base-public</name>
    <description>LIMS BASE模型层</description>

    <build>
        <finalName>lims-base-public</finalName>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.lims</groupId>
            <artifactId>lims-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sinoyd.frame</groupId>
            <artifactId>frame-arch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-sqlserver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-workflow-activiti-client</artifactId>
            <version>5.2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-report-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-report-word</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
