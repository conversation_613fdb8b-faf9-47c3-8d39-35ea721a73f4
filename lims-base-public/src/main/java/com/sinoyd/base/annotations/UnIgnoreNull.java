package com.sinoyd.base.annotations;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 用于标记更新时是否忽略空值，主要解决Integer类型为null时，页面清空导致不更新的问题
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/29
 */
@Documented
@Target({FIELD})
@Retention(RUNTIME)
public @interface UnIgnoreNull {

    /**
     * 是否忽略null属性，默认不忽略，不加该注解是忽略
     */
    boolean value() default true;
}
