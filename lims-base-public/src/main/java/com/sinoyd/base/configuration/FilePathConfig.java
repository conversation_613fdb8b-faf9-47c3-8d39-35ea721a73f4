package com.sinoyd.base.configuration;

import com.sinoyd.base.vo.PathConfigVO;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * 附件上传XML文件配置
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@XmlRootElement(name = "configs")
@XmlAccessorType(XmlAccessType.FIELD)
public class FilePathConfig {

    /**
     * 所有xml文件中的内容
     */
    @XmlElement(name = "config")
    private List<PathConfigVO> allConfigs;

    @XmlTransient
    public List<PathConfigVO> getAllConfigs() {
        return allConfigs;
    }

    public void setAllConfigs(List<PathConfigVO> allConfigs) {
        this.allConfigs = allConfigs;
    }
}