package com.sinoyd.base.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件配置
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Component
@ConfigurationProperties(prefix = "lims-file")
@Data
public class FilePropertyConfig {
    /**
     * 文件路径配置
     */
    private String filePath;

    /**
     * 输出路径
     */
    private String outputPath;

    /**
     * 模板名称
     */
    private String templatePath;

    /**
     * 允许上传的文件类型
     */
    private String fileSuffix;
}
