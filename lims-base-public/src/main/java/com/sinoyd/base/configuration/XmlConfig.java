package com.sinoyd.base.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

/**
 * 通用XML映射配置类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Configuration
public class XmlConfig {

    @Bean
    public FilePathConfig getFilePathConfigVO() {
        try {
            JAXBContext ctx = JAXBContext.newInstance(FilePathConfig.class);
            Unmarshaller unmarshaller = ctx.createUnmarshaller();
            FilePathConfig vo = (FilePathConfig) unmarshaller.unmarshal(XmlConfig.class.getResourceAsStream("/filePathConfig.xml"));
            return vo;
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }
    }
}