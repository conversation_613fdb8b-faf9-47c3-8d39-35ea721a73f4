package com.sinoyd.base.constants;

/**
 * 字典类型常量定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/2
 */
public interface ICodeTypeConstants {

    /**
     * 费用单位
     */
    String FEE_DIMENSION = "lims_eqms_feeDimension";

    /**
     * 项目编号类型字典编码
     */
    String PROJECT_SERIAL_NO_TYPE = "xm";

    /**
     * 报告编号类型字典编码
     */
    String REPORT_SERIAL_NO_TYPE = "bg";

    /**
     * 合同编号类型字典编码
     */
    String CONTRACT_SERIAL_NO_TYPE = "contract";

    /**
     * 采样单编号类型字典编码
     */
    String SAMPLING_FORM_NO_TYPE = "cyd";

    /**
     * 送样单编号类型字典编码
     */
    String SEND_FORM_NO_TYPE = "syd";

    /**
     * 样品编号类型字典编码
     */
    String SAMPLE_NO_TYPE = "yp";

    /**
     * 质控样品编号类型字典编码
     */
    String QC_SAMPLE_NO_TYPE = "zky";

    /**
     * 检测单编号类型字典编码
     */
    String INSPECT_FORM_NO_TYPE = "jcd";

    /**
     * 量纲类型
     */
    String DIMENSION_TYPE = "lims_rms_dimensionType";

    /**
     * 电子报告技术说明备注
     */
    String TECHNICAL_REMARK = "lims_edbp_electronicReport_technicalRemark";

}
