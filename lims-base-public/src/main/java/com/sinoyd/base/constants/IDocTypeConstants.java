package com.sinoyd.base.constants;

/**
 * 文档类型常量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/05
 */
public interface IDocTypeConstants {

    /**
     * 项目登记附件
     */
    String PROJECT_ATTACHMENT = "LIMS60_ProjectAttachment";

    /**
     * 点位示意图
     */
    String POINT_PICTURE = "LIMS_EDBP_PointPic";

    /**
     * 原始记录单附件
     */
    String INSPECT_FORM_RECORD = "LIMS_EDBP_InspectFormRecord";

    /**
     * 采样记录单附件
     */
    String SAMPLING_FORM_RECORD = "LIMS_EDBP_SamplingRecord";

    /**
     * 采样单附件
     */
    String SAMPLING_ATTACHMENT = "LIMS60_SamplingAttachment";

    /**
     * 采样其他附件
     */
    String SAMPLING_OTHER = "LIMS60_SamplingOther";

    /**
     * 采样点位附件
     */
    String SAMPLING_FOLDER = "LIMS60_SamplingFolder";

    /**
     * 报告其他附件
     */
    String REPORT_ATTACHMENT = "LIMS_EDBP_ReportAttachment";

    /**
     * 电子报告附件
     */
    String ELECTRONIC_REPORT = "LIMS_EDBP_ElectronicReport";

    /**
     * 副本件
     */
    String ELECTRONIC_COPY = "LIMS_EDBP_ElectronicReport_Copy";

    /**
     * 人员签名图片文档类型
     */
    String PERSON_SIGNATURE = "LIMS_LIM_PersonSignature";

    /**
     * CMA签章
     */
    String SEAL_CMA = "LIMS_LIM_SYSTEM_CMA";

    /**
     * CNAS签章
     */
    String SEAL_CNAS = "LIMS_LIM_SYSTEM_CNAS";

    /**
     * 检测检验专用签章
     */
    String SEAL_INSPECT = "LIMS_LIM_SYSTEM_InspectSeal";

    /**
     * 检测单仪器解析图谱附件
     */
    String INSTRUMENT_PARSE = "LIMS_EDBP_InstrumentParseFiles";
}
