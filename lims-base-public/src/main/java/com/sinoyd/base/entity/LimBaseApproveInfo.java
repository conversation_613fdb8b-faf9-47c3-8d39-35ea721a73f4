package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 通用审核信息实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/21
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class LimBaseApproveInfo extends LimsBaseEntity {

    public LimBaseApproveInfo(){
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 业务类型，枚举管理，比如：项目、方案、采样单、检测单等
     */
    private String businessType;

    /**
     * 关联业务id
     */
    private String businessId;

    /**
     * 审核步骤，1: 一审；2：二审....
     */
    private Integer approveStep;

    /**
     * 审核人员id
     */
    private String auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 是否最新待处理步骤，比如三步审核，走完第一步审核，则第二部审核时最新待处理步骤
     */
    private Boolean isNewestStep;

    /**
     * 审核结果，0：未通过；1：通过; NULL: 未审
     */
    private Integer auditorResult;

    /**
     * 审核意见
     */
    private String opinion;

    /**
     * 假删 标识
     */
    private Boolean isDeleted = Boolean.FALSE;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;
}