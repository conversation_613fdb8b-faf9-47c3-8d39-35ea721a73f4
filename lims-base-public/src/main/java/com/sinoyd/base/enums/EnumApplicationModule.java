package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应用模块枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/17
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumApplicationModule {

    通用模块("common", "com.sinoyd.common.algorithm.comparison.enums"),

    基础模块("base", "com.sinoyd.base.enums"),

    资源配置模块("rms", "com.sinoyd.lims.rms.enums"),

    质控模块("qcms", "com.sinoyd.lims.qcms.enums"),

    报价模块("eqms", "com.sinoyd.lims.eqms.enums"),

    报表模块("report", "com.sinoyd.report.enums"),

    业务模块("edbp", "com.sinoyd.lims.edbp.enums");

    private final String moduleName;

    private final String enumPackage;

    /**
     * 根据模块值获取枚举包名
     *
     * @param moduleName 模块值
     * @return 枚举
     */
    public static EnumApplicationModule getEnumItem(String moduleName) {
        for (EnumApplicationModule e : EnumApplicationModule.values()) {
            if (e.getModuleName().equals(moduleName)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的应用模块枚举值[%s]", moduleName));
    }
}
