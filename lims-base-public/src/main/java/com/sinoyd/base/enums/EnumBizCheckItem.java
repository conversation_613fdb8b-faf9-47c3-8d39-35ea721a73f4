package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务检查项枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/04/08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumBizCheckItem {

    项目登记提交("projectSubmit", "projectSubmitCheckServiceImpl"),
    采样单提交("samplingFormSubmit", "samplingFormSubmitCheckServiceImpl"),
    分析数据录入提交("labDataInputSubmit", "labDataInputSubmitCheckServiceImpl"),
    分析数据录入审核("labDataInputApprove", "labDataInputApproveCheckServiceImpl"),
    质控任务完成考核("qualityManageComplete", "qualityManageCompleteCheckServiceImpl"),
    订单合同提交("quotationSubmit", "quotationSubmitCheckServiceImpl");


    /**
     * 业务检查类型
     */
    private final String value;

    /**
     * 检查项实现类
     */
    private final String checkClassName;


    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumBizCheckItem getEnumItem(String value) {
        for (EnumBizCheckItem em : EnumBizCheckItem.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的业务检查项枚举枚举值[%s]", value));
    }
}
