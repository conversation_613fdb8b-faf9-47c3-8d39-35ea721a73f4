package com.sinoyd.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumBusinessType {

    报价单,

    项目,

    方案,

    样品,

    采样单,

    送样单,

    检测单,

    分析数据,

    报告;

    private final String value = name();
}
