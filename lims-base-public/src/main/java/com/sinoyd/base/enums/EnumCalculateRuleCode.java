package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 修约规则
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumCalculateRuleCode {

    先修约再计算("reviseCalculate"),

    先计算再修约("calculateRevise");

    private final String value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumCalculateRuleCode getEnumItem(String value) {
        for (EnumCalculateRuleCode em : EnumCalculateRuleCode.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的报告类型枚举值[%s]", value));
    }
}
