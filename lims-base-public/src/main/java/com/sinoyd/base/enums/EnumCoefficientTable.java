package com.sinoyd.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 置信系数对应表
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/09/06
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum EnumCoefficientTable {

    置信五(5, "2.571"),
    置信六(6, "2.447"),
    置信七(7, "2.365"),
    置信八(8, "2.306"),
    置信九(9, "2.262"),
    置信十(10, "2.228"),
    置信十一(11, "2.201"),
    置信十二(12, "1.179"),
    置信十三(13, "2.160"),
    置信十四(14, "2.145"),
    置信十五(15, "2.131"),
    置信十六(16, "2.120");
    private int value;

    private String reValue;

    public static String EnumReValue(int value) {
        for (EnumCoefficientTable c : EnumCoefficientTable.values()) {
            if (c.value == value) {
                return c.reValue;
            }
        }
        return "";
    }
}
