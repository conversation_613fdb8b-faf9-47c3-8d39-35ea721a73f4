package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 置信系数枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/21
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumConfidenceCoefficient {

    自由度五(5, "2.571"),

    自由度六(6, "2.447"),

    自由度七(7, "2.365"),

    自由度八(8, "2.306"),

    自由度九(9, "2.262"),

    自由度十(10, "2.228"),

    自由度十一(11, "2.201"),

    自由度十二(12, "1.179"),

    自由度十三(13, "2.160"),

    自由度十四(14, "2.145"),

    自由度十五(15, "2.131"),

    自由度十六(16, "2.120");

    /**
     * 自由度
     */
    private final Integer freedomDegrees;

    /**
     * t值
     */
    private final String tValue;

    /**
     * 根据自由度获取枚举项
     *
     * @param freedomDegrees 自由度
     * @return 枚举项
     */
    public static EnumConfidenceCoefficient getEnumItem(Integer freedomDegrees) {
        for (EnumConfidenceCoefficient em : EnumConfidenceCoefficient.values()) {
            if (em.freedomDegrees.equals(freedomDegrees)) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的置信系数枚举值[%d]", freedomDegrees));
    }
}
