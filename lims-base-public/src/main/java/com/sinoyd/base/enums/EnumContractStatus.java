package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同状态枚举类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/24
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumContractStatus {

    未签订(0, "info", "color: var(--el-color-info)"),

    已签订(1, "success", "color: var(--el-color-success)");

    private final Integer value;

    /**
     * tag， 前端使用
     */
    private final String tag;

    /**
     * 样式，前端使用
     */
    private final String style;

    public static String getNameByStatus(Integer value) {
        for (EnumContractStatus c : EnumContractStatus.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        throw new BaseException(String.format("不合法的枚举值[%s]", value.toString()));
    }
}
