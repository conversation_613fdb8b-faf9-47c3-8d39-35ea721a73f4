package com.sinoyd.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 控件类型
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumControlType {

    输入框(1),
    日期控件(2),
    数字输入框(3),
    选择器(4),
    单选框(5),
    多选框(6),
    日期时间控件(7),
    文本域(8),
    时间控件(9),
    开关控件(10),
    树选择器(11),
    日期时间控件不含秒(12),
    时间控件不含秒(13);


    /**
     * 枚举值
     */
    private Integer value;

    /**
     * 根据常量的Int值返回常量名称
     *
     * @param value 枚举值
     * @return 名称
     */
    public static String getName(Integer value) {
        for (EnumControlType controlType : EnumControlType.values()) {
            if (value.equals(controlType.getValue())) {
                return controlType.name();
            }
        }
        return "";
    }

}
