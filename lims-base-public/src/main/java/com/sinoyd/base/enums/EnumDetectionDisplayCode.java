package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 小于检出限显示
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumDetectionDisplayCode {

    ND("ND"),
    小于DL("小于DL"),
    检出限L("检出限L"),
    小于检出限("小于检出限");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 根据名称获取值
     *
     * @param name 名称
     * @return 直接返回名称
     */
    public static EnumDetectionDisplayCode getValueByName(String name) {
        for (EnumDetectionDisplayCode displayCode : EnumDetectionDisplayCode.values()) {
            if (displayCode.name().equals(name)) {
                return displayCode;
            }
        }
        throw new BaseException(String.format("非法的小于检出限显示类型枚举名称[%s]", name));
    }
}
