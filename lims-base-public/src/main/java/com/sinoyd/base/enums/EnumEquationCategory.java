package com.sinoyd.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 方程种类
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumEquationCategory {

    样品出证(0),

    空白出证(1),

    校核出证(3),

    标样出证(4),

    测得量出证(5),

    其他(6);

    /**
     * 枚举值
     */
    private Integer value;

    /**
     * 根据枚举值获取枚举名称
     *
     * @param value 枚举值
     * @return 枚举名称
     */
    public static String getName(Integer value) {
        for (EnumEquationCategory equationCategory : EnumEquationCategory.values()) {
            if (value.equals(equationCategory.getValue())) {
                return equationCategory.name();
            }
        }
        return "";
    }


}
