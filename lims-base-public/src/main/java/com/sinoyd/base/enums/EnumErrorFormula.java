package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 误差公式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/20
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumErrorFormula {

    公式1("([a]-[b])/b", "(a-b)/b"),

    公式2("([a]-[b])/([a]+[b])", "(a-b)/(a+b)"),

    公式3("([a]-[b])/(([a]+[b])/2)", "(a-b)/((a+b)/2)");

    /**
     * 公式(计算时使用)
     */
    private final String value;

    /**
     * 显示名称
     */
    private final String label;

    /**
     * 根据显示名称返回枚举项
     *
     * @param label 显示名称
     * @return 返回枚举项
     */
    public static EnumErrorFormula getEnumItem(String label) {
        for (EnumErrorFormula c : EnumErrorFormula.values()) {
            if (c.getLabel().equals(label)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的误差公式枚举值[%s]", label));
    }
}
