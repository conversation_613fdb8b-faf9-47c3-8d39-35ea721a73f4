package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评价标准结果枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/11
 */
@Getter
@AllArgsConstructor
public enum EnumEvaluateResult {

    未评价(0,"primary", "color: var(--el-color-primary)"),

    合格(1,"success", "color: var(--el-color-success)"),

    不合格(2,"danger", "color: var(--el-color-danger)"),

    不评价(3,"info", "color: var(--el-color-info)");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * tag， 前端使用
     */
    private final String tag;

    /**
     * 样式，前端使用
     */
    private final String style;


    /**
     * 根据枚举值返回枚举项
     *
     * @param value 枚举值
     * @return 返回枚举项
     */
    public static EnumEvaluateResult getEnumItem(Integer value) {
        for (EnumEvaluateResult c : EnumEvaluateResult.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的评价结果枚举值[%s]", value));
    }
}
