package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评价方式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/20
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumEvaluationWay {

    限值判定(1, "judgeLimitParams"),

    小于检出限(2, "detectionLimitParams"),

    回收率(3, "recoveryParams"),

    相对偏差(4, "relativeDeviationParams"),

    相对误差(5, "relativeErrorParams"),

    绝对偏差(6, "absoluteDeviationParams"),

    穿透率(7, "penetrationRateParams"),

    小于测定下限(8, "testLimitParams"),

    范围判定(9, "rangeJudgeParams"),

    相对准确度(10, "relativeAccuracyParams"),

    绝对误差(11, "absoluteErrorParams"),

    标曲A0波动范围(12, "curveRangeParams");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 策略类名
     */
    private final String beanName;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumEvaluationWay getEnumItem(Integer value) {
        for (EnumEvaluationWay em : EnumEvaluationWay.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的评价方式枚举值[%d]", value));
    }

}
