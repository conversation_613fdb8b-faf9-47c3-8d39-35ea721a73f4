package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Stream.of;

/**
 * Excel 签名类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/27
 */
@Getter
@AllArgsConstructor
public enum EnumExcelSignType {

    采样人员(10, of("采样人").collect(toList())),

    分析人员(20, of("分析人", "分析人员", "测试人员").collect(toList())),

    审核人员(30, of("审核人", "审核者").collect(toList())),

    复核人员(40, of("校核人", "复核人", "复核者").collect(toList())),

    采样日期(50, of("采样日期").collect(toList())),

    分析日期(60, of("分析日期").collect(toList())),

    审核日期(70, of("审核日期").collect(toList())),

    复核日期(80, of("复核日期").collect(toList())),

    采样人员_日期(90, of("采样人员/日期").collect(toList())),

    分析人员_日期(100, of("分析人员/日期").collect(toList())),

    审核人员_日期(110, of("审核人员/日期").collect(toList())),

    复核人员_日期(120, of("复核人员/日期").collect(toList()));


    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 单元格中需要签名的前单元格值标识符 <br/>
     * 一个类型的签名可对应多个标识符 <br/>
     * 例如：采样人员对应采样人、采样员等
     */
    private final List<String> marks;

    /**
     * 根据枚举值获取枚举项
     *
     * @param value 枚举值
     * @return 枚举项
     */
    public static EnumExcelSignType getEnumItem(Integer value) {
        for (EnumExcelSignType e : EnumExcelSignType.values()) {
            if (value.equals(e.getValue())) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的Excel签名类型枚举值[%d]", value));
    }

    /**
     * 是否为图片签名
     *
     * @param value 枚举值
     * @return 是否为图片签名
     */
    public static Boolean isImageSign(Integer value) {
        EnumExcelSignType e = getEnumItem(value);
        switch (e) {
            case 采样人员:
            case 分析人员:
            case 审核人员:
            case 复核人员:
                return true;

            default:
                return false;
        }
    }


}
