package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重要程度枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/7
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumImportanceDegree {

    一般(0),

    重要(1);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumImportanceDegree getEnumItem(Integer value) {
        for (EnumImportanceDegree em : EnumImportanceDegree.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的重要程度枚举值[%d]", value));
    }
}
