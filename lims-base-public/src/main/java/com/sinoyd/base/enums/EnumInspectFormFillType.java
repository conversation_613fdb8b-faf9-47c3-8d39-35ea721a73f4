package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 原始记录单配置录入方式
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumInspectFormFillType {

    普通录入(0, "inspectFormParams"),

    分析项目录入(1, "inspectFormItemParams"),

    样品录入(2, "inspectFormSampleParams");


    /**
     * 枚举值
     */
    private final Integer value;


    /**
     * 枚举策略类名
     */
    private final String beanName;

    /**
     * 根据枚举值获取枚举项
     *
     * @param value 枚举值
     * @return 枚举项
     */
    public static EnumInspectFormFillType getEnumItem(Integer value) {
        for (EnumInspectFormFillType c : EnumInspectFormFillType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的原始记录单配置录入方式枚举值[%d]", value));
    }

}
