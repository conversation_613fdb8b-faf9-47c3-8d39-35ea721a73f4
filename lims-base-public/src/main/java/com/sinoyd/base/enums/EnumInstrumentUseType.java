package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仪器使用类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@Getter
@AllArgsConstructor
public enum EnumInstrumentUseType {

    采样(0),

    现场分析(1),

    实验室分析(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumInstrumentUseType getEnumItem(Integer value) {
        for (EnumInstrumentUseType em : EnumInstrumentUseType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的仪器使用类型枚举值[%d]", value));
    }
}
