package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumLogType {

    流程(1),

    数据(2);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumLogType getEnumItem(Integer value) {
        for (EnumLogType em : EnumLogType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的日志类型枚举值[%d]", value));
    }
}
