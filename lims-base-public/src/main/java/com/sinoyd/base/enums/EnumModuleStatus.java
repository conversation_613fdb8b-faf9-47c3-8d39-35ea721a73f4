package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模块状态枚举类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/6
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumModuleStatus {

    全部(-1),

    未完成(0),

    已完成(1);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumModuleStatus getEnumItem(Integer value) {
        for (EnumModuleStatus em : EnumModuleStatus.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的模块状态枚举值[%d]", value));
    }
}
