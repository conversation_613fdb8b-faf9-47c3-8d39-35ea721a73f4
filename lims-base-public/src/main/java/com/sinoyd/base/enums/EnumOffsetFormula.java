package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 偏差公式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/17
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumOffsetFormula {

    A("Abs([a]-[b])/[b]", "|a-b|/b"),

    B("Abs([a]-[b])/([a]+[b])", "|a-b|/(a+b)"),

    C("Abs([a]-[b])/([a]+[b])/2", "|a-b|/(a+b)/2");

    /**
     * 公式
     */
    private final String value;

    /**
     * 显示名称
     */
    private final String label;

    /**
     * 根据显示名称返回枚举项
     *
     * @param label 名称
     * @return 返回枚举项
     */
    public static EnumOffsetFormula getEnumItem(String label) {
        for (EnumOffsetFormula c : EnumOffsetFormula.values()) {
            if (c.getLabel().equals(label)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的偏差公式枚举值[%s]", label));
    }
}
