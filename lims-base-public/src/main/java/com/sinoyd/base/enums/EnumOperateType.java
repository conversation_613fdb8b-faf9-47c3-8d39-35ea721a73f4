package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumOperateType {

    新增(1),

    修改(2),

    删除(3),

    提交(4),

    复核(5),

    审核(6),

    退回(7),

    复制(8),

    终止(9);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumOperateType getEnumItem(Integer value) {
        for (EnumOperateType em : EnumOperateType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的操作类型枚举值[%d]", value));
    }
}
