package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 参数种类枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumParamCategory {

    样品参数(1),

    数据参数(2),

    表头参数(3),

    公式参数(4);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取对应的枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumParamCategory getEnumItem(Integer value) {
        for (EnumParamCategory c : EnumParamCategory.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的参数种类枚举值[%d]", value));
    }
}
