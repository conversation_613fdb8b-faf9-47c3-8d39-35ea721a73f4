package com.sinoyd.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 参数类型
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumParamsType {

    公共参数(1),
    样品参数(2),
    分组参数(3),
    点位参数(4);

    private Integer value;

    public static String EnumParamsType(Integer value) {
        for (EnumParamsType c : EnumParamsType.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        return "";
    }
}
