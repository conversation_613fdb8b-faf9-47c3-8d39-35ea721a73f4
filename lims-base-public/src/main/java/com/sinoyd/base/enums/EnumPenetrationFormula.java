package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 穿透公式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumPenetrationFormula {

    A("[B]/[A]*100 ", "[B]/[A]*100 "),

    B(" [B]/([A]+[B])*100", " [B]/([A]+[B])*100");

    /**
     * 公式
     */
    private final String value;

    /**
     * 显示名称
     */
    private final String label;

    /**
     * 根据显示名称返回枚举项
     *
     * @param label 名称
     * @return 返回枚举项
     */
    public static EnumPenetrationFormula getEnumItem(String label) {
        for (EnumPenetrationFormula c : EnumPenetrationFormula.values()) {
            if (c.getLabel().equals(label)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的穿透公式枚举值[%s]", label));
    }
}
