package com.sinoyd.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人员证书状态枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/18
 */
@Getter
@AllArgsConstructor
public enum EnumPersonCertStatus {

    有证(0, "success", "color: var(--el-color-success)"),

    无证(1, "info", "color: var(--el-color-info)"),

    过期(2, "danger", "color: var(--el-color-danger)");

    private final Integer value;

    /**
     * tag， 前端使用
     */
    private final String tag;

    /**
     * 样式，前端使用
     */
    private final String style;

}
