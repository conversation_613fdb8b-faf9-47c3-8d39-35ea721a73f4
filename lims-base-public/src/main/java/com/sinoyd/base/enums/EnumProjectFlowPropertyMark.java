package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目流程性质标志枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/4
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumProjectFlowPropertyMark {

    采样类("CY", "copyCyProjectServiceImpl"),

    送样类("SY", "copySyProjectServiceImpl"),

    应急类("YJ", "copyYjProjectServiceImpl"),

    验收类("YS", "copyYsProjectServiceImpl"),

    全部分包类("FB", "copyFbProjectServiceImpl"),

    质控考核类("ZK", "copyZkProjectServiceImpl");


    private final String value;

    private final String copyProjectServiceName;

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumProjectFlowPropertyMark getEnumItem(String value) {
        for (EnumProjectFlowPropertyMark c : EnumProjectFlowPropertyMark.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的项目流程性质标志枚举值[%s]", value));
    }
}
