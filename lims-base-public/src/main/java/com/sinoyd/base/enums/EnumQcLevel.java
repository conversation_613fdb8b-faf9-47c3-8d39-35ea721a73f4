package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控等级枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/17
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumQcLevel {

    外部质控(1),

    内部质控(2);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumQcLevel getEnumItem(Integer value) {
        for (EnumQcLevel c : EnumQcLevel.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的质控等级枚举值[%d]", value));
    }
}
