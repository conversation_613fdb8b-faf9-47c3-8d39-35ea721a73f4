package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控限值检查项枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/9
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumQcLimitCheckItem {

    出证结果(1),

    其他(0);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumQcLimitCheckItem getEnumItem(Integer value) {
        for (EnumQcLimitCheckItem c : EnumQcLimitCheckItem.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的质控限值检查项枚举值[%d]", value));
    }
}
