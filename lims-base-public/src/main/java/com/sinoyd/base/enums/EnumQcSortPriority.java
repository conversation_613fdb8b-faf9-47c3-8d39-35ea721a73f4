package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控样排序优先级枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/6
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumQcSortPriority {

    样品排序优先(1),

    分析项目排序优先(2);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumQcSortPriority getEnumItem(Integer value) {
        for (EnumQcSortPriority c : EnumQcSortPriority.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的质控样排序优先级枚举值[%d]", value));
    }
}
