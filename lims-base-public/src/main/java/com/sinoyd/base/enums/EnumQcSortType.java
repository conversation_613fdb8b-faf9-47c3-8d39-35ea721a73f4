package com.sinoyd.base.enums;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控信息排序类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/17
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumQcSortType {

    未配置(0, 0, 0),

    前置排序(1, 10, 20),

    后置排序(2, 40, 20),

    紧前原样排序(3, 20, 10),

    紧后原样排序(4, 20, 30),

    按样品编号排序(5, 20, 20);

    private final Integer value;

    /**
     * 前置排序序号值、用于【前置类型排序值-同类型下的质控样排序值-样品编号-紧跟原样前、后排序值】中的【前置类型排序值】
     */
    private final Integer frontSortNum;

    /**
     * 后置排序序号值、用于【后置类型排序值-同类型下的质控样排序值-样品编号-紧跟原样前、后排序值】中的【紧跟原样前、后排序值】
     */
    private final Integer afterSortNum;


    /**
     * 获取排序编号
     * 是否跟随为false时（类型前置排序、后置排序）
     * 是否跟随为true时（类型为紧前原样排序、紧后原样排序、按样品编号排序）
     *
     * @param qcRuleSortOrder 排序规则质控排序值
     * @param sampleCode      样品编号
     * @param isFollow        是否为跟随排序
     * @return 排序编号
     */
    public String loadSortNum(String qcRuleSortOrder, String sampleCode, boolean isFollow) {
        if (isFollow) {
            return String.format("%s_%s_%s_%s", this.frontSortNum, sampleCode, this.afterSortNum, qcRuleSortOrder);
        } else {
            return String.format("%s_%s_%s_%s", this.frontSortNum, qcRuleSortOrder, sampleCode, this.afterSortNum);
        }
    }

    /**
     * 获取排序编号（未配置）
     *
     * @param sampleCode 样品编号
     * @return 排序编号
     */
    public String loadSortNum(String sampleCode) {
        return loadSortNum(IBaseConstants.EmptyValueDisplay.INTEGER_DEFAULT.toString(), sampleCode, true);
    }

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumQcSortType getEnumItem(Integer value) {
        for (EnumQcSortType c : EnumQcSortType.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的质控样排序类型枚举值[%d]", value));
    }
}
