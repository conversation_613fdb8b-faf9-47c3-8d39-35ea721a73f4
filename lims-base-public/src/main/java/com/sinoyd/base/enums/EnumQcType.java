package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控类别枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/9
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumQcType {

    空白(1),

    平行(2),

    加标(3),

    标样(4),

    曲线校核(5),

    校正系数检验(6),

    原样加原样(7),

    串联(8),

    替代样(9),

    阴性对照试验(10),

    阳性对照试验(11),

    稀释液(12);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 名称
     * @return 返回枚举项
     */
    public static EnumQcType getEnumItem(Integer value) {
        for (EnumQcType c : EnumQcType.values()) {
            if (c.getValue().equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的质控样类别枚举值[%d]", value));
    }
}
