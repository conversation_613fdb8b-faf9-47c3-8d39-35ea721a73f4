package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表基础配置的报表类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Getter
@AllArgsConstructor
public enum EnumReportConfigType {

    原始记录单(1, "WorkSheet"),
    采样单(2, "Sampling"),
    报告(3, "Report"),
    报表(4, "LIMReportForm"),
    标签(5, "Label");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 类型编码
     */
    private final String typeCode;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumReportConfigType getByValue(Integer value) {
        for (EnumReportConfigType c : EnumReportConfigType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的报表类型枚举值[%d]!", value));
    }

    /**
     * 根据枚举值获取类型编码
     *
     * @param value 枚举值
     * @return 类型编码
     */
    public static String getTypeCodeByValue(Integer value) {
        for (EnumReportConfigType c : EnumReportConfigType.values()) {
            if (c.value.equals(value)) {
                return c.typeCode;
            }
        }
        throw new BaseException(String.format("非法的报表类型枚举值[%d]!", value));
    }
}
