package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/2
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumReportType {

    监_监测报告(1),

    检_检测报告(2),

    检测报告(3),

    辐射报告(4),

    测试报告(5);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumReportType getEnumItem(Integer value) {
        for (EnumReportType em : EnumReportType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的报告类型枚举值[%d]", value));
    }
}
