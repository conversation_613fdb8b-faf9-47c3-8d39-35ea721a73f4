package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计算规则
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumReviseRuleCode {

    先较检出限再修约("compareRevise"),

    先修约再较检出限("reviseCompare");

    private final String value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumReviseRuleCode getEnumItem(String value) {
        for (EnumReviseRuleCode em : EnumReviseRuleCode.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的计算规则类型枚举值[%s]", value));
    }
}
