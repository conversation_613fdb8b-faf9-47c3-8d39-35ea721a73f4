package com.sinoyd.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 样品类别枚举
 * 注：如果需要添加新的枚举值，请确认其余枚举值的使用情况，避免影响已有业务逻辑
 * 现有使用逻辑：现场采样（采样单样品列表查询）、应急采样、采样管理、样品编辑、送样样品列表查询样品时，过滤掉内部质控样
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/07
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumSampleCategory {

    原样(0),

    外部质控样(1),

    内部质控样(2);

    /**
     * 枚举值
     */
    private final Integer value;
}
