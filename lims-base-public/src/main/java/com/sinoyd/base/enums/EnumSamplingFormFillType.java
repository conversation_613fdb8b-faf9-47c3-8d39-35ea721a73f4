package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采样单单配置录入方式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/26
 */
@AllArgsConstructor
@Getter
public enum EnumSamplingFormFillType {

    常规录入(0),
    按点位录入(1);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举名称
     *
     * @param value 枚举值
     * @return 枚举名称
     */
    public static String getEnumItem(Integer value) {
        for (EnumSamplingFormFillType c : EnumSamplingFormFillType.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        throw new BaseException(String.format("非法的采样单配置录入方式枚举值[%d]", value));
    }

}
