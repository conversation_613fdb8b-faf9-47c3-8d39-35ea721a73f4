package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Stream.of;
import static com.sinoyd.base.constants.IDocTypeConstants.*;

/**
 * 电子印章类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/13
 */
@Getter
@AllArgsConstructor
public enum EnumSealSignType {

    /**
     * CMA章
     */
    CMA(10, of("cmaSeal", "cmaSealSec").collect(toList()), SEAL_CMA),

    /**
     * CNAS章
     */
    CNAS(20, of("cnasSeal").collect(toList()), SEAL_CNAS),

    /**
     * 检验检测专用章
     */
    INSPECT(30, of("inspectSeal").collect(toList()), SEAL_INSPECT);

    /**
     * 电子印章类型枚举值
     */
    private final Integer value;

    /**
     * 电子印章类型对应的书签标识
     */
    private final List<String> marks;

    /**
     * 电子印章附件类型
     */
    private final String docType;

    /**
     * 根据枚举值获取枚举项
     *
     * @param value 电子印章类型枚举值
     * @return 电子印章类型枚举项
     */
    public static EnumSealSignType getEnumItem(Integer value) {
        for (EnumSealSignType e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的电子印章类型枚举值[%d]", value));
    }
}
