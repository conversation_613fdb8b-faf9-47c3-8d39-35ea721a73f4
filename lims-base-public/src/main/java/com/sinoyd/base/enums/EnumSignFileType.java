package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文档文件类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/11
 */
@Getter
@AllArgsConstructor
public enum EnumSignFileType {

    /**
     * 采样单、原始记录单
     */
    EXCEL(1, "signatureExcel"),

    /**
     * 报告
     */
    WORD(2, "signatureWord"),

    /**
     * 电子签章
     */
    SEAL(3, "signatureSeal");

    /**
     * 文件类型枚举值
     */
    private final Integer value;

    /**
     * 电子签名策略类名称
     */
    private final String beanName;

    /**
     * 根据枚举值获取枚举项
     *
     * @param value 枚举值
     * @return 枚举项
     */
    public static EnumSignFileType getEnumItem(Integer value) {
        for (EnumSignFileType e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的文档文件类型值[%d]", value));
    }
}
