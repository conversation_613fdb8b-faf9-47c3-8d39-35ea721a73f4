package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分包类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumSubcontractType {

    不分包(-1),

    分析分包(1),

    采测分包(2);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumSubcontractType getEnumItem(Integer value) {
        for (EnumSubcontractType em : EnumSubcontractType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的分包类型枚举值[%d]", value));
    }
}
