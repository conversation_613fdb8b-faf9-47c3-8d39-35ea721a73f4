package com.sinoyd.base.enums;

import com.sinoyd.boot.common.util.DateUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

/**
 * 分表策略枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumTableStrategy {

    年("year", templateTableName -> templateTableName + "_" + DateUtil.dateToString(new Date(), DateUtil.YEAR_PATTERN)),
    月("month", templateTableName -> templateTableName + "_" + DateUtil.dateToString(new Date(), DateUtil.YEAR_MM)),
    天("day", templateTableName -> templateTableName + "_" + DateUtil.dateToString(new Date(), "yyyyMMdd"));

    private final String value;

    private final ITableName tableName;


    public interface ITableName {

        /**
         * 获取表名
         *
         * @param templateTableName 模版表名称
         * @return 表名
         */
        String getTableName(String templateTableName);
    }
}
