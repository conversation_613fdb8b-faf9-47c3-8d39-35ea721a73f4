package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 紧急程度枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/7
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumUrgentDegree {

    一般(0),

    紧急(1);

    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumUrgentDegree getEnumItem(Integer value) {
        for (EnumUrgentDegree em : EnumUrgentDegree.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的紧急程度枚举值[%d]", value));
    }
}
