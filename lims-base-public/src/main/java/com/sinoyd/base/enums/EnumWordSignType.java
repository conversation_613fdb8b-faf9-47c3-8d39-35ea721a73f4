package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Stream.of;

/**
 * 电子签章类型枚举（Word）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/08
 */
@Getter
@AllArgsConstructor
public enum EnumWordSignType {

    编制人(10,
            of("maker").collect(toList()),
            of("makerDate").collect(toList()),
            of("makerOpinion").collect(toList()),
            of("makerName").collect(toList())
    ),

    审核人(20,
            of("auditor").collect(toList()),
            of("auditorDate").collect(toList()),
            of("auditorOpinion").collect(toList()),
            of("auditorName").collect(toList())
    ),

    签发人(30,
            of("signer").collect(toList()),
            of("signerDate", "reportDate", "reportDateCn").collect(toList()),
            of("signerOpinion").collect(toList()),
            of("signerName").collect(toList())
    );

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 电子签名书签标记
     */
    private final List<String> marks;

    /**
     * 当前签名日期书签标记
     */
    private final List<String> dateMarks;

    /**
     * 签名意见
     */
    private final List<String> signRemarks;

    /**
     * 签名人姓名书签标记
     */
    private final List<String> nameMarks;


    /**
     * 根据枚举值获取枚举项
     *
     * @param value 枚举值
     * @return 枚举项
     */
    public static EnumWordSignType getEnumItem(Integer value) {
        for (EnumWordSignType c : EnumWordSignType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的电子签章类型枚举值[%d]", value));
    }

}
