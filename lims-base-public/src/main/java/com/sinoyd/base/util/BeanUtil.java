package com.sinoyd.base.util;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * java bean相关工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/7/19
 */
public class BeanUtil {

    /**
     * 将T类型的bean实例集合转换成V类型的bean实例，该方法前提是约定好只转换T和V中相同属性名称的属性值
     *
     * @param vClass V类型class实例
     * @param t      T类型的数据
     * @param <T>    T类型
     * @param <V>    V类型
     * @return V类型的数据
     */
    public static <T, V> V convertT2V(Class<V> vClass, T t) {
        V v;
        try {
            v = vClass.newInstance();
            BeanUtils.copyProperties(t, v);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return v;
    }

    /**
     * 将T类型的bean实例集合转换成V类型的bean实例，该方法前提是约定好只转换T和V中相同属性名称的属性值
     *
     * @param vClass       V类型class实例
     * @param t            T类型的数据
     * @param ignoreFields 不复制的字段
     * @param <T>          T类型
     * @param <V>          V类型
     * @return V类型的数据
     */
    public static <T, V> V convertT2V(Class<V> vClass, T t, String... ignoreFields) {
        V v;
        try {
            v = vClass.newInstance();
            BeanUtils.copyProperties(t, v, ignoreFields);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return v;
    }

    /**
     * 将T类型的bean实例集合转换成V类型的bean实例集合，该方法前提是约定好只转换T和V中相同属性名称的属性值
     *
     * @param vClass V类型class实例
     * @param tList  T类型的数据集合
     * @param <T>    T类型
     * @param <V>    V类型
     * @return V类型的数据集合
     */
    public static <T, V> List<V> convertT2V(Class<V> vClass, Collection<T> tList) {
        List<V> voList = new ArrayList<>();
        try {
            if (tList != null && tList.size() > 0) {
                for (T t : tList) {
                    V v = vClass.newInstance();
                    BeanUtils.copyProperties(t, v);
                    voList.add(v);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return voList;
    }

    /**
     * 将V类型的bean实例集合转换成T类型的bean实例，该方法前提是约定好只转换T和V中相同属性名称的属性值
     *
     * @param tClass T类型的class实例
     * @param v      V类型的数据
     * @param <T>    T类型
     * @param <V>    V类型
     * @return T类型的数据
     */
    public static <T, V> T convertV2T(Class<T> tClass, V v) {
        T t;
        try {
            t = tClass.newInstance();
            BeanUtils.copyProperties(v, t);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * 将V类型的bean实例集合转换成T类型的bean实例集合，该方法前提是约定好只转换T和V中相同属性名称的属性值
     *
     * @param tClass T类型的class实例
     * @param vList  V类型的数据集合
     * @param <T>    T类型
     * @param <V>    V类型
     * @return T类型的数据集合
     */
    public static <T, V> List<T> convertV2T(Class<T> tClass, Collection<V> vList) {
        List<T> dtoList = new ArrayList<>();
        try {
            if (vList != null && vList.size() > 0) {
                for (V v : vList) {
                    T t = tClass.newInstance();
                    BeanUtils.copyProperties(v, t);
                    dtoList.add(t);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return dtoList;
    }

    /**
     * 复制list
     *
     * @param sourceList 源list内容
     * @param tClass     实体类型
     * @param <T>        泛型
     * @return 复制后的list内容
     */
    public static <T> List<T> copyEntityList(List<T> sourceList, Class<T> tClass) {
        return copyEntityList(sourceList, tClass, false);
    }

    /**
     * 复制list
     *
     * @param sourceList 源list内容
     * @param tClass     实体类型
     * @param isWithId   是否复制id
     * @param <T>        泛型
     * @return 复制后的list内容
     */
    public static <T> List<T> copyEntityList(List<T> sourceList, Class<T> tClass, boolean isWithId) {
        List<T> targetList = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(sourceList)) {
                for (T source : sourceList) {
                    T target = tClass.newInstance();
                    if (isWithId) {
                        BeanUtils.copyProperties(source, target);
                    } else {
                        BeanUtils.copyProperties(source, target, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                    }
                    targetList.add(target);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return targetList;
    }

    /**
     * map转换为实体
     *
     * @param map       拓展字段map
     * @param beanClass 实体
     * @param <T>       泛型
     * @return 实体
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<T> beanClass) {
        try {
            String json = JsonUtil.toJson(map);
            return JsonUtil.toObject(json, beanClass);
        } catch (Exception e) {
            throw new BaseException("字典数据转换实体失败");
        }
    }

    /**
     * 对象转Map
     *
     * @param object 对象
     * @return Map<String, Object>
     */
    public static Map<String, Object> beanToMap(Object object, String... ignoreFields) {
        return beanToMap(object, false, ignoreFields);
    }

    /**
     * 对象转Map
     *
     * @param object 对象
     * @param isDefaultEmptyStrValue 数据为null时是否自动填充为空字符串
     * @return Map<String, Object>
     */
    public static Map<String, Object> beanToMap(Object object,boolean isDefaultEmptyStrValue, String... ignoreFields) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotNull(object)) {
            List<Field> fields = objectFields(object.getClass());
            for (Field field : fields) {
                if (StringUtils.isNotEmpty(Arrays.stream(ignoreFields).filter(p -> field.getName().equals(p)).collect(Collectors.toList()))) {
                    continue;
                }
                field.setAccessible(true);
                try {
                    Object val = field.get(object);
                    map.put(field.getName(), (val == null && isDefaultEmptyStrValue) ? "" : val);
                } catch (IllegalAccessException e) {
                    throw new BaseException("对象获取属性名称失败");
                }
            }
        }
        return map;
    }

    /**
     * 获取对象所有属性
     *
     * @param c 对象类
     * @return 属性列表
     */
    public static List<Field> objectFields(Class<?> c) {
        List<Field> fieldList = Arrays.stream(c.getDeclaredFields()).collect(Collectors.toList());
        if (c.getSuperclass() != null && !c.getSuperclass().equals(Object.class)) {
            fieldList.addAll(objectFields(c.getSuperclass()));
        }
        return fieldList;
    }
}