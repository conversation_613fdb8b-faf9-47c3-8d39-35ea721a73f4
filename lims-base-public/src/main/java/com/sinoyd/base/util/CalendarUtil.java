package com.sinoyd.base.util;

import com.sinoyd.boot.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;

/**
 * 日历工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/19
 */
@Slf4j
public class CalendarUtil extends DateUtil {

    /**
     * 获取一年的第一天日期
     *
     * @param year 年份
     * @return 结果
     */
    public static Date getFirstDateOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(year, Calendar.JANUARY, 1);
        return calendar.getTime();
    }

    /**
     * 获取一年的最后一天日期
     *
     * @param year 年份
     * @return 结果
     */
    public static Date getLastDateOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(year, Calendar.DECEMBER, 31);
        return calendar.getTime();
    }

}