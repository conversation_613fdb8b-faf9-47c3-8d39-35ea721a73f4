package com.sinoyd.base.util;

import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.JsonUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis工具类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Component
public class RedisCache {

    private RedisTemplate<String, String> redisTemplate;

    /**
     * 保存value
     *
     * @param key redis键
     * @param t   保存对象
     */
    public <T> void saveValue(String key, T t) {
        redisTemplate.opsForValue().set(key, JsonUtils.serialize(t));
    }

    /**
     * 保存value
     *
     * @param key         redis键
     * @param t           保存对象
     * @param cachePeriod 缓存时长(分钟)
     */
    public <T> void saveValue(String key, T t, long cachePeriod) {
        redisTemplate.opsForValue().set(key, JsonUtils.serialize(t), cachePeriod, TimeUnit.MINUTES);
    }

    /**
     * 批量保存value
     *
     * @param keyValueMap 保存map，key为redis键，value为保存对象
     */
    public <T> void batchSaveValue(Map<String, T> keyValueMap) {
        Map<String, String> saveMap = new ConcurrentHashMap<>();
        keyValueMap.forEach((k, v) -> saveMap.put(getRedisKey(k), JsonUtils.serialize(v)));
        redisTemplate.opsForValue().multiSet(saveMap);
    }

    /**
     * 获取value
     *
     * @param key   redis键
     * @param clazz 获取对象的class对象
     * @return 返回结果
     */
    public <T> T getValue(String key, Class<T> clazz) {
        String json = redisTemplate.opsForValue().get(getRedisKey(key));
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JsonUtils.deserialize(json, clazz);
    }


    /**
     * 保存list
     *
     * @param key  redis键
     * @param list 保存的对象list
     */
    public <T> void saveList(String key, List<T> list) {
        redisTemplate.opsForList().rightPush(getRedisKey(key), JsonStream.serialize(list));
    }

    /**
     * 给list中添加元素
     *
     * @param key redis key
     * @param t   添加的元素
     */
    public <T> void addListItem(String key, T t) {
        redisTemplate.opsForList().rightPush(getRedisKey(key), JsonStream.serialize(t));
    }

    /**
     * 移除list中的元素
     *
     * @param key redis键
     * @param t   要删除的数据
     */
    public <T> void removeListItem(String key, T t) {
        redisTemplate.opsForList().remove(getRedisKey(key), 1, t);
    }

    /**
     * 保存数据到set
     *
     * @param key        redis键
     * @param collection 保存对象集合
     */
    public <T> void saveSet(String key, Collection<T> collection) {
        String[] saveArray = new String[collection.size()];
        Iterator<T> iterator = collection.iterator();
        for (int i = 0; iterator.hasNext(); i++) {
            saveArray[i] = JsonUtils.serialize(iterator.next());
        }
        redisTemplate.opsForSet().add(getRedisKey(key), saveArray);
    }

    /**
     * 保存到set
     *
     * @param key  redis键
     * @param objs 保存对象
     */
    @SafeVarargs
    public final <T> void saveSet(String key, T... objs) {
        SetOperations<String, String> stringStringSetOperations = redisTemplate.opsForSet();
        for (T obj : objs) {
            stringStringSetOperations.add(getRedisKey(key), JsonUtils.serialize(obj));
        }
    }

    /**
     * 删除set中的元素
     *
     * @param key  redis键
     * @param objs 要移除的元素
     */
    @SafeVarargs
    public final <T> void removeSetItem(String key, T... objs) {
        SetOperations<String, String> stringStringSetOperations = redisTemplate.opsForSet();
        for (T obj : objs) {
            stringStringSetOperations.remove(getRedisKey(key), JsonUtils.serialize(obj));
        }
    }

    /**
     * 保存单个hash数据
     *
     * @param redisKey redis键
     * @param hashKey  hash键
     * @param t        保存对象
     */
    public <T> void saveHash(String redisKey, String hashKey, T t) {
        redisTemplate.opsForHash().put(getRedisKey(redisKey), hashKey, JsonUtils.serialize(t));
    }

    /**
     * 保存单个hash数据
     *
     * @param redisKey redis键
     * @param t        保存对象
     */
    public <T extends BaseEntity> void saveHash(String redisKey, T t) {
        redisTemplate.opsForHash().put(getRedisKey(redisKey), t.getId(), JsonUtils.serialize(t));
    }

    /**
     * 批量保存hash数据
     *
     * @param redisKey redis键
     * @param map      批量保存容器，key为hash键，value为保存对象
     */
    public <T> void saveHash(String redisKey, Map<String, T> map) {
        Map<String, String> saveMap = new ConcurrentHashMap<>();
        map.forEach((k, v) -> saveMap.put(k, JsonUtils.serialize(v)));
        redisTemplate.opsForHash().putAll(getRedisKey(redisKey), saveMap);
    }

    /**
     * 批量保存hash数据
     *
     * @param redisKey redis键
     * @param list     需要保存的数据实体列表
     */
    public <T extends BaseEntity> void saveHash(String redisKey, List<T> list) {
        Map<String, T> map = new HashMap<>();
        list.forEach(p -> {
            String id = (String) p.getId();
            map.put(id, p);
        });
        saveHash(redisKey, map);
    }

    /**
     * 获取单个hash元素
     *
     * @param key     redis键
     * @param hashKey hash键
     * @param clazz   获取元素的class对象
     * @return 返回结果
     */
    public <T> T getHash(String key, String hashKey, Class<T> clazz) {
        Object json = redisTemplate.opsForHash().get(getRedisKey(key), hashKey);
        T result = null;
        if (StringUtils.isNotNull(json)) {
            result = JsonUtils.deserialize(json.toString(), clazz);
        }
        return result;
    }

    public <T> List<T> getCollectionFromHash(String key, String hashKey, Class<T> clazz) {
        Object json = redisTemplate.opsForHash().get(getRedisKey(key), hashKey);
        List<T> result = new ArrayList<>();
        if (StringUtils.isNotNull(json)) {
            result = JsonUtils.deserializeList(json.toString(), clazz);
        }
        return result;
    }


    /**
     * 删除单个hash元素
     *
     * @param key     redis键
     * @param hashKey hash键
     */
    public void removeHashItem(String key, String hashKey) {
        redisTemplate.opsForHash().delete(getRedisKey(key), hashKey);
    }

    /**
     * 批量删除hash元素
     *
     * @param key      redis键
     * @param hashKeys hash键集合
     */
    public void batchRemoveHash(String key, Collection<String> hashKeys) {
        HashOperations<String, String, String> stringObjectObjectHashOperations = redisTemplate.opsForHash();
        for (String hashKey : hashKeys) {
            stringObjectObjectHashOperations.delete(getRedisKey(key), hashKey);
        }
    }

    /**
     * 移除数据
     *
     * @param key 需要移除数据的key
     */
    public void removeKey(String key) {
        redisTemplate.delete(getRedisKey(key));
    }

    /**
     * 批量移除数据
     *
     * @param keys 需要移除数据key集合
     */
    public void batchRemoveKey(Collection<String> keys) {
        redisTemplate.delete(batchGetKey(keys));
    }

    /**
     * 缓存某些需要联想的字段
     *
     * @param redisKey   redis key
     * @param fieldKey   字段key
     * @param fieldValue 字段值
     */
    public void saveCacheField(String redisKey, String fieldKey, String fieldValue) {
        if (StringUtils.isNotEmpty(fieldKey) && StringUtils.isNotEmpty(fieldValue)) {
            List<String> redisCacheHash = getHash(getRedisKey(redisKey), fieldKey, List.class);
            if (StringUtils.isNotEmpty(redisCacheHash)) {
                if (!redisCacheHash.contains(fieldValue)) {
                    redisCacheHash.add(fieldValue);
                }
            } else {
                redisCacheHash = Collections.singletonList(fieldValue);
            }
            saveHash(redisKey, fieldKey, redisCacheHash);
        }
    }

    /**
     * 批量缓存某些需要联想的字段
     *
     * @param redisKey             redis key
     * @param fieldKey             字段key
     * @param fieldValueCollection 字段值集合
     */
    public void saveCacheFields(String redisKey, String fieldKey, Collection<String> fieldValueCollection) {
        if (StringUtils.isNotEmpty(fieldValueCollection)) {
            List<String> redisCacheHash = getHash(redisKey, fieldKey, List.class);
            if (StringUtils.isNotEmpty(redisCacheHash)) {
                List<String> finalRedisCacheHash = redisCacheHash;
                fieldValueCollection = fieldValueCollection.stream().filter(f -> !finalRedisCacheHash.contains(f)).collect(Collectors.toList());
                redisCacheHash.addAll(fieldValueCollection);
            } else {
                redisCacheHash = new ArrayList<>(fieldValueCollection);
            }
            saveHash(redisKey, fieldKey, redisCacheHash);
        }
    }

    /**
     * 将Lims中redis key进行转换
     *
     * @param key lims 枚举中redis的key
     * @return 完成转换的真正的key
     */
    private String getRedisKey(String key) {
        String orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        return key.replace("{0}", orgId);
    }

    /**
     * 批量对Lims中redis key进行转换
     *
     * @param key 需要转换的key集合
     * @return 完成转换的真正key集合
     */
    private List<String> batchGetKey(Collection<String> key) {
        List<String> result = new ArrayList<>();
        for (String s : key) {
            result.add(getRedisKey(s));
        }
        return result;
    }


    @Autowired
    public void setRedisTemplate(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
