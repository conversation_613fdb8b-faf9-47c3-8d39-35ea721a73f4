package com.sinoyd.base.util;

import cn.afterturn.easypoi.util.PoiPublicUtil;
import com.sinoyd.boot.common.util.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * SPEL表达式相关工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/3
 */
@SuppressWarnings("all")
public class SPELUtil {

    /**
     * 解析 spEL表达式, 返回集合
     *
     * @param spEL      spEL表达式
     * @param joinPoint 切点
     * @return 集合
     */
    public static Collection<Object> parseSpel2Collection(String spEL, JoinPoint joinPoint) {
        if (StringUtils.isNotEmpty(spEL)) {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            // 创建解析器
            SpelExpressionParser parser = new SpelExpressionParser();
            // 获取表达式
            Expression expression = parser.parseExpression(spEL);
            // 设置解析上下文
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = joinPoint.getArgs();
            // 获取运行时参数名称
            DefaultParameterNameDiscoverer discover = new DefaultParameterNameDiscoverer();
            String[] parameterNames = discover.getParameterNames(method);
            assert parameterNames != null;
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
            // 解析
            Object obj = expression.getValue(context);
            Collection<Object> valueList = null;
            if (obj != null) {
                if (obj instanceof Collection) {
                    valueList = (Collection<Object>) expression.getValue(context);
                } else {
                    valueList = new ArrayList<>();
                    valueList.add(obj);
                }
            }
            return valueList;
        }
        return null;
    }

    /**
     * 解析 spEL表达式, 返回Object
     *
     * @param spEL      spEL表达式
     * @param joinPoint 切点
     * @return Object实例
     */
    public static Object parseSpel2Object(String spEL, JoinPoint joinPoint) {
        if (StringUtils.isNotEmpty(spEL)) {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            // 创建解析器
            SpelExpressionParser parser = new SpelExpressionParser();
            // 获取表达式
            Expression expression = parser.parseExpression(spEL);
            // 设置解析上下文
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = joinPoint.getArgs();
            // 获取运行时参数名称
            DefaultParameterNameDiscoverer discover = new DefaultParameterNameDiscoverer();
            String[] parameterNames = discover.getParameterNames(method);
            assert parameterNames != null;
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
            // 解析
            return expression.getValue(context);
        }
        return null;
    }

    /**
     * 从方法执行结果中获取资源对象列表
     *
     * @param methodResult 方法执行结果
     * @param method       方法对象
     * @param resourceExp  资源表达式
     * @return 资源对象列表
     * @throws RuntimeException 如果在获取资源列表过程中出现异常，则抛出该异常
     */
    public static List<Object> getResourceFromMethodResult(Object methodResult, Method method, String resourceExp) {
        List<Object> resourceList = new ArrayList<>();
        try {
            String resourceFieldName = resourceExp.substring(resourceExp.indexOf(".") + 1);
            if (methodResult instanceof Collection) {
                String collectionParameterizedType = ((ParameterizedTypeImpl) method.getGenericReturnType()).getActualTypeArguments()[0].getTypeName();
                Class<?> collectionParameterizedTypeClass = Class.forName(collectionParameterizedType);
                Collection<?> resultCollection = (Collection<?>) methodResult;

                Field[] fields = PoiPublicUtil.getClassFields(collectionParameterizedTypeClass);
                for (Field field : fields) {
                    if (field.getName().equals(resourceFieldName)) {
                        field.setAccessible(true);
                        for (Object obj : resultCollection) {
                            resourceList.add(field.get(obj));
                        }
                        break;
                    }
                }
            } else {
                Field[] fields = PoiPublicUtil.getClassFields(method.getReturnType());
                for (Field field : fields) {
                    if (field.getName().equals(resourceFieldName)) {
                        field.setAccessible(true);
                        resourceList.add(field.get(methodResult));
                        break;
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resourceList;
    }

    /**
     * 从方法返回结果中获取指定资源列表
     *
     * @param methodResult     方法返回结果
     * @param methodReturnType 方法返回类型
     * @param resourceExp      资源表达式
     * @return 资源列表
     * @throws RuntimeException 如果获取资源时发生异常
     */
    public static List<Object> getResourceFromMethodResult(Object methodResult, Class<?> methodReturnType, String resourceExp) {
        List<Object> resourceList = new ArrayList<>();
        try {
            String resourceFieldName = resourceExp.substring(resourceExp.indexOf(".") + 1);
            Collection<?> resultCollection;
            if(methodResult instanceof Collection){
                resultCollection = (Collection<?>) methodResult;
            }else{
                resultCollection = new ArrayList<>();
                resultCollection = Collections.singletonList(methodResult);
            }
            Field[] fields = PoiPublicUtil.getClassFields(methodReturnType);
            for (Field field : fields) {
                if (field.getName().equals(resourceFieldName)) {
                    field.setAccessible(true);
                    for (Object obj : resultCollection) {
                        resourceList.add(field.get(obj));
                    }
                    break;
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return resourceList;
    }


    /**
     * 获取触发方法签名
     *
     * @param methodSignature 方法签名
     * @return 触发方法签名
     */
    public static String getTriggerMethodSignature(MethodSignature methodSignature) {
        String triggerPoint = methodSignature.getDeclaringTypeName() + "." + methodSignature.getName() + "(%s)";
        Class[] paramTypes = methodSignature.getParameterTypes();
        String paramType = "";
        if (paramTypes != null && paramTypes.length > 0) {
            for (Class clazz : paramTypes) {
                paramType += clazz.getName() + ",";
            }
        }
        if (StringUtils.isNotEmpty(paramType)) {
            paramType = paramType.substring(0, paramType.length() - 1);
        }
        return String.format(triggerPoint, paramType);
    }
}