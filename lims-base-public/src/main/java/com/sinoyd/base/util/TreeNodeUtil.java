package com.sinoyd.base.util;

import com.sinoyd.base.vo.TreeNodeVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 树工具类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
public class TreeNodeUtil {

    /**
     * 构建树
     *
     * @param treeNodeList 树节点列表
     * @return 完整树
     */
    public static List<TreeNodeVO> buildTree(List<TreeNodeVO> treeNodeList) {
        List<TreeNodeVO> trees = new ArrayList<>();
        sortTreeNode(treeNodeList);
        for (TreeNodeVO treeNode : treeNodeList) {
            if (StringUtils.isEmpty(treeNode.getParentId()) || UUIDHelper.guidEmpty().equals(treeNode.getParentId())) {
                trees.add(findChildren(treeNode, treeNodeList));
            }
        }
        return trees;
    }

    /**
     * 将树形拆解为无关系结构的List
     *
     * @param tree 需要拆解的树
     * @return 完成拆解的treeNode列表
     */
    public static List<TreeNodeVO> disassembleTree(List<TreeNodeVO> tree) {
        List<TreeNodeVO> result = new ArrayList<>();
        doDisassemble(tree, result);
        return result;
    }

    /**
     * 创建空节点
     *
     * @return 空节点
     */
    public static TreeNodeVO getEmptyNode() {
        TreeNodeVO node = new TreeNodeVO();
        node.setId(UUIDHelper.newId());
        node.setLabel("所有");
        node.setChildren(new ArrayList<>());
        return node;
    }

    /**
     * 递归查找子节点
     *
     * @param currentTreeNode 当前树节点
     * @param treeNodeList    树节点列表
     * @return 完整结构的树节点
     */
    private static TreeNodeVO findChildren(TreeNodeVO currentTreeNode, List<TreeNodeVO> treeNodeList) {
        for (TreeNodeVO treeNode : treeNodeList) {
            if (currentTreeNode.getId().equals(treeNode.getParentId())) {
                if (currentTreeNode.getChildren() == null) {
                    currentTreeNode.setChildren(new ArrayList<>());
                }
                //是否还有子节点，如果有的话继续往下遍历，如果没有则直接返回
                currentTreeNode.getChildren().add(findChildren(treeNode, treeNodeList));
            }
        }
        if (StringUtils.isEmpty(currentTreeNode.getChildren())) {
            currentTreeNode.setIsLeaf(Boolean.TRUE);
        } else {
            currentTreeNode.setIsLeaf(Boolean.FALSE);
            //子级排序
            sortTreeNode(currentTreeNode.getChildren());
        }
        return currentTreeNode;
    }

    /**
     * 拆解
     *
     * @param tree   要拆解的书
     * @param result 结果容器
     */
    private static void doDisassemble(List<TreeNodeVO> tree, List<TreeNodeVO> result) {
        for (TreeNodeVO node : tree) {
            if (StringUtils.isNotEmpty(node.getChildren())) {
                doDisassemble(node.getChildren(), result);
            }
            node.setChildren(new ArrayList<>());
            result.add(node);
        }
    }

    /**
     * 排序方法
     *
     * @param treeNodeList 需要排序的节点list
     */
    private static void sortTreeNode(List<TreeNodeVO> treeNodeList) {
        treeNodeList.sort(Comparator.comparing(v -> v.getOrderNum() == null ? 0 : v.getOrderNum(),
                Comparator.reverseOrder()));
    }
}