package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 账号VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
@Data
@Accessors(chain = true)
public class AccountVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 登录账号
     */
    private String loginId;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色
     */
    private List<String> roleIds;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 确认密码
     */
    private String confirmPassword;
}