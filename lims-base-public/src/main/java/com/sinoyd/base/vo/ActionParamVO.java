package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动作参数VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/13
 */
@Data
@Accessors(chain = true)
public class ActionParamVO<T> {

    /**
     * 实体
     */
    private T entity;

    /**
     * 实体集合
     */
    private Collection<T> entities;

    /**
     * 业务id集合
     */
    private Collection<String> businessIds;

    /**
     * 关联业务id，比如T是样品，如果需要将样品关联采样单，relationId为采样单id
     */
    private String relationId;

    /**
     * 复核人id
     */
    private String reviewersId;

    /**
     * 是否需要审核
     */
    private Boolean isNeedApproval;

    /**
     * 审核人id，从1审到N审，按顺序放入
     */
    private List<String> auditorIds;

    /**
     * 审核是否通过
     */
    private Boolean isApprovalPassed;

    /**
     * 意见
     */
    private String opinion;

    /**
     * 扩展属性
     */
    private Map<String, Object> expandMap = new HashMap<>();

    public ActionParamVO() {
        super();
    }

    public ActionParamVO(Collection<String> businessIds) {
        super();
        setBusinessIds(businessIds);
    }

    public ActionParamVO(WorkflowParamVO paramVO) {
        super();
        setBusinessIds(paramVO.getObjectIds());
        setIsNeedApproval(paramVO.getIsAudit());
        setAuditorIds(paramVO.getAuditorIds());
        setIsApprovalPassed(paramVO.getAuditResult());
        setOpinion(paramVO.getOpinion());
        setReviewersId(paramVO.getReviewersId());
    }

}