package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务检查项VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/30
 */
@Data
@Accessors(chain = true)
public class BizCheckItemVO {

    /**
     * 检查项名称
     */
    private String itemName;

    /**
     * 当出现异常时，是否阻断后续流程
     */
    private Boolean isBlock = Boolean.FALSE;

    /**
     * 检查是否通过
     */
    private Boolean isPassed = Boolean.TRUE;

    /**
     * 检查消息
     */
    private String checkMsg = "验证通过";

    /**
     * 排序值，用于排序
     */
    private int orderNum;

    /**
     * 构造方法：创建BizCheckItemVO对象
     */
    public BizCheckItemVO() {
        super();
    }

    /**
     * BizCheckItemVO类的构造方法，用于创建一个BizCheckItemVO对象并初始化其itemName属性
     */
    public BizCheckItemVO(String itemName) {
        this();
        this.itemName = itemName;
    }
}