package com.sinoyd.base.vo;

import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 业务检查结果VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/30
 */
@Data
@Accessors(chain = true)
public class BizCheckResultVO {

    /**
     * 检查项集合
     */
    private List<BizCheckItemVO> checkItemList;

    /**
     * 当出现异常时，是否阻断后续流程
     */
    private Boolean isBlock = Boolean.FALSE;

    /**
     * 异常项数量
     */
    private long failureAmounts;

    public void setBlock(Boolean block) {
        if (StringUtils.isNotEmpty(this.checkItemList) && this.checkItemList.stream().anyMatch(BizCheckItemVO::getIsBlock)) {
            this.isBlock = Boolean.TRUE;
        }
    }

    public Boolean getIsBlock() {
        if (StringUtils.isNotEmpty(this.checkItemList) && this.checkItemList.stream().anyMatch(BizCheckItemVO::getIsBlock)) {
            this.isBlock = Boolean.TRUE;
        }
        return isBlock;
    }
}