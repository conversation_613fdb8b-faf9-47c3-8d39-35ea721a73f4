package com.sinoyd.base.vo;

import lombok.Data;

import java.util.List;

/**
 * 通用数据标签页对象VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/07/23
 */
@Data
public class DataPageLabelVO {

    /**
     * 业务id
     */
    private String id;

    /**
     * label名称
     */
    private String typeName;

    /**
     * 标签页数据数量
     */
    private Integer count = 0;

    /**
     * 子节点集合
     */
    private List<DataPageLabelVO> children;


    /**
     * 构造函数
     *
     * @param id       检测类型id
     * @param typeName 检测类型名称
     */
    public DataPageLabelVO(String id, String typeName) {
        this.id = id;
        this.typeName = typeName;
    }
}
