package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 质控配置VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/10/16
 */
@Data
@Accessors(chain = true)
public class DeviationVO {

    /**
     * 公式
     */
    private String formula;

    /**
     * 质控配置
     */
    private QcLimitValueVO limit;

    /**
     * 质控计算数据
     */
    private String deivationValue;

    /**
     * 加标增值
     */
    private String qcAddedValue;

    /**
     * 在线值集合
     */
    private List<String> onlineDataList;

    /**
     * 实验室值集合
     */
    private List<String> laboratoryData;
}
