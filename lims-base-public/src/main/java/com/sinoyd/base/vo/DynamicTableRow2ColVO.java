package com.sinoyd.base.vo;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 动态表行列关联数据实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/07/22
 */
@Data
public class DynamicTableRow2ColVO {

    /**
     * 关联id
     */
    private String id = UUIDHelper.newId();

    /**
     * 行id
     */
    private String rowId;

    /**
     * 列id
     */
    private String colId;

    /**
     * 对应列值
     */
    private String val;

    /**
     * 获取数值
     *
     * @return 数值
     */
    public BigDecimal getMathVal() {
        BigDecimal val = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(this.val)) {
            String strVal = this.val.replaceAll(IBaseConstants.RED_MARK, "");
            if (MathUtil.isNumber(strVal)){
                val = new BigDecimal(strVal);
            }
        }
        return val;
    }

    /**
     * 无参构造函数
     */
    public DynamicTableRow2ColVO() {
    }

    /**
     * 构造函数
     *
     * @param rowId 行id
     * @param colId 列id
     * @param val   对应列值
     */
    public DynamicTableRow2ColVO(String rowId, String colId, String val) {
        this();
        this.rowId = rowId;
        this.colId = colId;
        this.val = val;
    }
}
