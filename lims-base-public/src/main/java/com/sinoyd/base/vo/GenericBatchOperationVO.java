package com.sinoyd.base.vo;

import lombok.Data;

import java.util.List;

/**
 * 批量操作VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/8
 */
@Data
public class GenericBatchOperationVO<T> {

    /**
     * 批量操作的数据id
     */
    private List<String> ids;

    /**
     * 批量操作的数据集合
     */
    private List<T> dataList;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 业务id，用于日志收集时将子级业务数据归类到父级业务数据下面
     */
    private String bizId;
}