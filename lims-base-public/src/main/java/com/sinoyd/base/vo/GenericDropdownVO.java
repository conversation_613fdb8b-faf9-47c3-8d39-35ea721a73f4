package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 通用下拉框VO，用于返回下拉框给前端
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/2/2
 */
@Data
@Accessors(chain = true)
public class GenericDropdownVO {

    /**
     * 下拉框条目显示值
     */
    private String label;

    /**
     * 下拉框条目值
     */
    private Object value;

    /**
     * 预留扩展属性，防止一些下拉框有额外属性需要显示
     */
    private Map<String, Object> extendMap;
}