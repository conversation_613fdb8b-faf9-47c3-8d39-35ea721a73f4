package com.sinoyd.base.vo;

import com.sinoyd.excel.verify.model.IExcelPOJODataModel;

import java.util.Set;

/**
 * LIMS Excel 基础模型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/7/19
 */
public class LimsExcelBaseVO implements IExcelPOJODataModel {

    /**
     * 行号
     */
    protected Integer rowNum;

    /**
     * 错误消息
     */
    protected String errorMsg;

    /**
     * 唯一性属性名集合
     */
    protected Set<String> uniqueFieldNames;

    @Override
    public Integer getRowNum() {
        if (this.rowNum != null) {
            return this.rowNum + 1;
        }
        return null;
    }

    @Override
    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public Set<String> getUniqueFieldNames() {
        return this.uniqueFieldNames;
    }

    @Override
    public void setUniqueFieldNames(Set<String> uniqueFieldNames) {
        this.uniqueFieldNames = uniqueFieldNames;
    }
}