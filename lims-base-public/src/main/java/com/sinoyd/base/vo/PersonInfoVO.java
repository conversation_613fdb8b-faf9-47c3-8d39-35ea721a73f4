package com.sinoyd.base.vo;

import com.sinoyd.base.enums.EnumPersonCertStatus;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 人员信息VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Data
@Accessors(chain = true)
public class PersonInfoVO {

    /**
     * 人员id
     */
    private String id;

    /**
     * 人员名称
     */
    private String name;

    /**
     * 人员所在部门id
     */
    private String deptId;

    /**
     * 是否有上岗证
     */
    private Boolean hasCert;

    /**
     * 人员上岗证状态
     */
    private Integer certStatus;

    /**
     * 人员上岗证状态名称
     */
    private String certStatusLabel;

    /**
     * 人员联系电话
     */
    private String telNo;

    /**
     * 头像路径
     */
    private String avatarPath;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 填充人员上岗证状态
     *
     * @param certStatus 人员上岗证状态
     */
    public PersonInfoVO fillCertStatus(EnumPersonCertStatus certStatus){
        if (certStatus == EnumPersonCertStatus.有证) {
            this.hasCert = true;
        } else {
            this.hasCert = false;
        }
        this.certStatus = certStatus.getValue();
        this.certStatusLabel = certStatus.name();
        return this;
    }
}