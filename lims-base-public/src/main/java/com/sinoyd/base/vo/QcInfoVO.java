package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 质控配置VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/10/16
 */
@Data
@Accessors(chain = true)
public class QcInfoVO {

    /**
     * 质控名称
     */
    private String name;

    /**
     * 质控编码
     */
    private String code;

    /**
     * 质控登记，枚举管理，1:外部质控 2: 内部质控
     */
    private Integer level;

    /**
     * 质控类别，枚举管理
     */
    private Integer qcType;

    /**
     * 是否启用，默认启用
     */
    private Boolean isEnabled;

    /**
     * 是否独立质控，默认不独立（独立质控则可不关联原样，非独立质控需要关联原样）
     */
    private Boolean isIndependent;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 偏差公式，枚举管理
     */
    private String offsetFormula;

    /**
     * 误差公式，枚举管理
     */
    private String errorFormula;

    /**
     * 有效位数
     */
    private Integer significantDigit;

    /**
     * 小数位数
     */
    private Integer decimalDigit;

    /**
     * 样品名称
     */
    private String sampleName;

}
