package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 质控限制配置VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/10/16
 */
@Data
@Accessors(chain = true)
public class QcLimitValueVO {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 评判方式，枚举管理
     */
    private Integer judgmentWay;

    /**
     * 是否设定检查项
     */
    private Boolean hasCheckItem;

    /**
     * 检查项，枚举管理
     */
    private Integer checkItem;

    /**
     * 检查项其他
     */
    private String otherCheckItem;

    /**
     * 检查项范围
     */
    private String checkItemRange;

    /**
     * 允许限值
     */
    private String validLimitValue;

    /**
     * 替代物id
     */
    private String substitutionId;

    /**
     * 公式，比如穿透公式
     */
    private String formula;

    /**
     * 技术说明
     */
    private String technicalDescription;

    /**
     * 评判方式显示名称
     */
    private String judgmentWayLabel;

    /**
     * 检查项显示名称
     */
    private String checkItemLabel;

    /**
     * 替代物名称
     */
    private String substitutionName;

    /**
     * 质控样排序值
     */
    private Integer qcInfoOrderNum;

    /**
     * 质控样
     */
    private QcInfoVO qcInfo;
}
