package com.sinoyd.base.vo;

import lombok.Data;

import java.util.Map;

/**
 * 远程请求参数实体VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/12
 */
@Data
public class RemoteRequestParamVO {

    /**
     * 网关地址
     */
    private String gateUrl;

    /**
     * 请求方式
     */
    private String httpMethod;

    /**
     * 网关代理地址
     */
    private String gateContext;

    /**
     * 请求令牌
     */
    private String token;

    /**
     * 请求地址
     */
    private String uri;

    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;

    /**
     * 构造函数
     *
     * @param gateUrl     网关地址
     * @param token       令牌
     * @param httpMethod  请求方式
     * @param uri         请求地址
     * @param gateContext 网关代理地址
     */
    public RemoteRequestParamVO(String gateUrl, String token, String httpMethod, String uri, String gateContext) {
        this.gateUrl = gateUrl;
        this.httpMethod = httpMethod;
        this.gateContext = gateContext;
        this.token = token;
        this.uri = uri;
    }


    /**
     * 构造函数
     *
     * @param gateUrl       网关地址
     * @param token         令牌
     * @param httpMethod    请求方式
     * @param uri           请求地址
     * @param gateContext   网关代理地址
     * @param requestParams 请求参数
     */
    public RemoteRequestParamVO(String gateUrl, String token, String httpMethod, String uri, String gateContext, Map<String, Object> requestParams) {
        this.gateUrl = gateUrl;
        this.httpMethod = httpMethod;
        this.gateContext = gateContext;
        this.token = token;
        this.uri = uri;
        this.requestParams = requestParams;
    }
}
