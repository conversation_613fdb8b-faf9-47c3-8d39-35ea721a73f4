package com.sinoyd.base.vo;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作流参数实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/26
 */
@Data
@Accessors(chain = true)
public class WorkflowParamVO {

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 当前状态
     */
    private Integer currentState;

    /**
     * 信号值
     */
    private String signal;

    /**
     * 对象ids
     */
    private List<String> objectIds = new ArrayList<>();

    /**
     * 业务对象实体集合
     */
    private List<? extends LimsBaseEntity> entities;

    /**
     * 复核人id
     */
    private String reviewersId;

    /**
     * 是否审核，一般用于提交时选择是否审核
     */
    private Boolean isAudit;

    /**
     * 审核人员id集合
     */
    private List<String> auditorIds;

    /**
     * 下一步审核人id
     */
    private String nextOperatorId;

    /**
     * 下一步审核人名称
     */
    private String nextOperatorName;

    /**
     * 意见
     */
    private String opinion;

    /**
     * 审核结果，true: 通过， false: 不通过
     */
    private Boolean auditResult;

    /**
     * 获取提交时信号值，根据审核人 auditorIds判断
     *
     * @return 信号值
     */
    public String getSubmitSignal() {
        if (StringUtils.isEmpty(this.auditorIds)) {
            return "noNeedAudit";
        } else if (this.auditorIds.size() == 1) {
            return "oneStepSubmit";
        } else if (this.auditorIds.size() == 2) {
            return "twoStepSubmit";
        } else if (this.auditorIds.size() == 3) {
            return "threeStepSubmit";
        } else if (this.auditorIds.size() == 4) {
            return "fourStepSubmit";
        } else if (this.auditorIds.size() == 5) {
            return "fiveStepSubmit";
        } else {
            throw new BaseException("审核步骤超过5步，系统暂不支持");
        }
    }

    /**
     * 获取审核时信号值，根据审核结果判断返回
     *
     * @return 信号值
     */
    public String getApprovalSignal() {
        if (this.auditResult == null) {
            throw new BaseException("页面请求未传递审核结果值");
        }
        if (this.auditResult) {
            return "passed";
        } else {
            return "rejected";
        }
    }

    /**
     * 获取退回时工作流信号值
     *
     * @return 信号值
     */
    public String getBackSignal(){
        return "back";
    }
    
}