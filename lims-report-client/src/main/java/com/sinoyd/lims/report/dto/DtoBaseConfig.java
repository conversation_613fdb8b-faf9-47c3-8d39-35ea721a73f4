package com.sinoyd.lims.report.dto;

import lombok.Data;

import java.util.Date;

/**
 * 报表配置对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/15
 */
@Data
public class DtoBaseConfig {


    /**
     * 报表id
     */
    private String id;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 报表类型编码，枚举维护：原始记录单 1、采样单 2、报告 3、报表 4、标签 5
     */
    private Integer reportTypeValue;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板位置
     */
    private String templatePath;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 受控编号
     */
    private String controlNum;

    /**
     * 配置报表名称
     */
    private String definedFileName;

    /**
     * 分页字段
     */
    private String pageFields;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
