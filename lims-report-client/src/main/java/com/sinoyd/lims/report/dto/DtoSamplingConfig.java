package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 采样单配置对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/31
 */
@Data
public class DtoSamplingConfig {
    /**
     * 主键id
     */
    private String id = UUIDHelper.newId();

    /**
     * 名称
     */
    private String formName;

    /**
     * 报表模板编码
     */
    private String reportCode;

    /**
     * 检测类型Id(小类)
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 录入方式，枚举管理
     */
    private Integer fillType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否仅关联测试项目显示
     */
    private Boolean isRelatedTest;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 参数配置id集合
     */
    private List<String> paramsConfigIds;

    /**
     * 报表路径
     */
    private String workPath;

    /**
     * 报表名称
     */
    private String workName;

    /**
     * 源配置id
     */
    private String sourceRecordConfigId;

    /**
     * 基础配置id
     */
    private String baseConfigId;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;


    /**
     * 关联的测试项目id集合
     */
    private List<String> testIds;
}
