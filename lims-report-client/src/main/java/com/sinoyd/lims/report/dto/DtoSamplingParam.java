package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;

/**
 * 采样单配置参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
public class DtoSamplingParam {

    /**
     * 主键
     */
    private String id = UUIDHelper.newId();

    /**
     * 采样单配置id
     */
    private String samplingConfigId;

    /**
     * 参数种类，枚举管理，如样品参数、数据参数
     */
    private Integer paramCategory;

    /**
     * 参数id
     */
    private String paramId;

    /**
     * 参数类型，枚举管理
     */
    private Integer paramType;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数别名
     */
    private String aliasName;

    /**
     * 控件类型，枚举管理
     */
    private Integer controlType;

    /**
     * 是否扩展参数
     */
    private Boolean isExtend;

    /**
     * 是否必填
     */
    private Boolean isMandatory;

    /**
     * 是否共享
     */
    private Boolean isShare;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 计量单位id
     */
    private String dimensionId;

    /**
     * 计量单位名称
     */
    private String dimensionName;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否显示
     */
    private Boolean isDisplay;

    /**
     * 参考文本
     */
    private String reference;

    /**
     * 数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置
     */
    private Integer dsType;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 分组Id
     */
    private String groupRuleConfigId = UUIDHelper.guidEmpty();

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
