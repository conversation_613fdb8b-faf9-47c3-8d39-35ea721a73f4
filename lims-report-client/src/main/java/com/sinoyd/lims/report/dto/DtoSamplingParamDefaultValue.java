package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import java.util.Date;

/**
 * 采样单默认参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
public class DtoSamplingParamDefaultValue {

    /**
     * 主键id
     */
    private String id = UUIDHelper.newId();

    /**
     * 采样单参数id
     */
    private String samplingParamId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
