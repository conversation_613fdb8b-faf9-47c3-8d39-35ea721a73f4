package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 测试项目公式
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
public class DtoTestFormula {

    private String id = UUIDHelper.newId();

    /**
     * 公式名称
     */
    private String formulaName;

    /**
     * 对象类型，枚举管理
     */
    private Integer objectType;

    /**
     * 对象id
     */
    private String objectId;

    /**
     * 是否启用
     */
    private Boolean isEnable;

    /**
     * 排序值
     */
    private Integer ordernum;

    /**
     * 对象名称
     */
    private String objectName;

    /**
     * 测试项目数量
     */
    private Integer testNum;

    /**
     * 样品出证公式
     */
    private String sampleFormula;

    /**
     * 测试项目名称
     */
    private String testNames;

    /**
     * 测试项目ids
     */
    private List<String> testIds;

    /**
     * 公式参数集合
     */
    private List<DtoTestFormulaEquationParam> testFormulaEquationParamList;

    /**
     * 公式集合
     */
    private List<DtoTestFormulaEquation> testFormulaEquationList;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
