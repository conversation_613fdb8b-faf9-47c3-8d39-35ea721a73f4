package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 测试项目公式
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
@Accessors(chain = true)
public class DtoTestFormulaEquation {

    private String id = UUIDHelper.newId();

    /**
     * 测试项目公式id
     */
    private String testFormulaId;

    /**
     * 方程种类，枚举管理
     */
    private Integer equationCategory;

    /**
     * 是否前置判定
     */
    private Boolean isPreJudge;

    /**
     * 前置判定方程
     */
    private String preJudgeEquation;

    /**
     * 计算方程
     */
    private String calculateEquation;

    /**
     * 有效位数
     */
    private Integer significantDigit;

    /**
     * 小数位数
     */
    private Integer decimalDigit;

    /**
     * 检出限
     */
    private String detectionValue;

    /**
     * 小于检出限显示字典编码，字典管理
     */
    private String detectionDisplayCode;

    /**
     * 修约规则字典编码，字典管理
     */
    private String reviseRuleCode;

    /**
     * 计算方式，枚举管理
     */
    private Integer calculateWay;

    /**
     * 计算优先级
     */
    private Integer calculatePriority;

    /**
     * 计算参数id
     */
    private String calculateParamId;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
