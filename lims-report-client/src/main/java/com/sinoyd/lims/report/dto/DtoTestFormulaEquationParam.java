package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 测试项目公式参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
public class DtoTestFormulaEquationParam {

    private String id = UUIDHelper.newId();

    /**
     * 测试项目公式方程id
     */
    private String testFormulaId;

    /**
     * 参数种类，枚举管理，如表头参数
     */
    private Integer paramCategory;

    /**
     * 参数id
     */
    private String paramId;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数别名
     */
    private String aliasName;

    /**
     * 控件类型，枚举管理
     */
    private Integer controlType;

    /**
     * 是否必填
     */
    private Boolean isMandatory;

    /**
     * 是否出证
     */
    private Boolean isCert;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 有效位数
     */
    private Integer significantDigit;

    /**
     * 小数位数
     */
    private Integer decimalDigit;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否显示
     */
    private Boolean isDisplay;

    /**
     * 参考文本
     */
    private String reference;

    /**
     * 数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置
     */
    private Integer dsType;

    /**
     * 当dsType是接口时，该字段存储接口参数及值
     */
    private String apiDsParams;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 参数默认值
     */
    private List<DtoTestFormulaEquationParamDefaultValue> defaultValueList;
}
