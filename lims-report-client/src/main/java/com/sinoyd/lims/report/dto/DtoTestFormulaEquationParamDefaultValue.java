package com.sinoyd.lims.report.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;

/**
 * 测试项目公式参数默认值
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Data
public class DtoTestFormulaEquationParamDefaultValue {

    private String id = UUIDHelper.newId();

    /**
     * 测试项目公式方程参数id
     */
    private String equationParamId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法标准编号
     */
    private String methodStandardNo;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析项目化学符号
     */
    private String chemicalSymbol;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
