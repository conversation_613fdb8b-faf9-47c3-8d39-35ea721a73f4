package com.sinoyd.lims.report.dto;

import lombok.Data;

import java.util.List;

/**
 * 原始记录单配置对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/03
 */
@Data
public class DtoWorkSheetConfig {

    /**
     * 主键id
     */
    private String id;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 原始记录单名称
     */
    private String formName;

    /**
     * 录入方式，枚举管理EnumInspectFormFillType
     */
    private Integer fillType;

    /**
     * 是否共享表头参数
     */
    private Boolean isShareHeadParam;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 报表路径
     */
    private String workPath;

    /**
     * 报表名称
     */
    private String workName;

    /**
     * 基础配置id
     */
    private String baseConfigId;


    /**
     * 当前配置下的参数数据
     */
    private List<DtoWorkSheetParam> paramList;

}
