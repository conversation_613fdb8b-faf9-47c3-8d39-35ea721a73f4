package com.sinoyd.lims.report.feign;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoBaseConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 报表配置Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/15
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/baseConfig",
        configuration = {FeignConfig.class}
)
public interface IBaseConfigFeignService {

    /**
     * 查询数据详细
     *
     * @param id 数据id
     * @return 数据详细
     */
    @GetMapping("/{id}")
    RestResponse<DtoBaseConfig> findOne(@PathVariable("id") String id);

    /**
     * 根据报表编码查询数据
     *
     * @param reportCode 报表编码
     * @return 数据详细
     */
    @GetMapping("/code/{reportCode}")
    RestResponse<DtoBaseConfig> findByCode(@PathVariable("reportCode") String reportCode);
}
