package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoSamplingConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 采样单配置Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/03
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/samplingConfig",
        configuration = {FeignConfig.class}
)
public interface ISamplingConfigFeignService {

    /**
     * 根据id查询采样单配置
     *
     * @param ids 实体列表
     * @return 采样单配置
     */
    @PostMapping("/ids")
    RestResponse<List<DtoSamplingConfig>> findByIds(@RequestBody Collection<String> ids);

    /**
     * 查询单条采样单配置
     *
     * @param id 采样单配置id
     * @return 采样单配置详情
     */
    @GetMapping("/{id}")
    RestResponse<DtoSamplingConfig> findOne(@PathVariable String id);
}
