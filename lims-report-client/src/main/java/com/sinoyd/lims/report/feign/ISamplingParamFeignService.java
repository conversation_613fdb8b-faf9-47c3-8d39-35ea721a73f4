package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoSamplingParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 采样单配置参数Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/4
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/samplingParam",
        configuration = {FeignConfig.class}
)
public interface ISamplingParamFeignService {

    /**
     * 根据采样单配置id查询参数集合
     *
     * @param ids 配置id
     * @return 响应结果
     */
    @PostMapping("/samplingConfigIds")
    RestResponse<List<DtoSamplingParam>> findBySamplingConfigIds(@RequestBody Collection<String> ids);

    /**
     * 根据ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoSamplingParam>> findByIds(@RequestBody Collection<String> ids);
}
