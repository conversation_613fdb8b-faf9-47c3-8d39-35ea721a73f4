package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoTestFormulaEquation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/07/08
 **/
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/testFormulaEquation",
        configuration = {FeignConfig.class}
)
public interface ITestFormulaEquationFeignService {

    /**
     * 根据项目公式ids查询项目公式方程
     *
     * @param ids 项目公式ids
     * @return 项目公式方程列表
     */
    @PostMapping("/testFormulaIds")
    RestResponse<List<DtoTestFormulaEquation>> findByTestFormulaIds(@RequestBody Collection<String> ids);
}
