package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoTestFormula;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/4
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/testFormula",
        configuration = {FeignConfig.class}
)
public interface ITestFormulaFeignService {

    /**
     * 根据采样单配置id查询参数集合
     *
     * @param ids 配置id
     * @return 响应结果
     */
    @PostMapping("/objectIds")
    RestResponse<List<DtoTestFormula>> findByObjectIds(@RequestBody Collection<String> ids);

    /**
     * 根据测试项目和公式获取数据集合
     *
     * @param formulaId 公式id
     * @param testId    测试id
     * @return 数据集合
     */
    @PostMapping("/formula/{formulaId}/{testId}")
    RestResponse<List<DtoTestFormula>> findByFormulaIdAndTestId(@PathVariable(name = "formulaId") String formulaId,
                                                                @PathVariable(name = "testId") String testId);

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param ids 采样单配置Ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoTestFormula>> findAllByIds(@RequestBody Collection<String> ids);

    /**
     * 根据 测试项目ids 获取公式
     *
     * @param testIds 测试项目ids
     * @return 公式集合
     */
    @PostMapping("/testIds")
    RestResponse<List<DtoTestFormula>> findByTestIds(@RequestBody Collection<String> testIds);
}
