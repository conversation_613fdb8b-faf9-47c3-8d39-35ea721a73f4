package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoWorkSheetConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单配置Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/03
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/workSheetConfig",
        configuration = {FeignConfig.class}
)
public interface IWorkSheetConfigFeignService {

    /**
     * 根据id查询原始记录单配置
     *
     * @param ids 实体列表
     * @return 原始记录单配置
     */
    @PostMapping("/ids")
    RestResponse<List<DtoWorkSheetConfig>> findByIds(@RequestBody Collection<String> ids);

    /**
     * 根据id查询原始记录单配置
     *
     * @param testIds 实体列表
     * @return 原始记录单配置
     */
    @PostMapping("/test/ids")
    RestResponse<List<DtoWorkSheetConfig>> findByTestIds(@RequestBody Collection<String> testIds);
}
