package com.sinoyd.lims.report.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.report.dto.DtoWorkSheetParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单配置参数Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/4
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-report}",
        path = "/api/report/workSheetParam",
        configuration = {FeignConfig.class}
)
public interface IWorkSheetParamFeignService {

    /**
     * 根据原始记录单配置id查询参数集合
     *
     * @param ids 配置id
     * @return 响应结果
     */
    @PostMapping("/workSheetConfigIds")
    RestResponse<List<DtoWorkSheetParam>> findByWorkSheetConfigIds(@RequestBody Collection<String> ids);

    /**
     * 根据参数ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoWorkSheetParam>> findByIds(@RequestBody Collection<String> ids);
}
