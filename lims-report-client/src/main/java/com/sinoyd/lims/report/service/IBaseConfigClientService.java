package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoBaseConfig;

/**
 * 报表配置对外服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/15
 */
public interface IBaseConfigClientService {

    /**
     * 查询数据详细
     *
     * @param id 数据id
     * @return 数据详细
     */
    DtoBaseConfig findOne(String id);

    /**
     * 根据报表编码查询数据
     *
     * @param reportCode 报表编码
     * @return 数据详细
     */
    DtoBaseConfig findByCode(String reportCode);
}
