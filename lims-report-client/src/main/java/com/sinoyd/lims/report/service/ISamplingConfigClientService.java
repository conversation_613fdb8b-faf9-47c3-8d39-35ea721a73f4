package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoSamplingConfig;

import java.util.Collection;
import java.util.List;

/**
 * 采样单配置对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/31
 */
public interface ISamplingConfigClientService {

    /**
     * 根据id查询采样单配置
     *
     * @param ids 实体列表
     * @return 采样单配置
     */
    List<DtoSamplingConfig> findByIds(Collection<String> ids);

    /**
     * 查询单条采样单配置
     *
     * @param id 采样单配置id
     * @return 采样单配置详情
     */
    DtoSamplingConfig findOne(String id);
}
