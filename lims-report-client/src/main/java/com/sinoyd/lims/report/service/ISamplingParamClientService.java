package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoSamplingParam;

import java.util.Collection;
import java.util.List;

/**
 * 采样单参数对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
public interface ISamplingParamClientService {

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param configIds 采样单配置Ids
     * @return 参数集合
     */
    List<DtoSamplingParam> findBySamplingConfigId(Collection<String> configIds);

    /**
     * 根据ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    List<DtoSamplingParam> findByIds(Collection<String> ids);
}
