package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoTestFormula;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
public interface ITestFormulaClientService {

    /**
     * 测试项目公式
     *
     * @param configIds 采样单配置Ids
     * @return 参数集合
     */
    List<DtoTestFormula> findByConfigIds(Collection<String> configIds);

    /**
     * 根据测试项目和公式获取数据集合
     *
     * @param formulaId 公式Id
     * @param testId    测试项目Id
     * @return 数据集合
     */
    List<DtoTestFormula> findByFormulaIdAndTestId(String formulaId, String testId);

    /**
     * 根据公式ids获取公式集合
     *
     * @param ids 公式ids
     * @return 公式集合
     */
    List<DtoTestFormula> findAllByIds(Collection<String> ids);

    /**
     * 根据 测试项目ids 获取公式
     *
     * @param testIds 测试项目ids
     * @return 公式集合
     */
    List<DtoTestFormula> findByTestIds(Collection<String> testIds);
}
