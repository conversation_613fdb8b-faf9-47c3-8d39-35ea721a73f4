package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoTestFormulaEquation;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/07/08
 **/
public interface ITestFormulaEquationClientService {

    /**
     * 根据测试项目公式id查询测试项目公式方程
     *
     * @param testFormulaIds 测试项目公式id集合
     * @return 测试项目公式方程集合
     */
    List<DtoTestFormulaEquation> findByTestFormulaIds(Collection<String> testFormulaIds);
}
