package com.sinoyd.lims.report.service;

import com.sinoyd.lims.report.dto.DtoWorkSheetConfig;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单配置对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/03
 */
public interface IWorkSheetConfigClientService {


    /**
     * 根据id查询原始记录单配置
     *
     * @param ids 实体列表
     * @return 原始记录单配置
     */
    List<DtoWorkSheetConfig> findByIds(Collection<String> ids);

    /**
     * 根据id查询原始记录单配置
     *
     * @param testIds 实体列表
     * @return 原始记录单配置
     */
    List<DtoWorkSheetConfig> findByTestIds(Collection<String> testIds);

}
