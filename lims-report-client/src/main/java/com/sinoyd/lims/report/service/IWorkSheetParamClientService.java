package com.sinoyd.lims.report.service;


import com.sinoyd.lims.report.dto.DtoWorkSheetParam;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
public interface IWorkSheetParamClientService {

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param configIds 采样单配置Ids
     * @return 参数集合
     */
    List<DtoWorkSheetParam> findByWorkSheetConfigId(Collection<String> configIds);

    /**
     * 根据参数ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    List<DtoWorkSheetParam> findByIds(Collection<String> ids);
}
