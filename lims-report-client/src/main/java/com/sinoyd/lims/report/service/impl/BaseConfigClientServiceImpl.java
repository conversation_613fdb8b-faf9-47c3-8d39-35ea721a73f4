package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoBaseConfig;
import com.sinoyd.lims.report.feign.IBaseConfigFeignService;
import com.sinoyd.lims.report.service.IBaseConfigClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报表配置对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/15
 */
@Service
public class BaseConfigClientServiceImpl implements IBaseConfigClientService {

    private IBaseConfigFeignService baseConfigFeignService;

    @Override
    public DtoBaseConfig findOne(String id) {
        RestResponse<DtoBaseConfig> response = baseConfigFeignService.findOne(id);
        return response.isSuccess() ? response.getData() : null;
    }

    @Override
    public DtoBaseConfig findByCode(String reportCode) {
        RestResponse<DtoBaseConfig> response = baseConfigFeignService.findByCode(reportCode);
        return response.isSuccess() ? response.getData() : null;
    }

    @Autowired
    public void setBaseConfigFeignService(IBaseConfigFeignService baseConfigFeignService) {
        this.baseConfigFeignService = baseConfigFeignService;
    }
}
