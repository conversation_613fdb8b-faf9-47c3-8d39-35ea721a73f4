package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoSamplingConfig;
import com.sinoyd.lims.report.feign.ISamplingConfigFeignService;
import com.sinoyd.lims.report.service.ISamplingConfigClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 采样单配置对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/31
 */
@Service
public class SamplingConfigClientServiceImpl implements ISamplingConfigClientService {

    private ISamplingConfigFeignService samplingConfigFeignService;

    @Override
    public List<DtoSamplingConfig> findByIds(Collection<String> ids) {
        RestResponse<List<DtoSamplingConfig>> response = samplingConfigFeignService.findByIds(ids);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Override
    public DtoSamplingConfig findOne(String id) {
        RestResponse<DtoSamplingConfig> response = samplingConfigFeignService.findOne(id);
        return response.isSuccess() ? response.getData() : null;
    }

    @Autowired
    public void setSamplingConfigFeignService(ISamplingConfigFeignService samplingConfigFeignService) {
        this.samplingConfigFeignService = samplingConfigFeignService;
    }
}
