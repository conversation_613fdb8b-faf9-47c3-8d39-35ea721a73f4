package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoSamplingParam;
import com.sinoyd.lims.report.feign.ISamplingParamFeignService;
import com.sinoyd.lims.report.service.ISamplingParamClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 采样单参数对外接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Service
@Slf4j
public class SamplingParamClientServiceImpl implements ISamplingParamClientService {

    private ISamplingParamFeignService samplingParamFeignService;

    @Override
    public List<DtoSamplingParam> findBySamplingConfigId(Collection<String> configIds) {
        RestResponse<List<DtoSamplingParam>> restResponse = samplingParamFeignService.findBySamplingConfigIds(configIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoSamplingParam> findByIds(Collection<String> ids) {
        RestResponse<List<DtoSamplingParam>> restResponse = samplingParamFeignService.findByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    public void setSamplingParamFeignService(ISamplingParamFeignService samplingParamFeignService) {
        this.samplingParamFeignService = samplingParamFeignService;
    }
}
