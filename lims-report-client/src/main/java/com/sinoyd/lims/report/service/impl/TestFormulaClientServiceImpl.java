package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoTestFormula;
import com.sinoyd.lims.report.feign.ITestFormulaFeignService;
import com.sinoyd.lims.report.service.ITestFormulaClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式对外接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Service
@Slf4j
public class TestFormulaClientServiceImpl implements ITestFormulaClientService {

    private ITestFormulaFeignService testFormulaFeignService;

    @Override
    public List<DtoTestFormula> findByConfigIds(Collection<String> configIds) {
        RestResponse<List<DtoTestFormula>> restResponse = testFormulaFeignService.findByObjectIds(configIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoTestFormula> findByFormulaIdAndTestId(String formulaId, String testId) {
        RestResponse<List<DtoTestFormula>> restResponse = testFormulaFeignService.findByFormulaIdAndTestId(formulaId, testId);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoTestFormula> findAllByIds(Collection<String> ids) {
        RestResponse<List<DtoTestFormula>> restResponse = testFormulaFeignService.findAllByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoTestFormula> findByTestIds(Collection<String> testIds) {
        RestResponse<List<DtoTestFormula>> restResponse = testFormulaFeignService.findByTestIds(testIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    public void setTestFormulaFeignService(ITestFormulaFeignService testFormulaFeignService) {
        this.testFormulaFeignService = testFormulaFeignService;
    }
}
