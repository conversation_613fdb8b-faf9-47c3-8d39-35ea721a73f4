package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoTestFormulaEquation;
import com.sinoyd.lims.report.feign.ITestFormulaEquationFeignService;
import com.sinoyd.lims.report.service.ITestFormulaEquationClientService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程对外接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/07/08
 **/
@Service
public class TestFormulaEquationClientServiceImpl implements ITestFormulaEquationClientService {

    private final ITestFormulaEquationFeignService testFormulaEquationFeignService;

    public TestFormulaEquationClientServiceImpl(ITestFormulaEquationFeignService testFormulaEquationFeignService) {
        this.testFormulaEquationFeignService = testFormulaEquationFeignService;
    }

    @Override
    public List<DtoTestFormulaEquation> findByTestFormulaIds(Collection<String> testFormulaIds) {
        RestResponse<List<DtoTestFormulaEquation>> restResponse = testFormulaEquationFeignService.findByTestFormulaIds(testFormulaIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }
}
