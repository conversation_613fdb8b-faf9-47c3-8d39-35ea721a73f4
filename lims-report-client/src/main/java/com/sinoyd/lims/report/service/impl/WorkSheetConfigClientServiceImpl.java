package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoWorkSheetConfig;
import com.sinoyd.lims.report.feign.IWorkSheetConfigFeignService;
import com.sinoyd.lims.report.service.IWorkSheetConfigClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 原始记录单配置对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/03
 */
@Service
public class WorkSheetConfigClientServiceImpl implements IWorkSheetConfigClientService {

    private IWorkSheetConfigFeignService workSheetConfigFeignService;

    @Override
    public List<DtoWorkSheetConfig> findByIds(Collection<String> ids) {
        RestResponse<List<DtoWorkSheetConfig>> response = workSheetConfigFeignService.findByIds(ids);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoWorkSheetConfig> findByTestIds(Collection<String> testIds) {
        RestResponse<List<DtoWorkSheetConfig>> response = workSheetConfigFeignService.findByTestIds(testIds);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Autowired
    public void setWorkSheetConfigFeignService(IWorkSheetConfigFeignService workSheetConfigFeignService) {
        this.workSheetConfigFeignService = workSheetConfigFeignService;
    }
}
