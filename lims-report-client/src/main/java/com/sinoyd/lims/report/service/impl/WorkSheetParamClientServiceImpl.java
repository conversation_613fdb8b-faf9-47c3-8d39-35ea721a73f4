package com.sinoyd.lims.report.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.report.dto.DtoWorkSheetParam;
import com.sinoyd.lims.report.feign.IWorkSheetParamFeignService;
import com.sinoyd.lims.report.service.IWorkSheetParamClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数对外接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/7
 */
@Service
@Slf4j
public class WorkSheetParamClientServiceImpl implements IWorkSheetParamClientService {

    private IWorkSheetParamFeignService workSheetParamFeignService;

    @Override
    public List<DtoWorkSheetParam> findByWorkSheetConfigId(Collection<String> configIds) {
        RestResponse<List<DtoWorkSheetParam>> restResponse = workSheetParamFeignService.findByWorkSheetConfigIds(configIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoWorkSheetParam> findByIds(Collection<String> ids) {
        RestResponse<List<DtoWorkSheetParam>> restResponse = workSheetParamFeignService.findByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    public void setWorkSheetParamFeignService(IWorkSheetParamFeignService workSheetParamFeignService) {
        this.workSheetParamFeignService = workSheetParamFeignService;
    }
}
