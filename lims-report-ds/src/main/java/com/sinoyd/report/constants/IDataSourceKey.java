package com.sinoyd.report.constants;

/**
 * 数据源MapKey值常量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
public interface IDataSourceKey {
    /**
     * 分析数据
     */
    String ANALYSE_DATA_LIST = "analyseDataList";

    /**
     * 分析分组数据（SampleId->AnalyseDataList）
     */
    String ANALYSE_DATA_MAP_GROUP = "analyseDataMapGroup";

    /**
     * 样品数据集合(原样+质控样)
     */
    String SAMPLE_DATA_LIST = "sampleDataList";

    /**
     * 样品数据Map(ID->Sample)(原样+质控样)
     */
    String SAMPLE_DATA_MAP = "sampleDataMap";

    /**
     * 原样样品数据集合
     */
    String ASSOCIATE_SAMPLE_DATA_LIST = "associateSampleDataList";

    /**
     * 原样样品数据Map(ID->Sample)
     */
    String ASSOCIATE_SAMPLE_DATA_MAP = "associateSampleDataMap";

    /**
     * 质控样品数据集合
     */
    String QC_SAMPLE_DATA_LIST = "qcSampleDataList";

    /**
     * 质控样品数据Map(ID->Sample)
     */
    String QC_SAMPLE_DATA_MAP = "qcSampleDataMap";

    /**
     * 测试项目ID集合
     */
    String TEST_IDS = "testIdData";

    /**
     * 测试项目集合数据
     */
    String TEST_DATA_LIST = "testDataList";

    /**
     * 测试项目数据集合Map(ID->Test)
     */
    String TEST_DATA_MAP = "testDataMap";

    /**
     * TODO:数据源策略>点位,点位ID集合
     */
    String FOLDER_IDS = "folderIdData";

    /**
     * TODO:数据源策略>点位,送样单ID集合
     */
    String RECEIVE_IDS = "receiveIdData";

    /**
     * TODO:数据源策略>点位,点位数据集合
     */
    String SAMPLE_FOLDER_DATA_LIST = "sampleFolderDataList";

    /**
     * TODO:数据源策略>点位,点位数据集合Map(ID->SampleFolder)
     */
    String SAMPLE_FOLDER_DATA_MAP = "sampleFolderDataMap";

    /**
     * 参数数据集合
     */
    String PARAMS_DATA_LIST = "paramsDataList";

    /**
     * 参数分组Map(SampleId->ParamsData集合)
     */
    String PARAMS_MAP_GROUP = "paramsMapGroup";

    /**
     * 仪器使用记录数据集合
     */
    String INSTRUMENT_USE_RECORD_DATA_LIST = "instrumentUseRecordDataList";

    /**
     * 项目数据(单条：用于报告生成 可能为null)
     */
    String PROJECT_DATA = "projectData";

    /**
     * 项目数据集合
     */
    String PROJECT_DATA_LIST = "projectDataList";

    /**
     * 项目数据Map(ID->Project)
     */
    String PROJECT_DATA_MAP = "projectDataMap";

    /**
     * 报告数据(单条：用于报告生成 可能为null)
     */
    String REPORT_DATA = "reportData";

    /**
     * 报告数据集合
     */
    String REPORT_DATA_LIST = "reportDataList";

    /**
     * 报告数据Map(ID->Report)
     */
    String REPORT_DATA_MAP = "reportDataMap";

    /**
     * 报告基本数据(单条：用于报告生成 可能为null)
     */
    String REPORT_BASE_INFO_DATA = "reportBaseInfoData";

    /**
     * 报告基本数据集合
     */
    String REPORT_BASE_INFO_LIST = "reportBaseInfoDataList";

    /**
     * 报告基本数据Map(ReportId->ReportBaseInfo)
     */
    String REPORT_BASE_INFO_MAP = "reportBaseInfoDataMap";

    /**
     * 报告点位数据集合
     */
    String REPORT_FOLDER_INFO_LIST = "reportFolderInfoDataList";

}
