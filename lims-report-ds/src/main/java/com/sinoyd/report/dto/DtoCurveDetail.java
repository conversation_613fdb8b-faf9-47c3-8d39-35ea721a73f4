package com.sinoyd.report.dto;

import com.sinoyd.report.entity.CurveDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 曲线明细详情实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_DATA_CurveDetail")
@Data
@DynamicInsert
public class DtoCurveDetail extends CurveDetail {
}
