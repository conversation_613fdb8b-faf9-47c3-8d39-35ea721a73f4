package com.sinoyd.report.dto;

import com.sinoyd.report.entity.EvaluationRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 评价明细实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_DATA_EvaluationRecord")
@Data
@DynamicInsert
public class DtoEvaluationRecord extends EvaluationRecord {
}
