package com.sinoyd.report.dto;

import com.sinoyd.report.entity.InstrumentUseRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 仪器使用信息实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_DATA_InstrumentUseRecord")
@Data
@DynamicInsert
public class DtoInstrumentUseRecord extends InstrumentUseRecord {
}
