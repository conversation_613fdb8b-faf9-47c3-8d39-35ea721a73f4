package com.sinoyd.report.dto;

import com.sinoyd.report.entity.Sub2Sample;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 领样单和样品关系实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_DATA_Sub2Sample")
@Data
@DynamicInsert
public class DtoSub2Sample extends Sub2Sample {
}
