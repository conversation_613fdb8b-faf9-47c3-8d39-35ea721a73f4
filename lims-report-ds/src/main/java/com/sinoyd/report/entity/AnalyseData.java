package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 数据信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class AnalyseData extends LimsBaseEntity {

    public AnalyseData() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 数据id
     */
    private String analyseDataId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 小工作单id
     */
    private String workSheetId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 国标名称
     */
    private String redCountryStandard;

    /**
     * 年度
     */
    private String yearSn;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 分析项目id
     */
    private String analyseItemId;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控类别
     */
    private Integer qcGrade;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检测结果
     */
    private String testOrignValue;

    /**
     * 检测结果修约值
     */
    private String testValueDstr;

    /**
     * 分析人员
     */
    private String analystName;

    /**
     * 数据分析时间
     */
    private Date analyzeTime;

    /**
     * 分析完成时间
     */
    private Date finishTime;

    /**
     * 有效性
     */
    private Boolean isDataEnabled;

    /**
     * 是否在现场完成
     */
    private Boolean isCompleteField;

    /**
     * 是否分析分包
     */
    private Boolean isOutsourcing;

    /**
     * 是否采样分包
     */
    private Boolean isSamplingOut;

    /**
     * 采集编号
     */
    private String gatherCode;

    /**
     * 质控信息
     */
    private String qcInfo;

    /**
     * 串联中间结果
     */
    private String seriesValue;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
