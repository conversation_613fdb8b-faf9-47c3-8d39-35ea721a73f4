package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 曲线信息
 *
 * <AUTHOR>
 * @version V6.0.0 
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class Curve extends LimsBaseEntity {

    public Curve(){
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }
    
    /**
     * id
     */
    @Id
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 校准日期
     */
    private Date checkDate;

    /**
     * 相关系数
     */
    private String coefficient;

    /**
     * 配置日期
     */
    private Date configDate;

    /**
     * 曲线零点
     */
    private String zeroPoint;

    /**
     * 斜率a
     */
    private String kValue;

    /**
     * 截距b
     */
    private String bValue;

    /**
     * 实数c
     */
    private String cValue;

    /**
     * 是否双曲线
     */
    private Boolean isDouble;

    /**
     * 曲线类型
     */
    private String curveType;

    /**
     * 曲线模型
     */
    private String curveMode;

    /**
     * 曲线信息
     */
    private String curveInfo;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
