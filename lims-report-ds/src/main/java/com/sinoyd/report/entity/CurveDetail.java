package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 曲线明细详情
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class CurveDetail extends LimsBaseEntity {

    public CurveDetail(){
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 曲线id
     */
    private String curveId;

    /**
     * 分析编号
     */
    private String analyseCode;

    /**
     * 标准溶液加入体积
     */
    private String addVolume;

    /**
     * 标准物加入量
     */
    private String addAmount;

    /**
     * 吸光度A
     */
    private String absorbance;

    /**
     * 减空白吸光度
     */
    private String lessBlankAbsorbance;

    /**
     * 吸光度B
     */
    private String absorbanceB;

    /**
     * 相对偏差
     */
    private String relativeDeviation;

    /**
     * 220吸光度
     */
    private String aValueTTZ;

    /**
     * 275吸光度
     */
    private String aValueTSF;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
