package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 评价明细
 *
 * <AUTHOR>
 * @version V6.0.0 
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class EvaluationRecord extends LimsBaseEntity {

    public EvaluationRecord(){
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 类型
     */
    private Integer objectType;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 评价等级id
     */
    private String evaluationLevelId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 上限运算符
     */
    private String upperLimitSymble;

    /**
     * 上限
     */
    private String upperLimitValue;

    /**
     * 下限运算符
     */
    private String lowerLimitSymble;

    /**
     * 下限
     */
    private String lowerLimitValue;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
