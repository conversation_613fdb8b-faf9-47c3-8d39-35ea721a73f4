package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 仪器使用信息
 *
 * <AUTHOR>
 * @version V6.0.0 
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class InstrumentUseRecord extends LimsBaseEntity {

    public InstrumentUseRecord(){
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器型号
     */
    private String instrumentModel;

    /**
     * 仪器编号
     */
    private String instrumentCode;

    /**
     * 仪器本站编号
     */
    private String instrumentSerialNo;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 使用类型
     */
    private Integer objectType;

    /**
     * 使用人名称
     */
    private String usePersonName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 测试项目ids
     */
    private String testIds;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 大气压
     */
    private String pressure;

    /**
     * 使用前情况
     */
    private String beforeUseSituation;

    /**
     * 使用后情况
     */
    private String beforeAfterSituation;

    /**
     * 是否辅助仪器
     */
    private Boolean isAssistInstrument;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}