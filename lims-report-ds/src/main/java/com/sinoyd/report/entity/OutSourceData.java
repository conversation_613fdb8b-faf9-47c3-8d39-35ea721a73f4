package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 分包信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class OutSourceData extends LimsBaseEntity {

    public OutSourceData() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 数据id
     */
    private String analyseDataId;

    /**
     * 分析名称
     */
    private String analyzeMethodName;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 分析日期
     */
    private Date analyzeTime;

    /**
     * 分析结束日期
     */
    private Date analyzeEndTime;

    /**
     * 分包商
     */
    private String subcontractor;

    /**
     * CMA编号
     */
    private String cmaCode;

    /**
     * 分包报告编号
     */
    private String outSourceReportCode;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}