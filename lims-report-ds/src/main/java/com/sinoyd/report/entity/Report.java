package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 报告信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class Report extends LimsBaseEntity {

    public Report() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 报告编号
     */
    private String code;

    /**
     * 报告编制日期
     */
    private Date reportDate;

    /**
     * 报告签发日期
     */
    private Date SignDate;

    /**
     * 报告签发人
     */
    private String SignPerson;


    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 报告年份
     */
    private Integer reportYear;

    /**
     * 分析项目排序id
     */
    private String analyseItemSortId;

    /**
     * 点位排序id
     */
    private String folderSortId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
