package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 样品制备信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class SamplePreparation extends LimsBaseEntity {


    public SamplePreparation() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 制备样品的分析项目名称
     */
    private String analyzeItemNames;

    /**
     * 制备开始时间
     */
    private Date preparationBeginTime;

    /**
     * 制备结束时间
     */
    private Date preparationEndTime;

    /**
     * 制备人名称
     */
    private String preparedPersonName;

    /**
     * 制备方法
     */
    private String method;

    /**
     * 制备内容
     */
    private String content;

    /**
     * 制备仪器ids
     */
    private String instrumentId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
