package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 试剂配置记录信息
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class WorksheetReagent extends LimsBaseEntity {


    public WorksheetReagent() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 试剂配置记录id
     */
    private String reagentConfigId;

    /**
     * 配置记录
     */
    private String reagent;

    /**
     * 需求的配置过程
     */
    private String context;

    /**
     * 试剂名称
     */
    private String reagentName;

    /**
     * 试剂规格
     */
    private String reagentSpecification;

    /**
     * 配置溶液
     */
    private String configurationSolution;

    /**
     * 配置日期
     */
    private Date configDate;

    /**
     * 有效期
     */
    private Date expiryDate;

    /**
     * 稀释过程记录
     */
    private String course;

    /**
     * 稀释液
     */
    private String diluent;

    /**
     * 试剂类型
     */
    private Integer reagentType;

    /**
     * 适用项目
     */
    private String suitItem;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
