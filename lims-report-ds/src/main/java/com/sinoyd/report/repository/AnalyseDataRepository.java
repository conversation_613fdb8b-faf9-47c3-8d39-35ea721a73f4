package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoAnalyseData;

import java.util.Collection;
import java.util.List;

/**
 * 分析数据数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface AnalyseDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoAnalyseData, String>,
        LimsRepository<DtoAnalyseData, String> {

    /**
     * 根据检测单id查询分析数据
     *
     * @param workSheetFolderId  检测单id
     * @return                   分析数据集合
     */
    List<DtoAnalyseData> findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 根据样品id查询到分析数据
     *
     * @param sampleIds 样品id集合
     * @return 分析数据
     */
    List<DtoAnalyseData> findBySampleIdIn(Collection<String> sampleIds);

    /**
     * 根据工作单id集合查询分析数据
     *
     * @param workSheetFolderIds 工作单id集合
     * @return 分析数据
     */
    List<DtoAnalyseData> findByWorkSheetFolderIdIn(Collection<String> workSheetFolderIds);

}
