package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoCurveDetail;

import java.util.List;

/**
 * 标准曲线信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface CurveDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoCurveDetail, String>,
        LimsRepository<DtoCurveDetail, String> {

    /**
     * 根据曲线id列表查询标准曲线明细数据
     *
     * @param curveIdList 检测单id
     * @return 分析数据集合
     */
    List<DtoCurveDetail> findByCurveIdIn(List<String> curveIdList);
}
