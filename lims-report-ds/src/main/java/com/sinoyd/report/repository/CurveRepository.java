package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoCurve;
import com.sinoyd.report.dto.DtoQualityControlEvaluate;

import java.util.List;

/**
 * 标准曲线信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface CurveRepository extends IBaseJpaPhysicalDeleteRepository<DtoCurve, String>,
        LimsRepository<DtoCurve, String> {

    /**
     * 根据检测单id查询标准曲线数据
     *
     * @param workSheetFolderId  检测单id
     * @return                   分析数据集合
     */
    List<DtoCurve> findByWorkSheetFolderId(String workSheetFolderId);
}
