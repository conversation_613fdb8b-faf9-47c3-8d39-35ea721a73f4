package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoInstrumentUseRecord;

import java.util.Collection;
import java.util.List;

/**
 * 标准曲线信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface InstrumentUseRecordRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentUseRecord, String>,
        LimsRepository<DtoInstrumentUseRecord, String> {

    /**
     * 根据曲线id列表查询标准曲线明细数据
     *
     * @param objectId   关联id
     * @param ObjectType 类型
     * @return 分析数据集合
     */
    List<DtoInstrumentUseRecord> findByObjectIdAndObjectType(String objectId, int ObjectType);

    /**
     * 根据对象id查询仪器使用记录
     *
     * @param objectIds 对象id集合（采样单id,领养单id,工作单id）
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdIn(Collection<String> objectIds);
}
