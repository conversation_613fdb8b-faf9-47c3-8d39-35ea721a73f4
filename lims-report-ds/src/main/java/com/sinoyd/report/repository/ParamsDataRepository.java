package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoQualityControlEvaluate;

import java.util.Collection;
import java.util.List;

/**
 * 参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface ParamsDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoParamsData, String>,
        LimsRepository<DtoParamsData, String> {

    /**
     * 根据关联数据id查询参数信息
     *
     * @param objectIdList  关联数据id列表
     * @return                   分析数据集合
     */
    List<DtoParamsData> findByObjectIdIn(Collection<String> objectIdList);
}
