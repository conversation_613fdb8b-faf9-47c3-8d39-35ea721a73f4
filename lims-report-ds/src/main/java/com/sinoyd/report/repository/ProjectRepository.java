package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoProject;

import java.util.Collection;
import java.util.List;

/**
 * 项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface ProjectRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject, String>,
        LimsRepository<DtoProject, String> {

    /**
     * 根据项目id查询
     *
     * @param projectIdList 项目id列表
     * @return 项目列表
     */
    List<DtoProject> findByProjectIdIn(Collection<String> projectIdList);
}
