package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoQualityControlEvaluate;

import java.util.List;

/**
 * 质控评价信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface QualityControlEvaluateRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoQualityControlEvaluate, String>,
        LimsRepository<DtoQualityControlEvaluate, String> {

    /**
     * 根据关联数据id查询质控评价数据
     *
     * @param objectIdList  关联数据id列表
     * @return                   分析数据集合
     */
    List<DtoQualityControlEvaluate> findByObjectIdIn(List<String> objectIdList);
}
