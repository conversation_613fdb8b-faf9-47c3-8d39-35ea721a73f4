package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoQualityControl;

/**
 * 质控信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface QualityControlRepository extends IBaseJpaPhysicalDeleteRepository<DtoQualityControl, String>,
        LimsRepository<DtoQualityControl, String> {
}
