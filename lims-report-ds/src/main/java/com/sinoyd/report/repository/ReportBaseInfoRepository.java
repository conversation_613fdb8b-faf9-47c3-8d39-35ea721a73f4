package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoReportBaseInfo;

import java.util.Collection;
import java.util.List;

/**
 * 报告基础信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface ReportBaseInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportBaseInfo, String>,
        LimsRepository<DtoReportBaseInfo, String> {

    /**
     * 根据报告Id集合获取报告基本数据
     *
     * @param reportIds 报告
     * @return 报告基础数据
     */
    List<DtoReportBaseInfo> findByReportIdIn(Collection<String> reportIds);
}
