package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoReportFolderInfo;

import java.util.Collection;
import java.util.List;

/**
 * 报告点位基础信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface ReportFolderInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportFolderInfo, String>,
        LimsRepository<DtoReportFolderInfo, String> {

    /**
     * 根据报告id集合获取报告点位数据
     *
     * @param reportIds 报告id集合
     * @return 报告点位数据
     */
    List<DtoReportFolderInfo> findByReportIdIn(Collection<String> reportIds);
}
