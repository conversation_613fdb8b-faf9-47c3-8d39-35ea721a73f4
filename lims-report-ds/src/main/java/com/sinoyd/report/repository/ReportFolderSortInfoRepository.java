package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoReportFolderSortInfo;

/**
 * 报告点位排序基础数据数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/29
 */
public interface ReportFolderSortInfoRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoReportFolderSortInfo, String>,
        LimsRepository<DtoReportFolderSortInfo, String> {
}
