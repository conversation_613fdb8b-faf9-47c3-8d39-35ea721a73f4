package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoReport;

/**
 * 报告数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface ReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoReport, String>,
        LimsRepository<DtoReport, String> {
}
