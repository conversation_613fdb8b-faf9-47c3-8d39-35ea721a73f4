package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoSampleGroup;

/**
 * 样品分组数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/29
 */
public interface SampleGroupRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleGroup, String>,
        LimsRepository<DtoSampleGroup, String> {
}
