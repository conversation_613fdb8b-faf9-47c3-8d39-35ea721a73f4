package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoSample;

import java.util.Collection;
import java.util.List;

/**
 * 样品数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface SampleRepository extends IBaseJpaPhysicalDeleteRepository<DtoSample, String>,
        LimsRepository<DtoSample, String> {

    /**
     * 查询样品下的质控样数据
     *
     * @param sampleIds 原样id集合
     * @return 质控样品数据
     */
    List<DtoSample> findByAssociateSampleIdIn(Collection<String> sampleIds);

    /**
     * 根据样品id集合查询样品数据
     *
     * @param sampleIds 样品id集合
     * @return 样品数据
     */
    List<DtoSample> findBySampleIdIn(Collection<String> sampleIds);


    /**
     * 根据送样单查询样品
     *
     * @param receiveIds 送样单id集合
     * @return 样品数据
     */
    List<DtoSample> findByReceiveIdIn(Collection<String> receiveIds);
}
