package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoWorksheetFolder;

/**
 * 工作单信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface WorkSheetFolderRepository extends IBaseJpaPhysicalDeleteRepository<DtoWorksheetFolder, String>,
        LimsRepository<DtoWorksheetFolder, String> {

    /**
     * 根据检测单id查询检测单
     *
     * @param workSheetFolderId 检测单id
     * @return 检测单
     */
    DtoWorksheetFolder findByWorkSheetFolderId(String workSheetFolderId);
}
