package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.report.dto.DtoWorksheetReagent;

import java.util.List;

/**
 * 标准曲线信息数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface WorkSheetReagentRepository extends IBaseJpaPhysicalDeleteRepository<DtoWorksheetReagent, String>,
        LimsRepository<DtoWorksheetReagent, String> {

    /**
     * 根据曲线id列表查询试剂配置记录列表
     *
     * @param workSheetFolderId 检测单id
     * @return 试剂配置记录列表
     */
    List<DtoWorksheetReagent> findByWorkSheetFolderId(String workSheetFolderId);
}
