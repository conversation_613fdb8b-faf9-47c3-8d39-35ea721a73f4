package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoAnalyseData;

import java.util.Collection;
import java.util.List;

/**
 * 分析数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
public interface AnalyseDataService extends LimsBaseService<DtoAnalyseData, String> {

    /**
     * 根据样品id查询到分析数据
     *
     * @param sampleIds 样品id集合
     * @return 分析数据
     */
    List<DtoAnalyseData> findBySampleIdIn(Collection<String> sampleIds);

    /**
     * 根据工作单id集合查询分析数据
     *
     * @param workSheetFolderIds 工作单id集合
     * @return 分析数据
     */
    List<DtoAnalyseData> findByWorkSheetFolderIdIn(Collection<String> workSheetFolderIds);
}
