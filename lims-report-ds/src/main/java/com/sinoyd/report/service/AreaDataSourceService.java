package com.sinoyd.report.service;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.vo.AreaRelationDataVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 原始记录单区域数据处理接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
public interface AreaDataSourceService {

    /**
     * 处理区域对应的数据Map
     *
     * @param businessParamVO 业务数据
     * @param areaConfigs     区域配置
     * @return 数据Map
     */
    Map<DtoAreaConfig, Map<String, Object>> findAreaDataMap(ExcelBusinessParamVO businessParamVO, List<DtoAreaConfig> areaConfigs, AreaRelationDataVO areaRelationData);

    /**
     * 处理区域关联数据Map
     *
     * @param areaConfigs      区域数据
     * @param areaRelationData 区域关联数据实体
     */
    void fillAreaRelationMap(Collection<DtoAreaConfig> areaConfigs, AreaRelationDataVO areaRelationData);
}
