package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoInstrumentUseRecord;

import java.util.Collection;
import java.util.List;

/**
 * 仪器使用记录服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/29
 */
public interface InstrumentUseRecordService extends LimsBaseService<DtoInstrumentUseRecord, String> {

    /**
     * 根据对象id查询仪器使用记录
     *
     * @param objectIds 对象id集合（采样单id,领养单id,工作单id）
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdIn(Collection<String> objectIds);
}
