package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoParamsData;

import java.util.Collection;
import java.util.List;

/**
 * 参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
public interface ParamsDataService extends LimsBaseService<DtoParamsData, String> {

    /**
     * 查询对应参数数据
     *
     * @param objectIds 对象id集合
     * @return 参数数据
     */
    List<DtoParamsData> findByObjectIdIn(Collection<String> objectIds);
}
