package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoProject;

import java.util.Collection;
import java.util.List;

/**
 * 项目服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public interface ProjectService extends LimsBaseService<DtoProject, String> {

    /**
     * 根据项目id集合获取项目数据
     *
     * @param projectIds 项目id集合
     * @return 项目数据
     */
    List<DtoProject> findByProjectIdIn(Collection<String> projectIds);
}
