package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoReportBaseInfo;

import java.util.Collection;
import java.util.List;

/**
 * 报告基础信息服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
public interface ReportBaseInfoService extends LimsBaseService<DtoReportBaseInfo, String> {

    /**
     * 根据报告Id集合获取报告基本数据
     *
     * @param reportIds 报告
     * @return 报告基础数据
     */
    List<DtoReportBaseInfo> findByReportIdIn(Collection<String> reportIds);
}
