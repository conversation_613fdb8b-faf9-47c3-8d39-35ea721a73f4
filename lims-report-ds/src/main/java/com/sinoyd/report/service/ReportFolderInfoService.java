package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoReportFolderInfo;

import java.util.Collection;
import java.util.List;

/**
 * 报告点位基础信息服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
public interface ReportFolderInfoService extends LimsBaseService<DtoReportFolderInfo,String> {

    /**
     * 根据报告id集合获取报告点位数据
     *
     * @param reportIds 报告id集合
     * @return 报告点位数据
     */
    List<DtoReportFolderInfo> findByReportIdIn(Collection<String> reportIds);
}
