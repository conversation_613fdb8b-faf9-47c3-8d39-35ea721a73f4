package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoReportSampleInfo;

import java.util.Collection;
import java.util.List;

/**
 * 报告样品关联服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/19
 */
public interface ReportSampleInfoService extends LimsBaseService<DtoReportSampleInfo, String> {

    /**
     * 根据报告Id查询到关联样品数据
     *
     * @param reportId 报告id
     * @return 关联样品数据
     */
    List<DtoReportSampleInfo> findByReportId(String reportId);

    /**
     * 根据样品id集合获取样品关联报告数据
     *
     * @param sampleIds 样品id集合
     * @return 报告样品关联数据
     */
    List<DtoReportSampleInfo> findBySampleIdIn(Collection<String> sampleIds);
}
