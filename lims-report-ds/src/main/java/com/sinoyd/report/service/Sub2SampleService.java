package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSub2Sample;

import java.util.Collection;
import java.util.List;

/**
 * 领养单关联样品服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/29
 */
public interface Sub2SampleService extends LimsBaseService<DtoSub2Sample, String> {

    /**
     * 根据样品id集合查询关联领养单数据
     *
     * @param sampleIds 样品id集合
     * @return 关联领养单数据
     */
    List<DtoSub2Sample> findBySampleIdIn(Collection<String> sampleIds);
}
