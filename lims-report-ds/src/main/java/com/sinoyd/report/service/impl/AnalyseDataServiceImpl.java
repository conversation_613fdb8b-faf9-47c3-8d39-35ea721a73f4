package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.repository.AnalyseDataRepository;
import com.sinoyd.report.service.AnalyseDataService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 分析数据服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
@Service
public class AnalyseDataServiceImpl extends LimsBaseServiceImpl<DtoAnalyseData, String, AnalyseDataRepository> implements AnalyseDataService {

    /**
     * 根据样品id查询到分析数据
     *
     * @param sampleIds 样品id集合
     * @return 分析数据
     */
    @Override
    public List<DtoAnalyseData> findBySampleIdIn(Collection<String> sampleIds) {
        if (StringUtils.isNotEmpty(sampleIds)) {
            return repository.findBySampleIdIn(sampleIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据工作单id集合查询分析数据
     *
     * @param workSheetFolderIds 工作单id集合
     * @return 分析数据
     */
    @Override
    public List<DtoAnalyseData> findByWorkSheetFolderIdIn(Collection<String> workSheetFolderIds) {
        if (StringUtils.isNotEmpty(workSheetFolderIds)){
            return repository.findByWorkSheetFolderIdIn(workSheetFolderIds);
        }
        return new ArrayList<>();
    }
}
