package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.report.config.WebParamConfig;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.enums.EnumDataSetType;
import com.sinoyd.report.service.ApiService;
import com.sinoyd.report.service.AreaDataSourceService;
import com.sinoyd.report.service.DataSetApplyColumnService;
import com.sinoyd.report.service.DataSetService;
import com.sinoyd.report.strategy.buiness.context.BusinessParamContext;
import com.sinoyd.report.util.ExcelApiExecuteUtil;
import com.sinoyd.report.util.ExcelJdbcExecuteUtil;
import com.sinoyd.report.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始记录单区域数据处理接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
@Service
public class AreaDataSourceServiceImpl implements AreaDataSourceService {

    private DataSetApplyColumnService dataSetApplyColumnService;

    private DataSetService dataSetService;

    private ApiService apiService;

    private BusinessParamContext businessParamContext;

    private WebParamConfig webParamConfig;

    /**
     * 处理区域对应的数据Map
     *
     * @param businessParamVO 业务参数
     * @param areaConfigs     区域配置
     * @return 数据Map
     */
    @Override
    public Map<DtoAreaConfig, Map<String, Object>> findAreaDataMap(ExcelBusinessParamVO businessParamVO, List<DtoAreaConfig> areaConfigs, AreaRelationDataVO areaRelationData) {
        //处理业务参数类型Id
        businessParamContext.handleBusinessParams(businessParamVO);
        //处理区域关联数据Map
        fillAreaRelationMap(areaConfigs, areaRelationData);
        return executeAreaConfig(areaConfigs, areaRelationData.getAreaIdToDataSetMap(), areaRelationData.getAreaIdToApiMap(), businessParamVO);
    }


    /**
     * 处理区域关联数据Map
     *
     * @param areaConfigs      区域数据
     * @param areaRelationData 区域关联数据实体
     */
    @Override
    public void fillAreaRelationMap(Collection<DtoAreaConfig> areaConfigs, AreaRelationDataVO areaRelationData) {
        Map<String, List<DtoDataSet>> areaIdToDataSetMap = areaRelationData.getAreaIdToDataSetMap();
        Map<String, List<DtoApi>> areaIdToApiMap = areaRelationData.getAreaIdToApiMap();
        //获取到所有的区域数据集应用数据
        List<DtoAreaConfig2DataSetApplyColumn> area2DSApplyColumn = new ArrayList<>();
        for (DtoAreaConfig areaConfig : areaConfigs) {
            area2DSApplyColumn.addAll(areaConfig.getDataSetApplyColumns());
        }
        //获取所有的数据集应用类数据
        List<String> dsApplyColumnIds = area2DSApplyColumn.stream().map(DtoAreaConfig2DataSetApplyColumn::getDataSetApplyColumnId).collect(Collectors.toList());
        List<DtoDataSetApplyColumn> dataSetApplyColumns = dataSetApplyColumnService.findAll(dsApplyColumnIds);
        //获取对应的数据集列
        List<String> dataSetIds = findFilterIds(dataSetApplyColumns, EnumDataSetType.SQL数据集.getValue());
        List<String> apiIds = findFilterIds(dataSetApplyColumns, EnumDataSetType.API接口.getValue());
        //获取数据集配置以及Api接口数据
        List<DtoDataSet> dataSetList = dataSetService.findAll(dataSetIds);
        List<DtoApi> apiList = apiService.findAll(apiIds);
        //区域下占位符数据
        Map<String, List<ExcelPlaceholderVO>> placeholderMap = new HashMap<>();
        for (DtoAreaConfig areaConfig : areaConfigs) {
            List<ExcelPlaceholderVO> placeholderList = new ArrayList<>();
            //获取区域下的数据集应用id
            List<String> dsApplyColIdsOfArea = areaConfig.getDataSetApplyColumns()
                    .stream().map(DtoAreaConfig2DataSetApplyColumn::getDataSetApplyColumnId).collect(Collectors.toList());
            //获取区域下的数据集应用数据
            List<DtoDataSetApplyColumn> dsApplyColOfArea = dataSetApplyColumns.stream()
                    .filter(p -> dsApplyColIdsOfArea.contains(p.getId())).collect(Collectors.toList());
            for (DtoDataSetApplyColumn dsApplyCol : dsApplyColOfArea) {
                placeholderList.add(new ExcelPlaceholderVO(dsApplyCol.getPlaceholderName(), dsApplyCol.getColumnCode(),dsApplyCol.getDataSetColumnId()));
            }
            //获取对应的数据集列Id
            List<String> dataSetIdsOfArea = findFilterIds(dsApplyColOfArea, EnumDataSetType.SQL数据集.getValue());
            List<String> apiIdsOfArea = findFilterIds(dsApplyColOfArea, EnumDataSetType.API接口.getValue());
            //获取对应的数据集
            List<DtoDataSet> dataSetOfArea = dataSetList.stream().filter(p -> dataSetIdsOfArea.contains(p.getId())).collect(Collectors.toList());
            List<DtoApi> apiOfArea = apiList.stream().filter(p -> apiIdsOfArea.contains(p.getId())).collect(Collectors.toList());
            //填充关联Map
            areaIdToDataSetMap.put(areaConfig.getId(), dataSetOfArea);
            areaIdToApiMap.put(areaConfig.getId(), apiOfArea);
            placeholderMap.put(areaConfig.getId(), placeholderList);
        }
        areaRelationData.setAreaIdToDataSetMap(areaIdToDataSetMap);
        areaRelationData.setAreaIdToApiMap(areaIdToApiMap);
        areaRelationData.setPlaceholderToColMap(placeholderMap);
    }

    /**
     * 获取过滤后的数据集Id/ApiId集合
     *
     * @param dsApplyCols 数据集列应用数据
     * @param type        需要获取的id类型
     * @return 数据集Id/ApiId集合
     */
    private List<String> findFilterIds(List<DtoDataSetApplyColumn> dsApplyCols, Integer type) {
        return dsApplyCols.stream().filter(p -> type.equals(p.getDataSetType()))
                .map(DtoDataSetApplyColumn::getDataSetId).collect(Collectors.toList());
    }

    /**
     * 获取区域对应的数据集查询结果数据
     *
     * @param areaConfigs        区域配置数据
     * @param areaIdToDataSetMap 区域对应数据集关联Map 区域id->数据集数据
     * @param areaIdToApiMap     区域对应Api关联Map   区域id->Api数据
     * @param businessParamVO    业务参数
     * @return 区域对应数据集数据Map 区域数据->数据集请求结果数据
     */
    protected Map<DtoAreaConfig, Map<String, Object>> executeAreaConfig(List<DtoAreaConfig> areaConfigs,
                                                                        Map<String, List<DtoDataSet>> areaIdToDataSetMap,
                                                                        Map<String, List<DtoApi>> areaIdToApiMap,
                                                                        ExcelBusinessParamVO businessParamVO) {
        Map<DtoAreaConfig, Map<String, Object>> areaConfigToDSMap = new HashMap<>();

        for (DtoAreaConfig areaConfig : areaConfigs) {
            //区域下数据集
            List<DtoDataSet> dataSetOfArea = areaIdToDataSetMap.get(areaConfig.getId());
            //请求所有数据集SQL
            Map<String, Object> areaDataMap = new HashMap<>(executeDataSet(dataSetOfArea, businessParamVO));
            //区域下Api
            List<DtoApi> apiOfArea = areaIdToApiMap.get(areaConfig.getId());
            //请求所有接口，获取数据
            areaDataMap.putAll(executeApi(apiOfArea, businessParamVO));
            //处理区域对应数据Map
            areaConfigToDSMap.put(areaConfig, areaDataMap);
        }
        return areaConfigToDSMap;
    }

    /**
     * 执行数据集
     *
     * @param dataSetList     数据集
     * @param businessParamVO 业务参数数据
     * @return 执行后的数据集Map 数据集编码->数据集列数据
     */
    protected Map<String, Object> executeDataSet(List<DtoDataSet> dataSetList,
                                                 ExcelBusinessParamVO businessParamVO) {
        Map<String, Object> dsResultMap = new HashMap<>();
        if (StringUtils.isNotEmpty(dataSetList)) {
            for (DtoDataSet dataSet : dataSetList) {
                //Sql执行参数
                Map<String, Object> sqlParamMap = new HashMap<>();
                for (DtoDataSetParam dsParams : dataSet.getDataSetParams()) {
                    sqlParamMap.put(dsParams.getParamName(),
                            businessParamVO.getBusinessTypeId(dsParams.getParamType(), ""));
                }
                JdbcExecuteVO executeVO = dataSetService.getExecuteVO(dataSet, sqlParamMap);
                //放置执行的结果数据
                dsResultMap.put(dataSet.getDataSetCode(), ExcelJdbcExecuteUtil.executeSQL(executeVO));
            }
        }
        return dsResultMap;
    }

    /**
     * 执行API接口
     *
     * @param apiList         Api数据
     * @param businessParamVO 业务参数数据
     * @return 执行后的数据集Map Api编码->数据集列数据
     */
    protected Map<String, Object> executeApi(List<DtoApi> apiList,
                                             ExcelBusinessParamVO businessParamVO) {
        Map<String, Object> apiResultMap = new HashMap<>();
        //请求所有接口，获取数据
        for (DtoApi api : apiList) {
            ApiExecuteVO apiExecuteVO = getApiExecuteVO(api, businessParamVO);
            //放置接口请求结果数据
            apiResultMap.put(api.getApiCode(), ExcelApiExecuteUtil.executeApi(apiExecuteVO));
        }
        return apiResultMap;
    }

    /**
     * 获取API接口验证对象
     *
     * @param api             接口数据实体
     * @param businessParamVO 业务参数
     * @return 接口验证对象
     */
    private ApiExecuteVO getApiExecuteVO(DtoApi api, ExcelBusinessParamVO businessParamVO) {
        ApiExecuteVO verifyVO = new ApiExecuteVO(webParamConfig.getApiHost(), api, getRequestParams(api.getApiParams(), businessParamVO));
        if (StringUtils.isNotEmpty(api.getAuthorizationApiId())) {
            DtoApi tokenApi = apiService.findOne(api.getAuthorizationApiId());
            verifyVO.setAuthorizationApi(getApiExecuteVO(tokenApi, businessParamVO));
        }
        return verifyVO;
    }

    /**
     * 获取请求参数数据
     *
     * @param params          api接口参数
     * @param businessParamVO 业务参数
     * @return 请求参数
     */
    private List<NameValuePair> getRequestParams(List<DtoApiParam> params, ExcelBusinessParamVO businessParamVO) {
        List<NameValuePair> requestParams = new ArrayList<>();
        for (DtoApiParam param : params) {
            //获取当前参数的值
            String parDefaultVal = StringUtils.isNotEmpty(param.getParamValue()) ? param.getParamValue() : "";
            //处理请求参数
            Object value = businessParamVO.getBusinessTypeId(param.getParamType(), parDefaultVal);
            NameValuePair requestParam = new NameValuePair(param.getParamName(), value);
            requestParams.add(requestParam);
        }
        return requestParams;
    }

    @Autowired
    public void setDataSetApplyColumnService(DataSetApplyColumnService dataSetApplyColumnService) {
        this.dataSetApplyColumnService = dataSetApplyColumnService;
    }

    @Autowired
    public void setDataSetService(DataSetService dataSetService) {
        this.dataSetService = dataSetService;
    }

    @Autowired
    public void setApiService(ApiService apiService) {
        this.apiService = apiService;
    }

    @Autowired
    public void setBusinessParamFactory(BusinessParamContext businessParamContext) {
        this.businessParamContext = businessParamContext;
    }

    @Autowired
    public void setWebParamConfig(WebParamConfig webParamConfig) {
        this.webParamConfig = webParamConfig;
    }
}
