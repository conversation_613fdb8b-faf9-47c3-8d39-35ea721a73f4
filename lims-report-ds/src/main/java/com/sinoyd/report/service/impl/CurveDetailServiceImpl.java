package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoCurveDetail;
import com.sinoyd.report.repository.CurveDetailRepository;
import com.sinoyd.report.service.CurveDetailService;
import org.springframework.stereotype.Service;

/**
 * 曲线详情服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/02
 */
@Service
public class CurveDetailServiceImpl
        extends LimsBaseServiceImpl<DtoCurveDetail, String, CurveDetailRepository>
        implements CurveDetailService {
}
