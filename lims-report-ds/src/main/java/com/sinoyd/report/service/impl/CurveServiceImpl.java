package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoCurve;
import com.sinoyd.report.repository.CurveRepository;
import com.sinoyd.report.service.CurveService;
import org.springframework.stereotype.Service;

/**
 * 曲线服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/02
 */
@Service
public class CurveServiceImpl
        extends LimsBaseServiceImpl<DtoCurve, String, CurveRepository>
        implements CurveService {
}
