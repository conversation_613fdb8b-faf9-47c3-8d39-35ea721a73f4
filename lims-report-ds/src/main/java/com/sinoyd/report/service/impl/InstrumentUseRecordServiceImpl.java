package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoInstrumentUseRecord;
import com.sinoyd.report.repository.InstrumentUseRecordRepository;
import com.sinoyd.report.service.InstrumentUseRecordService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 仪器使用记录服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/02
 */
@Service
public class InstrumentUseRecordServiceImpl
        extends LimsBaseServiceImpl<DtoInstrumentUseRecord, String, InstrumentUseRecordRepository>
        implements InstrumentUseRecordService {

    /**
     * 根据对象id查询仪器使用记录
     *
     * @param objectIds 对象id集合（采样单id,领养单id,工作单id）
     * @return 仪器使用记录
     */
    @Override
    public List<DtoInstrumentUseRecord> findByObjectIdIn(Collection<String> objectIds) {
        if (StringUtils.isNotEmpty(objectIds)){
            return repository.findByObjectIdIn(objectIds);
        }
        return new ArrayList<>();
    }
}
