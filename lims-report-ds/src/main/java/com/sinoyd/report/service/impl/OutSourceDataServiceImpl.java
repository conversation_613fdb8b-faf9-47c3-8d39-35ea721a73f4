package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoOutSourceData;
import com.sinoyd.report.repository.OutSourceDataRepository;
import com.sinoyd.report.service.OutSourceDataService;
import org.springframework.stereotype.Service;

/**
 * 分包数据服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/02
 */
@Service
public class OutSourceDataServiceImpl extends LimsBaseServiceImpl<DtoOutSourceData, String, OutSourceDataRepository> implements OutSourceDataService {
}
