package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.repository.ParamsDataRepository;
import com.sinoyd.report.service.ParamsDataService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 参数数据服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
@Service
public class ParamsDataServiceImpl extends LimsBaseServiceImpl<DtoParamsData, String, ParamsDataRepository> implements ParamsDataService {

    /**
     * 查询对应参数数据
     *
     * @param objectIds 对象id集合
     * @return 参数数据
     */
    @Override
    public List<DtoParamsData> findByObjectIdIn(Collection<String> objectIds) {
        if (StringUtils.isNotEmpty(objectIds)) {
            return repository.findByObjectIdIn(objectIds);
        }
        return new ArrayList<>();
    }
}
