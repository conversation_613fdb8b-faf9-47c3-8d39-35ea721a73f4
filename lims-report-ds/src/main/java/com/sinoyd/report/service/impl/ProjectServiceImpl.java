package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoProject;
import com.sinoyd.report.repository.ProjectRepository;
import com.sinoyd.report.service.ProjectService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 项目服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
@Service
public class ProjectServiceImpl extends LimsBaseServiceImpl<DtoProject, String, ProjectRepository> implements ProjectService {
    /**
     * 根据项目id集合获取项目数据
     *
     * @param projectIds 项目id集合
     * @return 项目数据
     */
    @Override
    public List<DtoProject> findByProjectIdIn(Collection<String> projectIds) {
        if (StringUtils.isNotEmpty(projectIds)){
            return repository.findByProjectIdIn(projectIds);
        }
        return new ArrayList<>();
    }
}
