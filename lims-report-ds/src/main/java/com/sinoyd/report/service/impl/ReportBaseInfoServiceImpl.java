package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoReportBaseInfo;
import com.sinoyd.report.repository.ReportBaseInfoRepository;
import com.sinoyd.report.service.ReportBaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 报告基础信息服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/18
 */
@Service
public class ReportBaseInfoServiceImpl extends LimsBaseServiceImpl<DtoReportBaseInfo, String, ReportBaseInfoRepository> implements ReportBaseInfoService {

    /**
     * 根据报告Id集合获取报告基本数据
     *
     * @param reportIds 报告
     * @return 报告基础数据
     */
    @Override
    public List<DtoReportBaseInfo> findByReportIdIn(Collection<String> reportIds) {
        if (StringUtils.isNotEmpty(reportIds)) {
            return repository.findByReportIdIn(reportIds);
        }
        return new ArrayList<>();
    }
}
