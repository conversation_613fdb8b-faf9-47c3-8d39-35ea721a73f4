package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoReportFolderInfo;
import com.sinoyd.report.repository.ReportFolderInfoRepository;
import com.sinoyd.report.service.ReportFolderInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 报告点位基础信息服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@Service
public class ReportFolderInfoServiceImpl extends LimsBaseServiceImpl<DtoReportFolderInfo, String, ReportFolderInfoRepository> implements ReportFolderInfoService {

    /**
     * 根据报告id集合获取报告点位数据
     *
     * @param reportIds 报告id集合
     * @return 报告点位数据
     */
    @Override
    public List<DtoReportFolderInfo> findByReportIdIn(Collection<String> reportIds) {
        if (StringUtils.isNotEmpty(reportIds)){
            return repository.findByReportIdIn(reportIds);
        }
        return new ArrayList<>();
    }
}
