package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoReportSampleInfo;
import com.sinoyd.report.repository.ReportSampleInfoRepository;
import com.sinoyd.report.service.ReportSampleInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 报告样品关联服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/19
 */
@Service
public class ReportSampleInfoServiceImpl
        extends LimsBaseServiceImpl<DtoReportSampleInfo, String, ReportSampleInfoRepository>
        implements ReportSampleInfoService {
    /**
     * 根据报告Id查询到关联样品数据
     *
     * @param reportId 报告id
     * @return 关联样品数据
     */
    @Override
    public List<DtoReportSampleInfo> findByReportId(String reportId) {
        return repository.findByReportId(reportId);
    }

    /**
     * 根据样品id集合获取样品关联报告数据
     *
     * @param sampleIds 样品id集合
     * @return 报告样品关联数据
     */
    @Override
    public List<DtoReportSampleInfo> findBySampleIdIn(Collection<String> sampleIds) {
        if (StringUtils.isNotEmpty(sampleIds)) {
            return repository.findBySampleIdIn(sampleIds);
        }
        return new ArrayList<>();
    }
}
