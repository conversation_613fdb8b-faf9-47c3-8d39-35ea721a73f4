package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.repository.SampleRepository;
import com.sinoyd.report.service.SampleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 样品服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
@Service
public class SampleServiceImpl extends LimsBaseServiceImpl<DtoSample, String, SampleRepository> implements SampleService {

    /**
     * 查询样品下的质控样数据
     *
     * @param sampleIds 原样id集合
     * @return 质控样品数据
     */
    @Override
    public List<DtoSample> findByAssociateSampleIdIn(Collection<String> sampleIds) {
        if (StringUtils.isNotEmpty(sampleIds)) {
            return repository.findByAssociateSampleIdIn(sampleIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据样品id集合查询样品数据
     *
     * @param sampleIds 样品id集合
     * @return 样品数据
     */
    @Override
    public List<DtoSample> findBySampleIdIn(Collection<String> sampleIds) {
        if (StringUtils.isNotEmpty(sampleIds)) {
            return repository.findBySampleIdIn(sampleIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据送样单查询样品
     *
     * @param receiveIds 送样单id集合
     * @return 样品数据
     */
    @Override
    public List<DtoSample> findByReceiveIdIn(Collection<String> receiveIds) {
        if (StringUtils.isNotEmpty(receiveIds)) {
            return repository.findByReceiveIdIn(receiveIds);
        }
        return new ArrayList<>();
    }
}
