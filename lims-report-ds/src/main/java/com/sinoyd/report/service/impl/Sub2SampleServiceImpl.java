package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoSub2Sample;
import com.sinoyd.report.repository.Sub2SampleRepository;
import com.sinoyd.report.service.Sub2SampleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 领养单关联样品服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/02
 */
@Service
public class Sub2SampleServiceImpl
        extends LimsBaseServiceImpl<DtoSub2Sample, String, Sub2SampleRepository>
        implements Sub2SampleService {
    /**
     * 根据样品id集合查询关联领养单数据
     *
     * @param sampleIds 样品id集合
     * @return 关联领养单数据
     */
    @Override
    public List<DtoSub2Sample> findBySampleIdIn(Collection<String> sampleIds) {
        if (StringUtils.isNotEmpty(sampleIds)){
            return repository.findBySampleIdIn(sampleIds);
        }
        return new ArrayList<>();
    }
}
