package com.sinoyd.report.strategy.buiness;

import com.sinoyd.report.service.AnalyseDataService;
import com.sinoyd.report.service.ParamsDataService;
import com.sinoyd.report.service.SampleService;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * SQL与API参数业务类型基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
@Component
public abstract class AbsBusinessParam {


    protected SampleService sampleService;

    protected AnalyseDataService analyseDataService;

    protected ParamsDataService paramsDataService;

    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    public abstract void fillBusinessParam(ExcelBusinessParamVO businessParamVO);

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }
}
