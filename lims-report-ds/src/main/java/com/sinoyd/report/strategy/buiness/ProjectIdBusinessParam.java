package com.sinoyd.report.strategy.buiness;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目ID业务类型参数策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/07
 */
@Component
public class ProjectIdBusinessParam extends AbsBusinessParam{

    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    @Override
    public void fillBusinessParam(ExcelBusinessParamVO businessParamVO) {
        if (StringUtils.isNotEmpty(businessParamVO.getReceiveId())){
            List<DtoSample> sampleList = sampleService.findByReceiveIdIn(Collections.singletonList(businessParamVO.getReceiveId()));
            Optional<String> projectIdOp = sampleList.stream().map(DtoSample::getProjectId).findFirst();
            projectIdOp.ifPresent(businessParamVO::setProjectId);
        } else if (StringUtils.isNotEmpty(businessParamVO.getWorkSheetFolderId())){
            List<DtoAnalyseData> analyseData = analyseDataService.findByWorkSheetFolderIdIn(Collections.singletonList(businessParamVO.getWorkSheetFolderId()));
            List<String> sampleIds = analyseData.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toList());
            List<DtoSample> sampleList = sampleService.findBySampleIdIn(sampleIds);
            Optional<String> projectIdOp = sampleList.stream().map(DtoSample::getProjectId).findFirst();
            projectIdOp.ifPresent(businessParamVO::setProjectId);
        }
        if (StringUtils.isEmpty(businessParamVO.getProjectId())){
            businessParamVO.setProjectId("");
        }
    }
}
