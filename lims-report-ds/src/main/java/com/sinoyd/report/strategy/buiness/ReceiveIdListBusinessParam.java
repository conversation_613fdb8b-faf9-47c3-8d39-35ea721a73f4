package com.sinoyd.report.strategy.buiness;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 送样单ID集合业务类型参数策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/07
 */
@Component
public class ReceiveIdListBusinessParam extends AbsBusinessParam {

    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    @Override
    public void fillBusinessParam(ExcelBusinessParamVO businessParamVO) {
        if (StringUtils.isNotEmpty(businessParamVO.getWorkSheetFolderId())) {
            List<DtoAnalyseData> analyseDataList = analyseDataService
                    .findByWorkSheetFolderIdIn(Collections.singletonList(businessParamVO.getWorkSheetFolderId()));
            List<String> sampleIds = analyseDataList.stream()
                    .map(DtoAnalyseData::getSampleId).collect(Collectors.toList());
            List<DtoSample> sampleList = sampleService.findBySampleIdIn(sampleIds);
            businessParamVO.setReceiveIds(sampleList.stream()
                    .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList()));
        }
        if (StringUtils.isEmpty(businessParamVO.getReceiveIds())){
            businessParamVO.setReceiveIds(new ArrayList<>());
        }
    }
}
