package com.sinoyd.report.strategy.buiness;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 样品ID集合业务类型参数策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/07
 */
@Component
public class SampleIdListBusinessParam extends AbsBusinessParam {

    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    @Override
    public void fillBusinessParam(ExcelBusinessParamVO businessParamVO) {
        List<DtoSample> sampleList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        if (StringUtils.isNotEmpty(businessParamVO.getWorkSheetFolderId())) {
            analyseDataList = analyseDataService.findByWorkSheetFolderIdIn(Collections.singletonList(businessParamVO.getWorkSheetFolderId()));
            businessParamVO.setSampleIds(analyseDataList.stream()
                    .map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList()));
        } else if (StringUtils.isNotEmpty(businessParamVO.getReceiveId())) {
            sampleList = sampleService.findByReceiveIdIn(Collections.singletonList(businessParamVO.getReceiveId()));
            businessParamVO.setSampleIds(sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList()));
        }
        businessParamVO.setSampleList(sampleList);
        if (StringUtils.isEmpty(businessParamVO.getSampleIds())){
            businessParamVO.setSampleIds(new ArrayList<>());
        }
        List<DtoParamsData> sampleParamsDataList = new ArrayList<>();
        Map<String, List<DtoParamsData>> sampleParamsDataMap = new HashMap<>();
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getAnalyseDataId).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(analyseDataIdList)) {
            sampleParamsDataList = paramsDataService.findByObjectIdIn(analyseDataIdList);
            sampleParamsDataMap = sampleParamsDataList.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId));
        }
        businessParamVO.setSampleParamsDataList(sampleParamsDataList);
        businessParamVO.setSampleParamsDataMap(sampleParamsDataMap);
        List<DtoParamsData> worksheetFolderParamsDataList = paramsDataService.findByObjectIdIn(Collections.singletonList(businessParamVO.getWorkSheetFolderId()));
        Map<String, List<DtoParamsData>> worksheetFolderParamsDataMap = worksheetFolderParamsDataList.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId));
        businessParamVO.setWorksheetFolderParamsDataList(worksheetFolderParamsDataList);
        businessParamVO.setWorksheetFolderParamsDataMap(worksheetFolderParamsDataMap);
    }
}
