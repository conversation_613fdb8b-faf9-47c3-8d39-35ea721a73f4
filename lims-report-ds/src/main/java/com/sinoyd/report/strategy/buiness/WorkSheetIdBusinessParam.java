package com.sinoyd.report.strategy.buiness;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.stereotype.Component;

/**
 * 工作单ID业务类型参数策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/07
 */
@Component
public class WorkSheetIdBusinessParam extends AbsBusinessParam {
    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    @Override
    public void fillBusinessParam(ExcelBusinessParamVO businessParamVO) {
        businessParamVO.setWorkSheetFolderId(businessParamVO.getWorkSheetFolderId());
        if (StringUtils.isEmpty(businessParamVO.getWorkSheetFolderId())){
            businessParamVO.setWorkSheetFolderId("");
        }
    }
}
