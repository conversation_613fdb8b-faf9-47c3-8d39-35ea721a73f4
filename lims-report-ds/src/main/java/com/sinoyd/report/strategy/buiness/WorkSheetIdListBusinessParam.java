package com.sinoyd.report.strategy.buiness;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作单ID业务类型参数策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/07
 */
@Component
public class WorkSheetIdListBusinessParam extends AbsBusinessParam {

    /**
     * 获取业务参数数据
     *
     * @param businessParamVO 生成时的所传的业务Id,如：workSheetFolderId/receiveId
     */
    @Override
    public void fillBusinessParam(ExcelBusinessParamVO businessParamVO) {
        if (StringUtils.isNotEmpty(businessParamVO.getReceiveId())) {
            List<DtoSample> sampleList = sampleService
                    .findByReceiveIdIn(Collections.singletonList(businessParamVO.getReceiveId()));
            List<String> sampleIds = sampleList.stream()
                    .map(DtoSample::getSampleId).collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = analyseDataService.findBySampleIdIn(sampleIds);
            List<String> workSheetIds = analyseDataList.stream()
                    .map(DtoAnalyseData::getWorkSheetFolderId)
                    .distinct().collect(Collectors.toList());
            businessParamVO.setWorkSheetFolderIds(workSheetIds);
        }
        if (StringUtils.isEmpty(businessParamVO.getWorkSheetFolderIds())){
            businessParamVO.setWorkSheetFolderIds(new ArrayList<>());
        }
    }
}
