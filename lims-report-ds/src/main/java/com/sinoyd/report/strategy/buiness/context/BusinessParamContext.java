package com.sinoyd.report.strategy.buiness.context;

import com.sinoyd.report.strategy.buiness.AbsBusinessParam;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 报表生成业务参数上下文管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/25
 */
@Component
public class BusinessParamContext {

    private Map<String, AbsBusinessParam> strategyMap = new HashMap<>();

    /**
     * 处理所有的查询业务参数
     *
     * @param paramVO 业务参数
     */
    public void handleBusinessParams(ExcelBusinessParamVO paramVO) {
        for (Map.Entry<String, AbsBusinessParam> entry : strategyMap.entrySet()) {
            entry.getValue().fillBusinessParam(paramVO);
        }
    }

    @Autowired
    public void setStrategyMap(Map<String, AbsBusinessParam> strategyMap) {
        this.strategyMap = strategyMap;
    }
}
