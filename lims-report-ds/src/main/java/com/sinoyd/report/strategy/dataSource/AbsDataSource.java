package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoReportSampleInfo;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.base.enums.EnumReportConfigType;
import com.sinoyd.report.service.*;
import com.sinoyd.report.vo.ReportInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报告数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2021/12/21
 */
@Component
@Slf4j
public abstract class AbsDataSource {

    protected ReportSampleInfoService reportSampleInfoService;

    protected SampleService sampleService;

    protected ReportService reportService;

    protected ReportBaseInfoService reportBaseInfoService;

    protected AnalyseDataService analyseDataService;

    protected ParamsDataService paramsDataService;

    protected ProjectService projectService;

    protected InstrumentUseRecordService instrumentUseRecordService;

    protected Sub2SampleService sub2SampleService;

    protected ReportFolderInfoService reportFolderInfoService;

    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    public abstract void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo);

    /**
     * 获取样品数据
     *
     * @param reportInfoVO 报表基础参数
     * @return 样品数据
     */
    protected List<DtoSample> sampleList(ReportInfoVO reportInfoVO) {
        List<DtoSample> sampleList = new ArrayList<>();
        List<DtoSample> associationSampleList = associationSampleList(reportInfoVO);
        List<DtoSample> qcSampleList = qcSampleList(associationSampleList);
        sampleList.addAll(associationSampleList);
        sampleList.addAll(qcSampleList);
        return sampleList;
    }

    /**
     * 获取原样样品数据
     *
     * @param reportInfoVO 报表基础参数
     * @return 样品数据
     */
    protected List<DtoSample> associationSampleList(ReportInfoVO reportInfoVO) {
        List<DtoSample> sampleList = new ArrayList<>();
        if (EnumReportConfigType.报告.getValue().equals(reportInfoVO.getReportType())){
            //通过报告获取所有关联样品
            List<DtoReportSampleInfo> reportSampleInfoList = reportSampleInfoService.findByReportId(reportInfoVO.getReportId());
            //样品ids
            List<String> sampleIds = reportSampleInfoList.stream().map(DtoReportSampleInfo::getSampleId).distinct().collect(Collectors.toList());
            //样品数据
            sampleList = sampleService.findBySampleIdIn(sampleIds);
        }else if (EnumReportConfigType.采样单.getValue().equals(reportInfoVO.getReportType())){
            //TODO:获取采样单下样品数据
        }else if (EnumReportConfigType.原始记录单.getValue().equals(reportInfoVO.getReportType())){
            //TODO:获取工作单下所有样品
        }
        return sampleList;
    }

    /**
     * 获取质控样品数据
     *
     * @param sampleList 原样样品数据
     * @return 质控样样品数据
     */
    protected List<DtoSample> qcSampleList(List<DtoSample> sampleList){
        //质控样数据
        List<DtoSample> qcSampleList = getLocalQcSample(sampleList);
        //从检测单中获取质控样
        List<DtoSample> innerQcSampleList = getInnerQcSampleList(sampleList, qcSampleList);
        if (StringUtils.isNotEmpty(innerQcSampleList)) {
            qcSampleList.addAll(innerQcSampleList);
        }
        //从现场送样单中获取现场质控样
        List<DtoSample> recordQcSampleList = getReceiveSampleRecordQcSampleList(sampleList, qcSampleList);
        if (StringUtils.isNotEmpty(recordQcSampleList)) {
            qcSampleList.addAll(recordQcSampleList);
        }
        // TODO:比对质控样获取
        // TODO:现场室内质控样获取(现场任务中，现场数据录入添加的质控样)
        return qcSampleList;
    }


    /**
     * 获现场取质控样品
     *
     * @param sampleList 样品列表
     * @return 现场质控样
     */
    protected List<DtoSample> getLocalQcSample(List<DtoSample> sampleList) {
        List<DtoSample> resList = new ArrayList<>();
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        List<DtoSample> qcSampleList = sampleService.findByAssociateSampleIdIn(sampleIdList);
        if (StringUtils.isNotEmpty(qcSampleList)) {
            resList.addAll(qcSampleList);
            resList.addAll(getLocalQcSample(qcSampleList));
        }
        return resList;
    }

    /**
     * 获取室内质控样
     *
     * @param sampleList   原样id集合
     * @param qcSampleList 现在质控id集合
     * @return 室内质控样
     */
    public List<DtoSample> getInnerQcSampleList(List<DtoSample> sampleList, List<DtoSample> qcSampleList) {
        //获取质控样以及原因id集合
        List<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        //用于存储已经有的样品id(原样id, 现场质控样id)
        List<String> existSampleIdList = new ArrayList<>(sampleIds);
        existSampleIdList.addAll(qcSampleIdList);
        List<DtoSample> resList = new ArrayList<>();
        //获取分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataService.findBySampleIdIn(sampleIds);
        //获取所有工作单id
        List<String> worksheetFolderIdList = analyseDataList.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getWorkSheetFolderId()) && !UUIDHelper.guidEmpty().equals(p.getWorkSheetFolderId()))
                .map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        if (StringUtils.isNotEmpty(worksheetFolderIdList)) {
            //获取到所有工作单id下分析数据并过滤出现场与原样的数据
            List<DtoAnalyseData> analyseDataForFolderList = analyseDataService.findByWorkSheetFolderIdIn(worksheetFolderIdList);
            analyseDataForFolderList = analyseDataForFolderList.stream()
                    .filter(p -> !existSampleIdList.contains(p.getSampleId())
                            && StringUtils.isNotEmpty(p.getQcId())
                            && !UUIDHelper.guidEmpty().equals(p.getQcId()))
                    .collect(Collectors.toList());
            List<String> workSheetQcSampleIdList = analyseDataForFolderList.stream()
                    .map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            //查询室内质控样数据
            if (StringUtils.isNotEmpty(workSheetQcSampleIdList)) {
                resList = sampleService.findBySampleIdIn(workSheetQcSampleIdList);
            }
        }
        return resList;
    }

    /**
     * 获取送样单关联质控样
     *
     * @param sampleList   原样数据
     * @param qcSampleList 原样关联质控样数据
     * @return 送样单关联质控样数据
     */
    public List<DtoSample> getReceiveSampleRecordQcSampleList(List<DtoSample> sampleList, List<DtoSample> qcSampleList) {
        //获取质控样以及原样id集合
        List<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        //获取到所有原样以及关联质控样的所有送样单id
        List<String> receiveIdList = sampleList.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getReceiveId()) && !UUIDHelper.guidEmpty().equals(p.getReceiveId()))
                .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        //根据送样单id查询到所有送样单质控样
        List<DtoSample> receiveQcSampleList = sampleService.findByReceiveIdIn(receiveIdList);
        if (StringUtils.isNotEmpty(receiveQcSampleList)) {
            receiveQcSampleList = receiveQcSampleList.stream()
                    .filter(p -> !sampleIds.contains(p.getSampleId()) && !qcSampleIdList.contains(p.getSampleId()))
                    .collect(Collectors.toList());
        }
        return receiveQcSampleList;
    }


    @Autowired
    public void setReportSampleInfoService(ReportSampleInfoService reportSampleInfoService) {
        this.reportSampleInfoService = reportSampleInfoService;
    }

    @Autowired
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    @Autowired
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }

    @Autowired
    public void setReportBaseInfoService(ReportBaseInfoService reportBaseInfoService) {
        this.reportBaseInfoService = reportBaseInfoService;
    }

    @Autowired
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    public void setInstrumentUseRecordService(InstrumentUseRecordService instrumentUseRecordService) {
        this.instrumentUseRecordService = instrumentUseRecordService;
    }

    @Autowired
    public void setSub2SampleService(Sub2SampleService sub2SampleService) {
        this.sub2SampleService = sub2SampleService;
    }

    @Autowired
    public void setReportFolderInfoService(ReportFolderInfoService reportFolderInfoService) {
        this.reportFolderInfoService = reportFolderInfoService;
    }
}
