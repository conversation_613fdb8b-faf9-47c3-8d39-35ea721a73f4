package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分析数据数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.ANALYSE_DATA)
public class AnalyseDataDataSource extends AbsDataSource {
    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        //样品数据
        List<DtoSample> sampleList = sampleList(reportInfo);
        Set<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toSet());
        //分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataService.findBySampleIdIn(sampleIds);
        //分析项目按照样品分组
        Map<String, List<DtoAnalyseData>> analyseMapList = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        //测试项目id集合
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        dataInfo.put(IDataSourceKey.ANALYSE_DATA_LIST, analyseDataList);
        dataInfo.put(IDataSourceKey.ANALYSE_DATA_MAP_GROUP, analyseMapList);
        dataInfo.put(IDataSourceKey.TEST_IDS, testIds);
    }
}
