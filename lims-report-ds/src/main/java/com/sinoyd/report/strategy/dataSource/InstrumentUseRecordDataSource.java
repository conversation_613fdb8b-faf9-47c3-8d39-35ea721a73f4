package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoInstrumentUseRecord;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.dto.DtoSub2Sample;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 仪器使用记录数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.INS_USE_RECORD)
public class InstrumentUseRecordDataSource extends AbsDataSource{

    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        //样品数据
        List<DtoSample> sampleList = sampleList(reportInfo);
        Set<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toSet());
        //分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataService.findBySampleIdIn(sampleIds);
        //获取样品关联领养单数据
        List<DtoSub2Sample> sub2Samples = sub2SampleService.findBySampleIdIn(sampleIds);
        //获取送样单id(用于查询采样仪器数据)
        Set<String> insUseCriteria = sampleList.stream().map(DtoSample::getReceiveId).collect(Collectors.toSet());
        //获取领养单id(用于查询现场仪器数据)
        List<String> subReceiveIds = sub2Samples.stream().map(DtoSub2Sample::getSubId).collect(Collectors.toList());
        insUseCriteria.addAll(subReceiveIds);
        //获取工作单id(用于查询实验室仪器数据)
        List<String> worksheetFolderIds = analyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).collect(Collectors.toList());
        insUseCriteria.addAll(worksheetFolderIds);
        List<DtoInstrumentUseRecord> insUseRecords = instrumentUseRecordService.findByObjectIdIn(insUseCriteria);
        dataInfo.put(IDataSourceKey.INSTRUMENT_USE_RECORD_DATA_LIST, insUseRecords);
    }
}
