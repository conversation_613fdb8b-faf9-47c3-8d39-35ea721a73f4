package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 参数数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.PARAMS_DATA)
public class ParamsDataDataSource extends AbsDataSource {
    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        List<DtoSample> sampleList = sampleList(reportInfo);
        //样品id集合
        Set<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toSet());
        //查询参数数据
        List<DtoParamsData> paramsData = paramsDataService.findByObjectIdIn(sampleIds);
        //参数按照objectId分组
        Map<String, List<DtoParamsData>> paramsDataMapGroup = paramsData.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId));
        dataInfo.put(IDataSourceKey.PARAMS_DATA_LIST, paramsData);
        dataInfo.put(IDataSourceKey.PARAMS_MAP_GROUP, paramsDataMapGroup);
    }
}
