package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoProject;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 项目相关数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.PROJECT)
public class ProjectDataSource extends AbsDataSource {
    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        List<DtoSample> sampleList = sampleList(reportInfo);
        Set<String> projectIds = sampleList.stream().map(DtoSample::getProjectId).collect(Collectors.toSet());
        //项目数据
        List<DtoProject> projectList = projectService.findByProjectIdIn(projectIds);
        //项目数据Map
        Map<String, DtoProject> projectMap = projectList.stream().collect(Collectors.toMap(DtoProject::getProjectId, project -> project));
        dataInfo.put(IDataSourceKey.PROJECT_DATA, StringUtils.isNotEmpty(projectList) ? projectList.get(0) : null);
        dataInfo.put(IDataSourceKey.PROJECT_DATA_LIST, projectList);
        dataInfo.put(IDataSourceKey.PROJECT_DATA_MAP, projectMap);
    }
}
