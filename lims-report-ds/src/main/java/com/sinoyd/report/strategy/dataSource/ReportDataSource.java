package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoReport;
import com.sinoyd.report.dto.DtoReportBaseInfo;
import com.sinoyd.report.dto.DtoReportSampleInfo;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ReportInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报告相关数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.REPORT)
@Slf4j
public class ReportDataSource extends AbsDataSource {
    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        List<DtoSample> sampleList = sampleList(reportInfo);
        //样品id集合
        Set<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toSet());
        List<DtoReportSampleInfo> reportSampleInfos = reportSampleInfoService.findBySampleIdIn(sampleIds);
        //报告id集合
        Set<String> reportIds = reportSampleInfos.stream().map(DtoReportSampleInfo::getReportId).collect(Collectors.toSet());
        List<DtoReport> reportList = reportService.findAll(reportIds);
        Map<String, DtoReport> reportMap = reportList.stream().collect(Collectors.toMap(DtoReport::getId, report -> report));
        //报告基础信息数据
        List<DtoReportBaseInfo> reportBaseInfoList = reportBaseInfoService.findByReportIdIn(reportIds);
        Map<String, DtoReportBaseInfo> baseInfoMap;
        try {
            baseInfoMap = reportBaseInfoList.stream().collect(Collectors.toMap(DtoReportBaseInfo::getReportId, baseInfo -> baseInfo));
        } catch (IllegalStateException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("报告基础数据Map转换失败,Report与ReportInfo不是一对一的关系");
        }
        dataInfo.put(IDataSourceKey.REPORT_DATA, StringUtils.isNotEmpty(reportList) ? reportList.get(0) : null);
        dataInfo.put(IDataSourceKey.REPORT_DATA_LIST, reportList);
        dataInfo.put(IDataSourceKey.REPORT_DATA_MAP, reportMap);
        dataInfo.put(IDataSourceKey.REPORT_BASE_INFO_DATA, baseInfoMap.getOrDefault(reportInfo.getReportId(), null));
        dataInfo.put(IDataSourceKey.REPORT_BASE_INFO_LIST, reportBaseInfoList);
        dataInfo.put(IDataSourceKey.REPORT_BASE_INFO_MAP, baseInfoMap);
    }
}
