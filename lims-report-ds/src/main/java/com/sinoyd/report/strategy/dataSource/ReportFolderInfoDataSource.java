package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoReportFolderInfo;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 报告点位数据数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/09
 */
@Component(IDataSourceStrategy.REPORT_FOLDER_INFO)
public class ReportFolderInfoDataSource extends AbsDataSource{


    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        String reportId = reportInfo.getReportId();
        //根据点位id获取到报告点位数据
        List<DtoReportFolderInfo> reportFolderList = reportFolderInfoService.findByReportIdIn(Collections.singletonList(reportId));
        //放置数据
        dataInfo.put(IDataSourceKey.REPORT_FOLDER_INFO_LIST, reportFolderList);
    }
}
