package com.sinoyd.report.strategy.dataSource;

import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 样品数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/20
 */
@Component(IDataSourceStrategy.SAMPLE)
public class SampleDataSource extends AbsDataSource {
    /**
     * 数据源
     *
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    @Override
    public void dataSource(ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        //所有
        List<DtoSample> sampleList = sampleList(reportInfo);
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getSampleId, dto -> dto));
        //原样数据
        List<DtoSample> associateSampleList = associationSampleList(reportInfo);
        Map<String, DtoSample> associateSampleMap = associateSampleList.stream().collect(Collectors.toMap(DtoSample::getSampleId, dto -> dto));
        //质控样数据
        List<DtoSample> qcSampleList = qcSampleList(associateSampleList);
        Map<String, DtoSample> qcSampleMapData = qcSampleList.stream().collect(Collectors.toMap(DtoSample::getSampleId, dto -> dto));
        dataInfo.put(IDataSourceKey.SAMPLE_DATA_LIST, sampleList);
        dataInfo.put(IDataSourceKey.SAMPLE_DATA_MAP, sampleMap);
        dataInfo.put(IDataSourceKey.ASSOCIATE_SAMPLE_DATA_LIST, associateSampleList);
        dataInfo.put(IDataSourceKey.ASSOCIATE_SAMPLE_DATA_MAP, associateSampleMap);
        dataInfo.put(IDataSourceKey.QC_SAMPLE_DATA_LIST, qcSampleList);
        dataInfo.put(IDataSourceKey.QC_SAMPLE_DATA_MAP, qcSampleMapData);
    }
}
