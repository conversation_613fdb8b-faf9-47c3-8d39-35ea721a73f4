package com.sinoyd.report.strategy.dataSource.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.strategy.dataSource.AbsDataSource;
import com.sinoyd.report.vo.ReportInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 报表数据源策略上下文管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Component
public class ReportDataSourceContext {

    private final Map<String, AbsDataSource> dataMap = new ConcurrentHashMap<>();

    /**
     * 绑定质控数据
     *
     * @param beanName   状态bean名称
     * @param reportInfo 参数
     * @param dataInfo   数据
     */
    public void dataSource(String beanName, ReportInfoVO reportInfo, Map<String, Object> dataInfo) {
        //赋值数据源
        AbsDataSource dataSource = this.dataMap.get(beanName);
        if (StringUtils.isNotNull(dataSource)) {
            dataSource.dataSource(reportInfo, dataInfo);
        }
    }

    @Autowired
    public ReportDataSourceContext(Map<String, AbsDataSource> strategyMap) {
        this.dataMap.putAll(strategyMap);
    }
}
