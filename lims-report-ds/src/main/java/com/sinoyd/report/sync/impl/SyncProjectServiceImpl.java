package com.sinoyd.report.sync.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.service.*;
import com.sinoyd.report.sync.SyncProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同步项目数据（临时接口）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/22
 */
@Service
public class SyncProjectServiceImpl implements SyncProjectService {

    private final HTTPCaller httpCaller = HTTPCaller.getInstance();

    private ProjectService projectService;

    private SampleService sampleService;

    private ReportService reportService;

    private ReportBaseInfoService reportBaseInfoService;

    private ReportSampleInfoService reportSampleInfoService;

    private ParamsDataService paramsDataService;

    private AnalyseDataService analyseDataService;

    private ReportFolderInfoService reportFolderInfoService;

    private SamplePreparationService samplePreparationService;

    private SampleGroupService sampleGroupService;

    private Sub2SampleService sub2SampleService;

    private WorkSheetFolderService workSheetFolderService;

    private WorkSheetReagentService workSheetReagentService;

    private InstrumentUseRecordService instrumentUseRecordService;

    private CurveService curveService;

    private CurveDetailService curveDetailService;

    /**
     * 根据id同步所关联所有数据
     *
     * @param projectId 项目id
     */
    @Override
    @Transactional
    public void sync(String projectId) {
        String token = getToken();
        //项目查询条件数据
        List<NameValuePair> criteria = new ArrayList<>();
        List<String> projectIds = new ArrayList<>();
        projectIds.add(projectId);
        criteria.add(new NameValuePair("projectIds",projectIds));
        //项目数据
        String HOST = "http://127.0.0.1:11002";
        JSONArray projectJsons = httpCaller.postList(HOST, "api/pro/foreign/dataProject/syncData", token, criteria);
        List<DtoProject> projectList = JSONObject.parseObject(projectJsons.toString(), new TypeReference<List<DtoProject>>() {}.getType());
        //样品数据
        JSONArray sampleJsons = httpCaller.postList(HOST, "api/pro/foreign/dataSample/syncData", token, criteria);
        List<DtoSample> sampleList = JSONObject.parseObject(sampleJsons.toString(), new TypeReference<List<DtoSample>>() {}.getType());
        //报告数据
        JSONArray reportJsons = httpCaller.postList(HOST, "api/pro/foreign/dataReport/syncData", token, criteria);
        List<DtoReport> reportList = JSONObject.parseObject(reportJsons.toString(), new TypeReference<List<DtoReport>>() {}.getType());
        //报告BaseInfo
        List<String> reportIds = reportList.stream().map(DtoReport::getId).collect(Collectors.toList());
        criteria.clear();
        criteria.add(new NameValuePair("reportIds",reportIds));
        JSONArray reportBaseJsons = httpCaller.postList(HOST, "api/pro/foreign/dataReportBaseInfo/syncData", token, criteria);
        List<DtoReportBaseInfo> reportBaseList = JSONObject.parseObject(reportBaseJsons.toString(), new TypeReference<List<DtoReportBaseInfo>>() {}.getType());
        //分析数据
        List<String> sampleIds = sampleList.stream().map(DtoSample::getSampleId).collect(Collectors.toList());
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).collect(Collectors.toList());
        criteria.clear();
        criteria.add(new NameValuePair("sampleIds",sampleIds));
        JSONArray anaDataJsons = httpCaller.postList(HOST, "api/pro/foreign/analyseData/syncData", token, criteria);
        List<DtoAnalyseData> anaDataList = JSONObject.parseObject(anaDataJsons.toString(), new TypeReference<List<DtoAnalyseData>>() {}.getType());
        //样品制备数据
        JSONArray samplePreparationJsons = httpCaller.postList(HOST, "api/pro/foreign/dataSamplePreparation/syncData", token, criteria);
        List<DtoSamplePreparation> samplePreparationList = JSONObject.parseObject(samplePreparationJsons.toString(), new TypeReference<List<DtoSamplePreparation>>() {}.getType());
        //领养单关联样品数据
        criteria.add(new NameValuePair("receiveIds",receiveIds));
        JSONArray sub2SampleJsons = httpCaller.postList(HOST, "api/pro/foreign/dataSub2Sample/syncData", token, criteria);
        List<DtoSub2Sample> sub2SampleList = JSONObject.parseObject(sub2SampleJsons.toString(), new TypeReference<List<DtoSub2Sample>>() {}.getType());
        List<String> subReceiveIds = sub2SampleList.stream().map(DtoSub2Sample::getSubId).distinct().collect(Collectors.toList());
        //样品分组数据
        JSONArray sampleGroupJsons = httpCaller.postList(HOST, "api/pro/foreign/dataSampleGroup/syncData", token, criteria);
        List<DtoSampleGroup> sampleGroupList = JSONObject.parseObject(sampleGroupJsons.toString(), new TypeReference<List<DtoSampleGroup>>() {}.getType());
        //参数数据
        criteria.clear();
        criteria.add(new NameValuePair("objectIds",sampleIds));
        JSONArray paramsDataJsons = httpCaller.postList(HOST, "api/pro/foreign/dataParamsData/syncData", token, criteria);
        List<DtoParamsData> paramsDataList = JSONObject.parseObject(paramsDataJsons.toString(), new TypeReference<List<DtoParamsData>>() {}.getType());
        //报告样品数据
        criteria.clear();
        criteria.add(new NameValuePair("reportIds",reportIds));
        criteria.add(new NameValuePair("sampleIds",sampleIds));
        JSONArray reportSampleJsons = httpCaller.postList(HOST, "api/pro/foreign/dataReportSampleInfo/syncData", token, criteria);
        List<DtoReportSampleInfo> reportSampleList = JSONObject.parseObject(reportSampleJsons.toString(), new TypeReference<List<DtoReportSampleInfo>>() {}.getType());
        //报告点位数据
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        criteria.clear();
        criteria.add(new NameValuePair("reportIds",reportIds));
        criteria.add(new NameValuePair("folderIds",folderIds));
        JSONArray reportFolderJsons = httpCaller.postList(HOST, "api/pro/foreign/dataReportFolderInfo/syncData", token, criteria);
        List<DtoReportFolderInfo> reportFolderList = JSONObject.parseObject(reportFolderJsons.toString(), new TypeReference<List<DtoReportFolderInfo>>() {}.getType());
        //工作单数据
        criteria.clear();
        List<String> workSheetFolderIds = anaDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        List<String> workSheetIds = anaDataList.stream().map(DtoAnalyseData::getWorkSheetId).distinct().collect(Collectors.toList());
        criteria.add(new NameValuePair("workSheetFolderIds", workSheetFolderIds));
        JSONArray workSheetFolderJsons = httpCaller.postList(HOST, "api/pro/foreign/dataWorksheetFolder/syncData", token, criteria);
        List<DtoWorksheetFolder> workSheetFolderList = JSONObject.parseObject(workSheetFolderJsons.toString(), new TypeReference<List<DtoWorksheetFolder>>() {}.getType());
        //试剂配制数据
        JSONArray workSheetRangeJsons = httpCaller.postList(HOST, "api/pro/foreign/dataWorksheetReagent/syncData", token, criteria);
        List<DtoWorksheetReagent> workSheetRangeList = JSONObject.parseObject(workSheetRangeJsons.toString(), new TypeReference<List<DtoWorksheetReagent>>() {}.getType());
        //仪器使用记录数据
        List<String> insUseCriteria = new ArrayList<>();
        insUseCriteria.addAll(workSheetFolderIds);
        insUseCriteria.addAll(receiveIds);
        insUseCriteria.addAll(subReceiveIds);
        criteria.clear();
        criteria.add(new NameValuePair("objectIds",insUseCriteria));
        JSONArray insUseJsons = httpCaller.postList(HOST, "api/pro/foreign/dataInstrumentUseRecord/syncData", token, criteria);
        List<DtoInstrumentUseRecord> insUseList = JSONObject.parseObject(insUseJsons.toString(), new TypeReference<List<DtoInstrumentUseRecord>>() {}.getType());
        //曲线数据
        criteria.clear();
        criteria.add(new NameValuePair("workSheetFolderIds",workSheetFolderIds));
        criteria.add(new NameValuePair("workSheetIds",workSheetIds));
        JSONArray curveJsons = httpCaller.postList(HOST, "api/pro/foreign/dataCurve/syncData", token, criteria);
        List<DtoCurve> curveList = JSONObject.parseObject(curveJsons.toString(), new TypeReference<List<DtoCurve>>() {}.getType());
        //曲线详情数据
        criteria.clear();
        criteria.add(new NameValuePair("workSheetIds",workSheetIds));
        JSONArray curveDetailJsons = httpCaller.postList(HOST, "api/pro/foreign/dataCurveDetail/syncData", token, criteria);
        List<DtoCurveDetail> curveDetailList = JSONObject.parseObject(curveDetailJsons.toString(), new TypeReference<List<DtoCurveDetail>>() {}.getType());

        if (StringUtils.isNotEmpty(projectList)){
            projectService.save(projectList);
        }
        if (StringUtils.isNotEmpty(sampleList)){
            sampleService.save(sampleList);
        }
        if (StringUtils.isNotEmpty(samplePreparationList)){
            samplePreparationService.save(samplePreparationList);
        }
        if (StringUtils.isNotEmpty(sub2SampleList)){
            sub2SampleService.save(sub2SampleList);
        }
        if (StringUtils.isNotEmpty(sampleGroupList)){
            sampleGroupService.save(sampleGroupList);
        }
        if (StringUtils.isNotEmpty(workSheetFolderList)){
            workSheetFolderService.save(workSheetFolderList);
        }
        if (StringUtils.isNotEmpty(workSheetRangeList)){
            workSheetReagentService.save(workSheetRangeList);
        }
        if (StringUtils.isNotEmpty(insUseList)){
            instrumentUseRecordService.save(insUseList);
        }
        if (StringUtils.isNotEmpty(curveList)){
            curveService.save(curveList);
        }
        if (StringUtils.isNotEmpty(curveDetailList)){
            curveDetailService.save(curveDetailList);
        }
        if (StringUtils.isNotEmpty(reportList)){
            reportService.save(reportList);
        }
        if (StringUtils.isNotEmpty(reportBaseList)){
            reportBaseInfoService.save(reportBaseList);
        }
        if (StringUtils.isNotEmpty(anaDataList)){
            analyseDataService.save(anaDataList);
        }
        if (StringUtils.isNotEmpty(paramsDataList)){
            paramsDataService.save(paramsDataList);
        }
        if (StringUtils.isNotEmpty(reportSampleList)){
            reportSampleInfoService.save(reportSampleList);
        }
        if (StringUtils.isNotEmpty(reportFolderList)){
            reportFolderInfoService.save(reportFolderList);
        }
    }

    /**
     * 获取token
     *
     * @return token
     */
    private String getToken(){
        List<NameValuePair> pairs = new ArrayList<>();
        pairs.add(new NameValuePair("pid", "SLpWJgT5Rv1iA6OLmTzkYg=="));
        pairs.add(new NameValuePair("uid", "i40XtquqCMru6oC8lDI0dw=="));
        JSONObject tokenResponse = httpCaller.postOne("http://127.0.0.1:11005", "/api/proxy/auth/login", null, pairs);
        return tokenResponse.get("token").toString();
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    @Autowired
    @Lazy
    public void setReportBaseInfoService(ReportBaseInfoService reportBaseInfoService) {
        this.reportBaseInfoService = reportBaseInfoService;
    }

    @Autowired
    @Lazy
    public void setReportSampleInfoService(ReportSampleInfoService reportSampleInfoService) {
        this.reportSampleInfoService = reportSampleInfoService;
    }

    @Autowired
    @Lazy
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setReportFolderInfoService(ReportFolderInfoService reportFolderInfoService) {
        this.reportFolderInfoService = reportFolderInfoService;
    }

    @Autowired
    @Lazy
    public void setSamplePreparationService(SamplePreparationService samplePreparationService) {
        this.samplePreparationService = samplePreparationService;
    }

    @Autowired
    @Lazy
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }

    @Autowired
    @Lazy
    public void setSub2SampleService(Sub2SampleService sub2SampleService) {
        this.sub2SampleService = sub2SampleService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetReagentService(WorkSheetReagentService workSheetReagentService) {
        this.workSheetReagentService = workSheetReagentService;
    }

    @Autowired
    @Lazy
    public void setInstrumentUseRecordService(InstrumentUseRecordService instrumentUseRecordService) {
        this.instrumentUseRecordService = instrumentUseRecordService;
    }

    @Autowired
    @Lazy
    public void setCurveService(CurveService curveService) {
        this.curveService = curveService;
    }

    @Autowired
    @Lazy
    public void setCurveDetailService(CurveDetailService curveDetailService) {
        this.curveDetailService = curveDetailService;
    }
}
