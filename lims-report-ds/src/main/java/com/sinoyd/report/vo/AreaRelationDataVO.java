package com.sinoyd.report.vo;

import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoDataSet;
import lombok.Data;

import java.util.*;

/**
 * 区域相关数据VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/18
 */
@Data
public class AreaRelationDataVO {

    /**
     * 区域id与数据集关联Map
     */
    private Map<String, List<DtoDataSet>> areaIdToDataSetMap = new HashMap<>();

    /**
     * 区域id与API关联Map
     */
    private Map<String, List<DtoApi>> areaIdToApiMap = new HashMap<>();

    /**
     * 当前生成的数据下的所有数据集数据
     */
    private List<DtoDataSet> dataSetList = new ArrayList<>();

    /**
     * 当前生成的表单下的所有接口数据
     */
    private List<DtoApi> apiList = new ArrayList<>();

    /**
     * 每个区域的占位符对应的列名Map
     *
     * key 区域Id
     * value 占位符映射
     */
    private Map<String, List<ExcelPlaceholderVO>> placeholderToColMap = new HashMap<>();
}
