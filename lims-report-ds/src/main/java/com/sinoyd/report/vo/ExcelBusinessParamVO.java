package com.sinoyd.report.vo;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.enums.EnumParamType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * excel报表生成前端传参VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
@Data
public class ExcelBusinessParamVO {
    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目id集合
     */
    private List<String> projectIds;

    /**
     * 当前生成数据所关联的送样单id集合
     */
    private List<String> receiveIds;

    /**
     * 样品id集合
     */
    private List<String> sampleIds;

    /**
     * 样品集合
     */
    private List<DtoSample> sampleList;

    /**
     * 工作单id集合
     */
    private List<String> workSheetFolderIds;

    /**
     * 样品参数集合
     */
    private List<DtoParamsData> sampleParamsDataList;

    /**
     * 工作单参数集合
     */
    private List<DtoParamsData> worksheetFolderParamsDataList;

    /**
     * 样品参数映射
     */
    private Map<String, List<DtoParamsData>> sampleParamsDataMap;

    /**
     * 工作单参数映射
     */
    private Map<String, List<DtoParamsData>> worksheetFolderParamsDataMap;

    /**
     * 报表生成时初始化构造函数
     *
     * @param workSheetFolderId 工作单id
     * @param receiveId         送样单id
     */
    public ExcelBusinessParamVO(String workSheetFolderId, String receiveId) {
        this.workSheetFolderId = workSheetFolderId;
        this.receiveId = receiveId;
    }

    /**
     * 根据业务参数类型获取对应值
     *
     * @param type       业务参数类型
     * @param fixedValue 固定值
     * @return 请求参数值
     */
    public Object getBusinessTypeId(String type, Object fixedValue) {
        EnumParamType paramType = EnumParamType.getByValue(type);
        switch (paramType) {
            case 工作单ID:
                return workSheetFolderId;
            case 工作单ID集合:
                return workSheetFolderIds;
            case 送样单ID:
                return receiveId;
            case 送样单ID集合:
                return receiveIds;
            case 项目ID:
                return projectId;
            case 项目ID集合:
                return projectIds;
            case 样品ID集合:
                return sampleIds;
            case 固定值:
                return fixedValue;
            default:
                throw new BaseException("业务参数类型出错，请确定所选参数类型为已有类型!");
        }
    }
}
