package com.sinoyd.report.vo;

import lombok.Data;

/**
 * 报表生成时的占位符映射列VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/19
 */
@Data
public class ExcelPlaceholderVO {

    /**
     * 占位符
     */
    private String placeholder;

    /**
     * 占位符对应字段
     */
    private String field;

    /**
     * 占位符关联的字段Id
     */
    private String relationId;

    /**
     * 全参构造方法
     *
     * @param placeholder 占位符
     * @param field       占位符对应字段
     * @param relationId  占位符关联的字段Id
     */
    public ExcelPlaceholderVO(String placeholder, String field, String relationId) {
        this.placeholder = placeholder;
        this.field = field;
        this.relationId = relationId;
    }
}
