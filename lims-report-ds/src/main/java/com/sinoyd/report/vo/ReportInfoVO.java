package com.sinoyd.report.vo;

import lombok.Data;

import java.util.List;

/**
 * 报表生成数据源查询的基本IdVO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 * */
@Data
public class ReportInfoVO {

    /**
     * 报表生成类型
     */
    Integer reportType;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品id集合(用于采样单采样准备生成传值)
     */
    private List<String> sampleIds;

    /**
     * 工作单Id
     */
    private String workSheetFolderId;
}
