package com.sinoyd.report.excel.constant;

/**
 * 原始记录单上的一些固定字段
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
public interface WorkSheetConstant {

    //#region 仪器一块固定的参数
    String 仪器型号名称及编号 = "仪器型号名称及编号";

    String 仪器名称 = "仪器名称";

    String 仪器型号及编号 = "仪器型号及编号";

    String 仪器型号及名称 = "仪器型号及名称";

    String 仪器名称及编号 = "仪器名称及编号";

    String 仪器名称及出厂编号 = "仪器名称及出厂编号";

    String 仪器型号及出厂编号 = "仪器型号及出厂编号";

    String 仪器型号名称及出厂编号 = "仪器型号名称及出厂编号";

    String 仪器名称型号 = "仪器名称型号";

    String 仪器型号 = "仪器型号";

    String 仪器使用日期 = "仪器使用日期";

    String 仪器编号 = "仪器编号";

    String 仪器出厂编号 = "仪器出厂编号";

    String 室温 = "室温";

    String 温度 = "温度";

    String 湿度 = "湿度";

    String 大气压 = "大气压";

    String 仪器溯源方式 = "仪器溯源方式";

    String 仪器溯源有效期 = "仪器溯源有效期";

    String 仪器溯源日期 = "仪器溯源日期";

    String 仪器使用时间 = "仪器使用时间";

    //#endregion

    //#region 分析一块的参数
    String 分析日期 = "分析日期";

    String 分析方法 = "分析方法";
    String 分析方法别名 = "分析方法别名";

    String 分析项目 = "分析项目";

    String 分析项目总称 = "分析项目总称";

    String 样值较检出限 = "样值较检出限";

    String 检出限 = "检出限";

    String 测试项目检出限 = "测试项目检出限";

    String 测试项目检出限带量纲 = "测试项目检出限带量纲";

    String 检出限带量纲 = "检出限带量纲";

    String 分析方法编号年度 = "分析方法编号年度";

    String 分析方法别名编号年度 = "分析方法别名编号年度";

    String 方法编号及年度 = "方法编号及年度";

    String 测试项目 = "测试项目";

    String 测试项目公式 = "测试项目公式";

    String 量纲 = "量纲";

    String 测试项目量纲 = "测试项目量纲";

    String 是否合格 = "是否合格";

    String 合格 = "合格";

    String 允许加标偏差 = "允许加标偏差";
    //#endregion

    //#region 试剂配制记录
    String 试剂配置 = "试剂配置";

    String 试剂配置全 = "试剂配置全";

    String 溶液浓度 = "溶液浓度";

    String 溶液名称及浓度 = "溶液名称及浓度";

    String 溶液名称及浓度全 = "溶液名称及浓度全";

    String 标液名称 = "标液名称";

    String 标液名称全 = "标液名称全";

    String 溶液配制日期 = "溶液配制日期";

    String 溶液配制日期全 = "溶液配制日期全";

    String 增加值 = "增加值";

    String 加入标准量浓度 = "加入标准量浓度";
    //#endregion

    //#region 样品信息
    String 大备注 = "大备注";

    String 采样日期 = "采样日期";

    String 采样人 = "采样人";

    String 样品类型 = "样品类型";

    String 领样日期 = "领样日期";

    String 样品编号 = "样品编号";
    String 点位编号 = "点位编号";

    String 接样日期 = "接样日期";

    String 送样日期 = "送样日期";

    //#endregion

    //#region 项目信息

    String 项目编号 = "项目编号";


    String 项目名称 = "项目名称";

    String 委托性质 = "委托性质";


    String 委托单位 = "委托单位";

    String 受检单位 = "受检单位";

    //#endregion

    //#region 页码数据
    String 页码 = "页码";

    String 页数 = "页数";

    String 页数页码 = "页数页码";

    String 页码页数 = "页码页数";

    String 序号 = "序号";
    //#endregion

    //#region 平行出证数据
    String 检测结果及出证带平行 = "检测结果及出证带平行";
    String 检测结果及出证带平行均值 = "检测结果及出证带平行均值";

    //#endregion

    //#region 标准曲线数据
    String 标准溶液加入体积 = "标准溶液加入体积";

    String 标准物加入量 = "标准物加入量";

    String 吸光度 = "吸光度";

    String 减空白吸光度 = "减空白吸光度";

    String 两吸光度之差 = "两吸光度之差";

    String 两空白吸光度之差 = "两空白吸光度之差";

    String 背景吸光度 = "背景吸光度";

    String 回归方程 = "回归方程";

    String 相关系数 = "相关系数";

    String 实数c = "实数c";

    String 相对偏差 = "相对偏差";
    String 允许相对偏差 = "允许相对偏差";

    String 吸光度1 = "吸光度1";

    String 吸光度2 = "吸光度2";

    String X值 = "X值";

    String Y值 = "Y值";

    String 截距 = "截距";

    String 斜率 = "斜率";

    String 吸光度220 = "220吸光度";

    String 吸光度275 = "275吸光度";

    String 含量值 = "含量值";
    String 标准使用液加入浓度 = "标准使用液加入浓度";

    String A0 = "A0";

    String A1 = "A1";

    String A2 = "A2";

    String 校准曲线绘制日期 = "校准曲线绘制日期";
    //#endregion

    //#region 分析数据
    String 采样地点 = "采样地点";

    String 采样点位 = "采样点位";

    String 点位名称 = "点位名称";

    String 加标体积 = "加标体积";

    String 加入标准量 = "加入标准量";

    String 加标液浓度 = "加标液浓度";

    String 加标样品测定值 = "加标样品测定值";

    String 原样品测定值 = "原样品测定值";

    String 回收率 = "回收率";

    String 检测结果 = "检测结果";

    String 出证结果 = "出证结果";

    String 检测结果修约 = "检测结果修约";

    String 样品浓度 = "样品浓度";

    String 备注 = "备注";

    String 平行样 = "平行样";

    String 平行样浓度 = "平行样浓度";

    String 原样 = "原样";

    String 平行编号 = "平行编号";

    String 原样编号和平行样编号 = "原样编号和平行样编号";

    String 原样浓度 = "原样浓度";

    String 偏差类型 = "偏差类型";

    String 偏差 = "偏差";

    String 允许平行偏差 = "允许平行偏差";

    String 平行样编号 = "平行样编号";

    String 平均 = "平均";

    String 原样与平行样 = "原样与平行样";

    String 原样检查结果修约 = "原样检查结果修约";

    String 平行样检测结果修约 = "平行样检测结果修约";

    String 标样编号 = "标样编号";

    String 标样测定值 = "标样测定值";

    String 标样标准值 = "标样标准值";

    String 标样不确定度 = "标样不确定度";

    String 标样出厂值 = "标样出厂值";

    String 标样配置日期 = "配置日期";

    String 标样有效期 = "有效期";

    String 相对误差 = "相对误差";

    String 温湿度 = "温湿度";

    String 质控类型 = "质控类型";

    String 质控级别 = "质控级别";

    String 采集编号 = "采集编号";

    String 允许相对误差 = "允许相对误差";

    String 质控信息 = "质控信息";

    String 室内空白样编号 = "室内空白样编号";
    String 室内空白样出证结果 = "室内空白样出证结果";
    String 室内空白样质控限值 = "室内空白样质控限值";
    String 室内空白样是否合格 = "室内空白样是否合格";
    String 室内空白样采集编号 = "室内空白样采集编号";
    String 全程序空白样编号 = "全程序空白样编号";
    String 全程序空白样出证结果 = "全程序空白样出证结果";
    String 全程序空白样采集编号 = "全程序空白样采集编号";
    String 全程序空白样质控限值 = "全程序空白样质控限值";
    String 全程序空白样是否合格 = "全程序空白样是否合格";

    String 曲线校核样样品编号 = "曲线校核样样品编号";
    String 曲线校核样采集编号 = "曲线校核样采集编号";
    String 标准物质加入量 = "标准物质加入量";
    String 曲线校核样相对偏差 = "曲线校核样相对偏差";
    String 曲线校核样允许相对偏差 = "曲线校核样允许相对偏差";
    String 曲线校核样出证结果 = "曲线校核样出证结果";
    String 曲线校核样是否合格 = "曲线校核样是否合格";
    String 校正点浓度 = "校正点浓度";
    String 校正系数检验样出证结果 = "校正系数检验样出证结果";
    String 校正系数检验样偏差 = "校正系数检验样偏差";
    String 校正系数检验样允许相对偏差 = "校正系数检验样允许相对偏差";
    String 校正系数检验样是否合格 = "校正系数检验样是否合格";
}