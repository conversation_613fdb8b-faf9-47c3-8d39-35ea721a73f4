package com.sinoyd.report.excel.controller;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.report.excel.service.GenerateExcelReportService;
import com.sinoyd.report.excel.vo.FrontParamVO;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 通用excel报表导出接口(采样单，原始记录单)
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@RestController
@RequestMapping("api/report/generate/excel")
public class ExcelReportGenerateController extends ExceptionHandlerController<GenerateExcelReportService> {

    /**
     * excel报表生成接口
     *
     * @param reportCode   报表编码
     * @param frontParamVO 业务传参
     * @param response     响应体
     * @return 返回
     */
    @PostMapping("/{reportCode}")
    public RestResponse<String> generate(@PathVariable("reportCode") String reportCode,
                                         @RequestBody FrontParamVO frontParamVO,
                                         HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.generate(reportCode, frontParamVO, response);
        restResponse.setMsg("导出成功！");
        return restResponse;
    }
}
