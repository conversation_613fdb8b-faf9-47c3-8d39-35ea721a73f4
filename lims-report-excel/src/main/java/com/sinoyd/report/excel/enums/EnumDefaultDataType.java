package com.sinoyd.report.excel.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Getter
@AllArgsConstructor
public enum EnumDefaultDataType {

    表头(1),

    质控(2),

    曲线(3),

    加标(4),

    平行(5),

    标样(6),

    表头扩展(7),

    曲线校准(8),

    空白(9),

    点位(10),

    曲线校核(11),

    室内空白(12),

    校正系数检验(13);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举名称
     *
     * @param value 枚举值
     * @return 枚举名称
     */
    public static String getNameByValue(Integer value) {
        for (EnumDefaultDataType c : EnumDefaultDataType.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        throw new BaseException(String.format("非法的区域类型枚举值[%d]!", value));
    }
}
