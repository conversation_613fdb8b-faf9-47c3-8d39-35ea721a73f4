package com.sinoyd.report.excel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Getter
@AllArgsConstructor
public enum EnumJudgingMethod {

    限值判定(1),
    小于检出限(2),
    回收率(3),
    相对偏差(4),
    相对误差(5),
    绝对偏差(6),
    穿透率(7),
    小于测定下限(8),
    相对准确度(9),
    绝对误差(10);

    /**
     * 枚举值
     */
    private final Integer value;
}
