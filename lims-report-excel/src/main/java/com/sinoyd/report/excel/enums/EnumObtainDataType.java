package com.sinoyd.report.excel.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Getter
@AllArgsConstructor
public enum EnumObtainDataType {

    加标(1),
    平行(2),
    标样(3),
    数据(4),
    曲线(5),
    质控(6),
    测试项目数据(7),
    曲线校准(8),
    空白(9),
    点位(10),
    样品数据(11);


    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举名称
     *
     * @param value 枚举值
     * @return 枚举名称
     */
    public static String getNameByValue(Integer value) {
        for (EnumObtainDataType c : EnumObtainDataType.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        throw new BaseException(String.format("非法的区域类型枚举值[%d]", value));
    }
}
