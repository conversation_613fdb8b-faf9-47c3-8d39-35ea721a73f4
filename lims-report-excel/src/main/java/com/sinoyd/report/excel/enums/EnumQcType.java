package com.sinoyd.report.excel.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质控类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
@Getter
@AllArgsConstructor
public enum EnumQcType {

    平行(1),
    空白(2),
    加标(4),
    标准(8),
    原样加原样(16),
    串联样(32),
    曲线校核(64),
    洗涤剂(128),
    运输空白(256),
    仪器空白(512),
    试剂空白(1024),
    罐空白(2048),
    校正系数检验(4096),
    替代物(8192),
    阴性对照试验(16384),
    阳性对照试验(32768),
    采样介质空白(65536),
    空白加标(131072),
    质控样(262144),
    替代样(524288),
    现场空白(1048576);

    private final Integer value;

    /**
     * 根据值获取名称
     *
     * @param value 值
     * @return 直接返回名称
     */
    public static String getNameByValue(Integer value) {
        for (EnumQcType enumQCType : EnumQcType.values()) {
            if (enumQCType.getValue().equals(value)) {
                return enumQCType.toString();
            }
        }
        throw new BaseException(String.format("非法的质控类型枚举值[%d]", value));
    }
}
