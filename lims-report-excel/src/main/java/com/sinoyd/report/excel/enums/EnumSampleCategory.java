package com.sinoyd.report.excel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 样品类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
@Getter
@AllArgsConstructor
public enum EnumSampleCategory {

    原样(0, -1, -1),
    质控样(1, -1, -1),
    串联样(2, EnumQcGrade.外部质控.getValue(), 32),
    原样加原样(3, EnumQcGrade.内部质控.getValue(), 16),
    比对样(4, -1, -1),
    洗涤剂(5, 1, 128),
    比对评价样(6, -1, -1);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 质控等级
     */
    private final Integer qcGrade;

    /**
     * 质控类型
     */
    private final Integer qcType;
}
