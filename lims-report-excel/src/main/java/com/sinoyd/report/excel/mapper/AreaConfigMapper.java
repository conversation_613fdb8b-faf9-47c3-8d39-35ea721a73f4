package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.excel.vo.AreaConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AreaConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/6
 */
@Mapper
public interface AreaConfigMapper {

    AreaConfigMapper INSTANCE = Mappers.getMapper(AreaConfigMapper.class);

    /**
     * 将实例转换成AreaConfigVO实例
     *
     * @param areaConfig 报表区域配置实体
     * @return 报表区域配置vo实例
     */
    AreaConfigVO toAreaConfigVO(DtoAreaConfig areaConfig);
}
