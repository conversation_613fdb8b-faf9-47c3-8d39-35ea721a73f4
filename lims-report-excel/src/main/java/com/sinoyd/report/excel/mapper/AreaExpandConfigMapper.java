package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoAreaExpandConfig;
import com.sinoyd.report.excel.vo.AreaExpandConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AreaExpandConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/25
 */
@Mapper
public interface AreaExpandConfigMapper {

    AreaExpandConfigMapper INSTANCE = Mappers.getMapper(AreaExpandConfigMapper.class);

    /**
     * 将实例转换成AreaExpandConfigVO实例
     *
     * @param areaExpandConfig 报表区域配置实体
     * @return 报表区域扩展配置vo实例
     */
    AreaExpandConfigVO toAreaExpandConfigVO(DtoAreaExpandConfig areaExpandConfig);
}
