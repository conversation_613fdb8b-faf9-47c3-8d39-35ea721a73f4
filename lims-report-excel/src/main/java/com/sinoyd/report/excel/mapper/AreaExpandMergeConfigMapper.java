package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.excel.vo.AreaExpandMergeConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AreaExpandMergeConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/06/25
 */
@Mapper
public interface AreaExpandMergeConfigMapper {

    AreaExpandMergeConfigMapper INSTANCE = Mappers.getMapper(AreaExpandMergeConfigMapper.class);

    /**
     * 将AreaExpandMergeConfig实例转换成AreaExpandMergeConfigVO实例
     *
     * @param areaExpandMergeConfig 报表区域扩展配置实体
     * @return 报表区域扩展配置vo实例
     */
    AreaExpandMergeConfigVO toAreaExpandMergeConfigVO(DtoAreaExpandMergeConfig areaExpandMergeConfig);
}
