package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.excel.vo.BaseConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * BasicConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/6
 */
@Mapper
public interface BaseConfigMapper {

    BaseConfigMapper INSTANCE = Mappers.getMapper(BaseConfigMapper.class);

    /**
     * 将baseConfig实例转换成BaseConfigVO实例
     *
     * @param baseConfig 报表基础配置实体
     * @return 报表基础配置vo实例
     */
    BaseConfigVO toBasicConfigVO(DtoBaseConfig baseConfig);
}
