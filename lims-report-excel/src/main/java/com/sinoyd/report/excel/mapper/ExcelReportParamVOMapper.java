package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.excel.vo.CommonExcelReportParamVO;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * ExcelReportParamVO映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/6
 */
@Mapper(uses = {BaseConfigMapper.class, GlobalConfigMapper.class, AreaConfigMapper.class})
public interface ExcelReportParamVOMapper {

    ExcelReportParamVOMapper INSTANCE = Mappers.getMapper(ExcelReportParamVOMapper.class);

    /**
     * 将ExcelReportParamVO实例转换成CommonExcelReportParamVO实例
     *
     * @param excelReportParamVO 报表配置参数实体
     * @return 报表配置vo实例
     */
    @Mapping(source = "baseConfig", target = "baseConfig")
    @Mapping(source = "globalConfig", target = "globalConfig")
    @Mapping(source = "areaConfigList", target = "areaConfigList")
    CommonExcelReportParamVO toCommonVO(ExcelReportParamVO excelReportParamVO);
}
