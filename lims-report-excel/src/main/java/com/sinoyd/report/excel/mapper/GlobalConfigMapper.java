package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoGlobalConfig;
import com.sinoyd.report.excel.vo.GlobalConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * GlobalConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/6
 */
@Mapper
public interface GlobalConfigMapper {

    GlobalConfigMapper INSTANCE = Mappers.getMapper(GlobalConfigMapper.class);

    /**
     * 将实例转换成BaseConfigVO实例
     *
     * @param globalConfig 报表全局配置实体
     * @return 报表全局配置vo实例
     */
    GlobalConfigVO toGlobalConfigVO(DtoGlobalConfig globalConfig);
}
