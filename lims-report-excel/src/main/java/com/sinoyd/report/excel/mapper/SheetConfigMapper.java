package com.sinoyd.report.excel.mapper;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.excel.vo.AreaConfigVO;
import com.sinoyd.report.excel.vo.SheetConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * SheetConfig映射器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/06/18
 */
@Mapper
public interface SheetConfigMapper {

    SheetConfigMapper INSTANCE = Mappers.getMapper(SheetConfigMapper.class);

    /**
     * 将实例转换成SheetConfigVO实例
     *
     * @param sheetConfig 报表区域配置实体
     * @return 报表区域配置vo实例
     */
    SheetConfigVO toSheetConfigVO(DtoSheetConfig sheetConfig);
}
