package com.sinoyd.report.excel.service;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.vo.AreaRelationDataVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;

import java.util.List;
import java.util.Map;

/**
 * Excel报表分页服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/14
 */
public interface ExcelReportPageService {
    /**
     * 处理数据行集合
     *
     * @param dsMap              数据行集合
     * @param areaTypeDataMap    区域数据集合
     * @param businessParamVO    业务参数
     * @param excelReportParamVO 报表生成相关参数
     */
    void handleReportPage(Map<String, List<Map<String, Object>>> dsMap,
                          Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap,
                          ExcelBusinessParamVO businessParamVO,
                          ExcelReportParamVO excelReportParamVO,
                          AreaRelationDataVO areaRelationData);
}
