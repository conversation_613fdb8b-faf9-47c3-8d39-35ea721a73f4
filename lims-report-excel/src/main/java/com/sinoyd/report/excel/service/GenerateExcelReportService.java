package com.sinoyd.report.excel.service;


import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * 通用excel报表导出接口(采样单，原始记录单)
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/09
 */
public interface GenerateExcelReportService {

    /**
     * 根据报表基础配置及前端传参获取及组装报表数据
     *
     * @param frontParamVO       接口传参
     * @param excelReportParamVO excel报表生成数据传输对象
     * @return 报表数据
     */
    Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO, ExcelBusinessParamVO businessParamVO);

    /**
     * 根据报表基础配置获取报表数据并生成excel报表
     *
     * @param reportCode   报表编码
     * @param frontParamVO 接口传参
     * @param response     响应对象
     */
    void generate(String reportCode, FrontParamVO frontParamVO, HttpServletResponse response);

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param frontParamVO 接口传参
     * @param outputStream 输出流
     */
    void generateStream(FrontParamVO frontParamVO, OutputStream outputStream);

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param response           响应对象
     */
    void generateExcelReport(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, HttpServletResponse response);

    /**
     * 生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param outputStream       输出流
     */
    void generateExcelReportStream(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, OutputStream outputStream);


    /**
     * 根据报表配置获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    WorkbookDesigner generateDesignerByParam(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap);

    /**
     * 设置报表名称
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    void setExcelReportName(ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO);

    /**
     * excel报表数据源个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    void setExcelReportDataSrcInd(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                  ExcelBusinessParamVO businessParamVO);

    /**
     * excel报表数据源个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    void setExcelReportCellMerge(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                 ExcelBusinessParamVO businessParamVO);

}
