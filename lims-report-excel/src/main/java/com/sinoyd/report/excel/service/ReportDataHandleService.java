package com.sinoyd.report.excel.service;

import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;

import java.util.List;
import java.util.Map;

/**
 * 区域处理类接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
public interface ReportDataHandleService {

    /**
     * 获取区域数据处理后的数据集
     *
     * @param frontParamVO       请求参数
     * @param excelReportParamVO 报表配置数据
     * @return 处理后的数据集
     */
    Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO, ExcelBusinessParamVO businessParamVO);
}
