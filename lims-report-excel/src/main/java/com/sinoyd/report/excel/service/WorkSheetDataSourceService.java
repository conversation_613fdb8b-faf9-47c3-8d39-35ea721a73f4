package com.sinoyd.report.excel.service;

import com.sinoyd.report.dto.*;

import java.util.List;

/**
 * 工作单相关的数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
public interface WorkSheetDataSourceService {

    /**
     * 根据工作单id 获取工作单
     *
     * @param workSheetFolderId 工作单id
     * @return 工作单
     */
    DtoWorksheetFolder findWorkSheetFolder(String workSheetFolderId);

    /**
     * 根据工作单id 获取相关的数据
     *
     * @param workSheetFolderId 工作单id
     * @return 返回数据
     */
    List<DtoAnalyseData> findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 根据样品id 获取样品
     *
     * @param sampleIdList 样品id列表
     * @return 返回数据
     */
    List<DtoSample> findSample(List<String> sampleIdList);

    /**
     * 根据质控信息id，获取质控信息列表
     *
     * @param qcIdList 质控信息id
     * @return 返回数据
     */
    List<DtoQualityControl> findQualityControl(List<String> qcIdList);

    /**
     * 根据关联数据id，获取质控评价列表
     *
     * @param objectIdList 关联数据id
     * @return 返回数据
     */
    List<DtoQualityControlEvaluate> findQualityControlEvaluate(List<String> objectIdList);

    /**
     * 返回工作单相应的表头数据
     *
     * @param workSheetIdList 小工作单id列表
     * @return 返回工作单相应的表头数据
     */
    List<DtoParamsData> findWorkSheetParamsDataByObjId(List<String> workSheetIdList);

    /**
     * 返回工作单相应的曲线数据
     *
     * @param workSheetFolderId 工作单id
     * @return 返回工作单相应的曲线数据
     */
    List<DtoCurve> findCurveByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 返回工作单相应的曲线明细数据
     *
     * @param curveIdList 曲线id列表
     * @return 返回工作单相应的曲线数据
     */
    List<DtoCurveDetail> findCurveDetail(List<String> curveIdList);

    /**
     * 返回工作单相应的仪器使用记录
     *
     * @param workSheetFolderId 检测单id
     * @param objectType        类型
     * @return 返回工作单相应的仪器使用记录
     */
    List<DtoInstrumentUseRecord> findUseRecordByWorkSheetFolderId(String workSheetFolderId, int objectType);

    /**
     * 返回工作单相应的试剂配置记录
     *
     * @param workSheetFolderId 检测单id
     * @param reagentType       试剂类型
     * @return 返回工作单相应的试剂配置记录
     */
    List<DtoWorksheetReagent> findWorkSheetReagent(String workSheetFolderId, int reagentType);

    /**
     * 返回工作单相应的项目信息
     *
     * @param projectIdList 项目id列表
     * @return 项目信息
     */
    List<DtoProject> findProject(List<String> projectIdList);

}
