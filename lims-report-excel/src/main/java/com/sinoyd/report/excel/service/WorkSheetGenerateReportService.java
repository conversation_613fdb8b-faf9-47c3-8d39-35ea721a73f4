package com.sinoyd.report.excel.service;


import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;

import java.util.List;
import java.util.Map;

/**
 * 原始记录单报表导出数据组装处理接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
public interface WorkSheetGenerateReportService {

    /**
     * 根据报表基础配置及前端传参获取及组装报表数据
     *
     * @param frontParamVO       接口传参
     * @param excelReportParamVO excel报表生成数据传输对象
     * @return 报表数据
     */
    Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO);

}
