package com.sinoyd.report.excel.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.JsonUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.entity.CustomParamConfig;
import com.sinoyd.report.enums.EnumAreaType;
import com.sinoyd.report.enums.EnumCustomParamCode;
import com.sinoyd.report.excel.service.ExcelReportPageService;
import com.sinoyd.report.excel.util.ExcelReportUtil;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.SubAreaInfoVO;
import com.sinoyd.report.service.DataSetApplyColumnService;
import com.sinoyd.report.vo.AreaRelationDataVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import com.sinoyd.report.vo.ExcelPlaceholderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.report.enums.EnumAreaType.全局固定区域;
import static com.sinoyd.report.excel.constant.ExcelReportConstants.PARAM_PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.constant.ExcelReportConstants.PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.util.ExcelReportUtil.checkValEmpty;

/**
 * Excel报表分页服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/14
 */
@Component
public class ExcelReportPageServiceImpl implements ExcelReportPageService {

    private DataSetApplyColumnService dataSetApplyColumnService;

    /**
     * 处理数据行集合
     *
     * @param dsMap              数据行集合
     * @param areaTypeDataMap    区域数据集合
     * @param businessParamVO    业务参数
     * @param excelReportParamVO 报表生成相关参数
     */
    @Override
    public void handleReportPage(Map<String, List<Map<String, Object>>> dsMap, Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap,
                                 ExcelBusinessParamVO businessParamVO, ExcelReportParamVO excelReportParamVO, AreaRelationDataVO areaRelationData) {
        //填充info类型数据到dsMap中
        fillInfoAreaData(areaTypeDataMap, areaRelationData, businessParamVO, dsMap);
        List<DtoSheetConfig> sheetConfigList = excelReportParamVO.getSheetConfigList();
        //移除add区域及页数页码的数据，后续单独进行分页处理
        Map<DtoAreaConfig, List<Map<String, Object>>> addDataMap = removeAddPageAreaData(areaTypeDataMap);
        Map<String, String> addFld2PlaceHolderMap = new HashMap<>();
        Map<String, Set<DtoAreaConfig>> field2AreaConfigMap = new HashMap<>();
        List<String> addKeyListForSheet = getAllAddKey(addDataMap, addFld2PlaceHolderMap, field2AreaConfigMap, areaRelationData);
        //区域数据按照对应的sheet页进行分组，同一sheet页的区域数据放在一起
        Map<DtoSheetConfig, Map<DtoAreaConfig, List<Map<String, Object>>>> sheetAreaDataMap = getSheetAreaDataMap(areaTypeDataMap, sheetConfigList);
        Map<String, DtoDataSetApplyColumn> applyColumnMap = getAllApplyColumnMap(sheetConfigList);
        Map<String, Map<String, List<String>>> pagePropertyDataMap = getPagePropertyDataMap(sheetConfigList, applyColumnMap, sheetAreaDataMap);
        //放置add区每一页的数据映射
        Map<Integer, Map<String, List<String>>> addIdx2DataListMap = new HashMap<>();
        //遍历每个sheet页，对其中的区域数据进行分页处理
        for (Map.Entry<DtoSheetConfig, Map<DtoAreaConfig, List<Map<String, Object>>>> entry : sheetAreaDataMap.entrySet()) {
            DtoSheetConfig sheetConfig = entry.getKey();
            Map<DtoAreaConfig, List<Map<String, Object>>> areaConfigDataMap = entry.getValue();
            List<DtoSheetPagingConfig> pagingConfigList = sheetConfig.getSheetPagingConfigList().stream().sorted(Comparator.comparing(DtoSheetPagingConfig::getPriority)
                    .reversed()).collect(Collectors.toList());
            Map<String, List<String>> propertyDataMap = pagePropertyDataMap.getOrDefault(sheetConfig.getId(), new HashMap<>());
            //判断最低优先级的分页配置是否需要按照分页属性对应展示区域数据
            Boolean correspondFlag = getCorrespondFlag(pagingConfigList, applyColumnMap, propertyDataMap);
            int endIdx = correspondFlag ? pagingConfigList.size() - 1 : pagingConfigList.size();
            //遍历每个区域，对其中的数据进行分页处理
            Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap = new HashMap<>();
            Map<Integer, Integer> pageConfigIdx2PageCntMap = new HashMap<>();
            //判断当前sheet页的区域配置中是否有区域数据主从配置，如果有则进行单独处理（例如：原始记录页中平行样区域需要跟随原样样品区域进行分页获取，
            // 此时原样区域为主区域，平行样区域为从属区域，目前仅考虑主从区域配置在同一个sheet页中的情况）
            List<DtoAreaConfig> pagedAreaConfigList = mainSubAreaProcessInd(excelReportParamVO, areaRelationData, applyColumnMap, pagingConfigList, areaConfigDataMap, propertyDataMap, correspondFlag, endIdx,
                    pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap);
            for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> areaDataEntry : areaConfigDataMap.entrySet()) {
                DtoAreaConfig areaConfig = areaDataEntry.getKey();
                if (!pagedAreaConfigList.contains(areaConfig)) {
                    List<Map<String, Object>> areaMapList = areaDataEntry.getValue();
                    areaDataPaged(areaRelationData, applyColumnMap, pagingConfigList, propertyDataMap, areaConfig, areaMapList, correspondFlag, endIdx,
                            pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, 0);
                }
            }
            List<DtoAreaConfig> areaConfigList = new ArrayList<>(areaConfigDataMap.keySet());
            fillDsMap(dsMap, areaRelationData, pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, areaConfigList, addKeyListForSheet,
                    addIdx2DataListMap, businessParamVO, correspondFlag);
        }
        //分页填充add类型数据到dsMap中
        fillAddAreaData(dsMap, addIdx2DataListMap, addFld2PlaceHolderMap, field2AreaConfigMap, addKeyListForSheet);
    }

    /**
     * 区域数据按照所在sheet页的分页属性及区域的扩展方式进行分页
     *
     * @param areaRelationData         区域相关数据VO
     * @param applyColumnMap           数据集应用映射
     * @param pagingConfigList         分页配置列表
     * @param propertyDataMap          分页属性数据值映射
     * @param areaConfig               区域配置对象
     * @param areaMapList              区域数据列表
     * @param pageConfigIdx2PageCntMap 分页后每页数据映射
     * @param addIdx                   分页依据数据索引的前置增量
     */
    private void areaDataPaged(AreaRelationDataVO areaRelationData, Map<String, DtoDataSetApplyColumn> applyColumnMap, List<DtoSheetPagingConfig> pagingConfigList,
                               Map<String, List<String>> propertyDataMap, DtoAreaConfig areaConfig, List<Map<String, Object>> areaMapList, Boolean correspondFlag, int endIdx,
                               Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap, Map<Integer, Integer> pageConfigIdx2PageCntMap, int addIdx) {
        Map<String, String> fld2PlaceHolderMap = getAreaHolderNames(areaRelationData, areaConfig);
        List<String> fldNameList = new ArrayList<>(fld2PlaceHolderMap.keySet());
        //先按照sheet页分页配置进行分页
        List<List<Map<String, Object>>> areaPagedMapList = getPagedData(pagingConfigList, areaMapList, propertyDataMap, applyColumnMap, 0, endIdx);
        if (!correspondFlag) {
            //再按照区域的扩展方式进行分页
            getPagedDataByAreaConfig(areaPagedMapList, areaConfig, pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, addIdx);
        } else {
            getCorrespondPagedData(areaPagedMapList, areaConfig, pagingConfigList.get(pagingConfigList.size() - 1), pageConfigIdx2PageDataMap,
                    pageConfigIdx2PageCntMap, applyColumnMap, propertyDataMap, fldNameList, addIdx);
        }
    }

    /**
     * sheet页区域主从配置数据处理
     *
     * @param areaRelationData   区域相关数据VO
     * @param applyColumnMap     数据集应用映射
     * @param excelReportParamVO excel报表生成数据传输对象
     * @return 已完成分页的主区域及对应从属区域列表
     */
    private List<DtoAreaConfig> mainSubAreaProcessInd(ExcelReportParamVO excelReportParamVO, AreaRelationDataVO areaRelationData, Map<String, DtoDataSetApplyColumn> applyColumnMap,
                                                      List<DtoSheetPagingConfig> pagingConfigList, Map<DtoAreaConfig, List<Map<String, Object>>> areaConfigDataMap,
                                                      Map<String, List<String>> propertyDataMap, Boolean correspondFlag, int endIdx,
                                                      Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap, Map<Integer, Integer> pageConfigIdx2PageCntMap) {
        List<DtoCustomParamConfig> customParamConfigList = excelReportParamVO.getGlobalConfig().getCustomParamConfigList();
        List<DtoAreaConfig> pagedAreaConfigList = new ArrayList<>();
        if (StringUtils.isNotEmpty(customParamConfigList)) {
            List<DtoCustomParamConfig> mainSubConfigList = customParamConfigList.stream().filter(p -> EnumCustomParamCode.区域数据主从配置.getCode().equals(p.getParamCode()))
                    .sorted(Comparator.comparing(CustomParamConfig::getCreateDate)).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(mainSubConfigList)) {
                Map<String, List<DtoAreaConfig>> areaConfigMap = new ArrayList<>(areaConfigDataMap.keySet()).stream().collect(Collectors.groupingBy(DtoAreaConfig::getAreaName));
                for (DtoCustomParamConfig paramConfig : mainSubConfigList) {
                    String configInfo = paramConfig.getConfigInfo();
                    if (StringUtils.isNotEmpty(configInfo)) {
                        Map<String, Object> map = JsonUtils.deserialize(configInfo, Map.class);
                        String mainAreaName = map.getOrDefault("mainAreaName", "").toString();
                        List<Map<String, Object>> subAreaList = (List<Map<String, Object>>) map.getOrDefault("subAreaList", new ArrayList<>());
                        if (areaConfigMap.containsKey(mainAreaName) && StringUtils.isNotEmpty(subAreaList)) {
                            DtoAreaConfig mainAreaConfig = areaConfigMap.get(mainAreaName).get(0);
                            pagedAreaConfigList.add(mainAreaConfig);
                            //收集从属区域信息,封装到Vo对象中，便于后续操作
                            List<SubAreaInfoVO> subAreaInfoVoList = initSubAreaInfo(subAreaList, areaConfigMap, pagedAreaConfigList);
                            //先对主区域数据进行分页
                            List<Map<String, Object>> mainAreaDataList = areaConfigDataMap.get(mainAreaConfig);
                            areaDataPaged(areaRelationData, applyColumnMap, pagingConfigList, propertyDataMap, mainAreaConfig, mainAreaDataList, correspondFlag, endIdx,
                                    pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, 0);
                            //主区域和从属区域数据页数调整时放置默认空白的list
                            Map<String, Map<Integer, List<List<Map<String, Object>>>>> kbPageConfigIdx2PageDataMap = new HashMap<>();
                            Map<String, Map<Integer, List<List<Map<String, Object>>>>> subKbPageConfigIdx2PageDataMap = new HashMap<>();
                            //从属区域数据跟随主区域数据进行分页
                            List<String> keyList = new ArrayList<>(pageConfigIdx2PageDataMap.keySet()).stream().sorted().collect(Collectors.toList());
                            for (int m = 0; m < keyList.size(); m++) {
                                String key = keyList.get(m);
                                int loopIdx = Integer.parseInt(key.split("_")[1]);
                                List<List<Map<String, Object>>> mainPageConfigDataList = pageConfigIdx2PageDataMap.get(key);
                                //临时放置每一页主区域数据对应的从属区域数据的页数,每次遍历一页主区域数据后就把各从属区域数据的页数保存起来
                                Map<String, Integer> tmpPageConfigIdx2PageCntMap = new HashMap<>();
                                Map<String, Integer> subAreaPageCntMap = new HashMap<>();
                                for (int i = 0; i < mainPageConfigDataList.size(); i++) {
                                    List<Map<String, Object>> mainPageDataList = mainPageConfigDataList.get(i);
                                    //从当前页主区域数据中构建一个 propertyDataMap 映射（由于当前页主区域数据已经按照各个分页属性分页完毕，
                                    // 因此新构建的propertyDataMap中每个分页属性只会有一组值）,新构建的propertyDataMap映射用于从属区域数据在当前页的主区域数据上进行分页
                                    Map<String, List<String>> mainPagePropertyDataMap = getPropertyDataMap(applyColumnMap, pagingConfigList,
                                            Collections.singletonMap(mainAreaConfig, mainPageDataList));
                                    //遍历每个从属区域，过滤出当前页主区域数据关联的从属区域数据（用全局配置中的关联属性进行过滤）并进行分页
                                    for (SubAreaInfoVO subAreaInfoVO : subAreaInfoVoList) {
                                        List<Map<String, Object>> fltSubDataList = fltSubAreaDataByMainData(subAreaInfoVO, mainPageDataList, areaConfigDataMap,
                                                new ArrayList<>(propertyDataMap.keySet()));
                                        areaDataPaged(areaRelationData, applyColumnMap, pagingConfigList, mainPagePropertyDataMap, subAreaInfoVO.getAreaConfig(), fltSubDataList,
                                                correspondFlag, endIdx, pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, m);
                                    }
                                    //由于每一页主区域数据可能会对应多页的从属区域数据，因此在从属区域数据分页完成后需要将各个区域的页数调整一致
                                    //先获取当前页主区域数据对应的从属区域数据的最大页数
                                    int maxSubPageCnt = getMaxSubPageCnt(subAreaInfoVoList, pageConfigIdx2PageDataMap, tmpPageConfigIdx2PageCntMap, subAreaPageCntMap, loopIdx);
                                    //按照从属区域数据的最大页数调整各个从属区域的页数（加入空白页数据）
                                    addSubAreaKbPageData(subKbPageConfigIdx2PageDataMap, tmpPageConfigIdx2PageCntMap, subAreaPageCntMap, subAreaInfoVoList, maxSubPageCnt, loopIdx);
                                    //再按照该最大页数调整主区域数据的页数（加入空白页数据）
                                    addMainAreaKbPageData(kbPageConfigIdx2PageDataMap, key, maxSubPageCnt, i);
                                    subAreaPageCntMap.clear();
                                }
                            }
                            //遍历完每一页的主区域数据后，再把新增的空白页加入到各区域数据中（包括主区域和从属区域）
                            insertMainKbPageData(kbPageConfigIdx2PageDataMap, pageConfigIdx2PageDataMap);
                            insertMainKbPageData(subKbPageConfigIdx2PageDataMap, pageConfigIdx2PageDataMap);
                            //重新调整 pageConfigIdx2PageCntMap 的数据（加入空白页后,每个分页属性下的页数会增加，因此需要重新调整，
                            // 由于主区域数据的页数已经和各从属区域的数据页数调整一致，因此这里无需考虑从属区域新增的空白数据导致的页数的增加）
                            reCntPageConfigIdx2PageCntMap(pageConfigIdx2PageDataMap, pageConfigIdx2PageCntMap, mainAreaConfig);
                        }
                    }
                }
            }
        }
        return pagedAreaConfigList;
    }

    /**
     * 重新调整 pageConfigIdx2PageCntMap 的数据
     *
     * @param pageConfigIdx2PageDataMap 分页属性分页数据
     * @param pageConfigIdx2PageCntMap  各个分页属性对应的最大页数映射
     * @param mainAreaConfig            主区域对象
     */
    private void reCntPageConfigIdx2PageCntMap(Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap, Map<Integer, Integer> pageConfigIdx2PageCntMap,
                                               DtoAreaConfig mainAreaConfig) {
        Set<String> keySet = pageConfigIdx2PageDataMap.keySet();
        List<String> mainKeyList = keySet.stream().filter(p -> p.contains(mainAreaConfig.getId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(mainKeyList)) {
            for (String mainKey : mainKeyList) {
                int size = pageConfigIdx2PageDataMap.get(mainKey).size(), idx = Integer.valueOf(mainKey.split("_")[1]);
                if (pageConfigIdx2PageCntMap.containsKey(idx)) {
                    int cnt = pageConfigIdx2PageCntMap.get(idx);
                    pageConfigIdx2PageCntMap.put(idx, Math.max(cnt, size));
                }
            }
        }
    }

    /**
     * 新增的空白页数据加入到主区域数据中
     *
     * @param kbPageConfigIdx2PageDataMap 新增的空白页数据
     * @param pageConfigIdx2PageDataMap   分页数据映射
     */
    private void insertMainKbPageData(Map<String, Map<Integer, List<List<Map<String, Object>>>>> kbPageConfigIdx2PageDataMap,
                                      Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap) {
        for (Map.Entry<String, Map<Integer, List<List<Map<String, Object>>>>> entry : kbPageConfigIdx2PageDataMap.entrySet()) {
            String key = entry.getKey();
            Map<Integer, List<List<Map<String, Object>>>> kbDataMap = entry.getValue();
            if (pageConfigIdx2PageDataMap.containsKey(key)) {
                List<List<Map<String, Object>>> oriPageDataList = pageConfigIdx2PageDataMap.get(key);
                //在原有数据中插入新数据
                List<Integer> instIdxList = new ArrayList<>(kbDataMap.keySet());
                List<List<Map<String, Object>>> newPageDataList = new ArrayList<>();
                for (int i = 0; i < oriPageDataList.size(); i++) {
                    newPageDataList.add(oriPageDataList.get(i));
                    if (instIdxList.contains(i)) {
                        List<List<Map<String, Object>>> kbDataList = kbDataMap.get(i);
                        newPageDataList.addAll(kbDataList);
                    }
                }
                pageConfigIdx2PageDataMap.put(key, newPageDataList);
            }
        }
    }

    /**
     * 按照最大页数调整从属区域数据的页数（加入空白页数据）
     *
     * @param subKbPageConfigIdx2PageDataMap 存放新增的空白页数据
     * @param maxSubPageCnt                  最大页数
     * @param subAreaInfoVoList              从属区域数据列表
     */
    private void addSubAreaKbPageData(Map<String, Map<Integer, List<List<Map<String, Object>>>>> subKbPageConfigIdx2PageDataMap,
                                      Map<String, Integer> tmpPageConfigIdx2SubPageCntMap, Map<String, Integer> subAreaPageCntMap, List<SubAreaInfoVO> subAreaInfoVoList,
                                      int maxSubPageCnt, int loopIdx) {
        for (SubAreaInfoVO subAreaInfoVO : subAreaInfoVoList) {
            String areaConfigId = subAreaInfoVO.getAreaConfig().getId();
            String key = areaConfigId + "_" + loopIdx;
            int subPageCnt = tmpPageConfigIdx2SubPageCntMap.get(key);
            if (subPageCnt > 0) {
                if (!subKbPageConfigIdx2PageDataMap.containsKey(key)) {
                    subKbPageConfigIdx2PageDataMap.put(key, new HashMap<>());
                }
                int areaPageCnt = subAreaPageCntMap.getOrDefault(areaConfigId, 1);
                Map<Integer, List<List<Map<String, Object>>>> areaKbListMap = subKbPageConfigIdx2PageDataMap.get(key);
                if (maxSubPageCnt > areaPageCnt) {
                    areaKbListMap.put(subPageCnt - 1, new ArrayList<>());
                    for (int i = areaPageCnt; i < maxSubPageCnt; i++) {
                        List<Map<String, Object>> kbList = Collections.singletonList(new HashMap<>());
                        areaKbListMap.get(subPageCnt - 1).add(kbList);
                    }
                }
            }
        }
    }

    /**
     * 按照最大页数调整主区域数据的页数（加入空白页数据）
     *
     * @param kbPageConfigIdx2PageDataMap 存放新增的空白页数据
     * @param maxSubPageCnt               最大页数
     * @param insertIdx                   插入空白数据的列表位置索引
     */
    private void addMainAreaKbPageData(Map<String, Map<Integer, List<List<Map<String, Object>>>>> kbPageConfigIdx2PageDataMap, String key,
                                       int maxSubPageCnt, int insertIdx) {
        if (!kbPageConfigIdx2PageDataMap.containsKey(key)) {
            kbPageConfigIdx2PageDataMap.put(key, new HashMap<>());
        }
        Map<Integer, List<List<Map<String, Object>>>> KbListMap = kbPageConfigIdx2PageDataMap.get(key);
        if (maxSubPageCnt > 1) {
            KbListMap.put(insertIdx, new ArrayList<>());
            for (int i = 1; i < maxSubPageCnt; i++) {
                List<Map<String, Object>> kbList = Collections.singletonList(new HashMap<>());
                KbListMap.get(insertIdx).add(kbList);
            }
        }
    }

    /**
     * 获取当前页主区域数据对应的从属区域数据的最大页数
     *
     * @param subAreaInfoVoList              从属区域信息列表
     * @param pageConfigIdx2PageDataMap      各区域分页数据映射
     * @param tmpPageConfigIdx2SubPageCntMap 每一页主区域数据对应的从属区域数据的页数
     * @param loopIdx                        当前遍历的分页属性的索引
     */
    private int getMaxSubPageCnt(List<SubAreaInfoVO> subAreaInfoVoList, Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap,
                                 Map<String, Integer> tmpPageConfigIdx2SubPageCntMap, Map<String, Integer> subAreaPageCntMap, Integer loopIdx) {
        int maxSubPageCnt = 1;
        for (SubAreaInfoVO subAreaInfoVO : subAreaInfoVoList) {
            String areaId = subAreaInfoVO.getAreaConfig().getId();
            subAreaPageCntMap.put(areaId, 1);
            String key = areaId + "_" + loopIdx;
            List<List<Map<String, Object>>> pageDataList = pageConfigIdx2PageDataMap.get(key);
            if (StringUtils.isNotEmpty(pageDataList)) {
                int size = pageDataList.size();
                if (tmpPageConfigIdx2SubPageCntMap.containsKey(key)) {
                    size = size - tmpPageConfigIdx2SubPageCntMap.get(key);
                }
                if (size > maxSubPageCnt) {
                    maxSubPageCnt = size;
                }
                subAreaPageCntMap.put(areaId, size);
                tmpPageConfigIdx2SubPageCntMap.put(key, pageDataList.size());
            }
        }
        return maxSubPageCnt;
    }

    /**
     * 过滤出从属区域数据
     *
     * @param subAreaInfoVO     从属区域信息对象
     * @param mainPageDataList  主区域数据
     * @param areaConfigDataMap 区域数据映射
     * @param propertyList      分页属性数据值映射
     * @return 过滤后的区域数据
     */
    private List<Map<String, Object>> fltSubAreaDataByMainData(SubAreaInfoVO subAreaInfoVO, List<Map<String, Object>> mainPageDataList, Map<DtoAreaConfig, List<Map<String, Object>>> areaConfigDataMap,
                                                               List<String> propertyList) {
        DtoAreaConfig subConfig = subAreaInfoVO.getAreaConfig();
        if (StringUtils.isNotNull(subConfig)) {
            List<Map<String, Object>> subAreaDataList = areaConfigDataMap.getOrDefault(subConfig, Collections.singletonList(new HashMap<>()));
            Map<String, Object> mainPageData = StringUtils.isNotEmpty(mainPageDataList) ? mainPageDataList.get(0) : new HashMap<>();
            for (String property : propertyList) {
                if (StringUtils.isNotNull(mainPageData.get(property))) {
                    subAreaDataList = subAreaDataList.stream().filter(p -> StringUtils.isNotNull(p.get(property)) && mainPageData.get(property).toString()
                            .equals(p.get(property).toString())).collect(Collectors.toList());
                }
            }
            String mainRelatedProp = StringUtils.isNotNull(subAreaInfoVO.getMainRelatedProp()) ? subAreaInfoVO.getMainRelatedProp() : "",
                    subRelatedProp = StringUtils.isNotNull(subAreaInfoVO.getSubRelatedProp()) ? subAreaInfoVO.getSubRelatedProp() : "";
            Set<String> mainRelPropValSet = new HashSet<>();
            for (Map<String, Object> map : mainPageDataList) {
                if (StringUtils.isNotNull(map.get(mainRelatedProp))) {
                    mainRelPropValSet.add(map.get(mainRelatedProp).toString());
                }
            }
            subAreaDataList = subAreaDataList.stream().filter(p -> StringUtils.isNotNull(p.get(subRelatedProp)) && mainRelPropValSet.contains(p.get(subRelatedProp).toString())).collect(Collectors.toList());
            if (subAreaDataList.isEmpty()) {
                subAreaDataList.add(new HashMap<>());
            }
            return subAreaDataList;
        }
        return Collections.singletonList(new HashMap<>());
    }

    /**
     * 收集从属区域信息,封装到Vo对象中
     *
     * @param subAreaList   从属区域配置信息
     * @param areaConfigMap 区域配置映射
     */
    private List<SubAreaInfoVO> initSubAreaInfo(List<Map<String, Object>> subAreaList, Map<String, List<DtoAreaConfig>> areaConfigMap, List<DtoAreaConfig> pagedAreaConfigList) {
        List<SubAreaInfoVO> subAreaInfoVoList = new ArrayList<>();
        for (Map<String, Object> subArea : subAreaList) {
            String subAreaName = subArea.getOrDefault("subAreaName", "").toString(), mainRelatedProp = subArea.getOrDefault("mainRelatedProp", "").toString(),
                    subRelatedProp = subArea.getOrDefault("subRelatedProp", "").toString();
            DtoAreaConfig areaConfig = areaConfigMap.containsKey(subAreaName) ? areaConfigMap.get(subAreaName).get(0) : null;
            if (StringUtils.isNotNull(areaConfig)) {
                subAreaInfoVoList.add(new SubAreaInfoVO(areaConfig, mainRelatedProp, subRelatedProp));
                pagedAreaConfigList.add(areaConfig);
            }
        }
        return subAreaInfoVoList;
    }

    /**
     * 填充info类型数据到dsMap中
     *
     * @param areaTypeDataMap 区域数据映射
     * @param dsMap           报表数据集
     */
    private void fillInfoAreaData(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap, AreaRelationDataVO areaRelationData,
                                  ExcelBusinessParamVO businessParamVO, Map<String, List<Map<String, Object>>> dsMap) {
        List<DtoAreaConfig> areaConfigList = new ArrayList<>(areaTypeDataMap.keySet());
        List<DtoAreaConfig> infoAreaList = areaConfigList.stream().filter(p -> 全局固定区域.getValue().equals(p.getAreaType())).collect(Collectors.toList());
        Map<String, Object> infoMap = new HashMap<>();
        if (StringUtils.isNotEmpty(infoAreaList)) {
            for (DtoAreaConfig infoArea : infoAreaList) {
                Map<String, String> fld2PlaceHolderMap = getAreaHolderNames(areaRelationData, infoArea);
                Map<String, Object> map = areaTypeDataMap.get(infoArea).get(0);
                List<Map<String, Object>> holderRowList = toHolderRowMapList(Collections.singletonList(map), fld2PlaceHolderMap, businessParamVO, infoArea);
                infoMap.putAll(holderRowList.get(0));
                areaTypeDataMap.remove(infoArea);
            }
        }
        dsMap.put(全局固定区域.getValue() + 1, Collections.singletonList(infoMap));
    }

    /**
     * 移除add区域及页码区域的数据，后续单独进行分页处理
     *
     * @param areaTypeDataMap 区域数据映射
     * @return add区数据映射
     */
    private Map<DtoAreaConfig, List<Map<String, Object>>> removeAddPageAreaData(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap) {
        Map<DtoAreaConfig, List<Map<String, Object>>> addDataMap = new HashMap<>();
        List<DtoAreaConfig> areaConfigList = new ArrayList<>(areaTypeDataMap.keySet());
        List<DtoAreaConfig> addAreaList = areaConfigList.stream().filter(p -> EnumAreaType.分页表头区域.getValue().equals(p.getAreaType())
                || EnumAreaType.页码数据区域.getValue().equals(p.getAreaType())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(addAreaList)) {
            for (DtoAreaConfig config : addAreaList) {
                if (EnumAreaType.分页表头区域.getValue().equals(config.getAreaType())) {
                    addDataMap.put(config, areaTypeDataMap.get(config));
                }
                areaTypeDataMap.remove(config);
            }
        }
        return addDataMap;
    }

    /**
     * 分页填充add类型数据到dsMap中
     *
     * @param dsMap                 报表数据集
     * @param addIdx2DataListMap    各sheet页各区域的分页数据
     * @param addFld2PlaceHolderMap add区字段名称与模板占位符的关系
     */
    private void fillAddAreaData(Map<String, List<Map<String, Object>>> dsMap, Map<Integer, Map<String, List<String>>> addIdx2DataListMap,
                                 Map<String, String> addFld2PlaceHolderMap, Map<String, Set<DtoAreaConfig>> field2AreaConfigMap, List<String> addKeyListForSheet) {
        for (Map.Entry<Integer, Map<String, List<String>>> entry : addIdx2DataListMap.entrySet()) {
            Integer addPage = entry.getKey();
            Map<String, Object> addPageMap = new HashMap<>();
            Map<String, List<String>> addMap = entry.getValue();
            for (Map.Entry<String, List<String>> entry1 : addMap.entrySet()) {
                if (addFld2PlaceHolderMap.containsKey(entry1.getKey())) {
                    String placeHolder = addFld2PlaceHolderMap.get(entry1.getKey());
                    List<String> valList = entry1.getValue().stream().filter(StringUtils::isNotEmpty).distinct().sorted().collect(Collectors.toList());
                    addPageMap.put(placeHolder, String.join("，", valList));
                }
            }
            for (Map.Entry<String, String> holderEntry: addFld2PlaceHolderMap.entrySet()) {
                String holderKey = holderEntry.getKey();
                if (!addPageMap.containsKey(holderEntry.getValue())) {
                    String dftVal = StringUtils.isNotEmpty(field2AreaConfigMap.get(holderKey))
                            ? new ArrayList<>(field2AreaConfigMap.get(holderKey)).get(0).getEmptyPlaceHolder() : "";
                    addPageMap.put(holderEntry.getValue(), StringUtils.isNotEmpty(dftVal) ? dftVal : "");
                }
            }
            dsMap.put(EnumAreaType.分页表头区域.getValue() + addPage, Collections.singletonList(addPageMap));
        }
    }

    /**
     * 获取所有add区域配置的字段名
     *
     * @param addDataMap add区数据映射
     * @return 所有add区域配置的字段名
     */
    private List<String> getAllAddKey(Map<DtoAreaConfig, List<Map<String, Object>>> addDataMap, Map<String, String> addFld2PlaceHolderMap,
                                      Map<String, Set<DtoAreaConfig>> field2AreaConfigMap, AreaRelationDataVO areaRelationData) {
        List<String> addKeyList = new ArrayList<>();
        for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : addDataMap.entrySet()) {
            for (Map<String, Object> map : entry.getValue()) {
                for (Map.Entry<String, Object> entry1 : map.entrySet()) {
                    if (!addKeyList.contains(entry1.getKey())) {
                        addKeyList.add(entry1.getKey());
                    }
                }
            }
            addFld2PlaceHolderMap.putAll(getAreaHolderNames(areaRelationData, entry.getKey()));
            fillField2AreaConfigMap(areaRelationData, entry.getKey(), field2AreaConfigMap);
        }
        return addKeyList;
    }

    /**
     * 获取区域数据是否跟随分页属性对应展示标记（默认只在最低优先级的分页配置上配置展示标记）
     *
     * @param pagingConfigList sheet页分页配置
     */
    private Boolean getCorrespondFlag(List<DtoSheetPagingConfig> pagingConfigList, Map<String, DtoDataSetApplyColumn> applyColumnMap,
                                      Map<String, List<String>> propertyDataMap) {
        if (StringUtils.isNotEmpty(pagingConfigList)) {
            DtoSheetPagingConfig lastPagingConfig = pagingConfigList.get(pagingConfigList.size() - 1);
            Boolean flag = lastPagingConfig.getCorrespondFlag();
            List<String> propertyData = getPropertyData(lastPagingConfig, applyColumnMap, propertyDataMap);
            return StringUtils.isNotNull(flag) && flag && StringUtils.isNotEmpty(propertyData);
        }
        return false;
    }

    /**
     * 用分好页的数据填充dsMap，同一sheet页分页配置下，区域数据页数不同时补上空白数据
     *
     * @param dsMap                     报表数据源
     * @param areaRelationData          区域关系数据
     * @param pageConfigIdx2PageDataMap 区域分页数据映射
     * @param pageConfigIdx2PageCntMap  每个sheet页分页配置对应的页数最大值映射
     * @param areaConfigList            区域配置列表
     */
    private void fillDsMap(Map<String, List<Map<String, Object>>> dsMap, AreaRelationDataVO areaRelationData, Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap,
                           Map<Integer, Integer> pageConfigIdx2PageCntMap, List<DtoAreaConfig> areaConfigList, List<String> addKeyListForSheet,
                           Map<Integer, Map<String, List<String>>> addIdx2DataListMap, ExcelBusinessParamVO businessParamVO, Boolean correspondFlag) {
        List<String> areaConfigIdIdxList = new ArrayList<>(pageConfigIdx2PageDataMap.keySet());
        int page = 1;
        for (DtoAreaConfig areaConfig : areaConfigList) {
            boolean coFlag = correspondFlag && StringUtils.isNotNull(areaConfig.getPageColumnSize()) && areaConfig.getPageColumnSize() > 0;
            Map<String, String> fld2PlaceHolderMap = getAreaHolderNames(areaRelationData, areaConfig);
            List<String> holderNameList = new ArrayList<>(fld2PlaceHolderMap.values());
            List<String> keyList = areaConfigIdIdxList.stream().filter(p -> p.contains(areaConfig.getId())).sorted().collect(Collectors.toList());
            for (String key : keyList) {
                List<List<Map<String, Object>>> pageDataListForIdx = pageConfigIdx2PageDataMap.get(key);
                Integer idx = Integer.valueOf(key.split("_")[1]);
                int maxPageForIdx = pageConfigIdx2PageCntMap.get(idx);
                for (int j = 0; j < maxPageForIdx; j++) {
                    if (j < pageDataListForIdx.size()) {
                        List<Map<String, Object>> pageFldRowList = new ArrayList<>(pageDataListForIdx.get(j));
                        if (!addIdx2DataListMap.containsKey(page)) {
                            addIdx2DataListMap.put(page, new HashMap<>());
                        }
                        Map<String, List<String>> addDataListMap = addIdx2DataListMap.get(page);
                        for (Map<String, Object> rowMap : pageFldRowList) {
                            for (Map.Entry<String, Object> entry : rowMap.entrySet()) {
                                String fldKey = entry.getKey();
                                if (addKeyListForSheet.contains(fldKey) && StringUtils.isNotNull(entry.getValue())) {
                                    if (!addDataListMap.containsKey(fldKey)) {
                                        addDataListMap.put(fldKey, new ArrayList<>());
                                    }
                                    addDataListMap.get(fldKey).add(entry.getValue().toString());
                                }
                            }
                        }
                        List<Map<String, Object>> pageHolderRowList = toHolderRowMapList(pageFldRowList, fld2PlaceHolderMap, businessParamVO, areaConfig);
                        if (pageHolderRowList.isEmpty() || pageHolderRowList.get(0).isEmpty()) {
                            pageHolderRowList.clear();
                            pageHolderRowList.add(initKbMap(areaConfig, holderNameList, coFlag));
                        } else if (pageHolderRowList.size() < areaConfig.getExpandPageSize()) {
                            pageHolderRowList.add(initKbMap(areaConfig, holderNameList, coFlag));
                        }
                        dsMap.put(areaConfig.getAreaType() + page, pageHolderRowList);
                    } else {
                        dsMap.put(areaConfig.getAreaType() + page, Collections.singletonList(initKbMap(areaConfig, holderNameList, coFlag)));
                    }
                    page++;
                }
            }
            page = 1;
        }
    }

    /**
     * 初始化空数据map对象
     *
     * @param areaConfig     区域配置对象
     * @param holderNameList 模板占位符名称
     */
    private Map<String, Object> initKbMap(DtoAreaConfig areaConfig, List<String> holderNameList, Boolean coFlag) {
        if (StringUtils.isNotNull(coFlag) && coFlag) {
            int pageColumnSize = (StringUtils.isNotNull(areaConfig.getPageColumnSize()) && areaConfig.getPageColumnSize() > 0) ? areaConfig.getPageColumnSize() : 1;
            List<String> fldList = new ArrayList<>();
            for (String holderName : holderNameList) {
                for (int i = 0; i < pageColumnSize; i++) {
                    fldList.add(holderName + i);
                }
            }
            return ExcelReportUtil.createKbMap(fldList, PLACEHOLDER_PREFIX + areaConfig.getEmptyTemplatePlaceHolder() + "0", areaConfig.getBlankIdentifier());
        } else {
            return ExcelReportUtil.createKbMap(holderNameList, PLACEHOLDER_PREFIX + areaConfig.getEmptyTemplatePlaceHolder(), areaConfig.getBlankIdentifier());
        }
    }

    /**
     * 转换为模板占位符对应的行数据映射列表
     *
     * @param fldRowMapList      区域关联数据列名称与数据值的映射
     * @param fld2PlaceHolderMap 区域关联数据列名称与模板占位符名称的映射
     * @return 模板占位符对应的行数据映射列表
     */
    private List<Map<String, Object>> toHolderRowMapList(List<Map<String, Object>> fldRowMapList, Map<String, String> fld2PlaceHolderMap,
                                                         ExcelBusinessParamVO businessParamVO, DtoAreaConfig areaConfig) {
        List<Map<String, Object>> holderRowMapList = new ArrayList<>();
        for (Map<String, Object> fldRowMap : fldRowMapList) {
            Map<String, Object> holderRowMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : fldRowMap.entrySet()) {
                String[] fldArr = entry.getKey().split("_");
                String idxStr = fldArr.length > 1 ? fldArr[1] : "";
                if (fld2PlaceHolderMap.containsKey(fldArr[0])) {
                    holderRowMap.put(fld2PlaceHolderMap.get(fldArr[0]) + idxStr, checkValEmpty(entry.getValue(), areaConfig.getEmptyPlaceHolder()));
                }
            }
            String analyseDataId = fldRowMap.containsKey("analyseDataId") ? fldRowMap.get("analyseDataId").toString() : UUIDHelper.guidEmpty();
            String folderId = StringUtils.isNotNull(businessParamVO.getWorkSheetFolderId()) ? businessParamVO.getWorkSheetFolderId() : UUIDHelper.guidEmpty();
            List<DtoParamsData> paramsDataList = businessParamVO.getSampleParamsDataMap().getOrDefault(analyseDataId, new ArrayList<>());
            List<DtoParamsData> worksheetFolderParamsDataList = businessParamVO.getWorksheetFolderParamsDataMap().getOrDefault(folderId, new ArrayList<>());
            paramsDataList.addAll(worksheetFolderParamsDataList);
            if (StringUtils.isNotEmpty(paramsDataList)) {
                for (DtoParamsData paramsData : paramsDataList) {
                    holderRowMap.put(PARAM_PLACEHOLDER_PREFIX + paramsData.getParamsConfigName(), checkValEmpty(paramsData.getParamsValue(), areaConfig.getEmptyPlaceHolder()));
                }
            }
            holderRowMapList.add(holderRowMap);
        }
        return holderRowMapList;
    }

    /**
     * 填充区域字段名称与区域配置对象的映射关系
     *
     * @param areaRelationData 区域相关数据VO
     * @param areaConfig       区域对象
     */
    private void fillField2AreaConfigMap(AreaRelationDataVO areaRelationData, DtoAreaConfig areaConfig, Map<String, Set<DtoAreaConfig>> field2AreaConfigMap) {
        List<ExcelPlaceholderVO> placeholderVOList = areaRelationData.getPlaceholderToColMap().getOrDefault(areaConfig.getId(), new ArrayList<>());
        for (ExcelPlaceholderVO vo : placeholderVOList) {
            if (!field2AreaConfigMap.containsKey(vo.getField())) {
                field2AreaConfigMap.put(vo.getField(), new HashSet<>());
            }
            field2AreaConfigMap.get(vo.getField()).add(areaConfig);
        }
    }

    /**
     * 获取区域字段名称与模板占位符的映射关系
     *
     * @param areaRelationData 区域相关数据VO
     * @param areaConfig       区域对象
     * @return 区域字段名称与模板占位符的映射关系
     */
    private Map<String, String> getAreaHolderNames(AreaRelationDataVO areaRelationData, DtoAreaConfig areaConfig) {
        List<ExcelPlaceholderVO> placeholderVOList = areaRelationData.getPlaceholderToColMap().getOrDefault(areaConfig.getId(), new ArrayList<>());
        Map<String, String> fld2PlaceHolderMap = new HashMap<>();
        placeholderVOList.forEach(p -> fld2PlaceHolderMap.put(p.getField(), p.getPlaceholder()));
        return fld2PlaceHolderMap;
    }

    /**
     * 获取分页配置对应的数据集应用列映射
     *
     * @param sheetConfigList sheet页配置
     * @return 数据集应用列映射
     */
    private Map<String, DtoDataSetApplyColumn> getAllApplyColumnMap(List<DtoSheetConfig> sheetConfigList) {
        List<DtoSheetPagingConfig> allPagingConfigList = new ArrayList<>();
        for (DtoSheetConfig sheetConfig : sheetConfigList) {
            if (StringUtils.isNotEmpty(sheetConfig.getSheetPagingConfigList())) {
                allPagingConfigList.addAll(sheetConfig.getSheetPagingConfigList());
            }
        }
        List<String> allApplyColumnIdList = allPagingConfigList.stream().map(DtoSheetPagingConfig::getDataSetApplyColumnId).distinct().collect(Collectors.toList());
        List<DtoDataSetApplyColumn> applyColumnList = StringUtils.isNotEmpty(allApplyColumnIdList) ? dataSetApplyColumnService.findAll(allApplyColumnIdList) : new ArrayList<>();
        return applyColumnList.stream().collect(Collectors.toMap(DtoDataSetApplyColumn::getId, dto -> dto));
    }

    /**
     * 获取分页配置对应的分页属性数据信息
     *
     * @param sheetConfigList  sheet页配置
     * @param sheetAreaDataMap 数据区域数据映射
     * @return 分页属性数据信息
     */
    private Map<String, Map<String, List<String>>> getPagePropertyDataMap(List<DtoSheetConfig> sheetConfigList, Map<String, DtoDataSetApplyColumn> applyColumnMap,
                                                                          Map<DtoSheetConfig, Map<DtoAreaConfig, List<Map<String, Object>>>> sheetAreaDataMap) {
        Map<String, Map<String, List<String>>> sheetPropertyDataMap = new HashMap<>();
        for (DtoSheetConfig sheetConfig : sheetConfigList) {
            List<DtoSheetPagingConfig> sheetPagingConfigList = sheetConfig.getSheetPagingConfigList();
            if (StringUtils.isNotEmpty(sheetPagingConfigList) && sheetAreaDataMap.containsKey(sheetConfig)) {
                Map<DtoAreaConfig, List<Map<String, Object>>> areaDataMap = sheetAreaDataMap.get(sheetConfig);
                Map<String, List<String>> propertyDataMap = getPropertyDataMap(applyColumnMap, sheetPagingConfigList, areaDataMap);
                sheetPropertyDataMap.put(sheetConfig.getId(), propertyDataMap);
            }
        }
        return sheetPropertyDataMap;
    }

    /**
     * 获取分页配置对应的分页属性数据信息
     *
     * @param applyColumnMap        sheet页配置
     * @param sheetPagingConfigList 分页配置列表
     * @param areaDataMap           区域数据映射
     * @return 分页属性数据信息映射
     */
    private Map<String, List<String>> getPropertyDataMap(Map<String, DtoDataSetApplyColumn> applyColumnMap, List<DtoSheetPagingConfig> sheetPagingConfigList,
                                                         Map<DtoAreaConfig, List<Map<String, Object>>> areaDataMap) {
        Map<String, List<String>> propertyDataMap = new HashMap<>();
        List<String> columnCodeList = new ArrayList<>();
        List<String> applyColumnIdList = sheetPagingConfigList.stream().map(DtoSheetPagingConfig::getDataSetApplyColumnId).distinct().collect(Collectors.toList());
        for (String applyColumnId : applyColumnIdList) {
            if (applyColumnMap.containsKey(applyColumnId)) {
                String columnCode = applyColumnMap.get(applyColumnId).getColumnCode();
                columnCodeList.add(columnCode);
                propertyDataMap.put(columnCode, new ArrayList<>());
            }
        }
        for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : areaDataMap.entrySet()) {
            List<Map<String, Object>> areaDataList = entry.getValue();
            for (Map<String, Object> map : areaDataList) {
                for (String columnCode : columnCodeList) {
                    String str = StringUtils.isNotNull(map.get(columnCode)) ? map.get(columnCode).toString() : "";
                    if (map.containsKey(columnCode) && !propertyDataMap.get(columnCode).contains(str) && StringUtils.isNotEmpty(str)) {
                        propertyDataMap.get(columnCode).add(str);
                    }
                }
            }
        }
        return propertyDataMap;
    }

    /**
     * 按照分页配置，对数据进行分组处理
     *
     * @param pagingConfigList 分页配置列表
     * @param areaMapList      数据区域数据映射列表
     * @param idx              分页配置索引
     * @return 分好页的数据列表
     */
    private List<List<Map<String, Object>>> getPagedData(List<DtoSheetPagingConfig> pagingConfigList, List<Map<String, Object>> areaMapList,
                                                         Map<String, List<String>> propertyDataMap, Map<String, DtoDataSetApplyColumn> applyColumnMap, int idx, int endIdx) {
        List<List<Map<String, Object>>> areaPagedMapList = new ArrayList<>();
        if (StringUtils.isNotEmpty(pagingConfigList) && idx < endIdx) {
            DtoSheetPagingConfig pagingConfig = pagingConfigList.get(idx);
            String applyColumnId = pagingConfig.getDataSetApplyColumnId();
            int countPerPage = (StringUtils.isNull(pagingConfig.getCountPerPage()) || pagingConfig.getCountPerPage() <= 0) ? 1 : pagingConfig.getCountPerPage();
            String columnCode = applyColumnMap.containsKey(applyColumnId) ? applyColumnMap.get(applyColumnId).getColumnCode() : "";
            List<List<Map<String, Object>>> tmpList = new ArrayList<>();
            if (StringUtils.isNotEmpty(columnCode)) {
                List<String> propertyData = propertyDataMap.get(columnCode);
                if (StringUtils.isNotEmpty(propertyData)) {
                    for (int i = 0; i < propertyData.size(); i += countPerPage) {
                        List<String> pagePropertyData = propertyData.stream().skip(i).limit(countPerPage).collect(Collectors.toList());
                        List<Map<String, Object>> mapListForProperty = areaMapList.stream().filter(p -> pagePropertyData.contains(StringUtils.isNotNull(p.get(columnCode))
                                ? p.get(columnCode).toString() : "")).collect(Collectors.toList());
                        tmpList.add(StringUtils.isEmpty(mapListForProperty) ? new ArrayList<>(areaMapList) : mapListForProperty);
                    }
                } else {
                    tmpList.add(areaMapList);
                }
            } else {
                tmpList.add(areaMapList);
            }
            idx++;
            for (List<Map<String, Object>> tmp : tmpList) {
                areaPagedMapList.addAll(getPagedData(pagingConfigList, tmp, propertyDataMap, applyColumnMap, idx, endIdx));
            }
        } else {
            areaPagedMapList.add(areaMapList);
        }
        return areaPagedMapList;
    }

    /**
     * 按照区域的扩展方式及每页数量进行分页
     *
     * @param areaPagedMapList 按照sheet页分页配置分页完成后的数据列表
     * @param areaConfig       区域配置对象
     */
    private void getPagedDataByAreaConfig(List<List<Map<String, Object>>> areaPagedMapList, DtoAreaConfig areaConfig,
                                          Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap,
                                          Map<Integer, Integer> pageConfigIdx2PageCntMap, int addIdx) {
        int expandPageSize = areaConfig.getExpandPageSize();
        expandPageSize = (expandPageSize <= 0) ? 1 : expandPageSize;
        //遍历每个按照分页配置分好页的数据列表，再按照区域配置的每页总行、列数进行分页
        for (int idx = 0; idx < areaPagedMapList.size(); idx++) {
            int actIdx = addIdx + idx;
            String key = areaConfig.getId() + "_" + actIdx;
            if (!pageConfigIdx2PageDataMap.containsKey(key)) {
                pageConfigIdx2PageDataMap.put(key, new ArrayList<>());
            }
            List<Map<String, Object>> areaPagedMap = areaPagedMapList.get(idx);
            int cnt = 0;
            for (int i = 0; i < areaPagedMap.size(); i += expandPageSize) {
                cnt++;
                List<Map<String, Object>> pageMapList = areaPagedMap.stream().skip(i).limit(expandPageSize).collect(Collectors.toList());
                pageConfigIdx2PageDataMap.get(key).add(pageMapList);
            }
            if (!pageConfigIdx2PageCntMap.containsKey(actIdx)) {
                pageConfigIdx2PageCntMap.put(actIdx, 1);
            }
            if (cnt > pageConfigIdx2PageCntMap.get(actIdx)) {
                pageConfigIdx2PageCntMap.put(actIdx, cnt);
            }
        }
    }

    /**
     * 区域数据跟随分页属性对应展示
     *
     * @param areaPagedMapList 按照sheet配置
     * @param areaConfig       区域配置对象
     */
    private void getCorrespondPagedData(List<List<Map<String, Object>>> areaPagedMapList, DtoAreaConfig areaConfig, DtoSheetPagingConfig pagingConfig,
                                        Map<String, List<List<Map<String, Object>>>> pageConfigIdx2PageDataMap, Map<Integer, Integer> pageConfigIdx2PageCntMap,
                                        Map<String, DtoDataSetApplyColumn> applyColumnMap, Map<String, List<String>> propertyDataMap, List<String> fldNameList, int addIdx) {
        String applyColumnId = pagingConfig.getDataSetApplyColumnId();
        String columnCode = applyColumnMap.containsKey(applyColumnId) ? applyColumnMap.get(applyColumnId).getColumnCode() : "";
        List<String> propertyData = getPropertyData(pagingConfig, applyColumnMap, propertyDataMap);
        int countPerPage = pagingConfig.getCountPerPage();
        Integer pageColumnSize = areaConfig.getPageColumnSize();
        pageColumnSize = (StringUtils.isNull(pageColumnSize) || pageColumnSize <= 0) ? 1 : pageColumnSize;
        //遍历每个按照分页配置分好页的数据列表，再按照最低优先级的分页配置的分页属性对应获取区域数据
        int actIdx = 0;
        for (List<Map<String, Object>> areaPagedMap : areaPagedMapList) {
            for (int i = 0; i < propertyData.size(); i += countPerPage) {
                List<String> pageProperty = propertyData.stream().skip(i).limit(countPerPage).collect(Collectors.toList());
                List<Map<String, Object>> mapListForProperty = areaPagedMap.stream().filter(p -> pageProperty.contains(StringUtils.isNotNull(p.get(columnCode))
                        ? p.get(columnCode).toString() : "")).collect(Collectors.toList());
                String key = areaConfig.getId() + "_" + (actIdx + addIdx);
                if (!pageConfigIdx2PageDataMap.containsKey(key)) {
                    pageConfigIdx2PageDataMap.put(key, new ArrayList<>());
                }
                //计算当前页的多个分页属性对应的区域数据需要分多少页
                Map<String, List<Map<String, Object>>> dataMapForProperty = mapListForProperty.stream().collect(Collectors.groupingBy(p -> StringUtils.isNotNull(p.get(columnCode))
                        ? p.get(columnCode).toString() : ""));
                List<Integer> sizeList = new ArrayList<>();
                dataMapForProperty.forEach((k, v) -> sizeList.add(v.size()));
                int maxSize = StringUtils.isNotEmpty(sizeList) ? Collections.max(sizeList) : 0;
                maxSize = maxSize % pageColumnSize == 0 ? (maxSize / pageColumnSize) : (maxSize / pageColumnSize + 1);
                for (int j = 0; j < maxSize; j++) {
                    List<Map<String, Object>> dt = new ArrayList<>();
                    for (String property : pageProperty) {
                        List<Map<String, Object>> dataForProperty = dataMapForProperty.getOrDefault(property, new ArrayList<>());
                        List<Map<String, Object>> pageDataForProperty = dataForProperty.stream().skip((long) j * pageColumnSize).limit(pageColumnSize).collect(Collectors.toList());
                        if (StringUtils.isEmpty(pageDataForProperty)) {
                            dt.add(new HashMap<>());
                        } else {
                            Map<String, Object> dr = new HashMap<>();
                            for (int k = 0; k < pageColumnSize; k++) {
                                int finalK = k;
                                if (k < pageDataForProperty.size()) {
                                    pageDataForProperty.get(k).forEach((a, b) -> dr.put(a + "_" + finalK, b));
                                } else {
                                    fldNameList.forEach(p -> dr.put(p + "_" + finalK, ""));
                                }
                            }
                            dt.add(dr);
                        }
                    }
                    pageConfigIdx2PageDataMap.get(key).add(dt);
                }
                if (!pageConfigIdx2PageCntMap.containsKey(actIdx)) {
                    pageConfigIdx2PageCntMap.put(actIdx, 1);
                }
                if (maxSize > pageConfigIdx2PageCntMap.get(actIdx)) {
                    pageConfigIdx2PageCntMap.put(actIdx, maxSize);
                }
                actIdx++;
            }
        }
    }

    /**
     * 获取sheet页分页配置的关联分页字段对应的所有字段值列表
     *
     * @param pagingConfig    分页配置对象
     * @param applyColumnMap  数据集应用列映射
     * @param propertyDataMap 字段值数据映射
     * @return 分页字段对应的所有字段值列表
     */
    private List<String> getPropertyData(DtoSheetPagingConfig pagingConfig, Map<String, DtoDataSetApplyColumn> applyColumnMap, Map<String, List<String>> propertyDataMap) {
        String applyColumnId = pagingConfig.getDataSetApplyColumnId();
        String placeHolder = applyColumnMap.containsKey(applyColumnId) ? applyColumnMap.get(applyColumnId).getColumnCode() : "";
        return propertyDataMap.get(placeHolder);
    }


    /**
     * 区域数据按照对应的sheet页进行分组，同一sheet页的区域数据放在一起
     *
     * @param areaDataMap     区域数据映射
     * @param sheetConfigList sheet页配置列表
     * @return 分组后的映射
     */
    private Map<DtoSheetConfig, Map<DtoAreaConfig, List<Map<String, Object>>>> getSheetAreaDataMap(Map<DtoAreaConfig, List<Map<String, Object>>> areaDataMap,
                                                                                                   List<DtoSheetConfig> sheetConfigList) {
        Map<String, DtoSheetConfig> sheetConfigMap = sheetConfigList.stream().collect(Collectors.toMap(DtoSheetConfig::getSheetName, dto -> dto));
        Map<DtoSheetConfig, Map<DtoAreaConfig, List<Map<String, Object>>>> sheetAreaDataMap = new HashMap<>();
        for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : areaDataMap.entrySet()) {
            String sheetName = entry.getKey().getSheetName();
            if (sheetConfigMap.containsKey(sheetName)) {
                DtoSheetConfig sheetConfig = sheetConfigMap.get(sheetName);
                if (!sheetAreaDataMap.containsKey(sheetConfig)) {
                    sheetAreaDataMap.put(sheetConfig, new HashMap<>());
                }
                sheetAreaDataMap.get(sheetConfig).put(entry.getKey(), entry.getValue());
            }
        }
        return sheetAreaDataMap;
    }

    @Autowired
    @Lazy
    public void setDataSetApplyColumnService(DataSetApplyColumnService dataSetApplyColumnService) {
        this.dataSetApplyColumnService = dataSetApplyColumnService;
    }
}
