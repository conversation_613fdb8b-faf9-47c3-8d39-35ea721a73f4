package com.sinoyd.report.excel.service.impl;

import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.frame.base.util.QrCodeUtil;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.entity.CustomParamConfig;
import com.sinoyd.report.enums.EnumCustomParamCode;
import com.sinoyd.report.excel.mapper.ExcelReportParamVOMapper;
import com.sinoyd.report.excel.service.CommonGenerateExcelReportService;
import com.sinoyd.report.excel.service.GenerateExcelReportService;
import com.sinoyd.report.excel.service.ReportDataHandleService;
import com.sinoyd.report.excel.strategy.cellMerge.context.ReportCellMergeContext;
import com.sinoyd.report.excel.strategy.reportDataSrcInd.context.ReportDataSrcIndContext;
import com.sinoyd.report.excel.strategy.reportFileName.context.ReportFileNameContext;
import com.sinoyd.report.excel.vo.*;
import com.sinoyd.report.repository.BaseConfigRepository;
import com.sinoyd.report.repository.CustomParamConfigRepository;
import com.sinoyd.report.repository.GlobalConfigRepository;
import com.sinoyd.report.service.AreaConfigService;
import com.sinoyd.report.service.AreaExpandConfigService;
import com.sinoyd.report.service.BaseConfigService;
import com.sinoyd.report.service.SheetConfigService;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.OutputStream;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 通用excel报表导出接口服务(采样单，原始记录单)
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/09
 */
@Service
@Slf4j
public class GenerateExcelReportServiceImpl implements GenerateExcelReportService {

    private BaseConfigRepository baseConfigRepository;
    private GlobalConfigRepository globalConfigRepository;
    private AreaConfigService areaConfigService;
    private CommonGenerateExcelReportService commonGenerateExcelReportService;
    private ReportDataHandleService reportDataHandleService;
    private FilePropertyConfig filePropertyConfig;
    private BaseConfigService baseConfigService;
    private ReportFileNameContext reportFileNameContext;
    private ReportDataSrcIndContext reportDataSrcIndContext;
    private ReportCellMergeContext reportCellMergeContext;
    private CustomParamConfigRepository customParamConfigRepository;
    private SheetConfigService sheetConfigService;
    private AreaExpandConfigService areaExpandConfigService;

    private ExcelReportParamVOMapper excelReportParamVOMapper;

    private static final Pattern PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    /**
     * 根据报表基础配置及前端传参获取及组装报表数据
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @return 报表数据
     */
    @Override
    public Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO, ExcelBusinessParamVO businessParamVO) {
        return reportDataHandleService.createData(frontParamVO, excelReportParamVO, businessParamVO);
    }

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param reportCode   报表编码
     * @param frontParamVO 接口传参
     * @param response     响应对象
     */
    @Override
    public void generate(String reportCode, FrontParamVO frontParamVO, HttpServletResponse response) {
        //初始化临时数据
        initFrontParam(reportCode, frontParamVO);
        //根据前端传参初始化报表配置对象
        ExcelReportParamVO excelReportParamVO = initExcelReportParam(frontParamVO);
        //获取报表配置
        getReportConfigInfo(excelReportParamVO);
        //组装报表数据
        ExcelBusinessParamVO businessParamVO = new ExcelBusinessParamVO(frontParamVO.getWorkSheetFolderId(), frontParamVO.getReceiveId());
        Map<String, List<Map<String, Object>>> dsMap = createData(frontParamVO, excelReportParamVO, businessParamVO);
        //报表数据源个性化操作
        setExcelReportDataSrcInd(dsMap, excelReportParamVO, frontParamVO, businessParamVO);
        //报表单元格合并处理
        setExcelReportCellMerge(dsMap, excelReportParamVO, frontParamVO, businessParamVO);
        //设置输出报表文件名称
        setExcelReportName(excelReportParamVO, frontParamVO, businessParamVO);
        //生成报表
        generateExcelReport(excelReportParamVO, dsMap, response);
    }

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param frontParamVO 接口传参
     * @param outputStream 输出流
     */
    @Override
    public void generateStream(FrontParamVO frontParamVO, OutputStream outputStream) {
        //根据前端传参初始化报表配置对象
        ExcelReportParamVO excelReportParamVO = initExcelReportParam(frontParamVO);
        //获取报表配置
        getReportConfigInfo(excelReportParamVO);
        //组装报表数据
        ExcelBusinessParamVO businessParamVO = new ExcelBusinessParamVO(frontParamVO.getWorkSheetFolderId(), frontParamVO.getReceiveId());
        Map<String, List<Map<String, Object>>> dsMap = createData(frontParamVO, excelReportParamVO, businessParamVO);
        //生成报表
        generateExcelReportStream(excelReportParamVO, dsMap, outputStream);
    }

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param response           响应对象
     */
    @Override
    public void generateExcelReport(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, HttpServletResponse response) {
        CommonExcelReportParamVO commonVO = convertToCommonVO(excelReportParamVO);
        initQRCodeImage(commonVO, dsMap);
        commonGenerateExcelReportService.generateExcelReport(commonVO, dsMap, response);
    }

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param outputStream       输出流
     */
    @Override
    public void generateExcelReportStream(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, OutputStream outputStream) {
        CommonExcelReportParamVO commonVO = convertToCommonVO(excelReportParamVO);
        initQRCodeImage(commonVO, dsMap);
        commonGenerateExcelReportService.generateExcelReportStream(commonVO, dsMap, outputStream);
    }

    /**
     * 获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    @Override
    public WorkbookDesigner generateDesignerByParam(ExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap) {
        CommonExcelReportParamVO commonVO = excelReportParamVOMapper.toCommonVO(excelReportParamVO);
//        CommonExcelReportParamVO commonVO = ExcelReportParamVOMapper.INSTANCE.toCommonVO(excelReportParamVO);
        initQRCodeImage(commonVO, dsMap);
        return commonGenerateExcelReportService.generateDesignerByParam(commonVO, dsMap);
    }

    /**
     * 初始化二维码图片对象
     *
     * @param commonVO excel报表生成数据传输VO
     */
    private void initQRCodeImage(CommonExcelReportParamVO commonVO, Map<String, List<Map<String, Object>>> dsMap) {
        List<AreaConfigVO> areaConfigVOList = commonVO.getAreaConfigList();
        Map<String, BufferedImage> areaConfig2ImageMap = new HashMap<>();
        for (AreaConfigVO areaConfigVO : areaConfigVOList) {
            if (StringUtils.isNotNull(areaConfigVO.getAreaExpandConfigVO())) {
                AreaExpandConfigVO areaExpandConfigVO = areaConfigVO.getAreaExpandConfigVO();
                String key = areaConfigVO.getAreaType() + "1";
                if (dsMap.containsKey(key)) {
                    Map<String, Object> map = dsMap.get(key).get(0);
                    if (map.containsKey("sampleQRCode")) {
                        String content = map.get("sampleQRCode").toString();
                        BufferedImage image = QrCodeUtil.createImageCode(content, "UTF-8", areaExpandConfigVO.getPicSize());
                        areaConfig2ImageMap.put(areaConfigVO.getId(), image);
                    }
                }
            }
        }
        commonVO.setAreaConfig2ImageMap(areaConfig2ImageMap);
    }

    /**
     * 设置报表名称
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    @Override
    public void setExcelReportName(ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO) {
        DtoGlobalConfig globalConfig = excelReportParamVO.getGlobalConfig();
        String namingMethod = globalConfig.getNamingMethod(), namingRules = globalConfig.getNameRules();
        String outPutName = excelReportParamVO.getOutPutName();
        if (StringUtils.isNotEmpty(namingMethod) && StringUtils.isNotEmpty(namingRules)) {
            Map<String, String> fileNameDataMap = reportFileNameContext.getFileNameDataMap(frontParamVO, businessParamVO, namingMethod);
            Matcher matcher = PATTERN.matcher(namingRules);
            while (matcher.find()) {
                String groupValue = matcher.group();
                String value = "";
                if (StringUtils.isNotNull(fileNameDataMap.get(groupValue))) {
                    value = fileNameDataMap.get(groupValue);
                }
                namingRules = namingRules.replace("[" + groupValue + "]", value);
            }
            int dotIdx = outPutName.lastIndexOf("."), pathIdx = outPutName.lastIndexOf(System.lineSeparator());
            String suffix = (dotIdx != -1) ? outPutName.substring(dotIdx) : "", path = (pathIdx != -1) ? outPutName.substring(0, pathIdx + 1) : "";
            outPutName = path + namingRules + suffix;
        }
        excelReportParamVO.setOutPutName(outPutName);
    }

    /**
     * excel报表数据源个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    @Override
    public void setExcelReportDataSrcInd(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                         ExcelBusinessParamVO businessParamVO) {
        List<DtoCustomParamConfig> customParamConfigList = excelReportParamVO.getGlobalConfig().getCustomParamConfigList();
        if (StringUtils.isNotEmpty(customParamConfigList)) {
            List<DtoCustomParamConfig> dataSrcConfigList = customParamConfigList.stream().filter(p -> EnumCustomParamCode.报表数据源个性化.getCode().equals(p.getParamCode()))
                    .sorted(Comparator.comparing(CustomParamConfig::getCreateDate)).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(dataSrcConfigList)) {
                for (DtoCustomParamConfig paramConfig : dataSrcConfigList) {
                    reportDataSrcIndContext.dataSrcInd(dsMap, excelReportParamVO, frontParamVO, businessParamVO, paramConfig);
                }
            }
        }
    }

    /**
     * excel报表数据源个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    @Override
    public void setExcelReportCellMerge(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                        ExcelBusinessParamVO businessParamVO) {
        List<DtoAreaConfig> areaConfigList = excelReportParamVO.getAreaConfigList();
        List<DtoSheetConfig> sheetConfigList = excelReportParamVO.getSheetConfigList();
        Map<String, List<DtoSheetConfig>> sheetConfigMap = sheetConfigList.stream().collect(Collectors.groupingBy(DtoSheetConfig::getSheetName));
        for (DtoAreaConfig areaConfig : areaConfigList) {
            DtoAreaExpandConfig expandConfig = areaConfig.getAreaExpandConfig();
            DtoSheetConfig sheetConfig = sheetConfigMap.containsKey(areaConfig.getSheetName()) ? sheetConfigMap.get(areaConfig.getSheetName()).get(0) : null;
            if (StringUtils.isNotNull(expandConfig) && StringUtils.isNotEmpty(expandConfig.getAreaExpandMergeConfigList())) {
                List<DtoAreaExpandMergeConfig> expandMergeConfigList = expandConfig.getAreaExpandMergeConfigList();
                for (int i = 0; i < expandMergeConfigList.size(); i++) {
                    DtoAreaExpandMergeConfig mergeConfig = expandMergeConfigList.get(i);
                    reportCellMergeContext.cellMergeInd(dsMap, mergeConfig, areaConfig, sheetConfig, excelReportParamVO, frontParamVO, businessParamVO, i);
                }
            }
        }

//        List<DtoCustomParamConfig> customParamConfigList = excelReportParamVO.getGlobalConfig().getCustomParamConfigList();
//        if (StringUtils.isNotEmpty(customParamConfigList)) {
//            List<DtoCustomParamConfig> dataSrcConfigList = customParamConfigList.stream().filter(p -> EnumCustomParamCode.报表数据源个性化.getCode().equals(p.getParamCode()))
//                    .collect(Collectors.toList());
//            if (StringUtils.isNotEmpty(dataSrcConfigList)) {
//                for (DtoCustomParamConfig paramConfig : dataSrcConfigList) {
//                    reportDataSrcIndContext.dataSrcInd(dsMap, excelReportParamVO, frontParamVO, businessParamVO, paramConfig);
//                }
//            }
//        }
    }

    /**
     * 初始化临时数据（临时方法，后期删除）
     *
     * @param reportCode   报表编码
     * @param frontParamVO 报表参数
     */
    private void initFrontParam(String reportCode, FrontParamVO frontParamVO) {
        DtoBaseConfig baseConfig = baseConfigService.findByReportCode(reportCode);
        frontParamVO.setReportCode(reportCode);
        frontParamVO.setWorkSheetFolderId("dc9e03e6-bdfb-404f-bbae-58306326e7d1");
        File resDirectory = new File("src/test/resources");
        String outPutPath = resDirectory.getAbsolutePath() + "/outputs";
        frontParamVO.setOutPutPath(outPutPath);
        frontParamVO.setOutPutName(baseConfig.getTemplateName());
    }

    /**
     * 获取excel报表模板配置信息
     *
     * @param excelReportParamVO 前端传参接收对象
     */
    private void getReportConfigInfo(ExcelReportParamVO excelReportParamVO) {
        String reportCode = StringUtils.isNotEmpty(excelReportParamVO.getReportCode()) ? excelReportParamVO.getReportCode() : "";
        DtoBaseConfig baseConfig = baseConfigRepository.findByReportCode(reportCode);
        if (StringUtils.isNull(baseConfig)) {
            throw new BaseException("报表基础配置不存在!" + reportCode);
        }
        excelReportParamVO.setBaseConfig(baseConfig);
        DtoGlobalConfig globalConfig = globalConfigRepository.findByReportCode(reportCode);
        if (StringUtils.isNull(globalConfig)) {
            throw new BaseException("报表模板全局配置不存在!" + reportCode);
        }
        List<DtoCustomParamConfig> customParamConfigList = customParamConfigRepository.findByGlobalConfigIdAndIsDeletedFalse(globalConfig.getId());
        globalConfig.setCustomParamConfigList(customParamConfigList);
        excelReportParamVO.setGlobalConfig(globalConfig);
        List<DtoAreaConfig> areaConfigList = areaConfigService.findByReportCode(reportCode);
        if (StringUtils.isEmpty(areaConfigList)) {
            throw new BaseException("报表模板区域配置不能为空!" + reportCode);
        }
        List<String> areaConfigIdList = areaConfigList.stream().map(DtoAreaConfig::getId).collect(Collectors.toList());
        List<DtoAreaExpandConfig> areaExpandConfigList = areaExpandConfigService.findByAreaIds(areaConfigIdList);
        Map<String, List<DtoAreaExpandConfig>> expandConfigMap = areaExpandConfigList.stream().collect(Collectors.groupingBy(DtoAreaExpandConfig::getAreaConfigId));
        for (DtoAreaConfig areaConfig : areaConfigList) {
            if (expandConfigMap.containsKey(areaConfig.getId())) {
                areaConfig.setAreaExpandConfig(expandConfigMap.get(areaConfig.getId()).get(0));
            }
        }
        excelReportParamVO.setAreaConfigList(areaConfigList);
        List<DtoSheetConfig> sheetConfigList = sheetConfigService.findByReportCode(reportCode);
        excelReportParamVO.setSheetConfigList(sheetConfigList);
    }

    /**
     * 转化为通用的报表配置参数对象
     *
     * @param excelReportParamVO 前端报表配置传参接收对象
     * @return 通用的报表配置参数对象
     */
    private CommonExcelReportParamVO convertToCommonVO(ExcelReportParamVO excelReportParamVO) {
        CommonExcelReportParamVO commonExcelReportParamVO = excelReportParamVOMapper.toCommonVO(excelReportParamVO);
        String templateName = commonExcelReportParamVO.getBaseConfig().getTemplateName();
        String templatePath = commonExcelReportParamVO.getBaseConfig().getTemplatePath();
        commonExcelReportParamVO.getBaseConfig().setTemplatePath(filePropertyConfig.getTemplatePath() + FileUtil.FILE_SEPARATOR + templatePath.replace(templateName, ""));
        return commonExcelReportParamVO;
    }

    /**
     * 根据前端传参初始化报表配置对象
     *
     * @param frontParamVO 接口传参
     * @return 报表配置对象
     */
    private ExcelReportParamVO initExcelReportParam(FrontParamVO frontParamVO) {
        String reportCode = frontParamVO.getReportCode();
        log.info("报表编码：" + reportCode);
        ExcelReportParamVO excelReportParamVO = new ExcelReportParamVO();
        excelReportParamVO.setReportCode(reportCode);
        excelReportParamVO.setOutPutPath(frontParamVO.getOutPutPath());
        excelReportParamVO.setFormatSci(true);
        excelReportParamVO.setOutPutName(frontParamVO.getOutPutName());
        excelReportParamVO.setAreaTypeParamMap(new HashMap<>());
        return excelReportParamVO;
    }

    @Autowired
    public void setBaseConfigRepository(BaseConfigRepository baseConfigRepository) {
        this.baseConfigRepository = baseConfigRepository;
    }

    @Autowired
    public void setGlobalConfigRepository(GlobalConfigRepository globalConfigRepository) {
        this.globalConfigRepository = globalConfigRepository;
    }

    @Autowired
    @Lazy
    public void setCommonGenerateExcelReportService(CommonGenerateExcelReportService commonGenerateExcelReportService) {
        this.commonGenerateExcelReportService = commonGenerateExcelReportService;
    }

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }

    @Autowired
    public void setReportDataHandleService(ReportDataHandleService reportDataHandleService) {
        this.reportDataHandleService = reportDataHandleService;
    }

    @Autowired
    @Lazy
    public void setAreaConfigService(AreaConfigService areaConfigService) {
        this.areaConfigService = areaConfigService;
    }

    @Autowired
    @Lazy
    public void setExcelReportParamVOMapper(ExcelReportParamVOMapper excelReportParamVOMapper) {
        this.excelReportParamVOMapper = excelReportParamVOMapper;
    }

    @Autowired
    @Lazy
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    public void setReportFileNameContext(ReportFileNameContext reportFileNameContext) {
        this.reportFileNameContext = reportFileNameContext;
    }

    @Autowired
    public void setCustomParamConfigRepository(CustomParamConfigRepository customParamConfigRepository) {
        this.customParamConfigRepository = customParamConfigRepository;
    }

    @Autowired
    public void setReportDataSrcIndContext(ReportDataSrcIndContext reportDataSrcIndContext) {
        this.reportDataSrcIndContext = reportDataSrcIndContext;
    }

    @Autowired
    public void setSheetConfigService(SheetConfigService sheetConfigService) {
        this.sheetConfigService = sheetConfigService;
    }

    @Autowired
    @Lazy
    public void setAreaExpandConfigService(AreaExpandConfigService areaExpandConfigService) {
        this.areaExpandConfigService = areaExpandConfigService;
    }

    @Autowired
    @Lazy
    public void setReportCellMergeContext(ReportCellMergeContext reportCellMergeContext) {
        this.reportCellMergeContext = reportCellMergeContext;
    }
}
