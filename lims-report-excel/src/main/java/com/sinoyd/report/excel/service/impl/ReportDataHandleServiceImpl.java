package com.sinoyd.report.excel.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.enums.EnumAreaType;
import com.sinoyd.report.enums.EnumCustomParamCode;
import com.sinoyd.report.excel.enums.EnumQcGrade;
import com.sinoyd.report.excel.service.ExcelReportPageService;
import com.sinoyd.report.excel.service.ReportDataHandleService;
import com.sinoyd.report.excel.strategy.dataSort.context.ReportDataSortContext;
import com.sinoyd.report.excel.vo.AreaTypeParamVO;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.service.AreaDataSourceService;
import com.sinoyd.report.vo.AreaRelationDataVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import com.sinoyd.report.vo.ExcelPlaceholderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域处理类接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/06
 */
@Slf4j
@Service
public class ReportDataHandleServiceImpl implements ReportDataHandleService {

    private AreaDataSourceService areaDataSourceService;

    private ExcelReportPageService excelReportPageService;
    private ReportDataSortContext reportDataSortContext;

    /**
     * 获取区域数据处理后的数据集
     *
     * @param frontParamVO       请求参数
     * @param excelReportParamVO 报表配置数据
     * @return 处理后的数据集
     */
    @Override
    public Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO, ExcelBusinessParamVO businessParamVO) {
        Map<String, List<Map<String, Object>>> dsMap = new HashMap<>();
        //所有的区域数据
        List<DtoAreaConfig> areaConfigList = excelReportParamVO.getAreaConfigList();
        //处理区域对应数据Map
        AreaRelationDataVO areaRelationData = new AreaRelationDataVO();
        Map<DtoAreaConfig, Map<String, Object>> areaDataMap = areaDataSourceService.findAreaDataMap(businessParamVO, areaConfigList, areaRelationData);
        //处理数据行集合
        handleDataMap(dsMap, areaDataMap, businessParamVO, excelReportParamVO, areaRelationData);
        //返回数据
        return dsMap;
    }

    /**
     * 区域数据个性化处理
     *
     * @param areaTypeDataMap    区域数据映射
     * @param excelReportParamVO 报表配置数据
     */
    protected void setAreaDataInd(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap, ExcelReportParamVO excelReportParamVO) {
        List<DtoCustomParamConfig> customParamConfigList = excelReportParamVO.getGlobalConfig().getCustomParamConfigList();
        if (StringUtils.isNotEmpty(customParamConfigList)) {
            //现场平行样是否展示个性化
            setOutParallelInd(areaTypeDataMap, customParamConfigList);
            //数据排序个性化
            setDataSortInd(areaTypeDataMap, customParamConfigList);
        }
    }

    /**
     * 设置Data区数据排序
     *
     * @param areaTypeDataMap       区域数据映射
     * @param customParamConfigList 自定义配置列表
     */
    protected void setDataSortInd(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap, List<DtoCustomParamConfig> customParamConfigList) {
        DtoCustomParamConfig sortConfig = customParamConfigList.stream().filter(p -> EnumCustomParamCode.数据排序规则.getCode()
                .equals(p.getParamCode())).findFirst().orElse(null);
        Map<DtoAreaConfig, List<Map<String, Object>>> updateMap = new HashMap<>();
        if (StringUtils.isNotNull(sortConfig)) {
            for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : areaTypeDataMap.entrySet()) {
                DtoAreaConfig areaConfig = entry.getKey();
                if (EnumAreaType.拓展数据区域.getValue().equals(areaConfig.getAreaType())) {
                    List<Map<String, Object>> rowMapList = entry.getValue();
                    List<Map<String, Object>> tmpRowMapList = reportDataSortContext.dataSortInd(rowMapList, sortConfig);
                    updateMap.put(areaConfig, tmpRowMapList);
                }
            }
            if (StringUtils.isNotEmpty(updateMap)) {
                for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : updateMap.entrySet()) {
                    areaTypeDataMap.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }

    /**
     * 设置质控页是否显示现场平行
     *
     * @param areaTypeDataMap       区域数据映射
     * @param customParamConfigList 自定义配置列表
     */
    protected void setOutParallelInd(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap, List<DtoCustomParamConfig> customParamConfigList) {
        DtoCustomParamConfig outParallelConfig = customParamConfigList.stream().filter(p -> EnumCustomParamCode.质控页是否显示现场平行.getCode()
                .equals(p.getParamCode())).findFirst().orElse(null);
        Map<DtoAreaConfig, List<Map<String, Object>>> updateMap = new HashMap<>();
        if (StringUtils.isNotNull(outParallelConfig) && "0".equals(outParallelConfig.getParamValue())) {
            String innerQcGrade = String.valueOf(EnumQcGrade.内部质控.getValue());
            for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : areaTypeDataMap.entrySet()) {
                DtoAreaConfig areaConfig = entry.getKey();
                List<Map<String, Object>> rowMapList = entry.getValue();
                if (EnumAreaType.平行数据区域.getValue().equals(areaConfig.getAreaType())) {
                    List<Map<String, Object>> tmpRowMapList = new ArrayList<>();
                    for (Map<String, Object> rowMap : rowMapList) {
                        if (!rowMap.containsKey("qcGrade") || innerQcGrade.equals(rowMap.get("qcGrade").toString())) {
                            Map<String, Object> tmpRowMap = new HashMap<>(rowMap);
                            tmpRowMapList.add(tmpRowMap);
                        }
                    }
                    updateMap.put(areaConfig, tmpRowMapList);
                }
            }
            if (StringUtils.isNotEmpty(updateMap)) {
                for (Map.Entry<DtoAreaConfig, List<Map<String, Object>>> entry : updateMap.entrySet()) {
                    areaTypeDataMap.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }


    /**
     * 处理数据行集合
     *
     * @param dsMap              数据行集合
     * @param areaDataMap        区域数据集合
     * @param businessParamVO    业务参数
     * @param excelReportParamVO 报表生成相关参数
     */
    protected void handleDataMap(Map<String, List<Map<String, Object>>> dsMap,
                                 Map<DtoAreaConfig, Map<String, Object>> areaDataMap,
                                 ExcelBusinessParamVO businessParamVO,
                                 ExcelReportParamVO excelReportParamVO,
                                 AreaRelationDataVO areaRelationData) {
        //区域id与数据集关联Map
        Map<String, List<DtoDataSet>> areaIdToDataSetMap = areaRelationData.getAreaIdToDataSetMap();
        //区域id与API关联Map
        Map<String, List<DtoApi>> areaIdToApiMap = areaRelationData.getAreaIdToApiMap();
        //放置相关数据集数据
        List<DtoDataSet> dataSetList = new ArrayList<>();
        areaIdToDataSetMap.values().forEach(dataSetList::addAll);
        areaRelationData.setDataSetList(dataSetList);
        //放置相关API接口数据
        List<DtoApi> apiList = new ArrayList<>();
        areaIdToApiMap.values().forEach(apiList::addAll);
        areaRelationData.setApiList(apiList);
        excelReportParamVO.setAreaRelationData(areaRelationData);
        //获取到所有区域类型的所有数据
        Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap = getAreaTypeDataMap(areaDataMap, excelReportParamVO);
        setAreaDataInd(areaTypeDataMap, excelReportParamVO);
        //处理分页数据
        excelReportPageService.handleReportPage(dsMap, areaTypeDataMap, businessParamVO, excelReportParamVO, areaRelationData);
    }

    /**
     * 填充每个区域的数据
     *
     * @param areaDataMap        处理前的区域数据集合
     * @param excelReportParamVO 报表生成相关参数
     * @return 区域的数据映射
     */
    protected Map<DtoAreaConfig, List<Map<String, Object>>> getAreaTypeDataMap(Map<DtoAreaConfig, Map<String, Object>> areaDataMap, ExcelReportParamVO excelReportParamVO) {
        //获取到所有区域类型的所有数据
        Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap = new LinkedHashMap<>();
        //循环区域，处理区域类型数据填充
        areaDataMap.forEach((key, value) -> handleAreaTypeMap(areaTypeDataMap, key, value, excelReportParamVO));
        return areaTypeDataMap;
    }

    /**
     * 处理每个区域的
     *
     * @param areaTypeDataMap    数据类型数据集
     * @param areaConfig         区域配置数据
     * @param areaDataMap        区域数据
     * @param excelReportParamVO 报表生成所需参数
     */
    protected void handleAreaTypeMap(Map<DtoAreaConfig, List<Map<String, Object>>> areaTypeDataMap,
                                     DtoAreaConfig areaConfig,
                                     Map<String, Object> areaDataMap,
                                     ExcelReportParamVO excelReportParamVO) {
        //获取到当前区域下的所有数据集信息
        Map<String, List<DtoDataSet>> areaIdToDataSetMap = excelReportParamVO.getAreaRelationData().getAreaIdToDataSetMap();
        List<DtoDataSet> dataSetsOfArea = areaIdToDataSetMap.get(areaConfig.getId());
        //获取到当前的某一个数据集数据
        Optional<DtoDataSet> dataSetOp = dataSetsOfArea.stream().findFirst();
        Map<String, AreaTypeParamVO> areaTypeParamMap = excelReportParamVO.getAreaTypeParamMap();
        //获取区域下的占位符数据
        Map<String, List<ExcelPlaceholderVO>> placeholderToColMap = excelReportParamVO.getAreaRelationData().getPlaceholderToColMap();
        List<ExcelPlaceholderVO> placeholderListOfArea = placeholderToColMap.get(areaConfig.getId());
        if (dataSetOp.isPresent() && StringUtils.isNotEmpty(areaDataMap)) {
            AreaTypeParamVO areaTypeParamVO = areaTypeParamMap.containsKey(areaConfig.getAreaType())
                    ? areaTypeParamMap.get(areaConfig.getAreaType()) : new AreaTypeParamVO();
            //获取到此区域下的所有占位符数据
            List<ExcelPlaceholderVO> placeholderList = StringUtils.isNotEmpty(areaTypeParamVO.getPlaceholderList())
                    ? areaTypeParamVO.getPlaceholderList() : new ArrayList<>();
            Set<String> placeholders = placeholderList.stream().map(ExcelPlaceholderVO::getPlaceholder).collect(Collectors.toSet());
            for (ExcelPlaceholderVO excelPlaceholderVO : placeholderListOfArea) {
                if (!placeholders.contains(excelPlaceholderVO.getPlaceholder())) {
                    placeholderList.add(excelPlaceholderVO);
                }
            }
            DtoDataSet dataSet = dataSetOp.get();
            //处理区域类型下数据
            try {
                if (dataSet.getIsCollection()) {
                    List<Map<String, Object>> valueList = new ArrayList<>();
                    for (Object value : areaDataMap.values()) {
                        valueList.addAll((List<Map<String, Object>>) value);
                    }
                    areaTypeDataMap.put(areaConfig, valueList);
                } else {
                    Map<String, Object> valueMap = new HashMap<>();
                    for (Object value : areaDataMap.values()) {
                        valueMap.putAll((Map<String, Object>) value);
                    }
                    areaTypeDataMap.put(areaConfig, Collections.singletonList(valueMap));
                }
            } catch (Exception e) {
                log.error("转换区域值出错：" + e.getMessage(), e);
                throw new RuntimeException(String.format("同一个区域[%s]下只能配置相同返回类型的结果字段!",
                        areaConfig.getAreaName()));
            }
            //区域结果是否为集合
            areaTypeParamVO.setIsCollection(dataSet.getIsCollection());
            areaTypeParamVO.setAreaConfig(areaConfig);
            areaTypeParamVO.setPlaceholderList(placeholderList);
            areaTypeParamMap.put(areaConfig.getAreaType(), areaTypeParamVO);
        }
        excelReportParamVO.setAreaTypeParamMap(areaTypeParamMap);
    }

    @Autowired
    public void setAreaDataSourceService(AreaDataSourceService areaDataSourceService) {
        this.areaDataSourceService = areaDataSourceService;
    }

    @Autowired
    public void setExcelReportPageService(ExcelReportPageService excelReportPageService) {
        this.excelReportPageService = excelReportPageService;
    }

    @Autowired
    public void setReportDataSortContext(ReportDataSortContext reportDataSortContext) {
        this.reportDataSortContext = reportDataSortContext;
    }
}
