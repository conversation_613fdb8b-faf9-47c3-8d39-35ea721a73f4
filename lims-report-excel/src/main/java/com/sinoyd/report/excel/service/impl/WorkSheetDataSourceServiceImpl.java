package com.sinoyd.report.excel.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.excel.enums.EnumReagentType;
import com.sinoyd.report.excel.service.WorkSheetDataSourceService;
import com.sinoyd.report.repository.*;
import com.sinoyd.report.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作单的原始数据源
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Service
public class WorkSheetDataSourceServiceImpl implements WorkSheetDataSourceService {

    private WorkSheetFolderRepository workSheetFolderRepository;
    private AnalyseDataService analyseDataService;
    private SampleService sampleService;
    private QualityControlService qualityControlService;
    private QualityControlEvaluateRepository qualityControlEvaluateRepository;
    private ParamsDataService paramsDataService;
    private CurveRepository curveRepository;
    private CurveDetailRepository curveDetailRepository;
    private InstrumentUseRecordService instrumentUseRecordService;
    private WorkSheetReagentRepository workSheetReagentRepository;
    private ProjectService projectService;

    @Override
    public DtoWorksheetFolder findWorkSheetFolder(String workSheetFolderId) {
        return workSheetFolderRepository.findByWorkSheetFolderId(workSheetFolderId);
    }

    @Override
    public List<DtoAnalyseData> findByWorkSheetFolderId(String workSheetFolderId) {
        return analyseDataService.findByWorkSheetFolderIdIn(Collections.singletonList(workSheetFolderId));
    }

    @Override
    public List<DtoSample> findSample(List<String> sampleIdList) {
        List<DtoSample> sampleList = new ArrayList<>();
        if (sampleIdList.size() > 0) {
            sampleList = sampleService.findBySampleIdIn(sampleIdList);
        }
        return sampleList;
    }

    @Override
    public List<DtoQualityControl> findQualityControl(List<String> qcIdList) {
        List<DtoQualityControl> qualityControlList = new ArrayList<>();
        if (StringUtils.isNotEmpty(qcIdList)) {
            qualityControlList = qualityControlService.findAll(qcIdList);
        }
        return qualityControlList;
    }

    @Override
    public List<DtoQualityControlEvaluate> findQualityControlEvaluate(List<String> objectIdList) {
        return StringUtils.isNotEmpty(objectIdList) ? qualityControlEvaluateRepository.findByObjectIdIn(objectIdList) : new ArrayList<>();
    }

    @Override
    public List<DtoParamsData> findWorkSheetParamsDataByObjId(List<String> workSheetIdList) {
        return paramsDataService.findByObjectIdIn(workSheetIdList);
    }

    @Override
    public List<DtoCurve> findCurveByWorkSheetFolderId(String workSheetFolderId) {
        return curveRepository.findByWorkSheetFolderId(workSheetFolderId);
    }

    @Override
    public List<DtoCurveDetail> findCurveDetail(List<String> curveIdList) {
        return StringUtils.isNotEmpty(curveIdList) ? curveDetailRepository.findByCurveIdIn(curveIdList) : new ArrayList<>();
    }

    @Override
    public List<DtoInstrumentUseRecord> findUseRecordByWorkSheetFolderId(String workSheetFolderId, int objectType) {
        List<DtoInstrumentUseRecord> recordList = instrumentUseRecordService.findByObjectIdIn(Collections.singletonList(workSheetFolderId));
        recordList = recordList.stream().filter(p -> p.getObjectType().equals(objectType)).collect(Collectors.toList());
        return recordList;
    }

    @Override
    public List<DtoWorksheetReagent> findWorkSheetReagent(String workSheetFolderId, int reagentType) {
        List<DtoWorksheetReagent> reagentList = workSheetReagentRepository.findByWorkSheetFolderId(workSheetFolderId);
        reagentList = reagentList.stream().filter(p -> EnumReagentType.标准溶液.getValue().equals(p.getReagentType())).collect(Collectors.toList());
        return reagentList;
    }

    @Override
    public List<DtoProject> findProject(List<String> projectIdList) {
        return projectService.findByProjectIdIn(projectIdList);
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    @Lazy
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }

    @Autowired
    public void setCurveRepository(CurveRepository curveRepository) {
        this.curveRepository = curveRepository;
    }

    @Autowired
    public void setCurveDetailRepository(CurveDetailRepository curveDetailRepository) {
        this.curveDetailRepository = curveDetailRepository;
    }

    @Autowired
    @Lazy
    public void setInstrumentUseRecordService(InstrumentUseRecordService instrumentUseRecordService) {
        this.instrumentUseRecordService = instrumentUseRecordService;
    }

    @Autowired
    public void setWorkSheetReagentRepository(WorkSheetReagentRepository workSheetReagentRepository) {
        this.workSheetReagentRepository = workSheetReagentRepository;
    }

    @Autowired
    public void setWorkSheetFolderRepository(WorkSheetFolderRepository workSheetFolderRepository) {
        this.workSheetFolderRepository = workSheetFolderRepository;
    }

    @Autowired
    @Lazy
    public void setQualityControlService(QualityControlService qualityControlService) {
        this.qualityControlService = qualityControlService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }
}
