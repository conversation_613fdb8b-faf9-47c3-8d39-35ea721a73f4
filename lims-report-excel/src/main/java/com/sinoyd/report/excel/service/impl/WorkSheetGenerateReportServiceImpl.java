package com.sinoyd.report.excel.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.excel.enums.*;
import com.sinoyd.report.excel.service.WorkSheetDataSourceService;
import com.sinoyd.report.excel.service.WorkSheetGenerateReportService;
import com.sinoyd.report.excel.util.GenerateExcelReportUtil;
import com.sinoyd.report.excel.util.MathUtil;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.excel.vo.worksheet.DtoWorkSheetData;
import com.sinoyd.report.excel.vo.worksheet.DtoWorkSheetPageData;
import com.sinoyd.report.excel.vo.worksheet.DtoWorkSheetPageInfo;
import com.sinoyd.report.excel.vo.worksheet.DtoWorkSheetPara;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.report.excel.constant.WorkSheetConstant.*;
import static com.sinoyd.report.excel.enums.EnumExcelBusinessAreaType.*;
import static com.sinoyd.report.excel.util.GenerateExcelReportUtil.*;

/**
 * 原始记录单报表导出数据组装处理接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Service
@SuppressWarnings("unchecked")
public class WorkSheetGenerateReportServiceImpl implements WorkSheetGenerateReportService {

    private WorkSheetDataSourceService workSheetDataSourceService;

    private static final List<String> inKbFldList = Arrays.asList(室内空白样编号, 室内空白样出证结果, 室内空白样质控限值, 室内空白样是否合格, 室内空白样采集编号);

    @Override
    public Map<String, List<Map<String, Object>>> createData(FrontParamVO frontParamVO, ExcelReportParamVO excelReportParamVO) {
        Map<String, List<Map<String, Object>>> dsMap = new HashMap<>();
        //工作单相关的数据
        String workSheetFolderId = frontParamVO.getWorkSheetFolderId();
        DtoWorksheetFolder worksheetFolder = workSheetDataSourceService.findWorkSheetFolder(workSheetFolderId);
        List<DtoAnalyseData> analyseDataList = workSheetDataSourceService.findByWorkSheetFolderId(workSheetFolderId);
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getAnalyseDataId).distinct().collect(Collectors.toList());
        List<String> sampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = workSheetDataSourceService.findSample(sampleIdList);
        //将分析数据处理成需要的工作单数据
        List<DtoWorkSheetData> workSheetDataList = findWorkSheetDataByList(analyseDataList, sampleList);
        List<DtoQualityControlEvaluate> evaluateList = workSheetDataSourceService.findQualityControlEvaluate(analyseDataIdList);
        List<String> projectIdList = sampleList.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = workSheetDataSourceService.findProject(projectIdList);
        //工作单相关的表头参数数据
        List<String> worksheetIdList = analyseDataList.stream().map(DtoAnalyseData::getWorkSheetId).distinct().collect(Collectors.toList());
        worksheetIdList.addAll(analyseDataIdList);
        List<DtoParamsData> paramsDataList = workSheetDataSourceService.findWorkSheetParamsDataByObjId(worksheetIdList);
        //工作单下相关的标准曲线数据
        List<DtoCurve> curveList = workSheetDataSourceService.findCurveByWorkSheetFolderId(workSheetFolderId);
        List<String> curveIdList = curveList.stream().map(DtoCurve::getId).collect(Collectors.toList());
        //工作单下相关的标准曲线明细数据
        List<DtoCurveDetail> curveDetailList = workSheetDataSourceService.findCurveDetail(curveIdList);

        //仪器的信息
        List<DtoInstrumentUseRecord> instrumentUseRecordList = workSheetDataSourceService.findUseRecordByWorkSheetFolderId(workSheetFolderId,
                EnumEnvRecObjType.实验室分析.getValue());
        //试剂配置记录信息
        List<DtoWorksheetReagent> worksheetReagentList = workSheetDataSourceService.findWorkSheetReagent(workSheetFolderId, EnumReagentType.标准溶液.getValue());
        DtoWorkSheetPara para = getWorkSheetPara(excelReportParamVO, frontParamVO, worksheetFolder);
        //写入相关的基本信息数据
        writeBasicInfo(dsMap, sampleList, workSheetDataList, paramsDataList, instrumentUseRecordList, worksheetReagentList, curveDetailList,
                worksheetFolder, curveList, frontParamVO, projectList);
        int page = 0;
        page = writeCommonData(dsMap, workSheetDataList, curveList, curveDetailList, instrumentUseRecordList, page, frontParamVO.getParType(), paramsDataList,
                worksheetReagentList, worksheetFolder, evaluateList, projectList, para, excelReportParamVO);
        //设置分页信息
        setPagination(page, dsMap);
        return dsMap;
    }

    /**
     * 设置分页信息
     *
     * @param page  页数
     * @param dsMap 报表数据
     */
    private void setPagination(int page, Map<String, List<Map<String, Object>>> dsMap) {
        List<Map<String, Object>> pageConfigList = new ArrayList<>();
        Map<String, Object> pageConfig = new HashMap<>();
        pageConfig.put("pageCount", page);
        pageConfigList.add(pageConfig);
        dsMap.put("Page1", pageConfigList);
    }


    /**
     * 根据基类中@config中的数据提取一些配置
     *
     * @return 返回数据
     */
    private DtoWorkSheetPara getWorkSheetPara(ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO, DtoWorksheetFolder worksheetFolder) {
        String sortId = "";
        DtoWorkSheetPara para = new DtoWorkSheetPara();
        //每页样品个数
        para.setSampleCountLimit(getAreaPageCnt(excelReportParamVO, EnumExcelAreaType.Data.name(), 10));

        //每页分析项目个数
        para.setAnalyzeItemCountLimit(getAreaPageCnt(excelReportParamVO, EnumExcelAreaType.Data.name(), 0));

        //每页平行样个数
        para.setPxCount(getAreaPageCnt(excelReportParamVO, EnumExcelBusinessAreaType.Parallel.name(), 0));

        //每页加标样个数
        para.setJbCount(getAreaPageCnt(excelReportParamVO, Addition.name(), 0));

        //报告编码
        para.setReportId(frontParamVO.getReportId());

        //原始记录单配置id
        para.setRecordId(frontParamVO.getRecordId());

        //工作单id
        para.setWorkSheetFolderId(worksheetFolder.getWorkSheetFolderId());

        //排序id
        para.setSortId(sortId);

        //每页标样个数
        para.setBzCount(getAreaPageCnt(excelReportParamVO, Standard.name(), 0));

        //每页校准曲线
        para.setJzCurveCount(getAreaPageCnt(excelReportParamVO, EnumExcelBusinessAreaType.JHCurve.name(), 0));

        //每页曲线
        para.setCurveCount(getAreaPageCnt(excelReportParamVO, EnumExcelBusinessAreaType.Curve.name(), 0));

        // 不跟随原样数据显示（1,2; 1表示外部质控，2表示平行样，多个类型用 ; 分隔）
        para.setNotShowInYYSampleData("");

        // 平行质控数据是否显示现场平行（1 表示显示现场 2 表示不显示 空表示不显示）
        para.setIsShowXCPX("2");

        // 是否同时显示双曲线（1 表示是 2 表示否 空表示否）
        para.setIsShowAllCurve("2");
        //是否根据因子分页（1 表示是 2 表示否 空表示否）
        para.setIsGroupByAnalyzeItem("");
        // 质控数据是否跟随原样（1 表示是 2 表示否 空表示否）
        para.setIsZKWithYY("");
        // 质控信息页（多个质控样）是否按项目进行分页（1 表示是 2 表示否 空表示否）
        para.setQcPagingByProject("2");
        //质控页质控样数据页数同步时，是否算上原始记录页data区的page页数（（1：是  2：否  空表示否））
        para.setPageSyncWithData("");
        //空白填充
        String blankFillStr = "";
        para.setBlankFill(frontParamVO.getBlankFill());
        para.setBlankFillStr(blankFillStr);
        return para;
    }

    /**
     * 获取指定区域每页扩展的数量
     *
     * @param excelReportParamVO 参数对象
     * @param areaTypeName       区域类型名称
     * @param defaultCount       默认数量
     * @return 每页扩展的数量
     */
    private int getAreaPageCnt(ExcelReportParamVO excelReportParamVO, String areaTypeName, int defaultCount) {
        DtoAreaConfig dataAreaConfig = excelReportParamVO.getAreaConfigList().stream().filter(p -> areaTypeName.equals(p.getAreaType()))
                .findFirst().orElse(null);
        return (StringUtils.isNotNull(dataAreaConfig) && dataAreaConfig.getExpandPageSize() > 0) ? dataAreaConfig.getExpandPageSize() : defaultCount;
    }

    /**
     * 找到记录单中所有样品关联的项目id
     *
     * @param workSheetDataList      检测单分析数据
     * @param projectId2sampleIdsMap 项目id和样品id列表映射
     */
    protected List<String> findProjectIdForWorkSheet(List<DtoWorkSheetData> workSheetDataList, Map<String, List<String>> projectId2sampleIdsMap) {
        List<String> projectIds = new ArrayList<>();
        List<String> sampleIdWithProject = new ArrayList<>();
        List<String> assSampleIdWithNoProject = new ArrayList<>();
        for (DtoWorkSheetData data : workSheetDataList) {
            if (!UUIDHelper.guidEmpty().equals(data.getProjectId())) {
                if (!projectIds.contains(data.getProjectId())) {
                    projectIds.add(data.getProjectId());
                }
                if (!sampleIdWithProject.contains(data.getSampleId())) {
                    sampleIdWithProject.add(data.getSampleId());
                }
            } else {
                if (!UUIDHelper.guidEmpty().equals(data.getAssociateSampleId()) && !assSampleIdWithNoProject.contains(data.getAssociateSampleId())) {
                    assSampleIdWithNoProject.add(data.getAssociateSampleId());
                }
            }
        }
        //有些质控样的项目id为空，并且其对应的原样不在检测单内，因此需要根据关联样id找到对应样品，再找到对应的项目id，避免遗漏项目id(仅考虑第一层关联样)
        assSampleIdWithNoProject = assSampleIdWithNoProject.stream().filter(p -> !sampleIdWithProject.contains(p)).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(assSampleIdWithNoProject)) {
            List<DtoSample> sampleList = workSheetDataSourceService.findSample(assSampleIdWithNoProject);
            for (DtoSample sample : sampleList) {
                String proId = sample.getProjectId();
                if (!projectId2sampleIdsMap.containsKey(proId)) {
                    projectId2sampleIdsMap.put(proId, new ArrayList<>());
                }
                List<String> smpIdList = projectId2sampleIdsMap.get(proId);
                if (!smpIdList.contains(sample.getId())) {
                    smpIdList.add(sample.getId());
                }
                if (!UUIDHelper.guidEmpty().equals(proId) && !projectIds.contains(proId)) {
                    projectIds.add(proId);
                }
            }
        }
        return projectIds;
    }

    /**
     * 获取当前项目(project)下所有样品id
     *
     * @param workSheetDataList 数据集合
     * @param sampleIds         原样id集合
     * @return 样品id列表
     */
    protected List<String> getSampleIdsTheProject(List<DtoWorkSheetData> workSheetDataList, List<String> sampleIds) {
        List<String> newSampleIds = workSheetDataList.stream().filter(p -> sampleIds.contains(p.getSampleId())
                || sampleIds.contains(p.getAssociateSampleId())).map(DtoWorkSheetData::getSampleId).distinct().collect(Collectors.toList());
        newSampleIds.addAll(sampleIds);
        newSampleIds = newSampleIds.stream().distinct().collect(Collectors.toList());
        if (sampleIds.size() != newSampleIds.size()) {
            return getSampleIdsTheProject(workSheetDataList, newSampleIds);
        }
        return newSampleIds;
    }

    /**
     * 通过质控样获取对应原样 （有的原样对应的 projectId 为空，因此通过质控样再次获取，避免遗漏）
     *
     * @param workSheetDataList 工作单数据
     * @param allSampleInThePro 项目相关的所有样品的 DtoWorkSheetData列表
     * @param qcSampleList      质控样的 DtoWorkSheetData列表
     */
    protected void getYyFromQc(List<DtoWorkSheetData> workSheetDataList, List<DtoWorkSheetData> allSampleInThePro,
                               List<DtoWorkSheetData> qcSampleList) {
        if (StringUtils.isEmpty(qcSampleList)) {
            return;
        }
        //质控样对应的样品id列表
        List<String> qcSampleIdList = new ArrayList<>();
        qcSampleList.forEach(p -> qcSampleIdList.add(p.getSampleId()));
        //获取质控样id及对应的原样id列表
        List<String> qcAndYySampleIdList = getSampleIdsTheProject(workSheetDataList, qcSampleIdList);
        //排除质控样的id，得到原样id列表
        List<String> yYSampleIdList = qcAndYySampleIdList.stream().filter(p -> !qcSampleIdList.contains(p)).collect(Collectors.toList());
        //根据得到的原样id列表 获取对应的 DtoWorkSheetData 列表，加入到 allSampleInThePro 中
        List<DtoWorkSheetData> yYSampleList = workSheetDataList.stream().filter(p -> yYSampleIdList.contains(p.getSampleId())).collect(Collectors.toList());
        allSampleInThePro.addAll(yYSampleList);
    }

    /**
     * 根据模板配置筛选需要显示的数据（可能需要将部分的质控样数据排除）
     *
     * @param data    数据集合
     * @param strType @config模板配置信息
     * @return 数据集合
     */
    protected List<DtoWorkSheetData> getShowWorkSheetData(List<DtoWorkSheetData> data,
                                                          String strType) {
        List<DtoWorkSheetData> workSheetDataList = data;
        if (StringUtils.isNotNullAndEmpty(strType)) {
            String[] strList = strType.split(";");
            List<String> notInIds = new ArrayList<>();
            for (String str : strList) {
                if (StringUtils.isNotNullAndEmpty(str)) {
                    String qcGrade = str.split(",")[0];
                    String qcType = str.split(",")[1];
                    //将质控的样品的id排除
                    notInIds.addAll(workSheetDataList.stream().filter(p -> p.getIsQC()
                            && p.getQcGrade().toString().equals(qcGrade)
                            && p.getQcType().toString().equals(qcType))
                            .map(DtoWorkSheetData::getSampleId).distinct()
                            .collect(Collectors.toList()));
                }
            }
            workSheetDataList = data.stream().filter(p -> !notInIds.contains(p.getSampleId())).collect(Collectors.toList());
        }
        return workSheetDataList;
    }

    /**
     * 设置当前样品对应的样品类型
     *
     * @param allSampleInThePro 项目相关的所有样品的 DtoWorkSheetData列表
     * @param dataMap           数据map映射
     */
    protected void setSampleTypeForProject(List<DtoWorkSheetData> allSampleInThePro, Map<String, Object> dataMap) {
        //设置样品类型，获取当前页的样品对应的样品类型
        List<String> sampleTypeNames = allSampleInThePro.stream().filter(p -> !p.getIsQC()).map(DtoWorkSheetData::getSampleTypeName)
                .distinct().collect(Collectors.toList());
        dataMap.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ?
                String.join("、", sampleTypeNames) : "");
        //如果工作单中没有原样，则取质控样上的样品类型
        if (sampleTypeNames.size() == 0) {
            sampleTypeNames = allSampleInThePro.stream().map(DtoWorkSheetData::getSampleTypeName).distinct().collect(Collectors.toList());
            dataMap.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ? String.join("、", sampleTypeNames) : "");
        }
    }


    /**
     * 写入公共的内容
     *
     * @param dsMap             数据集合
     * @param workSheetDataList 工作单数据
     * @param curveList         标准曲线数据
     * @param curveDetailList   标准曲线数据
     * @param page              分页数据
     * @param parType           类型
     * @param paramsDataList    工作单参数信息列表
     * @param workSheetFolder   工作单对象
     * @return 返回分析后数据
     */
    private int writeCommonData(Map<String, List<Map<String, Object>>> dsMap, List<DtoWorkSheetData> workSheetDataList, List<DtoCurve> curveList,
                                List<DtoCurveDetail> curveDetailList, List<DtoInstrumentUseRecord> useRecordList,
                                Integer page, String parType, List<DtoParamsData> paramsDataList,
                                List<DtoWorksheetReagent> workSheetReagentList, DtoWorksheetFolder workSheetFolder,
                                List<DtoQualityControlEvaluate> evaluateList, List<DtoProject> projectList, DtoWorkSheetPara para, ExcelReportParamVO excelReportParamVO) {
        if (workSheetDataList.size() > 0) {
            String blankFillStr = "";
            Map<String, List<String>> projectId2sampleIdsMap = new HashMap<>();
            List<String> projectIds = findProjectIdForWorkSheet(workSheetDataList, projectId2sampleIdsMap);
            int proCount = 0;
            DtoWorkSheetPageData workSheetPageData = new DtoWorkSheetPageData();
            workSheetPageData.setProjectNumber(proCount);
            List<DtoWorkSheetPageInfo> workSheetPageInfoList = new ArrayList<>();
            for (int i = 0; i < projectIds.size(); i++) {
                String proId = projectIds.get(i);
                DtoProject project = projectList.stream().filter(p -> p.getProjectId().equals(proId)).findFirst().orElse(null);

                //设置基础信息页，项目名称等基础信息数据(按照项目分页)
                Map<String, Object> basicMap = new HashMap<>();
                basicMap.put("项目名称", project != null ? checkValEmpty(project.getProjectName(), blankFillStr) : "/");
                basicMap.put("项目编号", project != null ? checkValEmpty(project.getProjectCode(), blankFillStr) : "/");

                //该项目所有相关的样品
                List<String> yyIds = workSheetDataList.stream().filter(p -> p.getProjectId().equals(proId))
                        .map(DtoWorkSheetData::getSampleId).distinct().collect(Collectors.toList());
                List<String> extSmpIdsForProject = projectId2sampleIdsMap.getOrDefault(proId, new ArrayList<>());
                for (String extSmpId : extSmpIdsForProject) {
                    if (!yyIds.contains(extSmpId)) {
                        yyIds.add(extSmpId);
                    }
                }

                List<String> sampleIdsInThePro = getSampleIdsTheProject(workSheetDataList, yyIds);
                //项目相关的所有样品
                List<DtoWorkSheetData> allSampleInThePro = workSheetDataList.stream()
                        .filter(p -> sampleIdsInThePro.contains(p.getSampleId())).collect(Collectors.toList());
                List<DtoWorkSheetData> qcSampleList = workSheetDataList.stream().filter(p -> p.getIsQC()
                        && p.getAssociateSampleId().equals(UUIDHelper.guidEmpty())).collect(Collectors.toList());
                //除了室内空白样,标样以外,其他不与项目相关的样品，只在遍历第一个项目的样品时获取
                //过滤出室内空白样，标样，和其他样品
                List<DtoWorkSheetData> inKbSampleList = new ArrayList<>();
                List<DtoWorkSheetData> bzSampleList = new ArrayList<>();
                List<DtoWorkSheetData> otherSampleList = new ArrayList<>();
                for (DtoWorkSheetData workSheetData : qcSampleList) {
                    if (EnumQcGrade.内部质控.getValue().equals(workSheetData.getQcGrade())
                            && EnumQcType.空白.getValue().equals(workSheetData.getQcType())) {
                        inKbSampleList.add(workSheetData);
                    } else if (EnumQcGrade.内部质控.getValue().equals(workSheetData.getQcGrade())
                            && EnumQcType.标准.getValue().equals(workSheetData.getQcType())) {
                        bzSampleList.add(workSheetData);
                    } else {
                        otherSampleList.add(workSheetData);
                    }
                }

                allSampleInThePro.addAll(inKbSampleList);
                allSampleInThePro.addAll(bzSampleList);
                if (i == 0) {
                    allSampleInThePro.addAll(otherSampleList);
                }
                getYyFromQc(workSheetDataList, allSampleInThePro, qcSampleList);
                //遍历每一个项目时，都把曲线校核，仪器空白，试剂空白，采样介质空白,阴性对照试验，阳性对照试验，校正系数检验样品添加进来
                addSamplesForEachProject(workSheetDataList, allSampleInThePro);
                List<DtoWorkSheetData> curveJhSampleList = new ArrayList<>(workSheetDataList.stream().filter(p -> EnumQcGrade.内部质控.getValue()
                        .equals(p.getQcGrade()) && EnumQcType.曲线校核.getValue().equals(p.getQcType())).collect(Collectors.toList()));
                for (DtoWorkSheetData curveDate : curveJhSampleList) {
                    //由于当前遍历项目所关联的曲线校核样已经添加进来，因此要剔除当前遍历项目所关联的曲线校核样，避免样品重复
                    if (allSampleInThePro.stream().filter(p -> p.getAnalyseDataId().equals(curveDate.getAnalyseDataId())).count() == 0) {
                        allSampleInThePro.add(curveDate);
                    }
                }
                //与原样一起显示的样品
                List<DtoWorkSheetData> sampleInThePro = getShowWorkSheetData(allSampleInThePro, para.getNotShowInYYSampleData());
                Map<String, List<DtoWorkSheetData>> id2AllSampleInTheProMap = allSampleInThePro.stream().collect(Collectors.groupingBy(DtoWorkSheetData::getSampleId));
                //所有测试项目
//                List<String> allTestIdList = allSampleInThePro.stream().map(DtoWorkSheetData::getTestId).distinct().collect(Collectors.toList());
                //质控限值配置信息
//                List<DtoQualityControlLimit> allQualityControlLimit = StringUtils.isNotEmpty(allTestIdList) ? qualityControlLimitRepository.findByTestIdIn(allTestIdList) : new ArrayList<>();
//                Map<String, List<DtoQualityControlLimit>> qualityControlLimitMap = StringUtils.isNotEmpty(allQualityControlLimit)
//                        ? allQualityControlLimit.stream().collect(Collectors.groupingBy(DtoQualityControlLimit::getTestId)) : new HashMap<>();
                //获取当前项目对应的样品类型
                setSampleTypeForProject(allSampleInThePro, basicMap);
                dsMap.put(EnumExcelBusinessAreaType.Basic.name() + (i + 1), Collections.singletonList(basicMap));
                DtoWorkSheetPageInfo workSheetPageInfo = new DtoWorkSheetPageInfo();
                workSheetPageInfo.setPage(page);
                workSheetPageInfo.setAllSampleInThePro(allSampleInThePro);
                workSheetPageInfo.setSampleInThePro(sampleInThePro);
                workSheetPageInfo.setPara(para);
//                workSheetPageInfo.setParamNames(paramNames);
                workSheetPageInfo.setWorkSheetDataList(workSheetDataList);
                workSheetPageInfo.setProject(project);
                workSheetPageInfo.setCurveList(curveList);
                workSheetPageInfo.setCurveDetailList(curveDetailList);
//                workSheetPageInfo.setWorkSheetCalibrationCurveDetailList(workSheetCalibrationCurveDetailList);
//                workSheetPageInfo.setWorkSheetCalibrationCurveList(workSheetCalibrationCurveList);
                workSheetPageInfo.setUseRecordList(useRecordList);
                workSheetPageInfo.setParamsDataList(paramsDataList);
//                workSheetPageInfo.setAnalyzeItemSortDetailList(analyzeItemSortDetailList);
                workSheetPageInfo.setAllSampleInTheProMap(id2AllSampleInTheProMap);
//                workSheetPageInfo.setQualityControlLimitMap(qualityControlLimitMap);
//                workSheetPageInfo.setTestExpandMap(testExpandMap);
//                workSheetPageInfo.setDimensionMap(dimensionMap);
//                workSheetPageInfo.setReceiveSampleRecordList(recordList);
                workSheetPageInfo.setWorkSheetFolder(workSheetFolder);
                workSheetPageInfo.setWorkSheetReagentList(workSheetReagentList);
                workSheetPageInfo.setQualityControlEvaluateList(evaluateList);
                List<String> folderIdList = allSampleInThePro.stream().map(DtoWorkSheetData::getSampleFolderId).distinct().collect(Collectors.toList());
//                List<DtoSampleFolder> folderList = StringUtils.isNotEmpty(folderIdList) ? sampleFolderRepository.findAll(folderIdList) : new ArrayList<>();
//                workSheetPageInfo.setFolderList(folderList);
                String defaultItems = "1";
                try {
//                    workSheetPageInfo.setPageConfigMap(pageConfigMap);
                    workSheetPageInfo.setReportCode(excelReportParamVO.getReportCode());
                    workSheetPageInfo.setWorkSheetService(this);
                    workSheetPageInfoList.add(workSheetPageInfo);
//                    setWorkSheetPageInfoInd(workSheetPageInfo);
                    page = findPageData(dsMap, workSheetPageInfo, workSheetPageData);
                    proCount++;
                    workSheetPageData.setProjectNumber(proCount);
                } catch (Exception exception) {
                    exception.printStackTrace();
                    throw new BaseException(exception.getMessage());
                }
            }
            if (page < proCount) {
                page = proCount;
            }
            if (!"1".equals(para.getQcPagingByProject())) {
                //质控信息页质控样不按照项目分页
                DtoWorkSheetPageInfo workSheetPageInfo = StringUtils.isNotEmpty(workSheetPageInfoList) ? workSheetPageInfoList.get(0) : null;
                if (workSheetPageInfo != null) {
                    //清除质控样数据页数信息
                    workSheetPageData.clearQcPageNum();
                    workSheetPageInfo.setAllSampleInThePro(workSheetDataList);
                    workSheetPageInfo.setSampleInThePro(getShowWorkSheetData(workSheetDataList, para.getNotShowInYYSampleData()));
                    workSheetPageInfo.setAllSampleInTheProMap(workSheetDataList.stream().collect(Collectors.groupingBy(DtoWorkSheetData::getSampleId)));
//                    fetchWorkSheetDataInfo(workSheetPageInfo);
                    page = setQualityControl(page, dsMap, workSheetPageData, workSheetPageInfo);
                    //曲线区域赋值
                    getCurveData(para, workSheetPageInfo, 0, dsMap, workSheetPageData, page);
                }
            }
            //个性化记录单数据
//            setWorkSheetInd(dsMap, workSheetPageInfoList, page);
        }
        return page;
    }

    private int findPageData(Map<String, List<Map<String, Object>>> dsMap, DtoWorkSheetPageInfo workSheetPageInfo, DtoWorkSheetPageData workSheetPageData) {
        try {
            //数据获取
            fetchWorkSheetDataInfo(workSheetPageInfo);
            //数据绑定
            return boundData(dsMap, workSheetPageData, workSheetPageInfo);
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new BaseException(exception.getMessage());
        }
    }

    /**
     * 绑定数据内容
     *
     * @param dsMap             数据集
     * @param workSheetPageData 分页数据
     * @param workSheetPageInfo 分页信息
     * @return 页数
     */
    private int boundData(Map<String, List<Map<String, Object>>> dsMap, DtoWorkSheetPageData workSheetPageData, DtoWorkSheetPageInfo workSheetPageInfo) {
        int page = workSheetPageInfo.getPage();
        DtoWorkSheetPara para = workSheetPageInfo.getPara();
        List<DtoWorkSheetData> sampleInThePro = workSheetPageInfo.getSampleInThePro();
        //region 按样品数据分页
//        sampleInThePro = getSortedSampleDatas(sampleInThePro);
        for (int index = 0; index < sampleInThePro.size(); index += para.getSampleCountLimit()) {
            page++;
            List<DtoWorkSheetData> filterData = sampleInThePro.stream().skip(index).limit(para.getSampleCountLimit()).collect(Collectors.toList());
            List<Map<String, Object>> addList = new ArrayList<>();
            //记录单数据页赋值
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) getData(EnumObtainDataType.数据.getValue(), workSheetPageInfo,
                    filterData, index, workSheetPageInfo.getReportCode());

            if (filterData.size() < para.getSampleCountLimit()) {
                Map<String, Object> blackData = new HashMap<>();
                blackData.put(样品编号, getKBStr(0));
                blackData.put(采集编号, getKBStr(0));
                //个性化
                dataList.add(blackData);
            }
            //表头数据赋值
            setDataValue(EnumDefaultDataType.表头.getValue(), addList, workSheetPageInfo, UUIDHelper.guidEmpty(),
                    filterData, sampleInThePro, workSheetPageInfo.getProject());
            //设置表头add扩展信息
            setDataValue(EnumDefaultDataType.表头扩展.getValue(), addList, filterData, workSheetPageInfo);
            dsMap.put(EnumExcelAreaType.Add.name() + page, addList);
            dsMap.put(EnumExcelAreaType.Data.name() + page, dataList);
            if ("1".equals(para.getIsZKWithYY()) && "1".equals(para.getQcPagingByProject())) {
                //质控页数据赋值
                page = setQualityControl(page, dsMap, workSheetPageData, workSheetPageInfo);
            }
        }
        if (!"1".equals(para.getIsZKWithYY()) && "1".equals(para.getQcPagingByProject())) {
            page = setQualityControl(page, dsMap, workSheetPageData, workSheetPageInfo);
        }
        //曲线页数据赋值
        if ("1".equals(para.getQcPagingByProject())) {
            getCurveData(para, workSheetPageInfo, workSheetPageData.getProjectNumber(), dsMap, workSheetPageData, page);
        }
        return page;
    }

    /**
     * 获取原始记录单通用数据信息
     *
     * @param workSheetPageInfo 数据页信息
     * @return 报表通用数据信息列表
     */
    public List<Object> fetchWorkSheetDataInfo(DtoWorkSheetPageInfo workSheetPageInfo) {
        List<Object> objectList = new ArrayList<>();
        try {
            List<String> testIdList = workSheetPageInfo.getSampleInThePro().stream().map(DtoWorkSheetData::getTestId).distinct().collect(Collectors.toList());
            objectList.add(testIdList);
            //所有的数据信息
            List<String> anaIds = workSheetPageInfo.getAllSampleInThePro().stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
//            List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(anaIds);
//            workSheetPageInfo.setAnalyseOriginalRecordList(analyseOriginalRecordList);
            //所有参数公式新
//            List<String> recordIds = workSheetPageInfo.getParamNames().stream().map(DtoParamsConfig::getObjId).distinct().collect(Collectors.toList());
//            List<String> objectIds = analyseOriginalRecordList.stream().map(DtoAnalyseOriginalRecord::getTestFormulaId).distinct().collect(Collectors.toList());
//            List<DtoParams2ParamsFormula> dtoParams2ParamsFormulaList = params2ParamsFormulaRepository.findByRecordIdInAndObjectIdIn(recordIds, objectIds);
//            workSheetPageInfo.setParams2ParamsFormulaList(dtoParams2ParamsFormulaList);
            //参数部分公式
//            List<String> formulaIds = dtoParams2ParamsFormulaList.stream().map(DtoParams2ParamsFormula::getId).distinct().collect(Collectors.toList());
//            List<DtoParamsPartFormula> partFormulaList = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
//            List<DtoParamsConfig> pcList = new ArrayList<>();
//            if (StringUtils.isNotEmpty(formulaIds)) {
//                pcList = paramsConfigRepository.findByObjIdInAndType(formulaIds, EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
//            }
//            workSheetPageInfo.setPartFormulaList(partFormulaList);
//            workSheetPageInfo.setParamsConfigList(pcList);
            //获取标干流量的有效位
            int speedSig = 3;
//            if (StringUtils.isNotEmpty(workSheetPageInfo.getParamNames())) {
//                DtoParamsConfig paramsConfig = workSheetPageInfo.getParamNames().stream().filter(p -> "标干流量".equals(p.getAlias())).findFirst().orElse(null);
//                if (StringUtils.isNotNull(paramsConfig) && paramsConfig.getMostSignificance() != -1) {
//                    speedSig = paramsConfig.getMostSignificance();
//                }
//            }
            workSheetPageInfo.setSpeedSig(speedSig);
//            List<DtoTest> testList = testRepository.findAll(testIdList);
//            ReportUtils.sortTestByAnaItemSort(testList, workSheetPageInfo.getAnalyzeItemSortDetailList());
//            workSheetPageInfo.setTestList(testList);
        } catch (Exception e) {
            throw new BaseException("原始记录单数据获取出错!");
        }
        return objectList;
    }

    private int setQualityControl(int page, Map<String, List<Map<String, Object>>> dsMap, DtoWorkSheetPageData workSheetPageData,
                                  DtoWorkSheetPageInfo workSheetPageInfo) {
        return setQualityControlData(page, dsMap, workSheetPageData, workSheetPageInfo);
    }

    /**
     * 分页设置质控数据
     *
     * @param page              页数
     * @param dsMap             报表数据集
     * @param workSheetPageData 数据集合
     * @param workSheetPageInfo 所有数据
     * @return 页数
     */
    private int setQualityControlData(int page, Map<String, List<Map<String, Object>>> dsMap,
                                      DtoWorkSheetPageData workSheetPageData, DtoWorkSheetPageInfo workSheetPageInfo) {
        //质控数据仅按照每页固定数量分页
        page = setQualityControlValue(dsMap, workSheetPageInfo, workSheetPageData, page, null, -1);
        return page;
    }

    /**
     * 质控数据赋值
     *
     * @param dsMap             报表数据集合
     * @param workSheetPageInfo 数据集合
     * @param workSheetPageData 配置信息
     * @param page              所有数据
     * @param pageTestIds       每页测试项目id
     * @param qCCnt             质控数据每页测试项目数量
     */
    private Integer setQualityControlValue(Map<String, List<Map<String, Object>>> dsMap, DtoWorkSheetPageInfo workSheetPageInfo, DtoWorkSheetPageData workSheetPageData,
                                           int page, List<String> pageTestIds, int qCCnt) {
        List<DtoWorkSheetData> list = workSheetPageInfo.getSampleInThePro();
        List<String> idInTheList = new ArrayList<>();
        if (null != list) {
            idInTheList.addAll(list.stream().map(DtoWorkSheetData::getSampleId).distinct().collect(Collectors.toList()));
        }
        //region 加标
        setDataValue(EnumDefaultDataType.加标.getValue(), dsMap, workSheetPageInfo, workSheetPageData, idInTheList, pageTestIds, new ArrayList<>(), qCCnt);
        //region 平行
        setDataValue(EnumDefaultDataType.平行.getValue(), dsMap, workSheetPageInfo, workSheetPageData, list, idInTheList, pageTestIds, new ArrayList<>(), qCCnt);
        //region 标样
        setDataValue(EnumDefaultDataType.标样.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region jzCurve
        setDataValue(EnumDefaultDataType.曲线校准.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 空白
        setDataValue(EnumDefaultDataType.空白.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 点位
        setDataValue(EnumDefaultDataType.点位.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 曲线校核样
        setDataValue(EnumDefaultDataType.曲线校核.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 室内空白样
        setDataValue(EnumDefaultDataType.室内空白.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 校正系数检验样
        setDataValue(EnumDefaultDataType.校正系数检验.getValue(), dsMap, workSheetPageInfo, workSheetPageData, pageTestIds, new ArrayList<>(), qCCnt);
        //region 空白数据
        return (Integer) getData(EnumObtainDataType.质控.getValue(), dsMap, workSheetPageData, page, workSheetPageInfo);
        //endregion
    }

    /**
     * 遍历每一个项目时，都把曲线校核，仪器空白，试剂空白，采样介质空白,阴性对照试验，阳性对照试验，校正系数检验样品添加进来
     *
     * @param workSheetDataList 记录单所有样品信息
     * @param allSampleInThePro 项目相关的所有样品的 DtoWorkSheetData列表
     */
    private void addSamplesForEachProject(List<DtoWorkSheetData> workSheetDataList, List<DtoWorkSheetData> allSampleInThePro) {
        List<DtoWorkSheetData> sampleList = workSheetDataList.stream().filter(p -> EnumQcGrade.内部质控.getValue().equals(p.getQcGrade())
                && (EnumQcType.曲线校核.getValue().equals(p.getQcType()) || EnumQcType.仪器空白.getValue().equals(p.getQcType())
                || EnumQcType.试剂空白.getValue().equals(p.getQcType()) || EnumQcType.罐空白.getValue().equals(p.getQcType())
                || EnumQcType.阴性对照试验.getValue().equals(p.getQcType()) || EnumQcType.阳性对照试验.getValue().equals(p.getQcType())
                || EnumQcType.校正系数检验.getValue().equals(p.getQcType()))).collect(Collectors.toList());
        for (DtoWorkSheetData sample : sampleList) {
            //由于当前遍历项目所关联的质控样已经添加进来，因此要剔除当前遍历项目所关联的质控样，避免样品重复
            if (allSampleInThePro.stream().noneMatch(p -> p.getAnalyseDataId().equals(sample.getAnalyseDataId()))) {
                allSampleInThePro.add(sample);
            }
        }
    }

    /**
     * 曲线数据赋值
     *
     * @param para     工作参数数据
     * @param proCount 项目数量
     * @param dsMap    数据集合
     */
    private void getCurveData(DtoWorkSheetPara para, DtoWorkSheetPageInfo workSheetPageInfo, Integer proCount, Map<String, List<Map<String, Object>>> dsMap,
                              DtoWorkSheetPageData workSheetPageData, int page) {
        int number = proCount + 1;
        curveData(workSheetPageInfo, dsMap, number, para, page);
        workSheetPageData.setProjectNumber(number);
    }

    private void curveData(DtoWorkSheetPageInfo workSheetPageInfo, Map<String, List<Map<String, Object>>> dsMap,
                           Integer number, DtoWorkSheetPara para, int page) {
        List<DtoCurveDetail> curveDetailList = workSheetPageInfo.getCurveDetailList();
        DtoProject project = workSheetPageInfo.getProject();
//        List<DtoWorkSheetCalibrationCurveDetail> workSheetCalibrationCurveDetailList = workSheetPageInfo.getWorkSheetCalibrationCurveDetailList();
        List<Map<String, Object>> cvDataList = new ArrayList<>();
        int serial = 0;
//        if (workSheetCalibrationCurveDetailList.size() > 0) {
//            List<BigDecimal> subtractKbValue = workSheetCalibrationCurveDetailList.stream().filter(p -> "0.00".equals(p.getAddVolume())
//                    && "0.00".equals(p.getAddAmount())).map(DtoWorkSheetCalibrationCurveDetail::getLessBlankAbsorbanceDoubleValue)
//                    .collect(Collectors.toList());
//            for (int i = 0; i < workSheetCalibrationCurveDetailList.size(); i++) {
//                serial = i + 1;
//                DtoWorkSheetCalibrationCurveDetail workSheetCalibrationCurveDetail = workSheetCalibrationCurveDetailList.get(i);
//                Map<String, Object> drCurve = (Map<String, Object>) getData(EnumObtainDataType.曲线.getValue(), workSheetPageInfo,
//                        workSheetCalibrationCurveDetail, serial, null, subtractKbValue, page);
//                cvDataList.add(drCurve);
//            }
//        }
        int oldSerial = serial;
        if (curveDetailList.size() > 0) {
            for (int i = 0; i < curveDetailList.size(); i++) {
                Map<String, Object> drCurve;
                serial = i + oldSerial + 1;
                DtoCurveDetail curveDetail = curveDetailList.get(i);
                drCurve = (Map<String, Object>) getData(EnumObtainDataType.曲线.getValue(), workSheetPageInfo, null, serial,
                        curveDetail, null, page);
                cvDataList.add(drCurve);
            }
        }
        if (cvDataList.size() < para.getCurveCount()) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(序号, getKBStr(0));
            mapValue.put(项目编号, getKBStr(2));
            mapValue.put(标准溶液加入体积, getKBStr(2));
            mapValue.put(标准物加入量, getKBStr(2));
            mapValue.put(吸光度, getKBStr(2));
            mapValue.put(减空白吸光度, getKBStr(2));
            mapValue.put(X值, getKBStr(2));
            mapValue.put(Y值, getKBStr(2));
            mapValue.put(背景吸光度, getKBStr(2));
            mapValue.put(回归方程, getKBStr(2));
            mapValue.put(相关系数, getKBStr(2));
            mapValue.put(相对偏差, getKBStr(2));
            mapValue.put(吸光度1, getKBStr(2));
            mapValue.put(吸光度2, getKBStr(2));
            mapValue.put(两吸光度之差, getKBStr(2));
            mapValue.put(两空白吸光度之差, getKBStr(2));
            mapValue.put(吸光度220, getKBStr(2));
            mapValue.put(吸光度275, getKBStr(2));
            mapValue.put(含量值, getKBStr(2));
            mapValue.put(标准使用液加入浓度, getKBStr(2));
            setDataValue(EnumDefaultDataType.曲线.getValue(), cvDataList, mapValue);
        }
        dsMap.put("Curve" + number.toString(), cvDataList);
        //补足第一页之后的curve区数据
        if (page > number) {
            List<String> fldList = Arrays.asList("序号", "", "", "标准溶液加入体积", "X值", "Y值", "减空白吸光度", "标准使用液加入浓度");
            for (int i = number + 1; i <= page; i++) {
                List<Map<String, Object>> tmpCvDataList = new ArrayList<>();
                Map<String, Object> tmpMap = new HashMap<>();
                fldList.forEach(p -> tmpMap.put(p, ""));
                tmpMap.put("序号", getKBStr(0));
                tmpCvDataList.add(tmpMap);
                dsMap.put("Curve" + i, tmpCvDataList);
            }
        }
    }

    /**
     * 绑定加标数据
     *
     * @param objectMap 参数
     */
    protected void setJbDataStr(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        List<String> idInTheList = (List<String>) objectMap.get(3);
        List<String> pageTestIds = (List<String>) objectMap.get(4);
//        List<DtoTest> pageTestList = (List<DtoTest>) objectMap.get(5);
        int qcCnt = (Integer) objectMap.get(6);
        int jbCount = workSheetPageData.getJbNumber();
        //endregion
        List<DtoWorkSheetData> jbSampleList = workSheetPageInfo.getAllSampleInThePro().stream().filter(p -> (idInTheList.size() > 0 ? idInTheList.contains(p.getAssociateSampleId()) : true)
                && p.getIsQC() && EnumQcGrade.内部质控.getValue().equals(p.getQcGrade())
                && ((EnumQcType.加标.getValue()).equals(p.getQcType()) || EnumQcType.空白加标.getValue().equals(p.getQcType()))).collect(Collectors.toList());
        if (jbSampleList.size() > 0) {
            List<String> testIdList = jbSampleList.stream().map(DtoWorkSheetData::getTestId).distinct().collect(Collectors.toList());
            //获取加标测得量公式(默认同一个检测单内的多个加标样都使用同一个公式)
//            List<String> formulaIdList = jbSampleList.stream().map(DtoWorkSheetData::getFormulaId).distinct().collect(Collectors.toList());
//            String formulaId = StringUtils.isNotEmpty(formulaIdList) ? formulaIdList.get(0) : "";
//            List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaId(formulaId);
//            //倒序排序
//            List<DtoParamsPartFormula> paramsPartFormulasJB = paramsPartFormulasAll.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue()))
//                    .sorted(Comparator.comparing(DtoParamsPartFormula::getOrderNum).reversed())
//                    .collect(Collectors.toList());
//            DtoParamsPartFormula jbPartFormula = paramsPartFormulasJB.stream().findFirst().orElse(null);
//
//            //获取当前测试项目的质控限值配置
//            List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIdList);
            List<String> jbAnaDataIdList = jbSampleList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
            List<DtoQualityControlEvaluate> qualityControlEvaluateList = workSheetDataSourceService.findQualityControlEvaluate(jbAnaDataIdList);
            jbCount = (Integer) getData(EnumObtainDataType.加标.getValue(), dsMap, workSheetPageInfo, jbSampleList, jbCount, pageTestIds,
                    new ArrayList<>(), qcCnt, new ArrayList<>(), null, qualityControlEvaluateList);
        } else {
            jbCount++;
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(样品编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(加标体积, getKBStr(2));
            mapValue.put(增加值, getKBStr(2));
            mapValue.put(样品浓度, getKBStr(2));
            mapValue.put(加入标准量, getKBStr(2));
            mapValue.put(加标液浓度, getKBStr(2));
            mapValue.put(加标样品测定值, getKBStr(2));
            mapValue.put(加入标准量浓度, getKBStr(2));
            mapValue.put(原样品测定值, getKBStr(2));
            mapValue.put(回收率, getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(分析项目, getKBStr(2));
            mapValue.put(允许加标偏差, getKBStr(2));
            mapValue.put(样值较检出限, getKBStr(2));
//            setJbDefaultValInd(mapValue);
            setDataValue(EnumDefaultDataType.质控.getValue(), Addition.name(), dsMap, mapValue, jbCount);
        }
        workSheetPageData.setJbNumber(jbCount);
    }

    public Object getData(Integer getType, Object... objects) {
        Object object = null;
        Map<Integer, Object> objectMap = new HashMap<>();
        int count = 0;
        for (Object obj : objects) {
            objectMap.put(count, obj);
            count++;
        }
        if (EnumObtainDataType.加标.getValue().equals(getType)) {
            object = getJbData(objectMap);
        } else if (EnumObtainDataType.平行.getValue().equals(getType)) {
            object = getPxData(objectMap);
        } else if (EnumObtainDataType.标样.getValue().equals(getType)) {
            object = getBzData(objectMap);
        } else if (EnumObtainDataType.数据.getValue().equals(getType)) {
            object = dealPageData(objectMap);
        } else if (EnumObtainDataType.曲线.getValue().equals(getType)) {
            object = getWorkSheetConstantByDetail(objectMap);
        } else if (EnumObtainDataType.质控.getValue().equals(getType)) {
            object = getQualityPage(objectMap);
        } else if (EnumObtainDataType.测试项目数据.getValue().equals(getType)) {
//            object = dealMargePageData(objectMap);
        } else if (EnumObtainDataType.曲线校准.getValue().equals(getType)) {
//            object = getJzData(objectMap);
        } else if (EnumObtainDataType.空白.getValue().equals(getType)) {
            object = getKbData(objectMap);
        } else if (EnumObtainDataType.点位.getValue().equals(getType)) {
//            object = getFolderData(objectMap);
        } else if (EnumObtainDataType.样品数据.getValue().equals(getType)) {
//            object = dealSampleDataPage(objectMap);
        }
        return object;
    }

    /**
     * 空白样数据赋值
     *
     * @param objectMap 参数
     */
    private Integer getKbData(Map<Integer, Object> objectMap) {
        return (int) (Integer) objectMap.get(3);
    }

    /**
     * 质控页数判断
     *
     * @param objectMap 参数
     */
    protected Integer getQualityPage(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(1);
        int jbCount = workSheetPageData.getJbNumber();
        int pxCount = workSheetPageData.getPxNumber();
        int bzCount = workSheetPageData.getBzNumber();
        int jzCount = workSheetPageData.getJzNumber();
        int kbCount = workSheetPageData.getKbNumber();
        int curveCount = workSheetPageData.getCurveNumber();
        int folderCount = workSheetPageData.getFolderNumber();
        int jhCurveCount = workSheetPageData.getCurveJhNumber();
        int correctionFactorCount = workSheetPageData.getCorrectionFactorNumber();
        int innerBlankCount = workSheetPageData.getInnerBlankNumber();
        int analyzeItemCount = workSheetPageData.getAnalyzeItemNumber();
        int page = (Integer) objectMap.get(2);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(3);
        DtoProject project = workSheetPageInfo.getProject();
        //endregion
        List<Integer> listPage = new ArrayList<>(Arrays.asList(jbCount, pxCount, bzCount, jzCount, kbCount, curveCount, folderCount, jhCurveCount,
                correctionFactorCount, innerBlankCount));
        if ("1".equals(workSheetPageInfo.getPara().getPageSyncWithData())) {
            listPage.add(page);
        }
        int pageMax = Collections.max(listPage);
        if (pageMax > pxCount) {
            //todo 这个可以当参数传进来
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(平行编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(平行样, getKBStr(0));
            mapValue.put(平行样浓度, getKBStr(2));
            mapValue.put(原样, getKBStr(2));
            mapValue.put(原样浓度, getKBStr(2));
            mapValue.put(偏差类型, getKBStr(2));
            mapValue.put(偏差, getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(允许平行偏差, getKBStr(2));
            mapValue.put(平均, getKBStr(2));
            mapValue.put(分析项目, getKBStr(2));
            mapValue.put(原样与平行样, getKBStr(0));
            mapValue.put(原样编号和平行样编号, getKBStr(0));
            mapValue.put(原样检查结果修约, getKBStr(2));
            mapValue.put(平行样检测结果修约, getKBStr(2));
            mapValue.put(相对偏差, getKBStr(2));
            mapValue.put(允许相对偏差, getKBStr(2));
            mapValue.put("绝对偏差", getKBStr(2));
            mapValue.put("允许绝对偏差", getKBStr(2));
            mapValue.put(出证结果, getKBStr(2));
//            setPxQualityPageInd(mapValue);
            for (int i = pxCount; i < pageMax; i++) {
                pxCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), Parallel.name(), dsMap, mapValue, pxCount);
                workSheetPageData.setPxNumber(pxCount);
            }
        }
        if (pageMax > jbCount) {
            //todo 这个可以当参数传进来
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(样品编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(加标体积, getKBStr(2));
            mapValue.put(增加值, getKBStr(2));
            mapValue.put(样品浓度, getKBStr(2));
            mapValue.put(加入标准量, getKBStr(2));
            mapValue.put(加标液浓度, getKBStr(2));
            mapValue.put(加标样品测定值, getKBStr(2));
            mapValue.put(加入标准量浓度, getKBStr(2));
            mapValue.put(原样品测定值, getKBStr(2));
            mapValue.put(回收率, getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(分析项目, getKBStr(2));
            mapValue.put(允许加标偏差, getKBStr(2));
//            setJbQualityPageInd(mapValue);
            for (int i = jbCount; i < pageMax; i++) {
                jbCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), Addition.name(), dsMap, mapValue, jbCount);
                workSheetPageData.setJbNumber(jbCount);
            }
        }
        if (pageMax > bzCount) {
            //todo 这个可以当参数传进来
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(标样编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(标样测定值, getKBStr(2));
            mapValue.put(标样标准值, getKBStr(2));
            mapValue.put(标样不确定度, getKBStr(2));
            mapValue.put(相对误差, getKBStr(2));
            mapValue.put("允许相对误差", getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(标样配置日期, getKBStr(2));
            mapValue.put(标样有效期, getKBStr(2));
//            setBzQualityPageInd(mapValue);
            for (int i = bzCount; i < pageMax; i++) {
                bzCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), Standard.name(), dsMap, mapValue, bzCount);
                workSheetPageData.setBzNumber(bzCount);
            }
        }
        if (pageMax > jzCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(曲线校核样样品编号, getKBStr(0));
            mapValue.put(曲线校核样采集编号, getKBStr(0));
            mapValue.put(标准物质加入量, getKBStr(2));
            mapValue.put(曲线校核样相对偏差, getKBStr(2));
            mapValue.put(曲线校核样出证结果, getKBStr(2));
            for (int i = jzCount; i < pageMax; i++) {
                jzCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), JZCurve.name(), dsMap, mapValue, jzCount);
                workSheetPageData.setJzNumber(jzCount);
            }
        }
        if (pageMax > kbCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(室内空白样编号, getKBStr(0));
            mapValue.put(室内空白样采集编号, getKBStr(0));
            mapValue.put(全程序空白样编号 + "0", getKBStr(0));
            mapValue.put(全程序空白样采集编号 + "0", getKBStr(0));
//            setKbQualityPageInd(mapValue);
            for (int i = kbCount; i < pageMax; i++) {
                kbCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), KBSampleData.name(), dsMap, mapValue, kbCount);
                workSheetPageData.setKbNumber(kbCount);
            }
        }
        if (pageMax > curveCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(样品编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            for (int i = curveCount; i < pageMax; i++) {
                curveCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), "Curve", dsMap, mapValue, curveCount);
                workSheetPageData.setCurveNumber(curveCount);
            }
        }
        if (pageMax > folderCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(点位编号, getKBStr(0));
//            setFolderQualityPageInd(mapValue, workSheetPageInfo);
            for (int i = folderCount; i < pageMax; i++) {
                folderCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), SampleFolder.name(), dsMap, mapValue, folderCount);
                workSheetPageData.setFolderNumber(folderCount);
            }
        }
        if (pageMax > jhCurveCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(曲线校核样样品编号 + "0", getKBStr(0));
            mapValue.put(曲线校核样采集编号 + "0", getKBStr(0));
//            setJhCurveQualityPageInd(mapValue, workSheetPageInfo);
            for (int i = jhCurveCount; i < pageMax; i++) {
                jhCurveCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), JHCurve.name(), dsMap, mapValue, jhCurveCount);
                workSheetPageData.setCurveJhNumber(jhCurveCount);
            }
        }
        if (pageMax > correctionFactorCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(校正点浓度, getKBStr(0));
            mapValue.put(校正系数检验样出证结果, getKBStr(2));
            mapValue.put(校正系数检验样偏差, getKBStr(2));
            mapValue.put(校正系数检验样允许相对偏差, getKBStr(2));
            mapValue.put(校正系数检验样是否合格, getKBStr(2));
            for (int i = correctionFactorCount; i < pageMax; i++) {
                correctionFactorCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), CorrectionFactor.name(), dsMap, mapValue, correctionFactorCount);
                workSheetPageData.setCorrectionFactorNumber(correctionFactorCount);
            }
        }
        if (pageMax > innerBlankCount) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(室内空白样编号, getKBStr(0));
            mapValue.put(室内空白样采集编号, getKBStr(0));
//            setInnerBlankQualityPageInd(mapValue, workSheetPageInfo);
            for (int i = innerBlankCount; i < pageMax; i++) {
                innerBlankCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), InnerBlank.name(), dsMap, mapValue, innerBlankCount);
                workSheetPageData.setInnerBlankNumber(innerBlankCount);
            }
        }
        if (pageMax > analyzeItemCount) {
            Map<String, String> mapValue = new HashMap<>();
            int analyzeItemCountLimit = workSheetPageInfo.getPara().getAnalyzeItemCountLimit();
            for (int i = 0; i < analyzeItemCountLimit; i++) {
                mapValue.put(分析项目 + i, getKBStr(2));
            }
            mapValue.put("分析项目0", getKBStr(0));
//            setAnalyzeItemQualityPageInd(mapValue, workSheetPageInfo);
            for (int i = analyzeItemCount; i < pageMax; i++) {
                analyzeItemCount++;
                setDataValue(EnumDefaultDataType.质控.getValue(), AnalyzeItem.name(), dsMap, mapValue, analyzeItemCount);
                workSheetPageData.setAnalyzeItemNumber(analyzeItemCount);
            }
        }
        if (pageMax > page) {
            for (int i = page; i < pageMax; i++) {
                page++;
                List<Map<String, Object>> dtData = new ArrayList<>();
                Map<String, Object> drData = new HashMap<>();
                drData.put(样品编号, getKBStr(0));
                drData.put(采集编号, getKBStr(0));
                dtData.add(drData);
                dsMap.put(EnumExcelAreaType.Data.name() + page, dtData);

                //判断是否要添加空的add数据区
                if (!dsMap.containsKey(EnumExcelAreaType.Add.name() + page)) {
                    List<Map<String, Object>> dtAdd = new ArrayList<>();
                    Map<String, Object> drAdd = new HashMap<>();
                    drAdd.put(项目编号, project.getProjectCode());
                    dtAdd.add(drAdd);
                    dsMap.put(EnumExcelAreaType.Add.name() + page, dtAdd);
                }
            }
        }
        return page;
    }

    /***
     * 标准曲线赋值
     * @param objectMap 参数
     */
    protected Map<String, Object> getWorkSheetConstantByDetail(Map<Integer, Object> objectMap) {
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(0);
//        DtoWorkSheetCalibrationCurveDetail workSheetCalibrationCurveDetail = (DtoWorkSheetCalibrationCurveDetail) objectMap.get(1);
        Integer no = (Integer) objectMap.get(2);
        DtoProject project = workSheetPageInfo.getProject();
        List<DtoCurveDetail> curveDetailList = workSheetPageInfo.getCurveDetailList();
        DtoCurveDetail curveDetail = (DtoCurveDetail) objectMap.get(3);
        List<BigDecimal> kbValue = (List<BigDecimal>) objectMap.get(4);
        int page = (Integer) objectMap.get(5);

        Map<String, Object> drCurve = new HashMap<>();
        //绑定标准曲线
        int curveType = 1;
        if (curveDetail == null) {
            //绑定校准曲线
            curveType = 2;
        }
        //绑定曲线数据
        setCurveData(drCurve, workSheetPageInfo, no, project, curveDetailList,
                curveDetail, kbValue, page);
        return drCurve;
    }

    /**
     * @param drCurve         曲线
     * @param no              序号
     * @param project         项目信息
     * @param curveDetailList 标准曲线
     * @param curveDetail     标准曲线详情
     * @param kbValue         空白吸光度
     */
    private void setCurveData(Map<String, Object> drCurve, DtoWorkSheetPageInfo workSheetPageInfo,
                              Integer no, DtoProject project, List<DtoCurveDetail> curveDetailList,
                              DtoCurveDetail curveDetail, List<BigDecimal> kbValue, int page) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        DtoCurve curve = StringUtils.isNotEmpty(workSheetPageInfo.getCurveList()) ? workSheetPageInfo.getCurveList().get(0) : null;
        setMapValue(drCurve, 序号, no.toString());
        setMapValue(drCurve, 项目编号, project.getProjectCode());
        setMapValue(drCurve, 标准溶液加入体积, StringUtils.isNotNull(curveDetail.getAddVolume()) ? curveDetail.getAddVolume() : blankFillStr);
        setMapValue(drCurve, 标准使用液加入浓度, "/");
        setMapValue(drCurve, 减空白吸光度, checkValEmpty(curveDetail.getLessBlankAbsorbance(), blankFillStr));
//        setMapValue(drCurve, 背景吸光度, checkValEmpty(curveDetail.getAValueBG(), blankFillStr));
//        setMapValue(drCurve, Y值, checkValEmpty(curveDetail.gety(), blankFillStr));
//        setMapValue(drCurve, X值, checkValEmpty(curveDetail.getXValue(), blankFillStr));
        setMapValue(drCurve, Y值, checkValEmpty(curveDetail.getAbsorbanceB(), blankFillStr));
        setMapValue(drCurve, X值, checkValEmpty(curveDetail.getAbsorbance(), blankFillStr));
//        setCurveAbsorbancy(drCurve, curveDetail);
        setMapValue(drCurve, 两空白吸光度之差, "0.00");
        setMapValue(drCurve, 回归方程, blankFillStr);
        setMapValue(drCurve, 相关系数, curve != null ? checkValEmpty(curve.getCoefficient(), blankFillStr) : blankFillStr);
        setMapValue(drCurve, 相对偏差, blankFillStr);
        setMapValue(drCurve, 标准物加入量, StringUtils.isNotNull(curveDetail.getAddAmount()) ? curveDetail.getAddAmount() : blankFillStr);
        setMapValue(drCurve, 分析项目, blankFillStr);
        setMapValue(drCurve, 截距, curve != null ? checkValEmpty(curve.getBValue(), blankFillStr) : blankFillStr);
        setMapValue(drCurve, 斜率, curve != null ? checkValEmpty(curve.getKValue(), blankFillStr) : blankFillStr);
        setMapValue(drCurve, 吸光度220, checkValEmpty(curveDetail.getAValueTTZ(), blankFillStr));
        setMapValue(drCurve, 吸光度275, checkValEmpty(curveDetail.getAValueTSF(), blankFillStr));
        setMapValue(drCurve, 含量值, blankFillStr);
//        setMapValue(drCurve, A0, StringUtils.isNotNull(curveDetail.getBlankAbsorbance()) ? curveDetail.getBlankAbsorbance() : blankFillStr);
//        setMapValue(drCurve, A1, StringUtils.isNotNull(curveDetail.getColorAbsorbance()) ? curveDetail.getColorAbsorbance() : blankFillStr);
//        setMapValue(drCurve, A2, StringUtils.isNotNull(curveDetail.getInterfereAbsorbance()) ? curveDetail.getInterfereAbsorbance() : blankFillStr);
        setMapValue(drCurve, "曲线id", curveDetail.getCurveId());
        setMapValue(drCurve, "曲线明细id", curveDetail.getId());
    }

    /**
     * 标样数据赋值
     *
     * @param objectMap 参数
     */
    protected Integer getBzData(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> bzList = (List<DtoWorkSheetData>) objectMap.get(2);
        int page = (Integer) objectMap.get(3);
        List<String> pageTestIds = (List<String>) objectMap.get(4);
//        List<DtoTest> pageTestList = (List<DtoTest>) objectMap.get(5);
        int qCCnt = (Integer) objectMap.get(6);
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = (List<DtoQualityControlEvaluate>) objectMap.get(8);
        int count = workSheetPageInfo.getPara().getBzCount();
//        List<DtoParamsConfig> paramNames = workSheetPageInfo.getParamNames();
        if (count == 0) {
            count = bzList.size();
        }
        bzList.sort(Comparator.comparing(DtoWorkSheetData::getGatherCode));
        for (int i = 0; i < bzList.size(); i += count) {
            page++;
            List<DtoWorkSheetData> list = bzList.stream().skip(i).limit(count).collect(Collectors.toList());
            List<Map<String, Object>> dt = new ArrayList<>();
            for (DtoWorkSheetData workSheetData : list) {
                Map<String, Object> dr = new HashMap<>();
//                for (DtoParamsConfig name : paramNames) {
//                    String str = getAnalyseValue(name, workSheetData.getAnalyseDataId(), workSheetPageInfo.getAnalyseOriginalRecordList(),
//                            workSheetPageInfo.getParams2ParamsFormulaList(), workSheetPageInfo.getPartFormulaList(), workSheetPageInfo.getParamsConfigList());
//                    setMapValue(dr, name.getAlias(), checkValEmpty(str, blankFillStr));
//                }
                //平行偏差数据赋值
                DtoQualityControlEvaluate bzEvaluate = qualityControlEvaluateList.stream()
                        .filter(p -> p.getObjectId().equals(workSheetData.getAnalyseDataId())).findFirst().orElse(null);
                setBzValue(dr, workSheetData, bzEvaluate, workSheetPageInfo);
                dt.add(dr);
            }
            if (list.size() < count) {
                Map<String, Object> dr = new HashMap<>();
                setUnderBlankValue(dr);
                dt.add(dr);
            }
            dsMap.put(Standard.name() + page, dt);
        }

        return page;
    }

    /**
     * 保存标样数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     */
    protected void setBzDataValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        setMapValue(dr, 标样编号, workSheetData.getQcCode());
        setMapValue(dr, 样品编号, workSheetData.getCode());
        setMapValue(dr, 采集编号, workSheetData.getGatherCode());
        setMapValue(dr, 标样测定值, StringUtils.isEmpty(workSheetData.getTestValueDstr()) ? blankFillStr : workSheetData.getTestValueDstr());
        setMapValue(dr, 标样标准值, workSheetData.getQcValue().contains("±") ? workSheetData.getQcValue().split("±")[0] : workSheetData.getQcValue());
        setMapValue(dr, 标样不确定度, workSheetData.getQcValue().contains("±") ? (workSheetData.getQcValue().split("±").length > 1 ? workSheetData.getQcValue().split("±")[1] : "") : blankFillStr);
        setMapValue(dr, 标样出厂值, StringUtils.isEmpty(workSheetData.getQcValue()) ? blankFillStr : workSheetData.getQcValue());
        setMapValue(dr, 分析项目, StringUtils.isEmpty(workSheetData.getRedAnalyzeItems()) ? blankFillStr : workSheetData.getRedAnalyzeItems());
        setMapValue(dr, 标样配置日期, StringUtils.isEmpty(workSheetData.getQcStandardDate()) ? blankFillStr : workSheetData.getQcStandardDate());
        setMapValue(dr, 标样有效期, (StringUtils.isEmpty(workSheetData.getQcValidDate()) || workSheetData.getQcValidDate().contains("1753")) ? blankFillStr : workSheetData.getQcValidDate());
//        setBzDataValueInd(dr, workSheetData);
    }

    /**
     * 保存标样偏差数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param bzEvaluate    质控评价对象
     */
    protected void setBzDeviationValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoQualityControlEvaluate bzEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        setMapValue(dr, 相对误差, blankFillStr);
        setMapValue(dr, 是否合格, blankFillStr);
        String[] valueArr = workSheetData.getQcValue().split("±");
        if (StringUtils.isNotNullAndEmpty(workSheetData.getTestValueDstr()) && StringUtils.isNotNullAndEmpty(workSheetData.getQcValue())) {
            if (MathUtil.isNumeric(workSheetData.getTestValueDstr()) && MathUtil.isNumeric(valueArr[0])) {
                String bcValue = workSheetData.getTestValueDstr();
                String bzValue = valueArr[0];
//                Boolean isPass = StringUtils.isNotNull(limit) ? new QualityStandard().deviationQualified(limit, deviation) : null;
//                setMapValue(dr, 是否合格, StringUtils.isNotNull(isPass) ? isPass ? "是" : "否" : "");
//                setMapValue(dr, 允许相对误差, StringUtils.isNotNull(limit) ? formatYxPc(limit.getAllowLimit()) : "");
                setMapValue(dr, 相对误差, StringUtils.isNotNull(bzEvaluate)
                        ? (StringUtils.isNotEmpty(bzEvaluate.getCheckItemValue()) ? bzEvaluate.getCheckItemValue().replace("%", "") : blankFillStr) : blankFillStr);
                setMapValue(dr, 允许相对误差, blankFillStr);
                BigDecimal wcV = new BigDecimal(bcValue).subtract(new BigDecimal(bzValue)).abs();
                if (workSheetData.getQcValue().contains("±") && valueArr.length > 1) {
                    BigDecimal fwValue;
                    //如果±后面的是%，进行处理
                    if (valueArr[1].contains("%")) {
                        String percent = valueArr[1].replace("%", "");
                        float f = Float.parseFloat(percent) / 100;
                        BigDecimal decimal = new BigDecimal(Float.toString(f));
                        BigDecimal bzValueDec = new BigDecimal(valueArr[0]);
                        fwValue = bzValueDec.multiply(decimal);
                    } else {
                        fwValue = new BigDecimal(valueArr[1]);
                    }
                    if (wcV.compareTo(fwValue) <= 0) {
                        setMapValue(dr, 是否合格, "+");
                    } else {
                        setMapValue(dr, 是否合格, "-");
                    }
                    //计算允许偏差
                    String allowLmt = fwValue.divide(new BigDecimal(bzValue), 10, BigDecimal.ROUND_HALF_EVEN)
                            .multiply(new BigDecimal("100")).setScale(1, BigDecimal.ROUND_HALF_EVEN).toPlainString();
                    setMapValue(dr, 允许相对误差, allowLmt);
                }
            }
        }
//        setBzDeviationValueInd(dr, workSheetData, workSheetPageInfo);
    }

    /**
     * 平行质控数据赋值
     *
     * @param objectMap 参数
     */
    private Integer getPxData(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> pxList = (List<DtoWorkSheetData>) objectMap.get(2);
        List<DtoWorkSheetData> yyList = (List<DtoWorkSheetData>) objectMap.get(3);
        int page = (Integer) objectMap.get(4);
        List<String> pageTestIds = (List<String>) objectMap.get(5);
//        List<DtoTest> pageTestList = (List<DtoTest>) objectMap.get(6);
        int qCCnt = (Integer) objectMap.get(7);
        //获取质控评价列表
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = (List<DtoQualityControlEvaluate>) objectMap.get(8);
        int count = workSheetPageInfo.getPara().getPxCount();
//        List<DtoParamsConfig> paramNames = workSheetPageInfo.getParamNames();
        //endregion

        if (count == 0) {
            count = pxList.size();
        }
        pxList.sort(Comparator.comparing(DtoWorkSheetData::getGatherCode));
        for (int i = 0; i < pxList.size(); i += count) {
            page++;
            List<DtoWorkSheetData> list = pxList.stream().skip(i).limit(count).collect(Collectors.toList());
            List<Map<String, Object>> dt = new ArrayList<>();
            for (DtoWorkSheetData workSheetData : list) {
                Map<String, Object> dr = new HashMap<>();
//                for (DtoParamsConfig name : paramNames) {
//                    String str = getAnalyseValue(name, workSheetData.getAnalyseDataId(), workSheetPageInfo.getAnalyseOriginalRecordList(),
//                            workSheetPageInfo.getParams2ParamsFormulaList(), workSheetPageInfo.getPartFormulaList(), workSheetPageInfo.getParamsConfigList());
//                    setMapValue(dr, name.getAlias(), checkValEmpty(str, blankFillStr));
//                }

                //平行偏差数据赋值
                DtoQualityControlEvaluate pxEvaluate = qualityControlEvaluateList.stream().filter(p -> p.getObjectId()
                        .equals(workSheetData.getAnalyseDataId())).findFirst().orElse(null);
                setPxDataValue(dr, workSheetData, yyList, pxEvaluate, workSheetPageInfo);
                dt.add(dr);
            }
            if (list.size() < count) {
                Map<String, Object> dr = new HashMap<>();
                setUnderBlankValue(dr);
                dt.add(dr);
            }
            dsMap.put(Parallel.name() + page, dt);
        }
        return page;
    }

    /**
     * 绑定平行数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param yyList        原样数据
     * @param pxEvaluate    平行样质控评价对象
     */
    private void setPxDataValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, List<DtoWorkSheetData> yyList,
                                DtoQualityControlEvaluate pxEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        if (yyList != null && !workSheetData.getAssociateSampleId().equals("") && !workSheetData.getAssociateSampleId().equals(UUIDHelper.guidEmpty())) {
            DtoWorkSheetData yySample = yyList.stream().filter(p -> p.getSampleId().equals(workSheetData.getAssociateSampleId())
                    && p.getAnalyseItemId().equals(workSheetData.getAnalyseItemId())).findFirst().orElse(null);
            if (StringUtils.isNotNull(yySample) && !UUIDHelper.guidEmpty().equals(yySample.getAssociateSampleId())
                    && EnumSampleCategory.串联样.getValue().equals(yySample.getSampleCategory())) {
                //串联样的平行样需要找到对应串联样的原样
                String assId = yySample.getAssociateSampleId();
                yySample = yyList.stream().filter(p -> p.getSampleId().equals(assId)
                        && p.getAnalyseItemId().equals(workSheetData.getAnalyseItemId())).findFirst().orElse(null);
            }
            if (null != yySample) {
                //保存平行样出证数据
                setPxTestValue(dr, workSheetData, yyList, yySample);
                //保存平行编号数据
                setPxSampleCodeValue(dr, workSheetData, yySample, yyList);
                //保存平行偏差数据
                setPxDeviationValue(dr, workSheetData, yySample, yyList, pxEvaluate, workSheetPageInfo);
            } else {
                //保存平行偏差默认值
                setPxDeviationDefultValue(dr, workSheetData.getCode(), workSheetPageInfo);
            }
        }
        setMapValue(dr, 分析项目, workSheetData.getRedAnalyzeItems());
    }

    /**
     * 保存平行偏差默认值
     *
     * @param dr 数据
     */
    private void setPxDeviationDefultValue(Map<String, Object> dr, String sampleCode, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        setMapValue(dr, 偏差类型, blankFillStr);
        setMapValue(dr, 偏差, blankFillStr);
        setMapValue(dr, 是否合格, blankFillStr);
        setMapValue(dr, 允许平行偏差, blankFillStr);
        setMapValue(dr, 原样浓度, blankFillStr);
        setMapValue(dr, 原样编号和平行样编号, blankFillStr);
        setMapValue(dr, 平行编号, sampleCode);
    }

    /**
     * 保存平行偏差数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param yySample      原样内容
     * @param yyList        所有样品分析数据
     * @param pxEvaluate    平行样质控评价对象
     */
    private void setPxDeviationValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoWorkSheetData yySample,
                                     List<DtoWorkSheetData> yyList, DtoQualityControlEvaluate pxEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        Boolean isPass = StringUtils.isNotNull(pxEvaluate) ? pxEvaluate.getIsPass() : null;
//        setMapValue(dr, 偏差类型, StringUtils.isNotNull(pxEvaluate) ? EnumJudgingMethod.getName(pxEvaluate.getJudgingMethod()) : blankFillStr);
        setMapValue(dr, 偏差, StringUtils.isNotNull(pxEvaluate) ? pxEvaluate.getCheckItemValue().replace("%", "") : blankFillStr);
        String checkItemVal = StringUtils.isNotNull(pxEvaluate) ? pxEvaluate.getCheckItemValue() : "";
        setMapValue(dr, 是否合格, (StringUtils.isNotEmpty(checkItemVal) && checkItemVal.contains("无法计算"))
                ? "未评价" : formatPass(isPass, blankFillStr));
        setMapValue(dr, 允许平行偏差, StringUtils.isNotNull(pxEvaluate) ? formatYxPc(pxEvaluate.getAllowLimit(), blankFillStr, "≤±", "~") : blankFillStr);

//        DtoTest test = testRepository.findOne(workSheetData.getTestId());
        List<String> valueList = getYyAndPxOriValueForCl(workSheetData, yySample, yyList);
        String yyOriginalValue = valueList.get(0);
        String pxOriginalVal = valueList.get(1);
        //计算均值
        if (MathUtil.isNumeric(yyOriginalValue) && MathUtil.isNumeric(pxOriginalVal)) {
            BigDecimal sum = new BigDecimal(0);
            sum = sum.add(new BigDecimal(yyOriginalValue));
//            if (EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
//                pxOriginalVal = proService.getDecimal(workSheetData.getMostSignificance(), workSheetData.getMostDecimal(), pxOriginalVal);
//            }
            sum = sum.add(new BigDecimal(pxOriginalVal));
            BigDecimal avgValue = sum.divide(new BigDecimal("2"), 10, BigDecimal.ROUND_HALF_EVEN);
            valueList.add(avgValue.toString());

            setMapValue(dr, 平均, avgValue.toString());
        }
//        setPxDeviationValueInd(dr, workSheetData, yySample, workSheetPageInfo);
    }

    /**
     * 绑定标样基础数据和偏差数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param bzEvaluate    质控评价对象
     */
    private void setBzValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoQualityControlEvaluate bzEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        //绑定标样基础数据
        setBzDataValue(dr, workSheetData, workSheetPageInfo);
        //绑定标样偏差数据
        setBzDeviationValue(dr, workSheetData, bzEvaluate, workSheetPageInfo);
    }

    /**
     * 保存平行编号数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param yySample      原样内容
     */
    protected void setPxSampleCodeValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoWorkSheetData yySample, List<DtoWorkSheetData> yyList) {
        setMapValue(dr, 原样, yySample.getCode());
        setMapValue(dr, 平行编号, yySample.getCode() + "与自平行");
        setMapValue(dr, 原样编号和平行样编号, yySample.getCode() + "与" + workSheetData.getCode());
        //采集编号获取对应原样的采集编号
        String gatherCode = yySample.getGatherCode();
        if (EnumQcGrade.外部质控.getValue().equals(workSheetData.getQcGrade())) {
            gatherCode = workSheetData.getGatherCode() + "（现场平行样）";
        }
        setMapValue(dr, 采集编号, gatherCode);
//        DtoTest test = testRepository.findOne(workSheetData.getTestId());
        List<String> valueList = getYyAndPxOriValueForCl(workSheetData, yySample, yyList);
        String yyOriginalValue = valueList.get(0);
        setMapValue(dr, 原样浓度, yyOriginalValue);
        if (StringUtils.isNotNullAndEmpty(yyOriginalValue) && StringUtils.isNotNullAndEmpty(yySample.getExamLimitValue())) {
            String yyValue = yyOriginalValue.replace("L", "").replace("<", "").replace("≥", "").replace("＜", "");
            Double yyExamValue = new BigDecimal(yySample.getExamLimitValue().trim()).doubleValue();
            if (MathUtil.isNumeric(yyValue)) {
                Double yyValueDstr = new BigDecimal(yyValue).doubleValue();
                if (yyValueDstr < yyExamValue) {
                    setMapValue(dr, 原样浓度, "ND(" + yySample.getExamLimitValue() + ")");
                }
            }
        } else {
//            setMapValue(dr, 原样浓度, getKBStr(2));
            setMapValue(dr, 平行编号, workSheetData.getCode());
        }
//        setPxSampleCodeValueInd(dr, workSheetData, yySample, yyOriginalValue);
    }


    /**
     * 保存平行样出证数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     */
    private void setPxTestValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, List<DtoWorkSheetData> yyList, DtoWorkSheetData yySample) {
        String code = EnumQcGrade.外部质控.getValue().equals(workSheetData.getQcGrade()) ? workSheetData.getCode() + "（现场平行样）" : workSheetData.getCode();
        setMapValue(dr, 平行样, code);
//        DtoTest test = testRepository.findOne(workSheetData.getTestId());
        List<String> valueList = getYyAndPxOriValueForCl(workSheetData, yySample, yyList);
        String pxOriginalValue = valueList.get(1);
        setMapValue(dr, 平行样浓度, pxOriginalValue);
        if (StringUtils.isNotNullAndEmpty(pxOriginalValue) && StringUtils.isNotNullAndEmpty(workSheetData.getExamLimitValue())) {
            String strValue = pxOriginalValue.replace("L", "").replace("<", "").replace("≥", "").replace("＜", "");
            double examValue = Double.parseDouble(workSheetData.getExamLimitValue().trim());//new BigDecimal(workSheetData.getExamLimitValue()).doubleValue();
            if (MathUtil.isNumeric(strValue)) {
                double valueDstr = Double.parseDouble(strValue.trim());//new BigDecimal(strValue).doubleValue();
                if (valueDstr < examValue) {
                    setMapValue(dr, 平行样浓度, "ND(" + workSheetData.getExamLimitValue() + ")");
                }
            }
        }
//        setPxTestValueInd(dr, workSheetData, pxOriginalValue);
    }

    protected List<String> getYyAndPxOriValueForCl(DtoWorkSheetData workSheetData, DtoWorkSheetData yySample, List<DtoWorkSheetData> yyList) {
        String examLimitValue = workSheetData.getExamLimitValue();
        List<String> valueList = new ArrayList<>();
        DtoWorkSheetData yyData = null;
        DtoWorkSheetData clData = null;
        List<DtoWorkSheetData> yyAndClList = new ArrayList<>();
        yyAndClList.add(yySample);
        //判断原样是否是串联样或者有对应串联样，如果有串联样，需要将串联样或对应原样的的检测结果和原样的检测结果相加
        if (EnumSampleCategory.串联样.getValue().equals(yySample.getSampleCategory())) {
            //原样为串联样,获取对应原样
            yyData = yyList.stream().filter(p -> p.getSampleId().equals(yySample.getAssociateSampleId()) && p.getTestId().equals(yySample.getTestId())).findFirst().orElse(null);
            if (StringUtils.isNotNull(yyData)) {
                yyAndClList.add(yyData);
            }
        } else {
            //不为串联样，判断是否有串联样(暂时只考虑一个串联样的情况)
            clData = yyList.stream().filter(p -> yySample.getSampleId().equals(p.getAssociateSampleId())
                    && EnumSampleCategory.串联样.getValue().equals(p.getSampleCategory())).findFirst().orElse(null);
            if (StringUtils.isNotNull(clData)) {
                yyAndClList.add(clData);
            }
        }
        if (EnumQcGrade.内部质控.getValue().equals(workSheetData.getQcGrade())) {
            BigDecimal yyOriginalValueDec = new BigDecimal("0");
            if (yyAndClList.size() > 1) {
                for (DtoWorkSheetData yyClData : yyAndClList) {
                    //获取原样检测结果
                    String originalValue = yyClData.getTestOrignValue();
                    //判定是否小于检出限，小于检出限则按照配置的取值方式取值
//                    originalValue = analyseDataService.halfLimit(originalValue, examLimitValue);
//                    if (StringUtils.isNotNull(test) && EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
//                        if (MathUtil.isNumeric(originalValue)) {
//                            originalValue = new BigDecimal(proService.getDecimal(workSheetData.getMostSignificance(), workSheetData.getMostDecimal(), originalValue)).toString();
//                        }
//                    }
                    if (MathUtil.isNumeric(originalValue)) {
                        yyOriginalValueDec = yyOriginalValueDec.add(new BigDecimal(originalValue));
                    }
                }
                String yyOriginalValue = yyOriginalValueDec.stripTrailingZeros().toPlainString();
//                yyOriginalValue = analyseDataService.calculateTestValue(test, yyOriginalValue, workSheetData.getMostSignificance(), workSheetData.getMostDecimal(),
//                        workSheetData.getExamLimitValue(), workSheetData.getExamLimitValueLess());
                yyOriginalValue = yyOriginalValue.replace("<", "＜");
                valueList.add(yyOriginalValue);
            } else {
//                String yyOriginalValue = analyseDataService.calculateTestValue(test, yyAndClList.get(0).getTestOrignValue(), workSheetData.getMostSignificance(), workSheetData.getMostDecimal(),
//                        workSheetData.getExamLimitValue(), workSheetData.getExamLimitValueLess());
                String yyOriginalValue = yyAndClList.get(0).getTestOrignValue();
                yyOriginalValue = yyOriginalValue.replace("<", "＜");
                valueList.add(yyOriginalValue);
            }
        } else {
            //现场平行样的原样检测结果获取原样的室内平行样的出证结果,不存在室内平行样时获取原样的检测结果修约值
            DtoWorkSheetData innerPxForYy = yyList.stream().filter(p -> p.getAssociateSampleId().equals(yySample.getSampleId())
                    && EnumQcGrade.内部质控.getValue().equals(p.getQcGrade()) && EnumQcType.平行.getValue().equals(p.getQcType())
                    && p.getAnalyseItemId().equals(yySample.getAnalyseItemId())).findFirst().orElse(null);
            if (StringUtils.isNotNull(innerPxForYy)) {
                valueList.add(innerPxForYy.getTestValue());
            } else {
//                String yyDstVal = reCalcDstWithExamVal(yySample.getTestOrignValue(), yySample.getTest(), yySample.getMostDecimal(),
//                        yySample.getMostSignificance(), yySample.getExamLimitValue(), yySample.getExamLimitValueLess());
                String yyDstVal = yySample.getTestOrignValue();
                valueList.add(yyDstVal);
            }
        }

        //放置当前平行样，以及其原样对应的串联样的平行样(或串联样的原样的平行样)
        List<DtoWorkSheetData> pxAndYyPxDataList = new ArrayList<>();
        pxAndYyPxDataList.add(workSheetData);
        DtoWorkSheetData yyClData = yyData;
        if (StringUtils.isNull(yyClData)) {
            yyClData = clData;
        }
        if (yyClData != null) {
            //存在原样或者串联样,则获取对应平行样（暂时只考虑一个平行样）
            String yyClSampleId = yyClData.getSampleId();
            Integer qcGrade = EnumQcGrade.内部质控.getValue().equals(workSheetData.getQcGrade())
                    ? EnumQcGrade.内部质控.getValue() : EnumQcGrade.外部质控.getValue();
            DtoWorkSheetData yyClPxData = yyList.stream().filter(p -> !UUIDHelper.guidEmpty().equals(p.getAssociateSampleId()) && qcGrade.equals(p.getQcGrade())
                    && (EnumQcType.平行.getValue()).equals(p.getQcType()) && yyClSampleId.equals(p.getAssociateSampleId())).findFirst().orElse(null);
            if (StringUtils.isNotNull(yyClPxData)) {
                pxAndYyPxDataList.add(yyClPxData);
            }
        }

        BigDecimal pxOriginalValDec = new BigDecimal("0");
        if (pxAndYyPxDataList.size() > 1) {
            for (DtoWorkSheetData data : pxAndYyPxDataList) {
                //获取平行样品检测结果
                String originalVal = data.getTestOrignValue();
//                originalVal = analyseDataService.halfLimit(originalVal, examLimitValue);
                if (MathUtil.isNumeric(originalVal)) {
//                    originalVal = proService.getDecimal(data.getMostSignificance(), data.getMostDecimal(), originalVal);
                    pxOriginalValDec = pxOriginalValDec.add(new BigDecimal(originalVal));
                }
            }
            String pxOriginalVal = pxOriginalValDec.stripTrailingZeros().toPlainString();
//            pxOriginalVal = analyseDataService.calculateTestValue(test, pxOriginalVal, workSheetData.getMostSignificance(),
//                    workSheetData.getMostDecimal(), workSheetData.getExamLimitValue(), workSheetData.getExamLimitValueLess());
            pxOriginalVal = pxOriginalVal.replace("<", "＜");
            valueList.add(pxOriginalVal);
        } else {
//            String pxOriginalVal = analyseDataService.calculateTestValue(test, pxAndYyPxDataList.get(0).getTestOrignValue(), workSheetData.getMostSignificance(),
//                    workSheetData.getMostDecimal(), workSheetData.getExamLimitValue(), workSheetData.getExamLimitValueLess());
            String pxOriginalVal = pxAndYyPxDataList.get(0).getTestOrignValue();
            pxOriginalVal = pxOriginalVal.replace("<", "＜");
            valueList.add(pxOriginalVal);
        }
        return valueList;
    }

    /**
     * 加标数据获取
     *
     * @param objectMap 参数
     */
    protected Integer getJbData(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> jbList = (List<DtoWorkSheetData>) objectMap.get(2);
        int page = (Integer) objectMap.get(3);
        List<String> pageTestIds = (List<String>) objectMap.get(4);
//        List<DtoTest> pageTestList = (List<DtoTest>) objectMap.get(5);
        int qCCnt = (Integer) objectMap.get(6);
        //获取当前测试项目的质控限值配置
//        List<DtoQualityControlLimit> qualityControlLimitList = (List<DtoQualityControlLimit>) objectMap.get(7);
//        DtoParamsPartFormula jbPartFormula = (DtoParamsPartFormula) objectMap.get(8);
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = (List<DtoQualityControlEvaluate>) objectMap.get(9);
        int count = workSheetPageInfo.getPara().getJbCount();
//        List<DtoParamsConfig> paramNames = workSheetPageInfo.getParamNames();
        Map<String, List<DtoWorkSheetData>> allSampleInTheProMap = workSheetPageInfo.getAllSampleInTheProMap();
        //endregion
        if (count == 0) {
            count = jbList.size();
        }
        //空白加标放在前面,普通加标放在后面，同一类型加标样按照采集编号排序
//        jbList = sortBlankJb(jbList);
        for (int i = 0; i < jbList.size(); i += count) {
            page++;
            //加标分页
            List<DtoWorkSheetData> list = jbList.stream().skip(i).limit(count).collect(Collectors.toList());
            List<Map<String, Object>> dt = new ArrayList<>();
            for (DtoWorkSheetData workSheetData : list) {
                Map<String, Object> dr = new HashMap<>();
//                for (DtoParamsConfig name : paramNames) {
//                    //参数的数据结果
//                    String str = getAnalyseValue(name, workSheetData.getAnalyseDataId(), workSheetPageInfo.getAnalyseOriginalRecordList(),
//                            workSheetPageInfo.getParams2ParamsFormulaList(), workSheetPageInfo.getPartFormulaList(), workSheetPageInfo.getParamsConfigList());
//                    //参数数据绑定
//                    setMapValue(dr, name.getAlias(), checkValEmpty(str, blankFillStr));
//                }
                //获取加标样对应的原样
                List<DtoWorkSheetData> yyDataList = allSampleInTheProMap.get(workSheetData.getAssociateSampleId());
                DtoWorkSheetData yyData = StringUtils.isNotEmpty(yyDataList) ? yyDataList.stream()
                        .filter(p -> workSheetData.getTestId().equals(p.getTestId())).findFirst().orElse(null) : null;
//                List<DtoQualityControlLimit> jbControlLimitList = qualityControlLimitList.stream().filter(p -> p.getTestId().equals(workSheetData.getTestId())
//                        && p.getQcType().equals(workSheetData.getQcType())).collect(Collectors.toList());
                DtoQualityControlEvaluate jbEvaluate = qualityControlEvaluateList.stream()
                        .filter(p -> p.getObjectId().equals(workSheetData.getAnalyseDataId())).findFirst().orElse(null);
                setJbDataValue(dr, workSheetData, yyData, jbEvaluate, workSheetPageInfo);
                dt.add(dr);
            }
            if (list.size() < count) {
                Map<String, Object> dr = new HashMap<>();
                setUnderBlankValue(dr);
                dt.add(dr);
            }
            dsMap.put(Addition.name() + page, dt);
        }

        return page;
    }

    /**
     * 以下空白默认值赋值
     *
     * @param dr 数据
     */
    protected void setUnderBlankValue(Map<String, Object> dr) {
        setMapValue(dr, 样品编号, getKBStr(0));
        setMapValue(dr, 标样编号, getKBStr(0));
        setMapValue(dr, 采集编号, getKBStr(0));
        setMapValue(dr, 平行样编号, getKBStr(1));
        setMapValue(dr, 平行样, getKBStr(0));
    }

    /**
     * 绑定加标数据
     *
     * @param dr            数据
     * @param workSheetData 赋值内容
     * @param yyData        加标样对应的原样
     * @param jbEvaluate    加标质控评价对象
     */
    protected void setJbDataValue(Map<String, Object> dr, DtoWorkSheetData workSheetData, DtoWorkSheetData yyData,
                                  DtoQualityControlEvaluate jbEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        setMapValue(dr, 样品编号, workSheetData.getCode());
        //采集编号获取对应原样的采集编号
        setMapValue(dr, 采集编号, StringUtils.isNotNull(yyData) ? yyData.getGatherCode() : blankFillStr);
        //加标偏差数据
        //添加默认的质控限值配置
//        jbControlLimitList.add(ReportUtils.initDefaultQualityControlLimit(workSheetData.getTestId(), workSheetData.getQcType(),
//                workSheetData.getQcGrade(), EnumBase.EnumJudgingMethod.回收率.getValue(), null));
//        setJbDeviationValue(dr, workSheetData, jbEvaluate, workSheetPageInfo);
                Boolean isPass = StringUtils.isNotNull(jbEvaluate) ? jbEvaluate.getIsPass() : null;
        boolean flag = StringUtils.isNotNull(jbEvaluate) && StringUtils.isNotEmpty(jbEvaluate.getCheckItemValue()) && jbEvaluate.getCheckItemValue().contains("无法计算");
        dr.put(是否合格, flag ? "未评价" : GenerateExcelReportUtil.formatPass(isPass, blankFillStr));
        dr.put(允许加标偏差, StringUtils.isNotNull(jbEvaluate) ? GenerateExcelReportUtil.formatYxPc(jbEvaluate.getAllowLimit(),
                blankFillStr, "≤±", "~") : blankFillStr);
        //加标标准量浓度数据
//        setJbScalarValue(dr, workSheetData);
        setMapValue(dr, 加标体积, StringUtils.isEmpty(workSheetData.getQcVolume()) ? blankFillStr : workSheetData.getQcVolume());
        setMapValue(dr, 加入标准量, StringUtils.isEmpty(workSheetData.getQcValue()) ? blankFillStr : workSheetData.getQcValue());
        setMapValue(dr, 加标液浓度, StringUtils.isEmpty(workSheetData.getQcConcentration()) ? blankFillStr : workSheetData.getQcConcentration());
        setMapValue(dr, 加标样品测定值, StringUtils.isEmpty(workSheetData.getQcTestValue()) ? blankFillStr : workSheetData.getQcTestValue());
        String realSampleTestValue = StringUtils.isEmpty(workSheetData.getRealSampleTestValue()) ? blankFillStr : workSheetData.getRealSampleTestValue();
        setMapValue(dr, 原样品测定值, realSampleTestValue);
        setMapValue(dr, 回收率, StringUtils.isEmpty(workSheetData.getQcInfo()) ? blankFillStr : workSheetData.getQcInfo().replace("%", ""));
        setMapValue(dr, 分析项目, StringUtils.isEmpty(workSheetData.getRedAnalyzeItems()) ? blankFillStr : workSheetData.getRedAnalyzeItems());
        //样值和检出限进行比较，若小于检出限,则显示测试项目上配置的小于检出限结果
//        String realSampleTestValueExam = realSampleTestValue;
//        if (MathUtil.isNumeral(realSampleTestValue) && MathUtil.isNumeral(workSheetData.getExamLimitValue())
//                && new BigDecimal(realSampleTestValue).compareTo(new BigDecimal(workSheetData.getExamLimitValue())) < 0) {
//            realSampleTestValueExam = workSheetData.getExamLimitValueLess();
//        }

//        String realSampleTestValueExam = reCalcDstWithExamVal(realSampleTestValue, workSheetData.getTest(), workSheetData.getMostDecimal(),
//                workSheetData.getMostSignificance(), workSheetData.getExamLimitValue(), workSheetData.getExamLimitValueLess());

        setMapValue(dr, 样值较检出限, checkValEmpty(realSampleTestValue, blankFillStr));
        //样值,加标样测定值和加标测得量公式配置的小于检出限进行比较,小于检出限则显示＜检出限
        dr.put(原样品测定值, realSampleTestValue);
        String qcTestVal = StringUtils.isNotNull(workSheetData.getQcTestValue()) ? workSheetData.getQcTestValue() : blankFillStr;
        dr.put(加标样品测定值, qcTestVal);
//        setJbDataValueInd(dr, workSheetData, yyData, jbControlLimitList, jbPartFormula);
    }

    public void setDataValue(Integer setType, Object... objects) {
        Map<Integer, Object> objectMap = new HashMap<>();
        Integer count = 0;
        for (Object obj : objects) {
            objectMap.put(count, obj);
            count++;
        }
        if (EnumDefaultDataType.表头.getValue().equals(setType)) {
            setHeaderValue(objectMap);
        } else if (EnumDefaultDataType.质控.getValue().equals(setType)) {
            setDefultData(objectMap);
        } else if (EnumDefaultDataType.曲线.getValue().equals(setType)) {
            setCurveDefultData(objectMap);
        } else if (EnumDefaultDataType.加标.getValue().equals(setType)) {
            setJbDataStr(objectMap);
        } else if (EnumDefaultDataType.平行.getValue().equals(setType)) {
            setPxDataStr(objectMap);
        } else if (EnumDefaultDataType.标样.getValue().equals(setType)) {
            setBzDataStr(objectMap);
        } else if (EnumDefaultDataType.表头扩展.getValue().equals(setType)) {
            setAddMsgValue(objectMap);
        } else if (EnumDefaultDataType.曲线校准.getValue().equals(setType)) {
            setJzDataStr(objectMap);
        } else if (EnumDefaultDataType.空白.getValue().equals(setType)) {
            setKbDataStr(objectMap);
        } else if (EnumDefaultDataType.点位.getValue().equals(setType)) {
            setFolderDataStr(objectMap);
        } else if (EnumDefaultDataType.曲线校核.getValue().equals(setType)) {
            setCurveJHDataStr(objectMap);
        } else if (EnumDefaultDataType.室内空白.getValue().equals(setType)) {
            setInnerBlankDataStr(objectMap);
        } else if (EnumDefaultDataType.校正系数检验.getValue().equals(setType)) {
            setCorrectionFactorDataStr(objectMap);
        }
    }

    /**
     * 绑定点位数据
     *
     * @param objectMap 参数
     */
    protected void setFolderDataStr(Map<Integer, Object> objectMap) {

    }

    /**
     * 绑定校正系数检验样数据
     *
     * @param objectMap 参数
     */
    private void setCorrectionFactorDataStr(Map<Integer, Object> objectMap) {

    }

    protected void setInnerBlankDataStr(Map<Integer, Object> objectMap) {
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        workSheetPageData.setInnerBlankNumber(setInnerBlank(workSheetPageInfo, workSheetPageData.getInnerBlankNumber(), 室内空白样编号, 2,
                (List<String>) objectMap.get(3), (Integer) objectMap.get(5), 2, dsMap));
    }

    /**
     * 获取并设置室内空白样信息
     *
     * @param workSheetPageInfo 报表数据集
     * @param innerBlankCount   室内空白样页数
     * @param kbFldName         以下空白属性名称
     * @param pageInKbLmtCnt    每页放置的室内空白样个数
     */
    public int setInnerBlank(DtoWorkSheetPageInfo workSheetPageInfo, int innerBlankCount, String kbFldName, int pageInKbLmtCnt, List<String> pageTestIds,
                             int pageTstCnt, int pagePerTstKbCnt, Map<String, List<Map<String, Object>>> dsMap) {
        List<DtoWorkSheetData> allSampleInTheProList = workSheetPageInfo.getAllSampleInThePro();
        //创建集合放室内空白样
        List<DtoWorkSheetData> inKbWorkSheetDataList = allSampleInTheProList.parallelStream().filter(p -> EnumQcGrade.内部质控.getValue().equals(p.getQcGrade()))
                .filter(p -> EnumQcType.空白.getValue().equals(p.getQcType())).collect(Collectors.toList());
//        setInKbWorkSheetDataListInd(inKbWorkSheetDataList);
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = new ArrayList<>();
        if (StringUtils.isNotEmpty(inKbWorkSheetDataList)) {
            //获取当前测试项目的质控限值配置
            List<String> anaDataIdList = inKbWorkSheetDataList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
            qualityControlEvaluateList = workSheetDataSourceService.findQualityControlEvaluate(anaDataIdList);
        }
        //如果室内空白样的个数不为空
        if (StringUtils.isNotEmpty(inKbWorkSheetDataList)) {
            //按照采集编号排序
            inKbWorkSheetDataList.sort(Comparator.comparing(DtoWorkSheetData::getGatherCode));
            //如果室内空白核样存在，赋值到InnerBlank集合
            for (int i = 0; i < inKbWorkSheetDataList.size(); i += pageInKbLmtCnt) {
                innerBlankCount++;
                Map<String, Object> inBlankMap = new HashMap<>();
                List<DtoWorkSheetData> pageInKbList = inKbWorkSheetDataList.stream().skip(i).limit(pageInKbLmtCnt).collect(Collectors.toList());
                for (int k = 0; k < pageInKbList.size(); k++) {
                    DtoWorkSheetData inKbData = pageInKbList.get(k);
                    DtoQualityControlEvaluate evaluate = qualityControlEvaluateList.stream()
                            .filter(p -> p.getObjectId().equals(inKbData.getAnalyseDataId())).findFirst().orElse(null);
                    setInKbData(inBlankMap, String.valueOf(k), inKbData, evaluate, workSheetPageInfo);
                }
                if (pageInKbList.size() < pageInKbLmtCnt) {
                    for (int j = pageInKbList.size(); j < pageInKbLmtCnt; j++) {
                        for (String fld : inKbFldList) {
                            inBlankMap.put(fld + "" + j, "");
                        }
                    }
                    inBlankMap.put(室内空白样编号 + pageInKbList.size(), getKBStr(0));
                    inBlankMap.put("室内空白样采集编号" + pageInKbList.size(), getKBStr(0));
                }
                dsMap.put(InnerBlank.name() + innerBlankCount, Collections.singletonList(inBlankMap));
            }
        } else {
            innerBlankCount++;
            Map<String, Object> inBlankMap = new HashMap<>();
            for (String fld : inKbFldList) {
                for (int i = 0; i < pageInKbLmtCnt; i++) {
                    inBlankMap.put(fld + i, "");
                    inBlankMap.put(fld + i, "");
                }
            }
            inBlankMap.put(kbFldName + 0, getKBStr(0));
            inBlankMap.put("室内空白样采集编号0", getKBStr(0));
            dsMap.put(InnerBlank.name() + innerBlankCount, Collections.singletonList(inBlankMap));
        }
        return innerBlankCount;
    }

    /**
     * 设置室内空白样行数据
     *
     * @param dataMap  室内空白样区行数据集
     * @param kbIdx    校核索引
     * @param kbData   校核样信息
     * @param evaluate 评价质控对象
     */
    public void setInKbData(Map<String, Object> dataMap, String kbIdx, DtoWorkSheetData kbData, DtoQualityControlEvaluate evaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        dataMap.put(室内空白样编号 + kbIdx, StringUtils.isNotEmpty(kbData.getCode()) ? kbData.getCode() : blankFillStr);
        dataMap.put(室内空白样采集编号 + kbIdx, StringUtils.isNotEmpty(kbData.getGatherCode()) ? kbData.getGatherCode() : blankFillStr);
        dataMap.put(室内空白样出证结果 + kbIdx, StringUtils.isNotEmpty(kbData.getTestValue()) ? kbData.getTestValue() : blankFillStr);
        //取质控限值及判断是否合格
        String samVal = StringUtils.isNotNull(evaluate) && StringUtils.isNotEmpty(evaluate.getCheckItemValue()) ? evaluate.getCheckItemValue() : blankFillStr;
        dataMap.put(室内空白样出证结果 + kbIdx, samVal);
        String isPass = StringUtils.isNotNull(evaluate) && StringUtils.isNotNull(evaluate.getIsPass()) ? evaluate.getIsPass() ? "是" : "否" : blankFillStr;
        dataMap.put(室内空白样质控限值 + kbIdx, getYxPcForBlank(evaluate.getJudgingMethod(), evaluate.getAllowLimit(), kbData.getExamLimitValue(), kbData.getLowerLimit(), blankFillStr));
        dataMap.put(室内空白样是否合格 + kbIdx, isPass);
//        dataMap.put("室内空白样是否合格" + kbIdx, ReportUtils.checkPassForBlank(evaluate, kbData.getExamLimitValue(), kbData.getLowerLimit(), samVal, "/", null, isPass));
    }

    private void setCurveJHDataStr(Map<Integer, Object> objectMap) {
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        workSheetPageData.setCurveJhNumber(setCurveJh(workSheetPageInfo, workSheetPageData.getCurveJhNumber(), 曲线校核样样品编号,
                (List<String>) objectMap.get(3), (Integer) objectMap.get(5), 2, dsMap));
    }

    /**
     * 获取并设置曲线校核样信息
     *
     * @param workSheetPageInfo 报表数据集
     * @param jHCount           曲线校核样页数
     * @param kbFldName         以下空白属性名称
     */
    private int setCurveJh(DtoWorkSheetPageInfo workSheetPageInfo, int jHCount, String kbFldName, List<String> pageTestIds,
                           int pageTstCnt, int pagePerTstKbCnt, Map<String, List<Map<String, Object>>> dsMap) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> allSampleInTheProList = workSheetPageInfo.getAllSampleInThePro();
        List<String> jhFldList = Arrays.asList(曲线校核样样品编号, 曲线校核样出证结果, 曲线校核样相对偏差, 标准物质加入量, 曲线校核样采集编号,
                曲线校核样允许相对偏差, 曲线校核样是否合格);
        //创建集合放曲线校核集合
        List<DtoWorkSheetData> jhWorkSheetDataList = allSampleInTheProList.parallelStream().filter(p -> EnumQcGrade.内部质控.getValue().equals(p.getQcGrade()))
                .filter(p -> EnumQcType.曲线校核.getValue().equals(p.getQcType())).collect(Collectors.toList());

        //如果曲线校核样的个数不为空
        if (StringUtils.isNotEmpty(jhWorkSheetDataList)) {
            List<String> pxAnaDataIdList = jhWorkSheetDataList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
            List<DtoQualityControlEvaluate> qualityControlEvaluateList = workSheetDataSourceService.findQualityControlEvaluate(pxAnaDataIdList);
            //按照采集编号排序
            jhWorkSheetDataList.sort(Comparator.comparing(DtoWorkSheetData::getGatherCode));
            //如果曲线校核样存在，赋值到JHCurve集合
            for (int i = 0; i < jhWorkSheetDataList.size(); i += 2) {
                jHCount++;
                Map<String, Object> jhMap = new HashMap<>();
                List<DtoWorkSheetData> pageJhList = jhWorkSheetDataList.stream().skip(i).limit(2).collect(Collectors.toList());
                for (int k = 0; k < pageJhList.size(); k++) {
                    DtoWorkSheetData jhData = pageJhList.get(k);
//                    for (DtoParamsConfig name : workSheetPageInfo.getParamNames()) {
//                        //参数的数据结果
//                        String str = this.getAnalyseValue(name, jhData.getAnalyseDataId(), workSheetPageInfo.getAnalyseOriginalRecordList(),
//                                workSheetPageInfo.getParams2ParamsFormulaList(), workSheetPageInfo.getPartFormulaList(), workSheetPageInfo.getParamsConfigList());
//                        //参数数据绑定
//                        WorkSheetUtils.setMapValue(jhMap, name.getAlias() + k, checkValEmpty(str, blankFillStr));
//                    }
                    DtoQualityControlEvaluate jhEvaluate = qualityControlEvaluateList.stream()
                            .filter(p -> p.getObjectId().equals(jhData.getAnalyseDataId())).findFirst().orElse(null);
                    setJhData(jhMap, String.valueOf(k), jhData, jhEvaluate, workSheetPageInfo);
                }
                if (pageJhList.size() < 2) {
                    jhFldList.forEach(p -> jhMap.put(p + "1", ""));
                    jhMap.put(kbFldName + 1, getKBStr(0));
                    jhMap.put(曲线校核样采集编号 + "1", getKBStr(0));
                }
                dsMap.put(JHCurve.name() + jHCount, Collections.singletonList(jhMap));
            }
        } else {
            jHCount++;
            Map<String, Object> jhMap = new HashMap<>();
            for (String fld : jhFldList) {
                jhMap.put(fld + "0", "");
                jhMap.put(fld + "1", "");
            }
            jhMap.put(kbFldName + 0, getKBStr(0));
            jhMap.put(曲线校核样采集编号 + "0", getKBStr(0));
            dsMap.put(JHCurve.name() + jHCount, Collections.singletonList(jhMap));
        }

        return jHCount;
    }

    /**
     * 设置曲线校核样行数据
     *
     * @param jhMap      报表曲线校核样区数据集
     * @param jhIdx      校核索引
     * @param jhData     校核样信息
     * @param jhEvaluate 质控评价对象
     */
    private void setJhData(Map<String, Object> jhMap, String jhIdx, DtoWorkSheetData jhData, DtoQualityControlEvaluate jhEvaluate, DtoWorkSheetPageInfo workSheetPageInfo) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        jhMap.put(曲线校核样样品编号 + jhIdx, StringUtils.isNotEmpty(jhData.getCode()) ? jhData.getCode() : blankFillStr);
        jhMap.put(曲线校核样出证结果 + jhIdx, StringUtils.isNotEmpty(jhData.getTestValue()) ? jhData.getTestValue() : blankFillStr);
        jhMap.put(曲线校核样相对偏差 + jhIdx, StringUtils.isNotEmpty(jhData.getQcInfo()) ? jhData.getQcInfo().replace("%", "") : blankFillStr);
        jhMap.put(标准物质加入量 + jhIdx, StringUtils.isNotEmpty(jhData.getQcValue()) ? jhData.getQcValue() : blankFillStr);
        jhMap.put(曲线校核样采集编号 + jhIdx, StringUtils.isNotEmpty(jhData.getGatherCode()) ? jhData.getGatherCode() : blankFillStr);
        Boolean isPass = StringUtils.isNotNull(jhEvaluate) ? jhEvaluate.getIsPass() : null;
        boolean countFlag = StringUtils.isNotNull(jhEvaluate) && StringUtils.isNotEmpty(jhEvaluate.getCheckItemValue()) && jhEvaluate.getCheckItemValue().contains("无法计算");
        jhMap.put(曲线校核样允许相对偏差 + jhIdx, StringUtils.isNotNull(jhEvaluate) ? formatYxPc(jhEvaluate.getAllowLimit(), blankFillStr, "≤±", "~") : blankFillStr);
        jhMap.put(曲线校核样是否合格 + jhIdx, countFlag ? "未评价" : formatPass(isPass, blankFillStr));
    }

    /**
     * 设置空白样数据
     */
    private void setKbDataStr(Map<Integer, Object> objectMap) {
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        workSheetPageData.setKbNumber(setOutKb(workSheetPageInfo, 2, workSheetPageData.getKbNumber(), (List<String>) objectMap.get(3),
                (Integer) objectMap.get(5), 1, dsMap));
    }

    /**
     * 获取并设置全程序空白样信息
     *
     * @param workSheetPageInfo 报表信息集
     * @param pageKbLmtCnt      每页空白样的个数
     * @return 空白样页数
     */
    public int setOutKb(DtoWorkSheetPageInfo workSheetPageInfo, int pageKbLmtCnt, int kbCount, List<String> pageTestIds,
                        int pageTstCnt, int pagePerTstKbCnt, Map<String, List<Map<String, Object>>> dsMap) {
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> allSampleInTheProList = workSheetPageInfo.getAllSampleInThePro();
        //创建集合放全程序空白样
        List<DtoWorkSheetData> outKbDataList = allSampleInTheProList.parallelStream()
                .filter(p -> EnumQcGrade.外部质控.getValue().equals(p.getQcGrade()))
                .filter(p -> EnumQcType.空白.getValue().equals(p.getQcType()) || EnumQcType.运输空白.getValue().equals(p.getQcType())).collect(Collectors.toList());
        //全程序空白样过滤掉串联样
        outKbDataList = outKbDataList.stream().filter(p -> !EnumSampleCategory.串联样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
        List<String> anaDataIdList = outKbDataList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = StringUtils.isNotEmpty(anaDataIdList) ? workSheetDataSourceService.findQualityControlEvaluate(anaDataIdList) : new ArrayList<>();
        List<String> outKbFldList = Arrays.asList(全程序空白样编号, 全程序空白样出证结果, 全程序空白样采集编号, 全程序空白样质控限值, 全程序空白样是否合格);
        //如果室内空白样的个数不为空
        if (StringUtils.isNotEmpty(outKbDataList)) {
            outKbDataList.sort(Comparator.comparing(DtoWorkSheetData::getGatherCode));
            for (int i = 0; i < outKbDataList.size(); i += pageKbLmtCnt) {
                Map<String, Object> kbMap = new HashMap<>();
                kbCount++;
                List<DtoWorkSheetData> pageOutKbList = outKbDataList.stream().skip(i).limit(pageKbLmtCnt).collect(Collectors.toList());
                //先赋默认值
                for (int k = 0; k < pageKbLmtCnt; k++) {
                    int finalK = k;
                    outKbFldList.forEach(p -> kbMap.put(p + finalK, ""));
                }
                for (int k = 0; k < pageOutKbList.size(); k++) {
                    DtoWorkSheetData kbData = pageOutKbList.get(k);
                    kbMap.put(全程序空白样编号 + k, StringUtils.isNotEmpty(kbData.getCode()) ? kbData.getCode() : blankFillStr);
                    kbMap.put(全程序空白样采集编号 + k, StringUtils.isNotEmpty(kbData.getGatherCode()) ? kbData.getGatherCode() : blankFillStr);
                    kbMap.put(全程序空白样出证结果 + k, StringUtils.isNotEmpty(kbData.getTestValue()) ? kbData.getTestValue() : blankFillStr);
                    DtoQualityControlEvaluate evaluate = qualityControlEvaluateList.stream()
                            .filter(p -> p.getObjectId().equals(kbData.getAnalyseDataId())).findFirst().orElse(null);
                    String samVal = StringUtils.isNotNull(evaluate) && StringUtils.isNotEmpty(evaluate.getCheckItemValue()) ? evaluate.getCheckItemValue() : blankFillStr;
                    kbMap.put(全程序空白样出证结果 + k, samVal);
                    String isPass = StringUtils.isNotNull(evaluate) && StringUtils.isNotNull(evaluate.getIsPass()) ? evaluate.getIsPass() ? "是" : "否" : blankFillStr;
                    kbMap.put(全程序空白样质控限值 + k, getYxPcForBlank(evaluate.getJudgingMethod(), evaluate.getAllowLimit(), kbData.getExamLimitValue(), kbData.getLowerLimit(), blankFillStr));
                    kbMap.put(全程序空白样是否合格 + k, isPass);
//                        kbMap.put("全程序空白样是否合格" + k, ReportUtils.checkPassForBlank(evaluate, kbData.getExamLimitValue(), kbData.getLowerLimit(), samVal, "/",
//                                null, isPass));
                }
                if (pageOutKbList.size() < pageKbLmtCnt) {
                    kbMap.put(全程序空白样编号 + pageOutKbList.size(), getKBStr(0));
                    kbMap.put(全程序空白样采集编号 + pageOutKbList.size(), getKBStr(0));
                }
                dsMap.put(KBSampleData.name() + kbCount, Collections.singletonList(kbMap));
            }
        } else {
            kbCount++;
            Map<String, Object> kbMap = new HashMap<>();
            for (int k = 0; k < pageKbLmtCnt; k++) {
                int finalK = k;
                outKbFldList.forEach(p -> kbMap.put(p + finalK, ""));
            }
            kbMap.put(全程序空白样编号 + "0", getKBStr(0));
            kbMap.put(全程序空白样采集编号 + "0", getKBStr(0));
            dsMap.put(KBSampleData.name() + kbCount, Collections.singletonList(kbMap));
        }

        return kbCount;
    }

    private static String getYxPcForBlank(Integer judgingMethod, String allowLimit, String examLimitValue, String lowerLimit, String defaultVal) {
        String yxPc = defaultVal;
        if (StringUtils.isNotNull(judgingMethod)) {
            if (EnumJudgingMethod.小于检出限.getValue().equals(judgingMethod)) {
                yxPc = "＜" + examLimitValue;
            } else if (EnumJudgingMethod.小于测定下限.getValue().equals(judgingMethod)) {
                yxPc = "＜" + lowerLimit;
            } else {
                yxPc = formatYxPc(allowLimit, defaultVal, "≤±", "~");
            }
        }
        return yxPc;
    }

    /**
     * 绑定曲线校准数据
     *
     * @param objectMap 参数
     */
    private void setJzDataStr(Map<Integer, Object> objectMap) {

    }

    /**
     * 表头扩展数据赋值
     *
     * @param objectMap 参数
     */
    protected void setAddMsgValue(Map<Integer, Object> objectMap) {
        List<Map<String, Object>> addList = (List<Map<String, Object>>) objectMap.get(0);
        List<DtoWorkSheetData> filterData = (List<DtoWorkSheetData>) objectMap.get(1);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(2);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        DtoProject project = workSheetPageInfo.getProject();
//        List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = workSheetPageInfo.getAnalyseOriginalRecordList();
        List<DtoInstrumentUseRecord> instrumentList = workSheetPageInfo.getUseRecordList();
        DtoWorksheetFolder workSheetFolder = workSheetPageInfo.getWorkSheetFolder();
//        List<DtoReceiveSampleRecord> recordList = workSheetPageInfo.getReceiveSampleRecordList();

        Map<String, Object> addMap = new HashMap<>();
        addMap.put(项目编号, StringUtils.isNotNull(project) &&
                StringUtils.isNotEmpty(project.getProjectCode()) ? project.getProjectCode() : blankFillStr);
        if (filterData.size() > 0) {
            DtoWorkSheetData dto = filterData.get(0);
            DtoWorkSheetData dimensionDto = filterData.stream().filter(p -> StringUtils.isNotEmpty(p.getDimension()) && !p.getIsQC()).findFirst().orElse(null);
            String dimension = dimensionDto != null ? dimensionDto.getDimension() : "";
            List<String> receiveIdsOfPage = filterData.stream().filter(p -> StringUtils.isNotEmpty(p.getReceiveId()) && !UUIDHelper.guidEmpty().equals(p.getReceiveId())).map(DtoWorkSheetData::getReceiveId).distinct().collect(Collectors.toList());
            DtoWorkSheetData dataWithReceiveId = filterData.stream().filter(p -> StringUtils.isNotEmpty(p.getReceiveId()) && !UUIDHelper.guidEmpty().equals(p.getReceiveId())).findFirst().orElse(null);
            String receiveId = dataWithReceiveId != null ? dataWithReceiveId.getReceiveId() : UUIDHelper.guidEmpty();
            addMap.put(样品类型, StringUtils.isNotEmpty(dto.getSampleTypeName()) ? dto.getSampleTypeName() : blankFillStr);
            addMap.put(分析项目, StringUtils.isNotEmpty(dto.getRedAnalyzeItems()) ? dto.getRedAnalyzeItems() : blankFillStr);
            addMap.put(检出限, StringUtils.isNotEmpty(dto.getExamLimitValue()) ? dto.getExamLimitValue() : blankFillStr);
            addMap.put(检出限带量纲, StringUtils.isNotEmpty(dto.getExamLimitValue()) ? dto.getExamLimitValue() + dimension : blankFillStr);
            addMap.put(采样日期, StringUtils.isNotEmpty(dto.getSamplingTimeBeginShort()) ? dto.getSamplingTimeBeginShort() : blankFillStr);
//            addMap.put(分析日期, StringUtils.isNotEmpty(dto.getAnalyzeTimeShort()) ? dto.getAnalyzeTimeShort() : "");
            addMap.put(分析日期, StringUtils.isNotNull(workSheetFolder.getFinishTime())
                    ? DateUtil.dateToString(workSheetFolder.getFinishTime(), DateUtil.YEAR_ZH_CN) : blankFillStr);
//            DtoReceiveSampleRecord record = recordList.stream().filter(p -> p.getId().equals(receiveId)).findFirst().orElse(null);
//            String receiveDate = StringUtils.isNotNull(record) ? (StringUtils.isNotNull(record.getReceiveSampleDate())
//                    ? DateUtil.dateToString(record.getReceiveSampleDate(), DateUtil.YEAR_ZH_CN) : "") : "";
//            receiveDate = (StringUtils.isNotEmpty(receiveDate) && !receiveDate.contains("1753")) ? receiveDate : blankFillStr;
            addMap.put(接样日期, "");
//            List<DtoReceiveSampleRecord> records = recordList.stream().filter(p -> receiveIdsOfPage.contains(p.getId())).collect(Collectors.toList());
            //所有的接样日期
//            List<String> receiveDateList = records.stream().filter(p -> StringUtils.isNotNull(p.getReceiveSampleDate()))
//                    .map(p -> DateUtil.dateToString(p.getReceiveSampleDate(), DateUtil.YEAR)).distinct().collect(Collectors.toList());
//            receiveDateList = receiveDateList.stream().filter(p -> StringUtils.isNotEmpty(p) && !p.contains("1753")).collect(Collectors.toList());
//            Collections.sort(receiveDateList);
//            if (StringUtils.isNotEmpty(receiveDateList)) {
//                addMap.put(接样日期, checkValEmpty(String.join(",", receiveDateList), blankFillStr));
//            }
//            DtoTest test = testService.findOne(dto.getTestId());
//            addMap.put(分析方法编号年度, StringUtils.isNotNull(test) ? test.getRedAnalyzeMethodName() + " " + test.getRedCountryStandard() : blankFillStr);

//            DtoAnalyseOriginalRecord analyseOriginalRecord = analyseOriginalRecordList.stream()
//                    .filter(p -> dto.getAnalyseDataId().equals(p.getAnalyseDataId())).findFirst().orElse(null);
//            assert analyseOriginalRecord != null;
//            addMap.put(测试项目公式, StringUtils.isNotNull(analyseOriginalRecord) ?
//                    StringUtils.isNotEmpty(analyseOriginalRecord.getTestFormula()) ? analyseOriginalRecord.getTestFormula() : blankFillStr : blankFillStr);
            addMap.put(检出限, StringUtils.isNotEmpty(dto.getExamLimitValue()) ? dto.getExamLimitValue() : blankFillStr);
            String workSheetFolderId = dto.getWorkSheetFolderId();
            List<DtoWorksheetReagent> workSheetReagents = workSheetDataSourceService.findWorkSheetReagent(workSheetFolderId, EnumReagentType.标准溶液.getValue());
            addMap.put(试剂配置, StringUtils.isNotEmpty(workSheetReagents) ?
                    StringUtils.isNotEmpty(workSheetReagents.get(0).getReagent()) ? workSheetReagents.get(0).getReagent() : blankFillStr : blankFillStr);
            addMap.put(溶液名称及浓度, StringUtils.isNotEmpty(workSheetReagents) ?
                    StringUtils.isNotEmpty(workSheetReagents.get(0).getReagentName()) ? workSheetReagents.get(0).getReagentName() : blankFillStr : blankFillStr);
            addMap.put(标液名称, StringUtils.isNotEmpty(workSheetReagents) ?
                    StringUtils.isNotEmpty(workSheetReagents.get(0).getReagentName()) ? workSheetReagents.get(0).getReagentName() : blankFillStr : blankFillStr);
            addMap.put(溶液配制日期, StringUtils.isNotEmpty(workSheetReagents) ?
                    StringUtils.isNotEmpty(workSheetReagents.get(0).getConfigDate().toString()) ? workSheetReagents.get(0).getConfigDate() : blankFillStr : blankFillStr);
            List<String> instrumentNameList = instrumentList.stream().
                    map(p -> p.getInstrumentName() + p.getInstrumentCode()).distinct().collect(Collectors.toList());
            addMap.put(仪器名称及编号, checkValEmpty(String.join("、", instrumentNameList), blankFillStr));
        }
        addList.add(addMap);
    }


    /**
     * 绑定标准数据
     *
     * @param objectMap 参数
     */
    protected void setBzDataStr(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        List<String> pageTestIds = (List<String>) objectMap.get(3);
//        List<DtoTest> pageTestList = (List<DtoTest>) objectMap.get(4);
        int qCCnt = (Integer) objectMap.get(5);
        int bzCount = workSheetPageData.getBzNumber();
        //endregion
        List<DtoWorkSheetData> bzSampleList = workSheetPageInfo.getAllSampleInThePro().stream().filter(p -> //(idInTheList.size() > 0 ? idInTheList.contains(p.getAssociateSampleId()) : true) &&
                p.getIsQC() && EnumQcGrade.内部质控.getValue().equals(p.getQcGrade())
                        && (EnumQcType.标准.getValue()).equals(p.getQcType())).collect(Collectors.toList());
//        setBzSampleInd(objectMap, bzSampleList);
        if (bzSampleList.size() > 0) {
            List<String> testIdList = bzSampleList.stream().map(DtoWorkSheetData::getTestId).distinct().collect(Collectors.toList());
            //获取当前测试项目的质控限值配置
//            List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIdList);
            List<String> bzAnaDataIdList = bzSampleList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
            List<DtoQualityControlEvaluate> qualityControlEvaluateList = workSheetDataSourceService.findQualityControlEvaluate(bzAnaDataIdList);
            bzCount = (Integer) getData(EnumObtainDataType.标样.getValue(), dsMap, workSheetPageInfo, bzSampleList, bzCount, pageTestIds,
                    new ArrayList<>(), qCCnt, new ArrayList<>(), qualityControlEvaluateList);
        } else {
            bzCount++;
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(标样编号, getKBStr(0));
            mapValue.put(样品编号, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(标样测定值, getKBStr(2));
            mapValue.put(标样标准值, getKBStr(2));
            mapValue.put(标样不确定度, getKBStr(2));
            mapValue.put(相对误差, getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(分析项目, getKBStr(2));
            mapValue.put(标样配置日期, getKBStr(2));
            mapValue.put(标样有效期, getKBStr(2));
            mapValue.put("允许相对误差", getKBStr(2));
//            setBzDefaultValInd(mapValue);
            setDataValue(EnumDefaultDataType.质控.getValue(), Standard.name(), dsMap, mapValue, bzCount);
        }
        workSheetPageData.setBzNumber(bzCount);
    }

    /**
     * 绑定平行数据
     *
     * @param objectMap 参数
     */
    private void setPxDataStr(Map<Integer, Object> objectMap) {
        //region 参数处理
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        DtoWorkSheetPageData workSheetPageData = (DtoWorkSheetPageData) objectMap.get(2);
        List<DtoWorkSheetData> list = (List<DtoWorkSheetData>) objectMap.get(3);
        List<String> idInTheList = (List<String>) objectMap.get(4);
        List<String> pageTestIds = (List<String>) objectMap.get(5);
        List<String> pageTestList = (List<String>) objectMap.get(6);
        int qcCnt = (Integer) objectMap.get(7);
        int pxCount = workSheetPageData.getPxNumber();
        //endregion
        List<DtoWorkSheetData> pxSampleList = workSheetPageInfo.getAllSampleInThePro().stream().filter(p -> (idInTheList.size() > 0 ? idInTheList.contains(p.getAssociateSampleId()) : true)
                && p.getIsQC() && EnumQcGrade.内部质控.getValue().equals(p.getQcGrade())
                && (EnumQcType.平行.getValue()).equals(p.getQcType())).collect(Collectors.toList());
        if ("1".equals(workSheetPageInfo.getPara().getIsShowXCPX())) {
            pxSampleList.addAll(workSheetPageInfo.getAllSampleInThePro().stream().filter(p -> (idInTheList.size() > 0 ? idInTheList.contains(p.getAssociateSampleId()) : true)
                    && p.getIsQC() && EnumQcGrade.外部质控.getValue().equals(p.getQcGrade())
                    && (EnumQcType.平行.getValue()).equals(p.getQcType())).collect(Collectors.toList()));
        }
        //过滤掉串联样的平行样（原样存在串联样时，只需展示原样的平行样即可）
//        pxSampleList = filterPxSampleForCl(pxSampleList, workSheetPageInfo.getAllSampleInThePro());
        if (pxSampleList.size() > 0) {
            List<String> pxAnaDataIdList = pxSampleList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
//            List<DtoQualityControlEvaluate> qualityControlEvaluateList = qualityControlEvaluateRepository.findByObjectIdIn(pxAnaDataIdList);
            pxCount = (Integer) getData(EnumObtainDataType.平行.getValue(), dsMap, workSheetPageInfo, pxSampleList, list,
                    pxCount, pageTestIds, pageTestList, qcCnt, new ArrayList<>());
        } else {
            pxCount++;
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put(平行编号, getKBStr(0));
            mapValue.put(平行样, getKBStr(0));
            mapValue.put(采集编号, getKBStr(0));
            mapValue.put(平行样浓度, getKBStr(2));
            mapValue.put(原样, getKBStr(2));
            mapValue.put(原样浓度, getKBStr(2));
            mapValue.put(偏差类型, getKBStr(2));
            mapValue.put(偏差, getKBStr(2));
            mapValue.put(合格, getKBStr(2));
            mapValue.put(是否合格, getKBStr(2));
            mapValue.put(允许平行偏差, getKBStr(2));
            mapValue.put(平均, getKBStr(2));
            mapValue.put(分析项目, getKBStr(2));
            mapValue.put(原样与平行样, getKBStr(0));
            mapValue.put(原样编号和平行样编号, getKBStr(0));
            mapValue.put(原样检查结果修约, getKBStr(2));
            mapValue.put(平行样检测结果修约, getKBStr(2));
            mapValue.put(出证结果, getKBStr(2));
//            setPxDefaultValInd(mapValue);
            setDataValue(EnumDefaultDataType.质控.getValue(), Parallel.name(), dsMap, mapValue, pxCount);
        }
        workSheetPageData.setPxNumber(pxCount);
    }

    /**
     * 质控样默认值赋值
     *
     * @param objectMap 参数
     */
    private void setCurveDefultData(Map<Integer, Object> objectMap) {
        List<Map<String, Object>> curveList = (List<Map<String, Object>>) objectMap.get(0);
        Map<String, String> mapValue = (Map<String, String>) objectMap.get(1);
        Map<String, Object> dr = new HashMap<>();
        for (String key : mapValue.keySet()) {
            setMapValue(dr, key, mapValue.get(key));
        }
        curveList.add(dr);
    }

    /**
     * 质控样默认值赋值
     *
     * @param objectMap 参数
     */
    private void setDefultData(Map<Integer, Object> objectMap) {
        String typeName = (String) objectMap.get(0);
        Map<String, List<Map<String, Object>>> dsMap = (Map<String, List<Map<String, Object>>>) objectMap.get(1);
        Map<String, String> mapValue = (Map<String, String>) objectMap.get(2);
        Integer pageCount = (Integer) objectMap.get(3);
        List<Map<String, Object>> dt = new ArrayList<>();
        Map<String, Object> dr = new HashMap<>();
        for (String key : mapValue.keySet()) {
            setMapValue(dr, key, mapValue.get(key));
        }
        dt.add(dr);
        dsMap.put(typeName + pageCount.toString(), dt);
    }

    /**
     * 表头数据赋值
     *
     * @param objectMap 参数
     */
    private void setHeaderValue(Map<Integer, Object> objectMap) {
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) objectMap.get(0);
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(1);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> sampleInThePro = workSheetPageInfo.getSampleInThePro();
        DtoProject project = workSheetPageInfo.getProject();
        String testId = (String) objectMap.get(2);
        List<DtoWorkSheetData> filterData = (List<DtoWorkSheetData>) objectMap.get(3);
        Map<String, Object> data = new HashMap<>();
        setMapValue(data, 项目编号, blankFillStr);
        setMapValue(data, 项目名称, blankFillStr);
        if (null != project) {
            setMapValue(data, 项目编号, StringUtils.isNotEmpty(project.getProjectCode()) ? project.getProjectCode() : blankFillStr);
            setMapValue(data, 项目名称, StringUtils.isNotEmpty(project.getProjectName()) ? project.getProjectName() : blankFillStr);
        }
        data.put(点位名称, StringUtils.isNotEmpty(sampleInThePro) ? sampleInThePro.get(0).getRedFolderName() : blankFillStr);
        DtoWorkSheetData dataForPerson = StringUtils.isNotEmpty(sampleInThePro) ? sampleInThePro.stream().filter(p -> StringUtils.isNotEmpty(p.getSamplingPersonName()))
                .findFirst().orElse(null) : null;
        data.put(采样人, dataForPerson != null ? dataForPerson.getSamplingPersonName() : blankFillStr);
//        List<DtoWorkSheetData> analyseDataList = sampleInThePro.stream().filter(p -> testId.equals(p.getTestId())).collect(Collectors.toList());
        DtoWorkSheetData analyseData = StringUtils.isNotEmpty(filterData) ? filterData.get(0) : null;
        DtoWorkSheetData dimensionData = filterData.stream().filter(p -> StringUtils.isNotEmpty(p.getDimension()) && !p.getIsQC()).findFirst().orElse(null);
        String dimension = dimensionData != null ? dimensionData.getDimension() : "";
        data.put("testId", testId);
        setMapValue(data, 分析项目, blankFillStr);
        data.put(检出限, blankFillStr);
        data.put(检出限带量纲, blankFillStr);
        if (null != analyseData) {
            setMapValue(data, 分析项目, StringUtils.isNotEmpty(analyseData.getRedAnalyzeItems()) ? analyseData.getRedAnalyzeItems() : blankFillStr);
            data.put(检出限, StringUtils.isNotEmpty(analyseData.getExamLimitValue()) ? analyseData.getExamLimitValue() : blankFillStr);
            data.put(检出限带量纲, StringUtils.isNotEmpty(analyseData.getExamLimitValue()) ? analyseData.getExamLimitValue() + dimension : blankFillStr);
        }

        //设置样品类型，获取当前页的样品对应的样品类型
        List<String> sampleTypeNames = sampleInThePro.stream().filter(p -> !p.getIsQC()).map(DtoWorkSheetData::getSampleTypeName)
                .distinct().collect(Collectors.toList());
        data.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ?
                String.join("、", sampleTypeNames) : blankFillStr);
        //如果工作单中没有原样，则取质控样上的样品类型
        if (sampleTypeNames.size() == 0) {
            sampleTypeNames = sampleInThePro.stream().map(DtoWorkSheetData::getSampleTypeName).distinct().collect(Collectors.toList());
            data.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ? String.join("、", sampleTypeNames) : blankFillStr);
        }
//        List<String> receiveIdList = filterData.stream().map(DtoWorkSheetData::getReceiveId).distinct().collect(Collectors.toList());
//        List<DtoReceiveSampleRecord> recordList = workSheetPageInfo.getReceiveSampleRecordList().stream().filter(p -> receiveIdList.contains(p.getId())).collect(Collectors.toList());
//        List<String> receiveDateList = recordList.stream().filter(p -> StringUtils.isNotNull(p.getReceiveSampleDate()))
//                .map(p -> DateUtil.dateToString(p.getReceiveSampleDate(), DateUtil.YEAR)).distinct().collect(Collectors.toList());
//        receiveDateList = receiveDateList.stream().filter(p -> !p.contains("1753")).collect(Collectors.toList());
        data.put(接样日期, blankFillStr);
        dataList.add(data);
//        setHeaderValueInd(objectMap);
    }

    /**
     * 处理分页数据
     *
     * @param objectMap 参数
     */
    private List<Map<String, Object>> dealPageData(Map<Integer, Object> objectMap) {
        //region 参数处理
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(0);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        List<DtoWorkSheetData> filterData = (List<DtoWorkSheetData>) objectMap.get(1);
        List<String> fltAnaDataIdList = filterData.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList());
        List<DtoParamsData> paramsDataForFltData = workSheetPageInfo.getParamsDataList().stream()
                .filter(p -> fltAnaDataIdList.contains(p.getObjectId())).collect(Collectors.toList());
        Map<String, List<DtoParamsData>> paramsDataMapForFltData = paramsDataForFltData.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId));
        int pageIndex = (Integer) objectMap.get(2);
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < filterData.size(); i++) {
            //数据获取参数
            Map<String, Object> workDataMap = setWorkSheetParamsValue(filterData, workSheetPageInfo, i, objectMap);
            List<DtoParamsData> loopParamsDataList = paramsDataMapForFltData.getOrDefault(filterData.get(i).getAnalyseDataId(), new ArrayList<>());
            for (DtoParamsData paramsData : loopParamsDataList) {
                String paramsVal = StringUtils.isNotEmpty(paramsData.getParamsConfigName()) ? paramsData.getParamsConfigName() : blankFillStr;
                workDataMap.put(paramsData.getParamsConfigName(), paramsVal);
            }
            loopParamsDataList.forEach(p -> workDataMap.put(p.getParamsConfigName(), p.getParamsValue()));
            //绑定序号
            setMapValue(workDataMap, 序号, pageIndex + i + 1);
            //出证结果判断绑定（包括平行样）
            setTestValue(workDataMap, filterData.get(i), workSheetPageInfo.getWorkSheetDataList(), -1, objectMap);
            //数据绑定
            setWorkDataValue(workDataMap, filterData.get(i), -1, objectMap);
            dataList.add(workDataMap);
        }
        return dataList;
    }

    /**
     * 设置参数值
     *
     * @param filterData        行数据列表
     * @param workSheetPageInfo 记录单信息集合
     * @param i                 索引
     * @param objectMap         报表数据源集合
     * @return 行数据映射
     */
    protected Map<String, Object> setWorkSheetParamsValue(List<DtoWorkSheetData> filterData, DtoWorkSheetPageInfo workSheetPageInfo,
                                                          int i, Map<Integer, Object> objectMap) {
        Map<String, Object> map = new HashMap<>();
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        DtoWorkSheetData workSheetData = filterData.get(i);
        List<DtoParamsData> loopParamsData = workSheetPageInfo.getParamsDataList().stream().filter(p -> p.getObjectId().equals(workSheetData.getAnalyseDataId())).collect(Collectors.toList());
        for (DtoParamsData param : loopParamsData) {
            if (!map.containsKey(param.getParamsConfigName())) {
                String strValue = param.getParamsValue();
                if ("null".equals(strValue)) {
                    strValue = blankFillStr;
                }
                map.put(param.getParamsConfigName(), checkValEmpty(strValue, blankFillStr));
            }
        }
        return map;
    }

    /**
     * 数据赋值
     *
     * @param workDataList 数据
     * @param data         赋值内容
     * @param objectMap    参数
     */
    protected void setWorkDataValue(Map<String, Object> workDataList, DtoWorkSheetData data, Integer num, Map<Integer, Object> objectMap) {
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(0);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        if (StringUtils.isNotNull(data)) {
//            String dim = getQcDim(workSheetPageInfo, data);
            String dim = data.getDimension();
            setMapValue(workDataList, 分析项目 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRedAnalyzeItems()) ? data.getRedAnalyzeItems() : blankFillStr);
            setMapValue(workDataList, 分析方法 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getAnalyzeMethodName()) ? data.getAnalyzeMethodName() : blankFillStr);
            setMapValue(workDataList, 样品编号 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getCode()) ? data.getCode() : getKBStr(2));
            setMapValue(workDataList, 样品类型 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getSampleTypeName()) ? data.getSampleTypeName() : blankFillStr);
            setMapValue(workDataList, 采样地点 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRedFolderName()) ? data.getRedFolderName() : blankFillStr);
            setMapValue(workDataList, 采样点位 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRedFolderName()) ? data.getRedFolderName() : blankFillStr);
            setMapValue(workDataList, 点位名称 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRedFolderName()) ? data.getRedFolderName() : blankFillStr);
            setMapValue(workDataList, 加标体积 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getQcVolume()) ? data.getQcVolume() : blankFillStr);
            setMapValue(workDataList, 加入标准量 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getQcValue()) ? data.getQcValue() : blankFillStr);
            setMapValue(workDataList, 加标样品测定值 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getQcTestValue()) ? data.getQcTestValue() : blankFillStr);
            setMapValue(workDataList, 原样品测定值 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRealSampleTestValue()) ? data.getRealSampleTestValue() : blankFillStr);
            setMapValue(workDataList, 回收率 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getTestValue()) ? data.getTestValue() : blankFillStr);
            setMapValue(workDataList, 出证结果 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getTestValue()) ? data.getTestValue() + dim : blankFillStr);
            setMapValue(workDataList, 检测结果 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getTestOrignValue()) ? data.getTestOrignValue() : blankFillStr);
            setMapValue(workDataList, 检测结果修约 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getTestValueDstr()) ? data.getTestValueDstr() : blankFillStr);
            setMapValue(workDataList, 检出限 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getExamLimitValue()) ? data.getExamLimitValue() : blankFillStr);
            String str = "ND";
            setMapValue(workDataList, 样品浓度 + (num > -1 ? num.toString() : getKBStr(2)), str.equals(data.getTestValue()) ? data.getExamLimitTestValue() : data.getTestValue());
            setMapValue(workDataList, 分析日期 + (num > -1 ? num.toString() : getKBStr(2)), data.getAnalyzeTimeShort());
            setMapValue(workDataList, 采样日期 + (num > -1 ? num.toString() : getKBStr(2)), data.getSamplingTimeBeginShort());
            setMapValue(workDataList, 备注 + (num > -1 ? num.toString() : getKBStr(2)), StringUtils.isNotEmpty(data.getRemark()) ? data.getRemark() : blankFillStr);
            setMapValue(workDataList, 采集编号 + (num > -1 ? num.toString() : getKBStr(2)), data.getGatherCode());
            setMapValue(workDataList, 质控信息 + (num > -1 ? num.toString() : getKBStr(2)),
                    StringUtils.isNotEmpty(data.getQcInfo()) ? data.getQcInfo() : blankFillStr);
        }
//        setWorkDataValueInd(workDataList, data, num, objectMap);
    }

    /**
     * 平行出证结果的绑定
     *
     * @param workDataList      数据
     * @param data              赋值内容
     * @param workSheetDataList 数据集
     */
    protected void setTestValue(Map<String, Object> workDataList, DtoWorkSheetData data, List<DtoWorkSheetData> workSheetDataList, int i, Map<Integer, Object> objectMap) {
        DtoWorkSheetPageInfo workSheetPageInfo = (DtoWorkSheetPageInfo) objectMap.get(0);
        String blankFillStr = workSheetPageInfo.getPara().getBlankFillStr();
        if (StringUtils.isNull(data)) {
            setMapValue(workDataList, 检测结果及出证带平行 + (i == -1 ? "" : i), blankFillStr);
            setMapValue(workDataList, 检测结果及出证带平行均值 + (i == -1 ? "" : i), blankFillStr);
            setMapValue(workDataList, 检出限 + (i == -1 ? "" : i), blankFillStr);
            return;
        }
        //判断是否为平行样
        if (!UUIDHelper.guidEmpty().equals(data.getAssociateSampleId())
                && EnumQcGrade.内部质控.getValue().equals(data.getQcGrade())
                && (EnumQcType.平行.getValue().equals(data.getQcType()))) {
            setMapValue(workDataList, 检测结果及出证带平行 + (i == -1 ? "" : i), data.getTestValueDstr() + "/" + data.getTestValue());
//            setMapValue(workDataList, 检测结果及出证带平行均值 + (i == -1 ? "" : i), data.getTestValueDstr() + "（均值：" + data.getTestValue() + "）");
            setMapValue(workDataList, 检测结果及出证带平行均值 + (i == -1 ? "" : i), data.getTestValueDstr());
//            setTestValueInd(workDataList, data, workSheetDataList, i, objectMap);
        } else {
            //判断原样是否存在平行
            List<DtoWorkSheetData> pxSample = workSheetDataList.stream().filter(p -> p.getAssociateSampleId().equals(data.getSampleId())
                    && p.getTestId().equals(data.getTestId())
                    && EnumQcGrade.内部质控.getValue().equals(p.getQcGrade())
                    && (EnumQcType.平行.getValue()).equals(p.getQcType())).collect(Collectors.toList());
            if (pxSample.size() > 0) {
                setMapValue(workDataList, 检测结果及出证带平行 + (i == -1 ? "" : i), data.getTestValueDstr() + "/" + data.getTestValue());
                setMapValue(workDataList, 检测结果及出证带平行均值 + (i == -1 ? "" : i), data.getTestValueDstr() + "\n" + "（均值：" + data.getTestValue() + "）");
//                setTestValueInd(workDataList, data, workSheetDataList, i, objectMap);
            } else {
//                String dim = getQcDim(workSheetPageInfo, data);
                setMapValue(workDataList, 检测结果及出证带平行 + (i == -1 ? "" : i), data.getTestValue() + data.getDimension());
                setMapValue(workDataList, 检测结果及出证带平行均值 + (i == -1 ? "" : i), data.getTestValue() + data.getDimension());
            }
        }
    }

    /**
     * 基础数据赋值
     *
     * @param dsMap                数据集合
     * @param workSheetDataList    检测单集合
     * @param paramsDataList       参数集合
     * @param useRecordList        仪器使用寄了集合
     * @param workSheetReagentList 试剂配置记录集合
     * @param workSheetFolder      检测单对象
     */
    protected void writeBasicInfo(Map<String, List<Map<String, Object>>> dsMap,
                                  List<DtoSample> sampleList,
                                  List<DtoWorkSheetData> workSheetDataList,
                                  List<DtoParamsData> paramsDataList,
                                  List<DtoInstrumentUseRecord> useRecordList,
                                  List<DtoWorksheetReagent> workSheetReagentList,
                                  List<DtoCurveDetail> curveDetailList, DtoWorksheetFolder workSheetFolder,
                                  List<DtoCurve> curveList, FrontParamVO frontParamVO, List<DtoProject> projectList) {
        String blankFillStr = "";
        String infoName = "Info1";
        List<Map<String, Object>> baseInfo = new ArrayList<>();
        //初始化默认值
        dealBaseInfoDefaultValue(baseInfo, blankFillStr);
        dsMap.put(infoName, baseInfo);

        //写入检测单数据
        writeWorkSheetInfo(dsMap, workSheetDataList, workSheetFolder, workSheetReagentList, blankFillStr);

        //写入样品、项目或者此采样日期相关数据
        writeSampleRecordInfo(dsMap, sampleList, workSheetDataList, projectList, blankFillStr);

        //写入参数数据
        writeParamsData(dsMap, paramsDataList, blankFillStr);

        //写入个性化数据
        writeIndividualInfo(dsMap, workSheetDataList, blankFillStr);

        //写入仪器信息
        writeInstrumentInfo(dsMap, useRecordList, blankFillStr);

        //写入标准曲线信息
        writeCurveInfo(dsMap, curveList, curveDetailList, blankFillStr);
    }

    /**
     * 基础数据（送样信息）赋值
     *
     * @param dsMap             数据集合
     * @param workSheetDataList 分析数据集合
     */
    protected void writeSampleRecordInfo(Map<String, List<Map<String, Object>>> dsMap, List<DtoSample> sampleList, List<DtoWorkSheetData> workSheetDataList,
                                         List<DtoProject> projectList, String blankFillStr) {
        List<Map<String, Object>> basicInfo = dsMap.get("Info1");
        Map<String, Object> basic = basicInfo.get(0);
//        List<String> receiveIds = workSheetDataList.stream().filter(p -> !p.getIsQC()).map(DtoWorkSheetData::getReceiveId)
//                .distinct().collect(Collectors.toList());
        List<String> sampleTypeNames = workSheetDataList.stream().filter(p -> !p.getIsQC()).map(DtoWorkSheetData::getSampleTypeName)
                .distinct().collect(Collectors.toList());
        basic.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ?
                String.join("、", sampleTypeNames) : blankFillStr);
        //如果工作单中没有原样，则取质控样上的样品类型
        if (sampleTypeNames.size() == 0) {
            sampleTypeNames = workSheetDataList.stream().map(DtoWorkSheetData::getSampleTypeName).distinct().collect(Collectors.toList());
            basic.put(样品类型, StringUtils.isNotEmpty(sampleTypeNames) ? String.join("、", sampleTypeNames) : blankFillStr);
//            receiveIds = workSheetDataList.stream().map(DtoWorkSheetData::getReceiveId)
//                    .filter(receiveId -> !receiveId.equals(UUIDHelper.guidEmpty()))
//                    .distinct().collect(Collectors.toList());
        }
        basic.put(采样日期, StringUtils.isNotEmpty(sampleList) ? checkValEmpty(DateUtil.dateToString(sampleList.get(0).getSamplingTime(),
                DateUtil.YEAR_ZH_CN), blankFillStr) : blankFillStr);
        basic.put(送样日期, StringUtils.isNotEmpty(sampleList) ? checkValEmpty(DateUtil.dateToString(sampleList.get(0).getReceiveSampleDate(),
                DateUtil.YEAR_ZH_CN), blankFillStr) : blankFillStr);
        basic.put(接样日期, blankFillStr);
        if (StringUtils.isNotEmpty(projectList)) {
            DtoProject project = projectList.get(0);
            basic.put(项目编号, checkValEmpty(project.getProjectCode(), blankFillStr));
            basic.put(项目名称, checkValEmpty(project.getProjectName(), blankFillStr));
            basic.put(委托单位, checkValEmpty(project.getCustomerName(), blankFillStr));
            basic.put(受检单位, checkValEmpty(project.getInspectedEnt(), blankFillStr));
        }

        basic.put(领样日期, blankFillStr);
    }

    /**
     * 基础数据（检测单参数）赋值
     *
     * @param dsMap          数据集合
     * @param paramsDataList 检测单参数集合
     * @param blankFillStr   默认值
     */
    private void writeParamsData(Map<String, List<Map<String, Object>>> dsMap,
                                 List<DtoParamsData> paramsDataList, String blankFillStr) {
        List<Map<String, Object>> basicInfo = dsMap.get("Info1");
        Map<String, Object> basic = basicInfo.get(0);
        Map<String, Object> paramsBasicMap = new HashMap<>();
        for (DtoParamsData paramsData : paramsDataList) {
            if (!paramsBasicMap.containsKey(paramsData.getParamsConfigName()) || "/".equals(paramsBasicMap.get(paramsData.getParamsConfigName()))) {
                String paramsVal = StringUtils.isNotEmpty(paramsData.getParamsValue()) ? paramsData.getParamsValue() : blankFillStr;
//                if (StringUtils.isNotNull(paramsData.getDefaultControl()) && paramsData.getDefaultControl() == 6 && paramsVal.contains(";")) {
//                    //多选框控件的参数值去掉";"
//                    paramsVal = paramsVal.substring(1);
//                    if (paramsVal.contains(";")) {
//                        List<String> paramValStrList = Arrays.asList(paramsVal.split(";"));
//                        paramsVal = String.join("，", paramValStrList);
//                    }
//                }
                paramsBasicMap.put(paramsData.getParamsConfigName(), checkValEmpty(paramsVal, blankFillStr));
            }
        }
        basic.putAll(paramsBasicMap);
    }

    /**
     * 基本信息（个性化）
     *
     * @param dsMap             数据集合
     * @param workSheetDataList 记录单数据集
     * @param blankFillStr      默认值
     */
    private void writeIndividualInfo(Map<String, List<Map<String, Object>>> dsMap,
                                     List<DtoWorkSheetData> workSheetDataList, String blankFillStr) {
        Map<String, Object> infoMap = dsMap.get("Info1").get(0);
        DtoWorkSheetData data = workSheetDataList.stream().filter(p -> StringUtils.isNotEmpty(p.getQcValidDate())
                && !p.getQcValidDate().contains("1753")).findFirst().orElse(null);
        String qcValidDate = StringUtils.isNotNull(data) ? data.getQcValidDate() : "";
        if (infoMap.containsKey("标准溶液厂家及批号")) {
            String stdInfo = infoMap.getOrDefault("标准溶液厂家及批号", "").toString();
            infoMap.put("标准溶液厂家及批号带有效期", checkValEmpty(stdInfo + " " + qcValidDate, blankFillStr));
        }
        if (infoMap.containsKey("厂家及批号")) {
            String stdInfo = infoMap.getOrDefault("厂家及批号", "").toString();
            infoMap.put("厂家及批号带有效期", checkValEmpty(stdInfo + " " + qcValidDate, blankFillStr));
        }
    }

    /**
     * 基本信息（仪器信息）
     *
     * @param dsMap         数据集合
     * @param useRecordList 仪器使用记录集合
     * @param blankFillStr  默认值
     */
    private void writeInstrumentInfo(Map<String, List<Map<String, Object>>> dsMap, List<DtoInstrumentUseRecord> useRecordList, String blankFillStr) {
        List<Map<String, Object>> basicInfo = dsMap.get("Info1");
        Map<String, Object> basic = basicInfo.get(0);
        List<String> str = useRecordList.stream().
                map(p -> p.getInstrumentModel() + p.getInstrumentName() + " " + p.getInstrumentCode()).distinct().collect(Collectors.toList());
        basic.put(仪器型号名称及编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentName).distinct().collect(Collectors.toList());
        basic.put(仪器名称, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentModel() + "（" + p.getInstrumentCode() + "）").distinct().collect(Collectors.toList());
        basic.put(仪器型号及编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentModel() + " " + p.getInstrumentName()).distinct().collect(Collectors.toList());
        basic.put(仪器型号及名称, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentName() + " " + p.getInstrumentCode()).distinct().collect(Collectors.toList());
        basic.put(仪器名称及编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentName() + p.getInstrumentModel()).distinct().collect(Collectors.toList());
        basic.put(仪器名称型号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentModel).distinct().collect(Collectors.toList());
        basic.put(仪器型号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentCode).distinct().collect(Collectors.toList());
        basic.put(仪器编号, checkValEmpty(String.join("、", str), blankFillStr));

        str = useRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentName).distinct().collect(Collectors.toList());
        basic.put("仪器名称", checkValEmpty(String.join("、", str), blankFillStr));

        str = useRecordList.stream().map(DtoInstrumentUseRecord::getTemperature).distinct().collect(Collectors.toList());
        basic.put(室温, checkValEmpty(String.join("、", str), blankFillStr));
        basic.put(温度, checkValEmpty(String.join("、", str), blankFillStr));
        String wsdStr = String.join("、", str);
        str = useRecordList.stream().map(DtoInstrumentUseRecord::getHumidity).distinct().collect(Collectors.toList());
        basic.put(湿度, checkValEmpty(String.join("、", str), blankFillStr));
        if (StringUtils.isNotNullAndEmpty(wsdStr)) {
            wsdStr = wsdStr + "/" + String.join("、", str);
        } else {
            wsdStr = String.join("、", str);
        }
        basic.put(温湿度, checkValEmpty(wsdStr, blankFillStr));
        str = useRecordList.stream().
                map(DtoInstrumentUseRecord::getPressure).distinct().collect(Collectors.toList());
        basic.put(大气压, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentName() + p.getInstrumentSerialNo()).distinct().collect(Collectors.toList());
        basic.put(仪器名称及出厂编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentModel() + p.getInstrumentSerialNo()).distinct().collect(Collectors.toList());
        basic.put(仪器型号及出厂编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(p -> p.getInstrumentModel() + p.getInstrumentName() + " " + p.getInstrumentSerialNo()).distinct().collect(Collectors.toList());
        basic.put(仪器型号名称及出厂编号, checkValEmpty(String.join("、", str), blankFillStr));
        str = useRecordList.stream().
                map(DtoInstrumentUseRecord::getInstrumentSerialNo).distinct().collect(Collectors.toList());
        basic.put(仪器出厂编号, checkValEmpty(String.join("、", str), blankFillStr));
        basic.put(仪器溯源有效期, "");
        basic.put(仪器溯源日期, "");
        List<Date> startTimes = useRecordList.stream().map(DtoInstrumentUseRecord::getStartTime).collect(Collectors.toList());
        List<Date> endTimes = useRecordList.stream().map(DtoInstrumentUseRecord::getEndTime).collect(Collectors.toList());
        if (startTimes.size() > 0 && endTimes.size() > 0) {
            Date starTime = Collections.min(startTimes);
            Date endTime = Collections.max(endTimes);
            basic.put(仪器使用时间, checkValEmpty(DateUtil.dateToString(starTime, DateUtil.YEAR_ZH_CN) + "~" + DateUtil.dateToString(endTime, DateUtil.YEAR_ZH_CN), blankFillStr));
        } else {
            basic.put(仪器使用时间, blankFillStr);
        }
    }

    /**
     * 标准曲线信息
     *
     * @param dsMap           数据集合
     * @param curveDetailList 曲线集合
     * @param blankFillStr    默认值
     */
    private void writeCurveInfo(Map<String, List<Map<String, Object>>> dsMap, List<DtoCurve> curveList,
                                List<DtoCurveDetail> curveDetailList, String blankFillStr) {
        List<Map<String, Object>> basicInfo = dsMap.get("Info1");
        Map<String, Object> basic = basicInfo.get(0);
        Date configDate = curveList.stream().map(DtoCurve::getConfigDate).findFirst().orElse(null);
        String str = StringUtils.isNotNull(configDate) ? DateUtil.dateToString(configDate, DateUtil.YEAR) : "";
        basic.put(校准曲线绘制日期, checkValEmpty(str, blankFillStr));
        //斜率
        String xl = "";
        //相关系数
        String xs = "";
        //截距
        String jj = "";
        if (StringUtils.isNotEmpty(curveList)) {
            DtoCurve fstCurve = curveList.get(0);
            xl = StringUtils.isNotNull(fstCurve) ? fstCurve.getKValue() : "";
            xs = StringUtils.isNotNull(fstCurve) ? fstCurve.getCoefficient() : "";
            jj = StringUtils.isNotNull(fstCurve) ? fstCurve.getBValue() : "";
        }
        basic.put(斜率, checkValEmpty(xl, blankFillStr));
        basic.put(相关系数, checkValEmpty(xs, blankFillStr));
        basic.put(截距, checkValEmpty(jj, blankFillStr));
    }


    /**
     * 基础数据赋值
     *
     * @param baseInfo   基础数据
     * @param defaultVal 默认值
     */
    private void dealBaseInfoDefaultValue(List<Map<String, Object>> baseInfo, String defaultVal) {
        Map<String, Object> map = new HashMap<>();
        map.put(仪器型号名称及编号, defaultVal);
        map.put(仪器型号及编号, defaultVal);
        map.put(仪器型号及名称, defaultVal);
        map.put(仪器名称及编号, defaultVal);
        map.put(仪器名称及出厂编号, defaultVal);
        map.put(仪器型号及出厂编号, defaultVal);
        map.put(仪器型号名称及出厂编号, defaultVal);
        map.put(仪器名称型号, defaultVal);
        map.put(仪器型号, defaultVal);
        map.put(仪器使用日期, defaultVal);
        map.put(仪器编号, defaultVal);
        map.put(仪器出厂编号, defaultVal);
        map.put(室温, defaultVal);
        map.put(温度, defaultVal);
        map.put(湿度, defaultVal);
        map.put(温湿度, defaultVal);
        map.put(大气压, defaultVal);
        map.put(仪器溯源方式, defaultVal);
        map.put(仪器溯源有效期, defaultVal);
        map.put(仪器溯源日期, defaultVal);
        map.put(分析日期, defaultVal);
        map.put(分析方法, defaultVal);
        map.put(分析项目, defaultVal);
        map.put(分析项目总称, defaultVal);
        map.put(检出限, defaultVal);
        map.put(测试项目检出限, defaultVal);
        map.put(检出限带量纲, defaultVal);
        map.put(试剂配置, defaultVal);
        map.put(溶液浓度, defaultVal);
        map.put(溶液名称及浓度, defaultVal);
        map.put(标液名称, defaultVal);
        map.put(分析方法编号年度, defaultVal);
        map.put(方法编号及年度, defaultVal);
        map.put(测试项目, defaultVal);
        map.put(测试项目公式, defaultVal);
        map.put(大备注, defaultVal);
        map.put(采样日期, defaultVal);
        map.put(样品类型, defaultVal);
        map.put(领样日期, defaultVal);
        map.put(项目编号, defaultVal);
        map.put(项目名称, defaultVal);
        map.put(委托性质, defaultVal);
        map.put(委托单位, defaultVal);
        map.put(受检单位, defaultVal);
        map.put(样品编号, defaultVal);
        map.put(送样日期, defaultVal);
        map.put(接样日期, defaultVal);
        map.put(页码, defaultVal);
        map.put(页数, defaultVal);
        map.put(页数页码, defaultVal);
        map.put(页码页数, defaultVal);
        map.put(量纲, defaultVal);
        map.put(测试项目量纲, defaultVal);
        map.put(仪器使用时间, defaultVal);
        baseInfo.add(map);
    }

    /**
     * 基础数据（检测单数据）赋值
     *
     * @param dsMap                数据集合
     * @param workSheetDataList    分析数据集合
     * @param workSheetFolder      工作单对象
     * @param workSheetReagentList 试剂配置记录集合
     */
    private void writeWorkSheetInfo(Map<String, List<Map<String, Object>>> dsMap, List<DtoWorkSheetData> workSheetDataList,
                                    DtoWorksheetFolder workSheetFolder, List<DtoWorksheetReagent> workSheetReagentList, String blankFillStr) {
        List<Map<String, Object>> baseInfo = dsMap.get("Info1");
        DtoWorkSheetData workSheetData = workSheetDataList.stream().findFirst().orElse(null);
        baseInfo.get(0).put(分析项目总称, "");
        DtoWorkSheetData dimensionDto = workSheetDataList.stream().filter(p -> StringUtils.isNotEmpty(p.getDimension()) && !p.getIsQC()).findFirst().orElse(null);
        String dimensionName = StringUtils.isNotNull(dimensionDto) ? dimensionDto.getDimension() : "";
        if (StringUtils.isNotNull(workSheetData)) {
            Map<String, Object> basic = baseInfo.get(0);
            basic.put(分析日期, checkValEmpty(DateUtil.dateToString(workSheetData.getAnalyzeTime(), DateUtil.YEAR_ZH_CN), blankFillStr));
            basic.put(分析方法, checkValEmpty(workSheetData.getAnalyzeMethodName(), blankFillStr));
            basic.put(分析方法别名, checkValEmpty(workSheetData.getAnalyzeMethodAlias(), blankFillStr));
            basic.put(分析方法编号年度, checkValEmpty(workSheetData.getAnalyzeMethodName(), blankFillStr));
            basic.put(分析方法别名编号年度, checkValEmpty(workSheetData.getAnalyzeMethodAlias(), blankFillStr));
            basic.put(分析项目, checkValEmpty(workSheetData.getRedAnalyzeItems(), blankFillStr));
            String examVal = workSheetData.getExamLimitValue();
            String sampleTypeId = StringUtils.isNotEmpty(workSheetData.getSampleTypeId()) ? workSheetData.getSampleTypeId() : UUIDHelper.guidEmpty();
            basic.put(测试项目检出限, checkValEmpty(examVal, blankFillStr));
            String dimName = workSheetData.getDimension();
            basic.put(测试项目量纲, checkValEmpty(dimName, blankFillStr));
            basic.put(测试项目检出限带量纲, checkValEmpty(StringUtils.isNotEmpty(dimName) ? examVal + "（" + dimName + "）" : examVal, blankFillStr));
            basic.put(量纲, checkValEmpty(dimensionName, blankFillStr));
            basic.put(检出限, checkValEmpty(workSheetData.getExamLimitValue(), blankFillStr));
            basic.put(检出限带量纲, checkValEmpty(workSheetData.getExamLimitValue() + dimensionName, blankFillStr));
            if (StringUtils.isNotEmpty(workSheetReagentList)) {
                basic.put(试剂配置, checkValEmpty(workSheetReagentList.get(0).getCourse(), blankFillStr));
                basic.put(溶液名称及浓度, checkValEmpty(workSheetReagentList.get(0).getConfigurationSolution(), blankFillStr));
                basic.put(标液名称, checkValEmpty(workSheetReagentList.get(0).getReagentName(), blankFillStr));
                basic.put(溶液配制日期, checkValEmpty(workSheetReagentList.get(0).getConfigDate().toString(), blankFillStr));
                if (workSheetReagentList.size() == 1) {
                    basic.put(试剂配置全, basic.get(试剂配置));
                    basic.put(溶液名称及浓度全, basic.get(溶液名称及浓度));
                    basic.put(标液名称全, basic.get(标液名称));
                    basic.put(溶液配制日期全, basic.get(溶液配制日期));
                } else {
                    //多配置记录情况，排序同页面
                    workSheetReagentList.sort(Comparator.comparing(DtoWorksheetReagent::getConfigDate));
                    List<String> courseFull = workSheetReagentList.stream().filter(r -> StringUtils.isNotEmpty(r.getCourse())).map(r -> r.getCourse().toString()).collect(Collectors.toList());
                    List<String> configurationSolutionFull = workSheetReagentList.stream().filter(r -> StringUtils.isNotEmpty(r.getConfigurationSolution())).map(r -> r.getConfigurationSolution().toString()).collect(Collectors.toList());
                    List<String> reagentNameFull = workSheetReagentList.stream().filter(r -> StringUtils.isNotEmpty(r.getReagentName())).map(r -> r.getReagentName().toString()).collect(Collectors.toList());
                    List<String> configDateFull = workSheetReagentList.stream().filter(r -> StringUtils.isNotEmpty(r.getConfigDate().toString())).map(r -> r.getConfigDate().toString()).distinct().collect(Collectors.toList());
                    basic.put(试剂配置全, checkValEmpty(String.join("\n", courseFull), blankFillStr));
                    basic.put(溶液名称及浓度全, checkValEmpty(String.join("，", configurationSolutionFull), blankFillStr));
                    basic.put(标液名称全, checkValEmpty(String.join("，", reagentNameFull), blankFillStr));
                    basic.put(溶液配制日期全, checkValEmpty(String.join("，", configDateFull), blankFillStr));
                }
            } else {
                basic.put(试剂配置, blankFillStr);
                basic.put(溶液名称及浓度, blankFillStr);
                basic.put(标液名称, blankFillStr);
                basic.put(溶液配制日期, blankFillStr);
                basic.put(试剂配置全, blankFillStr);
                basic.put(溶液名称及浓度全, blankFillStr);
                basic.put(标液名称全, blankFillStr);
                basic.put(溶液配制日期全, blankFillStr);
            }

//            List<String> anaIds = new ArrayList<>();
//            anaIds.addAll(workSheetDataList.stream().map(DtoWorkSheetData::getAnalyseDataId).distinct().collect(Collectors.toList()));
            basic.put(测试项目公式, "");
        }
    }

    /**
     * 查询该工作单下的所有的分析数据组合成工作单数据
     *
     * @param analyseDataList 分析数据
     * @return 返回工作单下的所有的分析数据组合成工作单数据
     */
    private List<DtoWorkSheetData> findWorkSheetDataByList(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList) {
//        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
//        List<DtoSample> sampleList = workSheetDataSourceService.findSample(sampleIds);
//        List<String> qcIds = sampleList.stream().map(DtoSample::getQcId)
//                .filter(qcId -> !qcId.equals(UUIDHelper.guidEmpty())).distinct().collect(Collectors.toList());
//        List<DtoQualityControl> dtoQualityControlList = workSheetDataSourceService.findQualityControl(qcIds);
        return dealWorkSheetData(analyseDataList, sampleList);
    }

    /**
     * 处理工作单数据信息
     *
     * @param analyseDataList 分析数据集合
     * @return 分析数据信息
     */
    private List<DtoWorkSheetData> dealWorkSheetData(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList) {
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getSampleId, dto -> dto, (a1, a2) -> a1));
        List<DtoWorkSheetData> workSheetData = new ArrayList<>();
        List<String> allTestIdList = new ArrayList<>();
        List<String> allAnalyzeMethodIdList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            if (!allTestIdList.contains(analyseData.getTestId())) {
                allTestIdList.add(analyseData.getTestId());
            }
            if (!allAnalyzeMethodIdList.contains(analyseData.getAnalyzeMethodId())) {
                allAnalyzeMethodIdList.add(analyseData.getAnalyzeMethodId());
            }
        }
        for (DtoAnalyseData anaData : analyseDataList) {
            DtoSample sample = sampleMap.getOrDefault(anaData.getSampleId(), null);
            DtoWorkSheetData dtoWorkSheetData = new DtoWorkSheetData();
            dtoWorkSheetData.setProjectId(StringUtils.isNotNull(sample) ? sample.getProjectId() : UUIDHelper.guidEmpty());
            dtoWorkSheetData.setAssociateSampleId(StringUtils.isNotNull(sample) ? sample.getAssociateSampleId() : UUIDHelper.guidEmpty());
            dtoWorkSheetData.setTestId(anaData.getTestId());
            dtoWorkSheetData.setAnalyseDataId(anaData.getAnalyseDataId());
            dtoWorkSheetData.setWorkSheetId(anaData.getWorkSheetId());
            dtoWorkSheetData.setAnalyseItemId(anaData.getAnalyseItemId());
            dtoWorkSheetData.setTestId(anaData.getTestId());
            dtoWorkSheetData.setAnalyzeMethodId(anaData.getAnalyzeMethodId());
            dtoWorkSheetData.setSampleId(anaData.getSampleId());
            dtoWorkSheetData.setRedAnalyzeItems(anaData.getRedAnalyzeItemName());
            dtoWorkSheetData.setTestOrignValue(anaData.getTestOrignValue());
            dtoWorkSheetData.setTestValue(anaData.getTestValue());
            dtoWorkSheetData.setExamLimitValue(anaData.getExamLimitValue());
            dtoWorkSheetData.setExamLimitValueLess("");
            dtoWorkSheetData.setLowerLimit("");
//            dtoWorkSheetData.setTest(null);
            String examLimitTestValue = "ND（" + anaData.getExamLimitValue() + "）";
            dtoWorkSheetData.setExamLimitTestValue(examLimitTestValue);
            dtoWorkSheetData.setAnalyzeTime(anaData.getAnalyzeTime());
            dtoWorkSheetData.setAnalystName(anaData.getAnalystName());
            dtoWorkSheetData.setWorkSheetFolderId(anaData.getWorkSheetFolderId());
            String analyzeMethodName = anaData.getRedAnalyzeMethodName() + " " + anaData.getRedCountryStandard();
            dtoWorkSheetData.setAnalyzeMethodName(analyzeMethodName);
            dtoWorkSheetData.setAnalyzeMethodAlias(anaData.getRedAnalyzeMethodName());
            dtoWorkSheetData.setTestValueDstr(anaData.getTestValueDstr());
            dtoWorkSheetData.setMostSignificance(anaData.getMostSignificance());
            dtoWorkSheetData.setMostDecimal(anaData.getMostDecimal());
            dtoWorkSheetData.setDimension(anaData.getDimension());
            dtoWorkSheetData.setQcGrade(anaData.getQcGrade());
            dtoWorkSheetData.setQcType(anaData.getQcType());
            SimpleDateFormat analyzeTimeShort = new SimpleDateFormat(DateUtil.YEAR_ZH_CN);
            dtoWorkSheetData.setAnalyzeTimeShort(analyzeTimeShort.format(anaData.getAnalyzeTime()));
            dtoWorkSheetData.setQcInfo(anaData.getQcInfo());
            dtoWorkSheetData.setGatherCode(StringUtils.isNotEmpty(anaData.getGatherCode()) ? anaData.getGatherCode() : "");
            dtoWorkSheetData.setSeriesValue(StringUtils.isNotEmpty(anaData.getSeriesValue()) ? anaData.getSeriesValue() : "");
            dtoWorkSheetData.setFormulaId("");
            Boolean isQc = (StringUtils.isNotNull(sample) && !sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) ? true : false;
            dtoWorkSheetData.setIsQC(isQc);
            dtoWorkSheetData.setSampleTypeName(StringUtils.isNotNull(sample) ? sample.getSampleTypeName() : UUIDHelper.guidEmpty());
            workSheetData.add(dtoWorkSheetData);
        }
        return workSheetData;
    }

    @Autowired
    @Lazy
    public void setWorkSheetDataSourceService(WorkSheetDataSourceService workSheetDataSourceService) {
        this.workSheetDataSourceService = workSheetDataSourceService;
    }
}
