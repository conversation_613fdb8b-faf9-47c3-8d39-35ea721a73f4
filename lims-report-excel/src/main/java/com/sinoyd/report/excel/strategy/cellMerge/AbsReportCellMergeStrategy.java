package com.sinoyd.report.excel.strategy.cellMerge;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表单元格合并处理基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbsReportCellMergeStrategy {

    /**
     * 单元格合并个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    public abstract void cellMergeProcess(Map<String, List<Map<String, Object>>> dsMap, DtoAreaExpandMergeConfig mergeConfig, DtoAreaConfig areaConfig,
                                          DtoSheetConfig sheetConfig, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                          ExcelBusinessParamVO businessParamVO, int idx);

    /**
     * 获取报表合并规则编码
     *
     * @return 报表合并规则编码
     */
    public abstract String getMergeRules();
}
