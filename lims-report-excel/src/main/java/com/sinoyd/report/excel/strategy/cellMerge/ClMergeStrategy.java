package com.sinoyd.report.excel.strategy.cellMerge;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.enums.EnumAreaMergeIndType;
import com.sinoyd.report.enums.EnumAreaType;
import com.sinoyd.report.enums.EnumMergeRules;
import com.sinoyd.report.excel.enums.EnumSampleCategory;
import com.sinoyd.report.excel.util.ExcelReportUtil;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sinoyd.report.excel.constant.ExcelReportConstants.PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.util.ExcelReportUtil.getCellRowCol;

/**
 * 串联样单元格合并处理策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class ClMergeStrategy extends AbsReportCellMergeStrategy {

    /**
     * 单元格合并个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    @Override
    public void cellMergeProcess(Map<String, List<Map<String, Object>>> dsMap, DtoAreaExpandMergeConfig mergeConfig, DtoAreaConfig areaConfig, DtoSheetConfig sheetConfig,
                                 ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO, int idx) {
        //合并样品浓度，基准排放浓度，排放速率单元格
        Map<String, List<Map<String, Object>>> dataRst = ExcelReportUtil.getAreaTypeData(dsMap, EnumAreaType.拓展数据区域.getValue());
        List<Map<String, Object>> mapList = new ArrayList<>();
        String key = areaConfig.getId() + EnumAreaType.合并数据区域.getValue() + idx;
        //原始记录页样品数据起始行
        int[] startArr = getCellRowCol(mergeConfig.getStartPosition()), endArr = getCellRowCol(mergeConfig.getEndPosition());
        int startRowIdx = startArr[0], startColIdx = startArr[1], endColIdx = endArr[1];
        int dataPageCnt = dataRst.keySet().size();
        String blankIdentifier = areaConfig.getBlankIdentifier(), emptyTemplatePlaceHolder = areaConfig.getEmptyTemplatePlaceHolder();
        //遍历每页样品数据收集合并单元格的map信息
        for (int i = 1; i <= dataPageCnt; i++) {
            List<Map<String, Object>> dataPageList = new ArrayList<>(dataRst.get(EnumAreaType.拓展数据区域.getValue() + i));
            if (blankIdentifier.equals(dataPageList.get(dataPageList.size() - 1).get(emptyTemplatePlaceHolder))) {
                //移除当前页样品数据中的以下空白行
                dataPageList.remove(dataPageList.size() - 1);
            }
            if (StringUtils.isEmpty(dataPageList)) {
                break;
            }
            //获取当前遍历页的合并单元格信息放入mapList中
            setPageMergeCells(dataPageList, mapList, startRowIdx, new int[][]{{startColIdx, endColIdx - startColIdx + 1}});
            startRowIdx += sheetConfig.getSheetRows();
        }
        dsMap.put(key, mapList);
    }

    /**
     * 获取当前遍历页的合并单元格信息
     *
     * @param dataPageList 当前页的data数据集
     * @param mapList      单元格合并信息集
     * @param startRowIdx  每页样品的起始行索引
     * @param cellIdxArr   需要合并的单元格的起始列索引和单元格列数量(多个单元格时，用列数组传递)
     */
    private void setPageMergeCells(List<Map<String, Object>> dataPageList, List<Map<String, Object>> mapList, int startRowIdx, int[][] cellIdxArr) {
        int loopIdx = 0;
        while (loopIdx < dataPageList.size()) {
            Map<String, Object> fstData = dataPageList.get(loopIdx);
            //获取原样id，如果是串联样则获取关联样品id
            String fstCategory = (String) fstData.getOrDefault(PLACEHOLDER_PREFIX + "sampleCategory", "");
            String yyId = EnumSampleCategory.串联样.getValue().toString().equals(fstCategory)
                    ? (String) fstData.getOrDefault(PLACEHOLDER_PREFIX + "associateSampleId", "") : (String) fstData.getOrDefault(PLACEHOLDER_PREFIX + "sampleId", "");
            //当前遍历样品需要合并排放浓度，排放速率单元格的开始行索引及合并的行数
            int mergeStartRowIdx = startRowIdx + loopIdx;
            int totalRows = 1;
            //默认串联样紧跟原样，因此可以直接往下循环遍历找到对应的串联样，直至找不到串联样为止
            loopIdx++;
            if (loopIdx < dataPageList.size()) {
                while (loopIdx < dataPageList.size() && EnumSampleCategory.串联样.getValue().toString().equals(dataPageList.get(loopIdx).getOrDefault(PLACEHOLDER_PREFIX + "sampleCategory", ""))
                        && yyId.equals(dataPageList.get(loopIdx).getOrDefault(PLACEHOLDER_PREFIX + "associateSampleId", ""))) {
                    totalRows++;
                    loopIdx++;
                }
            }
            if (totalRows > 1) {
                //合并行数大于1时才需要合并
                for (int[] cellIdx : cellIdxArr) {
                    Map<String, Object> mergeMap = new HashMap<>();
                    mergeMap.put("startRow", mergeStartRowIdx);
                    mergeMap.put("startColumn", cellIdx[0]);
                    mergeMap.put("totalRows", totalRows);
                    mergeMap.put("totalColumns", cellIdx[1]);
                    mapList.add(mergeMap);
                }
            }
        }
    }

    @Override
    public String getMergeRules() {
        return EnumAreaMergeIndType.原样与串联样合并.getValue();
    }
}
