package com.sinoyd.report.excel.strategy.cellMerge.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.excel.strategy.cellMerge.AbsReportCellMergeStrategy;
import com.sinoyd.report.excel.strategy.dataSort.AbsReportDataSortStrategy;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表单元格合并策略管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/17
 */
@Component
public class ReportCellMergeContext {

    private List<AbsReportCellMergeStrategy> strategyList;

    /**
     * 处理报表单元格合并策略
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    public void cellMergeInd(Map<String, List<Map<String, Object>>> dsMap, DtoAreaExpandMergeConfig mergeConfig, DtoAreaConfig areaConfig, DtoSheetConfig sheetConfig,
                             ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO, int idx) {
        AbsReportCellMergeStrategy strategy = strategyList.stream().filter(p -> p.getMergeRules().equals(mergeConfig.getMergeRules())).findFirst().orElse(null);
        if (StringUtils.isNotNull(strategy)) {
            strategy.cellMergeProcess(dsMap, mergeConfig, areaConfig, sheetConfig, excelReportParamVO, frontParamVO, businessParamVO, idx);
        }
    }

    @Autowired
    public void setStrategyList(List<AbsReportCellMergeStrategy> strategyList) {
        this.strategyList = strategyList;
    }
}
