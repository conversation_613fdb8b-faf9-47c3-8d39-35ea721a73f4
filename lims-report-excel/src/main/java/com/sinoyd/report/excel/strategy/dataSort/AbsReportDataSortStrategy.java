package com.sinoyd.report.excel.strategy.dataSort;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表数据排序处理基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbsReportDataSortStrategy {

    /**
     * 数据排序处理操作
     *
     * @param rowMapList 报表数据列表
     */
    public abstract List<Map<String, Object>> dataSortProcess(List<Map<String, Object>> rowMapList);

    /**
     * 获取报表数据排序配置编码
     *
     * @return 报表数据排序配置编码
     */
    public abstract String getCustomParamValue();
}
