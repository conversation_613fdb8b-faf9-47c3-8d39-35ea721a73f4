package com.sinoyd.report.excel.strategy.dataSort;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.enums.EnumDataSortType;
import com.sinoyd.report.enums.EnumDataSrcIndType;
import com.sinoyd.report.excel.enums.EnumQcGrade;
import com.sinoyd.report.excel.enums.EnumQcType;
import com.sinoyd.report.excel.enums.EnumSampleCategory;
import com.sinoyd.report.excel.strategy.reportDataSrcInd.AbsReportDataSrcIndStrategy;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sinoyd.report.excel.util.ExcelReportUtil.*;

/**
 * 默认数据排序个性化处理策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class DefaultDataSortStrategy extends AbsReportDataSortStrategy {

    /**
     * 数据排序个性化处理
     *
     * @param rowMapList 报表数据
     * @return
     */
    @Override
    public List<Map<String, Object>> dataSortProcess(List<Map<String, Object>> rowMapList) {
        List<Map<String, Object>> innerBlankMapList = new ArrayList<>(), jhMapList = new ArrayList<>(), innerPxMapList = new ArrayList<>(), jbMapList = new ArrayList<>(),
                bzMapList = new ArrayList<>(), clMapList = new ArrayList<>(), yyMapList = new ArrayList<>(), outParallelMapList = new ArrayList<>(),
                outBlankMapList = new ArrayList<>(), transportBlankMapList = new ArrayList<>(), otherMapList = new ArrayList<>();
        for (Map<String, Object> rowMap : rowMapList) {
            if (checkRowMap(rowMap, EnumQcType.空白.getValue(), EnumQcGrade.内部质控.getValue(), null)) {
                innerBlankMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.曲线校核.getValue(), EnumQcGrade.内部质控.getValue(), null)) {
                jhMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.平行.getValue(), EnumQcGrade.内部质控.getValue(), null)) {
                innerPxMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.加标.getValue(), EnumQcGrade.内部质控.getValue(), null)) {
                jbMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.标准.getValue(), EnumQcGrade.内部质控.getValue(), null)) {
                bzMapList.add(rowMap);
            } else if (checkRowMap(rowMap, null, null, EnumSampleCategory.串联样.getValue())) {
                clMapList.add(rowMap);
            } else if (checkRowMap(rowMap, null, null, EnumSampleCategory.原样.getValue())) {
                yyMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.平行.getValue(), EnumQcGrade.外部质控.getValue(), null)) {
                outParallelMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.空白.getValue(), EnumQcGrade.外部质控.getValue(), null)) {
                outBlankMapList.add(rowMap);
            } else if (checkRowMap(rowMap, EnumQcType.运输空白.getValue(), EnumQcGrade.外部质控.getValue(), null)) {
                transportBlankMapList.add(rowMap);
            } else {
                otherMapList.add(rowMap);
            }
        }

        yyMapList = yyMapList.stream().filter(p -> !EnumSampleCategory.串联样.getValue().equals(getRowMapIntVal(p, "sampleCategory", -1))).collect(Collectors.toList());
        yyMapList.sort(Comparator.comparing(p -> getRowMapStringVal(p, "sampleCode", "")));
        List<Map<String, Object>> sortRowMapList = new ArrayList<>(innerBlankMapList);
        sortRowMapList.addAll(jhMapList);
        for (Map<String, Object> yyMap : yyMapList) {
            sortRowMapList.add(yyMap);
            String yySampleId = getRowMapStringVal(yyMap, "sampleId", "");
            String testId = getRowMapStringVal(yyMap, "testId", "");
            //对应的串联样
            List<Map<String, Object>> clListForYy = clMapList.stream().filter(p -> getRowMapStringVal(p, "associateSampleId", "").equals(yySampleId)
                    && getRowMapStringVal(p, "testId", "").equals(testId)).collect(Collectors.toList());
            sortRowMapList.addAll(clListForYy);
            //对应的室内平行
            sortRowMapList.addAll(innerPxMapList.stream().filter(p -> getRowMapStringVal(p, "associateSampleId", "").equals(yySampleId) && getRowMapStringVal(p, "testId", "").equals(testId)).collect(Collectors.toList()));
            if (StringUtils.isNotEmpty(clListForYy)) {
                for (Map<String, Object> clMap : clListForYy) {
                    List<Map<String, Object>> yyClPx = innerPxMapList.stream().filter(p -> getRowMapStringVal(p, "associateSampleId", "").equals(getRowMapStringVal(clMap, "sampleId", ""))
                            && getRowMapStringVal(p, "testId", "").equals(getRowMapStringVal(clMap, "testId", ""))).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(yyClPx)) {
                        sortRowMapList.addAll(yyClPx);
                    }
                }
            }
            //对应的室内加标
            List<Map<String, Object>> yyJb = jbMapList.stream().filter(p -> getRowMapStringVal(p, "associateSampleId", "").equals(yySampleId)
                    && getRowMapStringVal(p, "testId", "").equals(testId)).collect(Collectors.toList());
            sortRowMapList.addAll(yyJb);
        }
        sortRowMapList.addAll(bzMapList);
        sortRowMapList.addAll(outBlankMapList);
        sortRowMapList.addAll(otherMapList);
        if (sortRowMapList.size() < rowMapList.size()) {
            List<String> anaDataIdList = sortRowMapList.stream().map(p -> p.getOrDefault("analyseDataId", "").toString()).distinct().collect(Collectors.toList());
            List<Map<String, Object>> extraMapList = rowMapList.stream().filter(p -> !anaDataIdList.contains(p.getOrDefault("analyseDataId", "").toString())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(extraMapList)) {
                sortRowMapList.addAll(extraMapList);
            }
        }
        return sortRowMapList;
    }

    @Override
    public String getCustomParamValue() {
        return EnumDataSortType.默认排序.getCode();
    }
}
