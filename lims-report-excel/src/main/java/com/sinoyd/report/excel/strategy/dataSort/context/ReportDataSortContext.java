package com.sinoyd.report.excel.strategy.dataSort.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.excel.strategy.dataSort.AbsReportDataSortStrategy;
import com.sinoyd.report.excel.strategy.reportDataSrcInd.AbsReportDataSrcIndStrategy;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表数据排序处理策略管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/17
 */
@Component
public class ReportDataSortContext {

    private List<AbsReportDataSortStrategy> strategyList;

    /**
     * 处理报表文件名称策略
     *
     * @param rowMapList 报表数据
     * @return 排序好的数据
     */
    public List<Map<String, Object>> dataSortInd(List<Map<String, Object>> rowMapList, DtoCustomParamConfig paramConfig) {
        AbsReportDataSortStrategy strategy = strategyList.stream().filter(p -> p.getCustomParamValue().equals(paramConfig.getParamValue())).findFirst().orElse(null);
        if (StringUtils.isNotNull(strategy)) {
            return strategy.dataSortProcess(rowMapList);
        }
        return rowMapList;
    }

    @Autowired
    public void setReportDataSortStrategyList(List<AbsReportDataSortStrategy> strategyList) {
        this.strategyList = strategyList;
    }
}
