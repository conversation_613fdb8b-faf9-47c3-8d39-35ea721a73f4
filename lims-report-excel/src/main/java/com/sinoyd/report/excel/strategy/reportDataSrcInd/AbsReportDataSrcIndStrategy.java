package com.sinoyd.report.excel.strategy.reportDataSrcInd;

import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表数据源个性化处理基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbsReportDataSrcIndStrategy {

    /**
     * 数据源个性化处理操作
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param paramConfig        自定义参数配置对象
     */
    public abstract void dataSrcIndProcess(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                           ExcelBusinessParamVO businessParamVO, DtoCustomParamConfig paramConfig);

    /**
     * 获取报表数据源个性化配置编码
     *
     * @return 报表数据源个性化配置编码
     */
    public abstract String getCustomParamValue();
}
