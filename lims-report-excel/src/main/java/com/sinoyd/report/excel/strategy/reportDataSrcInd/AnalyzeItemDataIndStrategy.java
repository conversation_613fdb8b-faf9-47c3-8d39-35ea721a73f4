package com.sinoyd.report.excel.strategy.reportDataSrcInd;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.JsonUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.enums.EnumDataSrcIndType;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.sinoyd.report.excel.constant.ExcelReportConstants.PARAM_PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.constant.ExcelReportConstants.PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.util.ExcelReportUtil.getAreaTypeData;

/**
 * 不同分析项目的数据进行不同的处理策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class AnalyzeItemDataIndStrategy extends AbsReportDataSrcIndStrategy {

    /**
     * 不同分析项目的数据个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param paramConfig        自定义参数配置对象
     */
    @Override
    public void dataSrcIndProcess(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                  ExcelBusinessParamVO businessParamVO, DtoCustomParamConfig paramConfig) {
        String configInfo = paramConfig.getConfigInfo();
        if (StringUtils.isNotEmpty(configInfo)) {
            Map<String, Object> map = JsonUtils.deserialize(configInfo, Map.class);
            boolean sourceParamFlag = "true".equals(map.getOrDefault("sourceParamFlag", "").toString()), targetParamFlag = "true".equals(map.getOrDefault("targetParamFlag", "").toString());
            String areaType = map.getOrDefault("areaType", "").toString(), sourcePlaceHolder = map.getOrDefault("sourcePlaceHolder", "").toString(),
                    targetPlaceHolder = map.getOrDefault("targetPlaceHolder", "").toString(), analyzeItemName = map.getOrDefault("analyzeItemName", "").toString();
            sourcePlaceHolder = sourceParamFlag ? (PARAM_PLACEHOLDER_PREFIX + sourcePlaceHolder) : (PLACEHOLDER_PREFIX + sourcePlaceHolder);
            targetPlaceHolder = targetParamFlag ? (PARAM_PLACEHOLDER_PREFIX + targetPlaceHolder) : (PLACEHOLDER_PREFIX + targetPlaceHolder);
            if (StringUtils.isNotEmpty(areaType)) {
                Map<String, List<Map<String, Object>>> areaData = getAreaTypeData(dsMap, areaType);
                if (StringUtils.isNotEmpty(areaData)) {
                    for (Map.Entry<String, List<Map<String, Object>>> entry : areaData.entrySet()) {
                        List<Map<String, Object>> rowMapList = entry.getValue();
                        for (Map<String, Object> rowMap : rowMapList) {
                            String loopItemName = rowMap.getOrDefault(PLACEHOLDER_PREFIX + "样品分析项目名称", "").toString();
                            if (analyzeItemName.equals(loopItemName) && rowMap.containsKey(sourcePlaceHolder) && StringUtils.isNotNull(rowMap.get(targetPlaceHolder))) {
                                Object targetObj = rowMap.get(targetPlaceHolder);
                                rowMap.put(sourcePlaceHolder, targetObj);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public String getCustomParamValue() {
        return EnumDataSrcIndType.分析项目数据处理个性化.getCode();
    }
}
