package com.sinoyd.report.excel.strategy.reportDataSrcInd;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.JsonUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.enums.EnumDataSrcIndType;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.sinoyd.report.excel.constant.ExcelReportConstants.PARAM_PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.constant.ExcelReportConstants.PLACEHOLDER_PREFIX;
import static com.sinoyd.report.excel.util.ExcelReportUtil.getAreaTypeData;

/**
 * 区域数据个性化处理策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class AreaTypeDataIndStrategy extends AbsReportDataSrcIndStrategy {

    /**
     * 区域数据个性化处理
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param paramConfig        自定义参数配置对象
     */
    @Override
    public void dataSrcIndProcess(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                  ExcelBusinessParamVO businessParamVO, DtoCustomParamConfig paramConfig) {
        String configInfo = paramConfig.getConfigInfo();
        if (StringUtils.isNotEmpty(configInfo)) {
            Map<String, Object> map = JsonUtils.deserialize(configInfo, Map.class);
            boolean paramFlag = "true".equals(map.getOrDefault("paramFlag", "").toString());
            String areaType = map.getOrDefault("areaType", "").toString(), placeHolder = map.getOrDefault("placeHolder", "").toString(),
                    dftVal = map.getOrDefault("value", "").toString();
            placeHolder = paramFlag ? (PARAM_PLACEHOLDER_PREFIX + placeHolder) : (PLACEHOLDER_PREFIX + placeHolder);
            String qcType = com.sinoyd.boot.common.util.StringUtils.isNotNull(map.get("qcType")) ? map.get("qcType").toString() : "-1",
            qcGrade = StringUtils.isNotNull(map.get("qcGrade")) ? map.get("qcGrade").toString() : "-1";
            if (StringUtils.isNotEmpty(areaType) && !"-1".equals(qcType) && !"-1".equals(qcGrade)) {
                Map<String, List<Map<String, Object>>> areaData = getAreaTypeData(dsMap, areaType);
                if (StringUtils.isNotEmpty(areaData)) {
                    for (Map.Entry<String, List<Map<String, Object>>> entry : areaData.entrySet()) {
                        List<Map<String, Object>> rowMapList = entry.getValue();
                        for (Map<String, Object> rowMap : rowMapList) {
                            if (rowMap.containsKey(placeHolder) && StringUtils.isNotNull(rowMap.get(placeHolder))) {
                                String curQcType = StringUtils.isNotNull(rowMap.get(PLACEHOLDER_PREFIX + "qcType")) ? rowMap.get(PLACEHOLDER_PREFIX + "qcType").toString() : "-1",
                                        curQcGrade = StringUtils.isNotNull(rowMap.get(PLACEHOLDER_PREFIX + "qcGrade")) ? rowMap.get(PLACEHOLDER_PREFIX + "qcGrade").toString() : "-1";
                                if (curQcGrade.equals(qcGrade) && curQcType.equals(qcType)) {
                                    rowMap.put(placeHolder, dftVal);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public String getCustomParamValue() {
        return EnumDataSrcIndType.区域数据个性化.getCode();
    }
}
