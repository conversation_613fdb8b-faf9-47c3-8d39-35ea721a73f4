package com.sinoyd.report.excel.strategy.reportDataSrcInd.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.excel.strategy.reportDataSrcInd.AbsReportDataSrcIndStrategy;
import com.sinoyd.report.excel.strategy.reportFileName.AbsReportFileNameStrategy;
import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报表数据源个性化处理策略管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Component
public class ReportDataSrcIndContext {

    private List<AbsReportDataSrcIndStrategy> strategyList;

    /**
     * 处理报表文件名称策略
     *
     * @param dsMap              报表数据源
     * @param frontParamVO       excel报表生成前端传参VO
     * @param businessParamVO    业务数据VO
     * @param excelReportParamVO excel报表生成数据传输对象
     */
    public void dataSrcInd(Map<String, List<Map<String, Object>>> dsMap, ExcelReportParamVO excelReportParamVO, FrontParamVO frontParamVO,
                                  ExcelBusinessParamVO businessParamVO, DtoCustomParamConfig paramConfig) {
        AbsReportDataSrcIndStrategy strategy = strategyList.stream().filter(p -> p.getCustomParamValue().equals(paramConfig.getParamValue())).findFirst().orElse(null);
        if (StringUtils.isNotNull(strategy)) {
            strategy.dataSrcIndProcess(dsMap, excelReportParamVO, frontParamVO, businessParamVO, paramConfig);
        }
    }

    @Autowired
    public void setReportDataSrcIndStrategyList(List<AbsReportDataSrcIndStrategy> strategyList) {
        this.strategyList = strategyList;
    }
}
