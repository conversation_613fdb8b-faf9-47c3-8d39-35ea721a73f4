package com.sinoyd.report.excel.strategy.reportFileName;

import com.sinoyd.report.excel.vo.ExcelReportParamVO;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表文件名处理基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbsReportFileNameStrategy {

    /**
     * 获取报表文件命名规则数据
     *
     * @param frontParamVO 前端传参对象
     * @return 报表文件命名规则数据
     */
    public abstract Map<String, String> getFileNameDataMap(FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO);

    /**
     * 获取报表文件命名方法
     *
     * @return 报表文件命名方法
     */
    public abstract String getFileNamingMethod();
}
