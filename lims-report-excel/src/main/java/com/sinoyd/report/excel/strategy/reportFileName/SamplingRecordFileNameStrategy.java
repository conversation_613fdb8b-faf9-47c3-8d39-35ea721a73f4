package com.sinoyd.report.excel.strategy.reportFileName;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.dto.DtoWorksheetFolder;
import com.sinoyd.report.enums.EnumNameRules;
import com.sinoyd.report.enums.EnumNamingMethod;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.service.WorkSheetFolderService;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 现场采样单报表文件名称策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class SamplingRecordFileNameStrategy extends AbsReportFileNameStrategy {

    private WorkSheetFolderService workSheetFolderService;

    /**
     * 获取报表文件命名规则数据
     *
     * @param frontParamVO 前端传参对象
     * @return 报表文件命名规则数据
     */
    @Override
    public Map<String, String> getFileNameDataMap(FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO) {
        List<DtoSample> sampleList = businessParamVO.getSampleList();
        String recordCode = "", samplingTime = "";
        if (StringUtils.isNotEmpty(sampleList)) {
            recordCode = sampleList.get(0).getRecordCode();
            Date samplingDate  = sampleList.get(0).getSamplingTime();
            if (StringUtils.isNotNull(samplingDate)) {
                String dateStr = DateUtil.dateToString(samplingDate, DateUtil.YEAR);
                if (StringUtils.isNotEmpty(dateStr) && !dateStr.contains("1753")) {
                    samplingTime = dateStr;
                }
            }
        }
        Map<String, String> map = new HashMap<>();
        map.put(EnumNameRules.送样单号.getCode(), recordCode);
        map.put(EnumNameRules.采样时间.getCode(), samplingTime);
        return map;
    }

    @Override
    public String getFileNamingMethod() {
        return EnumNamingMethod.原始记录单.getCode();
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }
}
