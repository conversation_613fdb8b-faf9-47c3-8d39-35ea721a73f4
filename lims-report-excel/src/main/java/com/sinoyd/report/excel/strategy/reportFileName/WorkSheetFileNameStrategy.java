package com.sinoyd.report.excel.strategy.reportFileName;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoWorksheetFolder;
import com.sinoyd.report.enums.EnumNameRules;
import com.sinoyd.report.enums.EnumNamingMethod;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.service.WorkSheetFolderService;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * 原始记录单报表文件名称策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class WorkSheetFileNameStrategy extends AbsReportFileNameStrategy {

    private WorkSheetFolderService workSheetFolderService;

    /**
     * 获取报表文件命名规则数据
     *
     * @param frontParamVO 前端传参对象
     * @return 报表文件命名规则数据
     */
    @Override
    public Map<String, String> getFileNameDataMap(FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO) {
        String workSheetFolderId = StringUtils.isNotEmpty(frontParamVO.getWorkSheetFolderId()) ? frontParamVO.getWorkSheetFolderId() : UUIDHelper.guidEmpty();
        DtoWorksheetFolder worksheetFolder = workSheetFolderService.findOne(workSheetFolderId);
        String workSheetCode = StringUtils.isNotNull(worksheetFolder) ? worksheetFolder.getWorkSheetCode() : "";
        return Collections.singletonMap(EnumNameRules.检测单编号.getCode(), workSheetCode);
    }

    @Override
    public String getFileNamingMethod() {
        return EnumNamingMethod.原始记录单.getCode();
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }
}
