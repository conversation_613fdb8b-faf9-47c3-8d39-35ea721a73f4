package com.sinoyd.report.excel.strategy.reportFileName.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.excel.strategy.reportFileName.AbsReportFileNameStrategy;
import com.sinoyd.report.excel.vo.FrontParamVO;
import com.sinoyd.report.vo.ExcelBusinessParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报表文件名策略管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
public class ReportFileNameContext {

    private List<AbsReportFileNameStrategy> fileNameStrategyList;

    /**
     * 处理报表文件名称策略
     *
     * @param frontParamVO 前端传参对象
     * @param namingMethod 命名方法
     */
    public Map<String, String> getFileNameDataMap(FrontParamVO frontParamVO, ExcelBusinessParamVO businessParamVO, String namingMethod) {
        Map<String, String> dataMap = new HashMap<>();
        AbsReportFileNameStrategy reportFileNameStrategy = fileNameStrategyList.stream().filter(p -> namingMethod.equals(p.getFileNamingMethod())).findFirst().orElse(null);
        if (StringUtils.isNotNull(reportFileNameStrategy)) {
            dataMap = reportFileNameStrategy.getFileNameDataMap(frontParamVO, businessParamVO);
        }
        return dataMap;
    }

    @Autowired
    public void setFileNameStrategyList(List<AbsReportFileNameStrategy> fileNameStrategyList) {
        this.fileNameStrategyList = fileNameStrategyList;
    }
}
