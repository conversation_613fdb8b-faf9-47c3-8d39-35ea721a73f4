package com.sinoyd.report.excel.vo;

import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.vo.ExcelPlaceholderVO;
import lombok.Data;

import java.util.List;

/**
 * 区域相关参数VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/18
 */
@Data
public class AreaTypeParamVO {

    /**
     * 每页条数
     */
    private Integer countNum;

    /**
     * 结果是否为集合
     */
    private Boolean isCollection;

    /**
     * 占位符数据
     */
    private List<ExcelPlaceholderVO> placeholderList;

    /**
     * 区域配置对象
     */
    private DtoAreaConfig areaConfig;
}
