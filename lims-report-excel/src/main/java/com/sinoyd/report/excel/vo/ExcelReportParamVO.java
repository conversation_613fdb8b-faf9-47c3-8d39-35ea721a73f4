package com.sinoyd.report.excel.vo;

import com.sinoyd.report.dto.*;
import com.sinoyd.report.vo.AreaRelationDataVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * excel报表生成数据传输VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/09
 */
@Data
public class ExcelReportParamVO {

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 报表基础配置
     */
    private DtoBaseConfig baseConfig;

    /**
     * 报表模板全局配置
     */
    private DtoGlobalConfig globalConfig;

    /**
     * 报表模板区域配置列表
     */
    private List<DtoAreaConfig> areaConfigList;

    /**
     * 报表模板sheet页配置列表
     */
    private List<DtoSheetConfig> sheetConfigList;

    /**
     * 区域相关数据VO
     */
    private AreaRelationDataVO areaRelationData;

    /**
     * 分页字段相关参数
     */
    private List<PageFieldParamVO> pageFieldParamList;

    /**
     * 区域类型相关参数Map
     */
    private Map<String, AreaTypeParamVO> areaTypeParamMap;


    /**
     * 是否转换科学计数法
     */
    private Boolean formatSci;

    /**
     * 生成报表的绝对路径地址
     */
    private String outPutPath;

    /**
     * 生成报表的文件名称
     */
    private String outPutName;
}
