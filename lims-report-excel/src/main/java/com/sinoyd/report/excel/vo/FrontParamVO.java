package com.sinoyd.report.excel.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * excel报表生成前端传参VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Data
public class FrontParamVO {

    private String reportCode;

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 报表应用配置id
     */
    private String recordId;

    /**
     * 报表id
     */
    private String reportId;

    /**
     * 样品id集合
     */
    private List<String> sampleIds;

    /**
     * 分组类型 （0:按分组，1:全因子，2:单因子）
     */
    private Integer groupType;

    /**
     * 检测类型对应的分组数据集合
     */
    private List<SampleTypeGroupVO> groupList = new ArrayList<>();

    /**
     * 报表类型
     */
    private String parType;

    /**
     * 是否保存
     */
    private Boolean isSave;

    /**
     * 是否空白填充
     */
    private Boolean blankFill;

    /**
     * 是否转换科学计数法
     */
    private Boolean formatSci;

    /**
     * 生成报表的绝对路径地址
     */
    private String outPutPath;

    /**
     * 生成报表的文件名称
     */
    private String outPutName;


    /**
     * 检测类型对应分组Id
     */
    @Data
    public static class SampleTypeGroupVO {

        /**
         * 检测类型Id
         */
        private String sampleTypeId;

        /**
         * 检测类型对应的分组id
         */
        private String groupRuleId;
    }
}
