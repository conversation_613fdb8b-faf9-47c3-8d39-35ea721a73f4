package com.sinoyd.report.excel.vo;

import lombok.Data;

import java.util.LinkedHashMap;

/**
 * 分页字段相关参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/03/18
 */
@Data
public class PageFieldParamVO {

    /**
     * 页数
     */
    private Integer page;

    /**
     * 分页字段组合字段名称
     */
    private String pageField;

    /**
     * 分页字段组合字段值
     */
    private String pageFieldVal;

    /**
     * 每个分页字段对应的值,按照分页字段先后排序
     */
    private LinkedHashMap<String, String> pageFieldMap;
}
