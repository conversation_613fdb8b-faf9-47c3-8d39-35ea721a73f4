package com.sinoyd.report.excel.vo;

import com.sinoyd.report.dto.DtoAreaConfig;
import lombok.Data;

/**
 * 从属区域信息VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/17
 */
@Data
public class SubAreaInfoVO {

    /**
     * 区域配置对象
     */
    private DtoAreaConfig areaConfig;

    /**
     * 主区域数据关联属性
     */
    private String mainRelatedProp;

    /**
     * 从属区域数据关联属性
     */
    private String subRelatedProp;

    public SubAreaInfoVO(DtoAreaConfig areaConfig, String mainRelatedProp, String subRelatedProp) {
        this.areaConfig = areaConfig;
        this.mainRelatedProp = mainRelatedProp;
        this.subRelatedProp = subRelatedProp;
    }
}
