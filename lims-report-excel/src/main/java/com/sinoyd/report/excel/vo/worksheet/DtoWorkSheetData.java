package com.sinoyd.report.excel.vo.worksheet;

import lombok.Data;

import java.util.Date;

/**
 * 原始记录单数据实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Data
public class DtoWorkSheetData {
    /**
     * 小工作单Id
     */
    private String workSheetId;

    /**
     * 测试项目Id
     */
    private String testId;

    /**
     * 分析数据Id
     */
    private String analyseDataId;

    /**
     * 分析项目Id
     */
    private String analyseItemId;

    /**
     * 分析方法Id
     */
    private String analyzeMethodId;

    /**
     * 样品Id
     */
    private String sampleId;

    /**
     * 送样单Id
     */
    private String receiveId;

    /**
     * 样品编号
     */
    private String code;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 采样开始时间
     */
    private Date samplingTimeBegin;

    /**
     * 采样开始时间（格式：'2020-01-01'）
     */
    private String samplingTimeBeginShort;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItems;

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 关联样Id
     */
    private String associateSampleId;

    /**
     * 检测修约结果
     */
    private String testOrignValue;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 小于检出限出证结果
     */
    private String examLimitValueLess;

    /**
     * 分析时间
     */
    private Date analyzeTime;

    /**
     * 分析人
     */
    private String analystName;

    /**
     * 是否质控
     */
    private Boolean isQC;

    /**
     * 质控类型（内部质控，外部质控）
     */
    private Integer qcGrade;

    /**
     * 质控样类别（空白、平行等）
     */
    private Integer qcType;

    /**
     * 质控编号（标样编号）
     */
    private String qcCode;

    /**
     * 编号（本站编号）
     */
    private String codeInStation;

    /**
     * 加入标准量
     */
    private String qcValue;

    /**
     * 加标液浓度
     */
    private String qcConcentration;

    /**
     * 加入体积
     */
    private String qcVolume;

    /**
     * 加标样测定值
     */
    private String qcTestValue;

    /**
     * 原样测定值
     */
    private String realSampleTestValue;

    /**
     * 样品类型名称
     */
    private String sampleTypeName;

    /**
     * 样品类型Id
     */
    private String sampleTypeId;

    /**
     * 工作单Id
     */
    private String workSheetFolderId;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法别名
     */
    private String analyzeMethodAlias;

    /**
     * 检测结果修约值
     */
    private String testValueDstr;

    /**
     * 带检出限的出证结果
     */
    private String examLimitTestValue;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 分析日期（格式：'2020年01月01日'）
     */
    private String analyzeTimeShort;

    /**
     * 老的报表id
     */
    private String oldReportId;

    /**
     * 原样品编号
     */
    private String yyCode;

    /**
     * 样品浓度加量纲
     */
    private String dimensionCon;

    /**
     * 备注
     */
    private String remark;


    /**
     * 量纲
     */
    private String dimension;

    /**
     * 采集编号
     */
    private String gatherCode;

    /**
     * 质控信息
     */
    private String qcInfo;

    /**
     * 样品类型
     */
    private Integer sampleCategory;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 标样的有效期
     */
    private String qcValidDate;

    /**
     * 标样的配置日期
     */
    private String qcStandardDate;

    /**
     * 测定下限
     */
    private String lowerLimit;

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 加标体积/标准溶液加入体积量纲id
     */
    private String qcVolumeDimensionId;

    /**
     * 加入标准量/标准物质加入量/替代物加入量量纲id
     */
    private String qcValueDimensionId;

    /**
     * 测定值量纲id
     */
    private String qcTestValueDimensionId;

    /**
     * 样值量纲id
     */
    private String realSampleTestValueDimensionId;

    /**
     * 加标液浓度量纲id
     */
    private String qcConcentrationDimensionId;

    /**
     * 加标液浓度量纲id
     */
    private String samplingPersonName;

    /**
     * 串联样/加原样的中间结果
     */
    private String seriesValue;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     *  频次
     */
    private Integer timesOrder;

}
