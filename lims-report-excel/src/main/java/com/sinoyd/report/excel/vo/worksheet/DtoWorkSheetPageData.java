package com.sinoyd.report.excel.vo.worksheet;

import lombok.Data;

/**
 * 原始记录单分页实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Data
public class DtoWorkSheetPageData {

    /**
     * 加标个数
     */
    private int jbNumber;

    /**
     * 标样个数
     */
    private int bzNumber;

    /**
     * 平行个数
     */
    private int pxNumber;

    /**
     * 项目个数
     */
    private int projectNumber;

    /**
     * jzCurve个数
     */
    private int jzNumber;

    /**
     * 空白个数
     */
    private int kbNumber;

    /**
     * 点位个数
     */
    private int folderNumber;

    /**
     * 曲线个数
     */
    private int curveNumber;

    /**
     * 曲线校核个数
     */
    private int curveJhNumber;

    /**
     * 室内空白样个数
     */
    private int innerBlankNumber;

    /**
     * 校正系数检验样个数
     */
    private int correctionFactorNumber;

    /**
     *分析项目个数
     */
    private int analyzeItemNumber;

    public void clearQcPageNum() {
        setJbNumber(0);
        setBzNumber(0);
        setPxNumber(0);
        setJzNumber(0);
        setKbNumber(0);
        setFolderNumber(0);
        setCurveNumber(0);
        setCurveJhNumber(0);
        setInnerBlankNumber(0);
    }
}
