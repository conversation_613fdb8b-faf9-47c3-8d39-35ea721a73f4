package com.sinoyd.report.excel.vo.worksheet;


import com.sinoyd.report.dto.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 原始记录单分页基础数据实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Data
public class DtoWorkSheetPageInfo {
    /**
     * 样品数据
     */
    private List<DtoWorkSheetData> sampleInThePro;
    /**
     * 所有的样品数据
     */
    private List<DtoWorkSheetData> allSampleInThePro;
    /**
     * 配置信息
     */
    private DtoWorkSheetPara para;
    /**
     * 页数
     */
    private Integer page;

    /**
     * 工作单信息
     */
    private List<DtoWorkSheetData> workSheetDataList;
    /**
     * 项目信息
     */
    private DtoProject project;
    /**
     * 标准曲线信息
     */
    private List<DtoCurve> curveList;

    /**
     * 标准曲线明细信息
     */
    private List<DtoCurveDetail> curveDetailList;


    /**
     * 仪器信息
     */
    private List<DtoInstrumentUseRecord> useRecordList;

    /**
     * 前端记录单模板配置pageConfig配置项
     */
    private Map<String, Object> pageConfigMap;

    /**
     * 记录单模板编码
     */
    private String reportCode;

    /**
     * 工作单参数
     */
    private List<DtoParamsData> paramsDataList;

    private Object workSheetService;

    private Map<String, List<DtoWorkSheetData>> allSampleInTheProMap;

    private int speedSig;

    private DtoWorksheetFolder workSheetFolder;

    private List<DtoWorksheetReagent> workSheetReagentList;

    private List<DtoQualityControlEvaluate> qualityControlEvaluateList;

}
