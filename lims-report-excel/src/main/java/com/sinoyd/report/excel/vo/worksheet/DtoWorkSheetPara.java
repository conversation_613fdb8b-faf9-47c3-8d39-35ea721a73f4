package com.sinoyd.report.excel.vo.worksheet;

import lombok.Data;

/**
 * 原始记录单参数实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/21
 */
@Data
public class DtoWorkSheetPara {

    /**
     * 样品个数
     */
    private Integer sampleCountLimit;

    /**
     * 分析指标个数
     */
    private Integer analyzeItemCountLimit;

    /**
     * 平行个数
     */
    private Integer pxCount;

    /**
     * 加标个数
     */
    private Integer jbCount;

    /**
     * 标样个数
     */
    private Integer bzCount;

    /**
     * 校准曲线
     */
    private Integer jzCurveCount;

    /**
     * 标准曲线
     */
    private Integer curveCount;

    /**
     * 报表Id
     */
    private String reportId;

    /**
     * 原始记录单配置id
     */
    private String recordId;

    /**
     * 不跟随原样数据显示（1,2; 1表示外部质控，2表示平行样，多个类型用 ; 分隔）
     */
    private String notShowInYYSampleData;

    /**
     * 平行质控数据是否显示现场平行（1 表示显示现场 2 表示不显示 空表示不显示）
     */
    private String isShowXCPX;

    /**
     * 是否同时显示双曲线（1 表示是 2 表示否 空表示否）
     */
    private String isShowAllCurve;

    /**
     * 是否根据因子分页（1 表示是 2 表示否 空表示否）
     */
    private String isGroupByAnalyzeItem;

    /**
     * 质控数据是否跟随原样（1 表示是 2 表示否 空表示否）
     */
    private String isZKWithYY;

    /**
     * 质控信息页（多个质控样）是否按项目进行分页（1 表示是 2 表示否 空表示否）
     */
    private String qcPagingByProject;

    /**
     * 质控页质控样数据页数同步时，是否算上原始记录页data区的page页数（（1：是  2：否  空表示否））
     */
    private String pageSyncWithData;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 排序id
     */
    private String sortId;

    /**
     * 空白填充
     */
    private Boolean blankFill;

    /**
     * 空白填充字符串
     */
    private String blankFillStr;
}
