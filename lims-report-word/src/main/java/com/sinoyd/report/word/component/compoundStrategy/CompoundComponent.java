package com.sinoyd.report.word.component.compoundStrategy;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.dto.DtoProject;
import com.sinoyd.report.dto.DtoReport;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.component.strategy.AbsReportComponent;
import com.sinoyd.report.word.groupType.factory.ReportGroupTypeFactory;
import com.sinoyd.report.word.utils.ReportGenerateUtils;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.sinoyd.report.constants.IReportGenerateConstants.*;

/**
 * 主表复合组件（标准版报告）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
@Component(IReportComponentStrategy.COMPOUND_COMPONENT)
@SuppressWarnings("unchecked")
public abstract class CompoundComponent extends AbsReportComponent {

    private ReportGroupTypeFactory goutTypeFactory;

    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        Map<String, Object> pageReportInfo = new HashMap<>();
        List<DtoSample> sampleList = (List<DtoSample>) reportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST);
        //根据配置的分页属性，对样品进行分组
        List<List<DtoSample>> pagedSamplesList = getPagedSamplesList(reportComponentVo.getPagePropertyList(), reportInfo, sampleList, 0);
        int idNumber = 1, dataId = 1;
        for (List<DtoSample> pagedSamples : pagedSamplesList) {
            pageReportInfo.put(IDataSourceKey.SAMPLE_DATA_LIST, pagedSamples);
            //对每个分组的样品渲染各个数据表
            dataId = initDataRow(reportComponentVo, reportInfo, pageReportInfo, ds, idNumber, dataId, false, false);
            idNumber++;
        }
    }

    @Override
    public DataTable initTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        return ReportGenerateUtils.initAddDataTable(Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID, "ReportCode", "ProjectCode", "EnterpriseName", "End"),
                reportComponentVo.getTableName(), ds);
    }

    @Override
    public DataTable initSourceTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        return null;
    }

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return null;
    }

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return null;
    }

    /**
     * 初始化表并设置表关系
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据集
     * @param ds                数据集
     */
    @Override
    public void initTableAndSetRelation(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        //初始化主数据表
        initTable(reportComponentVo, reportInfo, ds);
        //初始化子表
        for (ReportComponentVo sonTypeVo : reportComponentVo.getSonTypeVos()) {
            AbsReportComponent sonComponent = StringUtils.isNotEmpty(sonTypeVo.getModuleCode()) ? SpringContextAware.getBean(sonTypeVo.getModuleCode()) : null;
            if (sonComponent != null){
                //初始化表头数据表
                sonComponent.initTable(sonTypeVo, reportInfo, ds);
                if (StringUtils.isNotEmpty(sonTypeVo.getSourceTableName())){
                    sonComponent.initSourceTable(sonTypeVo, reportInfo, ds);
                }
            }
        }
        //设置表关系
        setTableRelation(reportComponentVo, ds);
    }


    /**
     * 设置表关系
     *
     * @param reportComponentVo 组件属性对象
     * @param ds                数据集
     */
    @Override
    public void setTableRelation(ReportComponentVo reportComponentVo, DataSet ds) {
        for (ReportComponentVo sonTypeVo : reportComponentVo.getSonTypeVos()) {
            setSonParentRelation(reportComponentVo.getTableName(), sonTypeVo.getTableName(), sonTypeVo.getSourceTableName(), ds);
        }
    }

    /**
     * 按照配置的分页属性列表，对样品进行分组处理
     *
     * @param pagePropertyList 分页属性列表
     * @param reportInfo       报告数据源集
     * @param sampleList       样品列表
     * @param idx              分页属性索引
     */
    public List<List<DtoSample>> getPagedSamplesList(List<String> pagePropertyList, Map<String, Object> reportInfo, List<DtoSample> sampleList, int idx) {
        List<List<DtoSample>> pagedSamplesList = new ArrayList<>();
        if (StringUtils.isNotEmpty(pagePropertyList) && idx < pagePropertyList.size()) {
            String pageProperty = pagePropertyList.get(idx);
            if (pageProperty.split("_").length < 3) {
                throw new BaseException("分页属性格式配置错误!");
            }
            Map<String, List<DtoSample>> smpMap = getSampleMapByProperty(sampleList, pageProperty, reportInfo);
            List<List<DtoSample>> sortSampleList = sortSampleMap(smpMap, pageProperty.split("_")[1]);
            idx++;
            for (List<DtoSample> samplesForProp : sortSampleList) {
                pagedSamplesList.addAll(getPagedSamplesList(pagePropertyList, reportInfo, samplesForProp, idx));
            }
        } else {
            //默认按照样品编号排序
            sampleList.sort(Comparator.comparing(DtoSample::getSampleCode));
            pagedSamplesList.add(sampleList);
        }
        return pagedSamplesList;
    }

    /**
     * 将给定样品按照配置的分页属性进行分组
     *
     * @param sampleList   样品列表
     * @param pageProperty 分页属性
     * @param reportInfo   属性对象
     * @return 分组后的样品
     */
    private Map<String, List<DtoSample>> getSampleMapByProperty(List<DtoSample> sampleList, String pageProperty, Map<String, Object> reportInfo) {
        List<String> propInfo = getProPInfo(pageProperty, reportInfo);
        //分页属性的名称
        String dataPropName = propInfo.get(0);
        //分页方式策略类的名称
        String groupTypeName = propInfo.get(1);
        return goutTypeFactory.groupSample(groupTypeName, sampleList, dataPropName);
    }

    /**
     * 对已经分组的样品进行组间排序
     *
     * @param smpMap      样品列表
     * @param dataSrcName 分页属性
     */
    public List<List<DtoSample>> sortSampleMap(Map<String, List<DtoSample>> smpMap, String dataSrcName) {
        List<List<DtoSample>> sortSampleList = new ArrayList<>();
        List<String> keyList = new ArrayList<>(smpMap.keySet());
        Collections.sort(keyList);
        for (String key : keyList) {
            sortSampleList.add(smpMap.get(key));
        }
        //按点位分页时，点位排序需要根据点位id对应的点位名称来排序
        if ("sampleFolderId".equals(dataSrcName)) {
            sortSampleList.sort(this::sort);
        }
        return sortSampleList;
    }

    /**
     * 样品列表排序
     * @param o1 样品1
     * @param o2 样品2
     */
    private int sort(List<DtoSample> o1, List<DtoSample> o2) {
        String folderName1 = o1.get(0).getRedFolderName();
        int idx = folderName1.lastIndexOf("(");
        if (idx != -1) {
            folderName1 = folderName1.substring(0, idx);
        }
        String folderName2 = o2.get(0).getRedFolderName();
        int idx2 = folderName2.lastIndexOf("(");
        if (idx2 != -1) {
            folderName2 = folderName2.substring(0, idx2);
        }
        return folderName1.compareTo(folderName2);
    }


    /**
     * 获取分组属性名称
     *
     * @param pageProperty 分页属性
     * @param reportInfo   报告数据源集
     * @return 分组属性名称, 分组类型策略名称
     */
    private List<String> getProPInfo(String pageProperty, Map<String, Object> reportInfo) {
        if (pageProperty.split("_").length < 3) {
            throw new BaseException("分页属性格式配置错误!");
        }
        //数据源名称
        String dataSrcName = pageProperty.split("_")[0];
        if (!reportInfo.containsKey(dataSrcName)) {
            throw new BaseException("分页属性数据源配置错误!");
        }
//        //获取数据源暂时只考虑list类型
//        List<Object> srcObjList = (List<Object>) reportInfo.get(dataSrcName);
        //分页属性名称
        return new ArrayList<>(Arrays.asList(pageProperty.split("_")[1], pageProperty.split("_")[2]));
    }

    /**
     * 设置父子表关系
     *
     * @param parentTableName 父表
     * @param tableName       当前表
     * @param sonTableName    子表
     * @param ds              报告数据集
     */
    public void setSonParentRelation(String parentTableName, String tableName, String sonTableName, DataSet ds) {
        DataTable parentTable = ds.getTables().get(parentTableName);
        if (StringUtils.isNotNull(parentTable)) {
            DataTable table = ds.getTables().get(tableName);
            if (StringUtils.isNotNull(table)) {
                writeReportInfo(ds, table, parentTable, IReportPlaceholder.TABLE_ID);
                DataTable sonTable = ds.getTables().get(sonTableName);
                if (StringUtils.isNotNull(sonTable)) {
                    writeReportInfo(ds, sonTable, table, IReportPlaceholder.ID);
                }
            }
        }
    }

    /**
     * 初始化一个最外层的dataSource表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo   报告数据
     * @param ds           报告数据集
     * @param idNumber     表索引
     * @param dataId       行数据表索引
     * @param endVal       换行符
     */
    public int initDataSrcRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds, int idNumber, int dataId, String endVal) {
        DtoProject project = (DtoProject) reportInfo.get(IDataSourceKey.PROJECT_DATA);
        DtoReport report = (DtoReport) reportInfo.get(IDataSourceKey.REPORT_DATA);
        DataTable dataTable = ds.getTables().get(reportComponentVo.getTableName());
        DataRow row = dataTable.newRow();
        row.set(IReportPlaceholder.ID, idNumber);
        row.set(IReportPlaceholder.TABLE_ID, dataId);
        row.set("ProjectCode", StringUtils.isNotNull(project) ? project.getProjectCode() : "");
        row.set("ReportCode", StringUtils.isNotNull(report) ? report.getCode() : "");
        row.set("EnterpriseName", "");
        row.set("End", endVal);
        dataTable.getRows().add(row);
        return dataId;
    }

    @Autowired
    public void setGoutTypeFactory(ReportGroupTypeFactory goutTypeFactory) {
        this.goutTypeFactory = goutTypeFactory;
    }
}
