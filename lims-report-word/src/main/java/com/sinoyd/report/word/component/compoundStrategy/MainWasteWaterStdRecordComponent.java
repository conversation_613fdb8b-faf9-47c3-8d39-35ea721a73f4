package com.sinoyd.report.word.component.compoundStrategy;

import com.aspose.words.net.System.Data.DataRowCollection;
import com.aspose.words.net.System.Data.DataSet;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IReportGenerateConstants;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.component.strategy.AbsReportComponent;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常规水主表组件（标准版报告）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/22
 */
@Component(IReportGenerateConstants.IReportComponentStrategy.MAIN_WASTE_WATER_STD_DATA)
@SuppressWarnings("unchecked")
public class MainWasteWaterStdRecordComponent extends CompoundComponent {

    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        List<DtoSample> sampleList = (List<DtoSample>) reportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST);
        List<DtoAnalyseData> anaDataList = (List<DtoAnalyseData>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        //获取配置的分页属性信息列表
        List<String> pagePropertyList = reportComponentVo.getPagePropertyList();
        //根据配置的分页属性，对样品进行分组
        List<List<DtoSample>> pagedSamplesList = getPagedSamplesList(pagePropertyList, reportInfo, sampleList, 0);
        int idNumber = 1, dataId = 1;
        reportInfo.put("pageCount", pagedSamplesList.size());
        reportInfo.put("pageNum", 0);
        for (int k = 0; k < pagedSamplesList.size(); k++) {
            reportInfo.put("pageNum", k);
            List<DtoSample> pagedSamples = pagedSamplesList.get(k);
            Map<String, Object> pageReportInfo = new HashMap<>();
            pageReportInfo.put(IDataSourceKey.SAMPLE_DATA_LIST, pagedSamples);
            pageReportInfo.put(IDataSourceKey.ANALYSE_DATA_LIST, anaDataList);
            //对每个分组的样品渲染各个数据表
            dataId = initDataRow(reportComponentVo, reportInfo, pageReportInfo, ds, idNumber, dataId, false, false);
            idNumber++;
        }
        DataRowCollection dataSrcRows = ds.getTables().get(reportComponentVo.getTableName()).getRows();
        dataSrcRows.get(dataSrcRows.getCount() - 1).set("End", "");
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    @Override
    public int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, Map<String, Object> pageReportInfo, DataSet ds, int idNumber, int dataId, boolean filterFlag, boolean evaFlag) {
        //初始化一个最外层的dataSource表
        initDataSrcRow(reportComponentVo, reportInfo, ds, idNumber, dataId, "\f");
        //初始化子表数据
        for (ReportComponentVo sonTypeVo : reportComponentVo.getSonTypeVos()) {
            AbsReportComponent sonTypeComponent = SpringContextAware.getBean(sonTypeVo.getModuleCode());
            dataId = sonTypeComponent.initDataRow(sonTypeVo, reportInfo, pageReportInfo, ds, idNumber, dataId, true, false);
        }
        return dataId;
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    @Override
    public List<String> getDataSourceStrategy() {
        return new ArrayList<>();
    }
}
