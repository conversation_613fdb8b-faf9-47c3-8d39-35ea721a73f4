package com.sinoyd.report.word.component.factory;

import com.aspose.words.Document;
import com.aspose.words.net.System.Data.DataSet;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.word.component.strategy.AbsReportComponent;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 报告组件生成工厂类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@Component
public class ComponentGenerateFactory {

    private final Map<String, AbsReportComponent> strategyMap = new ConcurrentHashMap<>();

    @Autowired
    public ComponentGenerateFactory(Map<String, AbsReportComponent> strategyMap) {
        this.strategyMap.putAll(strategyMap);
    }

    /**
     * 绑定质控数据
     *
     * @param beanName          组件Bean名称
     * @param reportComponentVo 报表类型
     * @param reportInfo        数据源
     * @param ds                数据
     */
    public void createTable(String beanName, ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        this.strategyMap.get(beanName).createTable(reportComponentVo, reportInfo, ds);
    }


    /**
     * 填充组件所需数据源策略集合
     *
     * @param reportComponentVo 组件对象
     * @param strategyList      数据源策略集合
     */
    public void fillingDSStrategy(ReportComponentVo reportComponentVo, List<String> strategyList) {
        AbsReportComponent component = this.strategyMap.get(reportComponentVo.getModuleCode());
        strategyList.addAll(component.getDataSourceStrategy());
        if (reportComponentVo.getIsCompound() && StringUtils.isNotEmpty(reportComponentVo.getSonTypeVos())) {
            for (ReportComponentVo sonTypeVo : reportComponentVo.getSonTypeVos()) {
                AbsReportComponent sonComponent = this.strategyMap.get(sonTypeVo.getModuleCode());
                strategyList.addAll(sonComponent.getDataSourceStrategy());
            }
        }
    }

    /**
     * 合并单元格操作
     *
     * @param reportComponentVo 报表类型
     * @param doc               报告对象
     * @param ds                报告表集合
     */
    public void mergeCells(ReportComponentVo reportComponentVo, Document doc, DataSet ds) {
        this.strategyMap.get(reportComponentVo.getModuleCode()).mergeCells(reportComponentVo, doc, ds);
    }

    /**
     * 科学技术法转换等操作
     *
     * @param reportComponentVo 报表类型
     * @param doc               报告对象
     * @param ds                报告表集合
     */
    public void sciData(ReportComponentVo reportComponentVo, Document doc, DataSet ds) {
        this.strategyMap.get(reportComponentVo.getModuleCode()).setScientificNotation(reportComponentVo, doc, ds);
    }
}
