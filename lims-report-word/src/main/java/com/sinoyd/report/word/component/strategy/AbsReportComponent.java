package com.sinoyd.report.word.component.strategy;

import com.aspose.words.Document;
import com.aspose.words.net.System.Data.DataRelation;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.report.word.vo.ReportComponentVo;

import java.util.List;
import java.util.Map;

import static com.sinoyd.report.word.utils.ReportGenerateUtils.initAddDataTable;

/**
 * 报告组件生成基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/04
 */
public abstract class AbsReportComponent {
    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    public abstract void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds);

    /**
     * 初始化表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据集
     * @param ds                数据集
     */
    public DataTable initTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        return initAddDataTable(getTableColumnList(reportComponentVo, reportInfo), reportComponentVo.getTableName(), ds);
    }

    /**
     * 初始化样品数据行表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据集
     * @param ds                数据集
     * @return 样品数据行表
     */
    public DataTable initSourceTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        return initAddDataTable(getSourceColumnList(reportComponentVo, reportInfo), reportComponentVo.getSourceTableName(), ds);
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    public abstract int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo,
                                    Map<String, Object> pageReportInfo, DataSet ds,
                                    int idNumber, int dataId, boolean filterFlag, boolean evaFlag);

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    public abstract List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo);

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    public abstract List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo);

    /**
     * 合并单元格操作
     *
     * @param reportComponentVo 报表组件VO
     * @param doc               报告对象
     * @param ds                报告表集合
     */
    public void mergeCells(ReportComponentVo reportComponentVo, Document doc, DataSet ds) {

    }

    /**
     * 设置科学计数法操作
     *
     * @param reportComponentVo 报表组件VO
     * @param doc               报告对象
     * @param ds                报告表集合
     */
    public void setScientificNotation(ReportComponentVo reportComponentVo, Document doc, DataSet ds) {

    }

    /**
     * 初始化表并设置表关系
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据集
     * @param ds                数据集
     */
    public void initTableAndSetRelation(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        //初始化检测数据表
        initTable(reportComponentVo, reportInfo, ds);
        //初始化样品数据表
        initSourceTable(reportComponentVo, reportInfo, ds);
        //设置表关系
        setTableRelation(reportComponentVo, ds);
    }

    /**
     * 设置表关系
     *
     * @param reportComponentVo 组件属性对象
     * @param ds                数据集
     */
    public void setTableRelation(ReportComponentVo reportComponentVo, DataSet ds) {
        DataTable sourceTable = ds.getTables().get(reportComponentVo.getSourceTableName());
        DataTable bodyTable = ds.getTables().get(reportComponentVo.getTableName());
        if (sourceTable != null && bodyTable != null) {
            writeReportInfo(ds, sourceTable, bodyTable);
        }
    }

    /**
     * 表关联方法
     *
     * @param ds             dataSet
     * @param mainTable      主表
     * @param secondaryTable 次表
     * @param columnName     列名
     */
    protected void writeReportInfo(DataSet ds, DataTable mainTable, DataTable secondaryTable, String columnName) {
        ds.getRelations().add(new DataRelation("result", secondaryTable.getColumns().get(columnName), mainTable.getColumns().get(columnName)));
    }

    /**
     * 表关联方法
     *
     * @param ds             dataSet
     * @param mainTable      主表
     * @param secondaryTable 次表
     */
    protected void writeReportInfo(DataSet ds, DataTable mainTable, DataTable secondaryTable) {
        ds.getRelations().add(new DataRelation("result", secondaryTable.getColumns().get("Id"), mainTable.getColumns().get("Id")));
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    public abstract List<String> getDataSourceStrategy();
}
