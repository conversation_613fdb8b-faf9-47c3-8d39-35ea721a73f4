package com.sinoyd.report.word.component.strategy;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.word.vo.ReportComponentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.report.constants.IReportGenerateConstants.*;
import static com.sinoyd.report.word.utils.ReportGenerateUtils.*;

/**
 * 报告基础信息组件赋值策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
@Slf4j
@Component(IReportComponentStrategy.REPORT_BASIC)
@SuppressWarnings("unchecked")
public class BasicRecordComponent extends AbsReportComponent {

    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        Map<String, Object> pageReportInfo = new HashMap<>();
        initDataRow(reportComponentVo, reportInfo, pageReportInfo, ds, 1, 1, false, false);
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    @Override
    public int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, Map<String, Object> pageReportInfo, DataSet ds, int idNumber, int dataId, boolean filterFlag, boolean evaFlag) {
        //获取到当前表数据
        DataTable dtTable = ds.getTables().get(reportComponentVo.getTableName());
        //新增数据行
        DataRow dr = dtTable.newRow();
        //赋值基础信息数据行数据
        writeInfoDataRow(reportInfo, dr);
        //分析数据
        writeAnalyzeData(reportInfo, dr);
        //样品及数据
        writeSampleDataRow(reportInfo, dr);
        //参数数据
        writeParamsDataRow(reportInfo, dr, dtTable);
        //添加行数据
        dtTable.getRows().add(dr);
        return 1;
    }

    /**
     * 初始化样品数据行表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据集
     * @param ds                数据集
     * @return 样品数据行表
     */
    @Override
    public DataTable initSourceTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        return null;
    }

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        List<DtoParamsData> paramsDataList = (List<DtoParamsData>) reportInfo.get(IDataSourceKey.PARAMS_DATA_LIST);
        List<String> fldList = new ArrayList<>(Arrays.asList(
                IReportPlaceholder.ProjectInfo.CUSTOMER_NAME, IReportPlaceholder.ProjectInfo.PROJECT_NAME, IReportPlaceholder.ProjectInfo.CAPITAL_DATE,
                IReportPlaceholder.ProjectInfo.INSPECTED_ADDRESS, IReportPlaceholder.ProjectInfo.REGISTER_TIME,
                IReportPlaceholder.ProjectInfo.INSPECTED_ENT, IReportPlaceholder.ProjectInfo.CUSTOMER_ADDRESS,
                IReportPlaceholder.ProjectInfo.LINK_PHONE, IReportPlaceholder.ProjectInfo.INSPECTED_LINK_PHONE, IReportPlaceholder.ProjectInfo.INSPECTED_LINK_MAN,
                IReportPlaceholder.ProjectInfo.PROJECT_TYPE, IReportPlaceholder.ProjectInfo.REPORT_CODE,
                IReportPlaceholder.ProjectInfo.MONITOR_PURPOSE, IReportPlaceholder.ProjectInfo.SIGN_DATE,
                IReportPlaceholder.SampleInfo.SENDER, IReportPlaceholder.SampleInfo.SAMPLE_TYPE, IReportPlaceholder.SampleInfo.SAMPLE_STATUS_DESCRIBE,
                IReportPlaceholder.SampleInfo.SAMPLING_TIME, IReportPlaceholder.SampleInfo.ANALYZE_TIME, IReportPlaceholder.SampleInfo.SAMPLING_PERSON,
                IReportPlaceholder.SampleInfo.ANALYZE_ITEM, IReportPlaceholder.SampleInfo.ANALYZE_METHOD, IReportPlaceholder.SampleInfo.INSTR_MODEL_NAME_LIST_FOR_ITEM,
                IReportPlaceholder.ProjectInfo.REPORT_DATE, IReportPlaceholder.ProjectInfo.REPORT_DATE_YEAR,
                IReportPlaceholder.ProjectInfo.REPORT_DATE_CN, IReportPlaceholder.ProjectInfo.REPORT_DATE_CN_BIG, IReportPlaceholder.ProjectInfo.PROJECT_CODE,
                IReportPlaceholder.ProjectInfo.ELECTRONIC_CUSTOMER_NAME, IReportPlaceholder.ProjectInfo.ELECTRONIC_CUSTOMER_ADDRESS, IReportPlaceholder.ProjectInfo.ELECTRONIC_INSPECTED_ADDRESS,
                IReportPlaceholder.ProjectInfo.ELECTRONIC_INSPECTED_ENT, IReportPlaceholder.ProjectInfo.ELECTRONIC_SYSTEM_CODE, IReportPlaceholder.ProjectInfo.ELECTRONIC_PROJECT_NAME,
                IReportPlaceholder.SystemInfo.ENTERPRISE_NAME,IReportPlaceholder.SystemInfo.ENTERPRISE_ENGLISH,IReportPlaceholder.SystemInfo.ENTERPRISE_ADDRESS,
                IReportPlaceholder.SystemInfo.ENTERPRISE_PHONE,IReportPlaceholder.SystemInfo.ENTERPRISE_ZIP_CODE,IReportPlaceholder.SystemInfo.SYSTEM_CODE));
        for (DtoParamsData paramsData : paramsDataList) {
            if (!fldList.contains(paramsData.getParamsConfigName())) {
                fldList.add(paramsData.getParamsConfigName());
            }
        }
        return fldList;
    }

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return null;
    }

    /**
     * 设置分析时间区间
     *
     * @param reportInfo 数据集合字典
     * @param dr         数据表行对象
     */
    public static void writeAnalyzeData(Map<String, Object> reportInfo, DataRow dr) {
        //分析数据
        List<DtoAnalyseData> anaList = (List<DtoAnalyseData>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        List<String> anaItemList = anaList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
        List<String> anaMethodList = anaList.stream().map(DtoAnalyseData::getRedAnalyzeMethodName).distinct().collect(Collectors.toList());
        //分析时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        List<String> analyseTimeList = new ArrayList<>();
        for (Date anaTime : anaList.stream().map(DtoAnalyseData::getAnalyzeTime).distinct().collect(Collectors.toList())) {
            analyseTimeList.add(sdf.format(anaTime));
        }
        String anaTimeMin = Collections.min(analyseTimeList);
        String anaTimeMax = Collections.max(analyseTimeList);
        if (anaTimeMin.equals(anaTimeMax)) {
            dr.set(IReportPlaceholder.SampleInfo.ANALYZE_TIME, anaTimeMin);
        } else {
            dr.set(IReportPlaceholder.SampleInfo.ANALYZE_TIME, anaTimeMin + "-" + anaTimeMax);
        }
        //分析项目
        String analyzeItem = String.join(",", anaItemList);
        dr.set(IReportPlaceholder.SampleInfo.ANALYZE_ITEM, analyzeItem);
        //分析方法
        String method = String.join(",", anaMethodList);
        dr.set(IReportPlaceholder.SampleInfo.ANALYZE_METHOD, method);
    }


    /**
     * 赋值样品数据行
     *
     * @param reportInfo 数据集合字典
     * @param dr         数据行
     */
    private void writeSampleDataRow(Map<String, Object> reportInfo, DataRow dr) {
        List<DtoInstrumentUseRecord> instrumentUseList = (List<DtoInstrumentUseRecord>) reportInfo.get(IDataSourceKey.INSTRUMENT_USE_RECORD_DATA_LIST);
        List<DtoSample> sampleList = (List<DtoSample>) reportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST);
        //送样人
        List<String> senderList = sampleList.stream().map(DtoSample::getSenderName).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        String sender = String.join(",", senderList);
        dr.set(IReportPlaceholder.SampleInfo.SENDER, sender);
        //检测类型
        List<String> sampleTypeNameList = sampleList.stream().map(DtoSample::getSampleTypeName).distinct().collect(Collectors.toList());
        String sampleType = String.join(",", sampleTypeNameList);
        dr.set(IReportPlaceholder.SampleInfo.SAMPLE_TYPE, sampleType);
        //样品特征
        List<String> sampleExplainList = sampleList.stream().map(DtoSample::getSampleExplain)
                .filter(StringUtils::isNotNullAndEmpty).distinct().collect(Collectors.toList());
        String statusDes = String.join(",", sampleExplainList);
        dr.set(IReportPlaceholder.SampleInfo.SAMPLE_STATUS_DESCRIBE, statusDes);
        dr.set(IReportPlaceholder.SampleInfo.SAMPLING_TIME, getSamplingTime(sampleList, "yyyy.MM.dd", "-"));
        //TODO:采样人
        String samplingPersonList = "采样人TODO";
        dr.set(IReportPlaceholder.SampleInfo.SAMPLING_PERSON, samplingPersonList);
        //仪器名称
        List<String> instrNameList = StringUtils.isNotNull(instrumentUseList) ?
                instrumentUseList.stream().map(DtoInstrumentUseRecord::getInstrumentName).distinct().collect(Collectors.toList()) :
                new ArrayList<>();
        String instModelName = String.join(",", instrNameList);
        dr.set(IReportPlaceholder.SampleInfo.INSTR_MODEL_NAME_LIST_FOR_ITEM, instModelName);

    }

    /**
     * 赋值参数数据行
     *
     * @param reportInfo 数据集合字典
     * @param dr         数据行
     * @param dataTable  数据表
     */
    private void writeParamsDataRow(Map<String, Object> reportInfo, DataRow dr, DataTable dataTable) {
        List<DtoParamsData> paramsDataList = (List<DtoParamsData>) reportInfo.get(IDataSourceKey.PARAMS_DATA_LIST);
        if (StringUtils.isNotEmpty(paramsDataList)) {
            for (DtoParamsData paramsData : paramsDataList) {
                if (StringUtils.isNotNull(paramsData.getParamsConfigName()) && dataTable.getColumns().contains(paramsData.getParamsConfigName())) {
                    dr.set(paramsData.getParamsConfigName(), paramsData.getParamsValue());
                }
            }
        }
    }

    /**
     * 处理基础信息数据行
     *
     * @param reportInfo 数据集合字典
     * @param dr         数据行
     */
    private void writeInfoDataRow(Map<String, Object> reportInfo, DataRow dr) {
        //基础信息数据
        DtoProject project = (DtoProject) reportInfo.getOrDefault(IDataSourceKey.PROJECT_DATA, new DtoProject());
        DtoReport report = (DtoReport) reportInfo.getOrDefault(IDataSourceKey.REPORT_DATA, new DtoReport());
        DtoReportBaseInfo reportBaseInfo = (DtoReportBaseInfo) reportInfo.getOrDefault(IDataSourceKey.REPORT_BASE_INFO_DATA, new DtoReportBaseInfo());
        //当前日期
        Date dateTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String capDate = getCapDate(dateTime);
        dr.set(IReportPlaceholder.ProjectInfo.CAPITAL_DATE, capDate);
        dr.set(IReportPlaceholder.ProjectInfo.CREATE_DATE, formatter.format(dateTime));
        //报告委托单位数据
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_CUSTOMER_NAME, StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo.getCustomerName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_PROJECT_NAME, StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo.getProjectName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_SYSTEM_CODE, StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo.getSystemCode() : "");
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_INSPECTED_ADDRESS, StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo.getInspectedAddress() : "");
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_CUSTOMER_ADDRESS, StringUtils.isNull(reportBaseInfo) ? "" : StringUtils.isNotEmpty(reportBaseInfo.getCustomerAddress())
                ? reportBaseInfo.getCustomerAddress().replace("\n", " ") : "");
        dr.set(IReportPlaceholder.ProjectInfo.ELECTRONIC_INSPECTED_ENT, StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo.getInspectedEnt() : "");
        //项目受检单位数据
        dr.set(IReportPlaceholder.ProjectInfo.REGISTER_TIME, StringUtils.isNotNull(project) ? project.getInceptTime() : "");
        dr.set(IReportPlaceholder.ProjectInfo.INSPECTED_ENT, StringUtils.isNotNull(project) ? project.getInspectedEnt() : "");
        dr.set(IReportPlaceholder.ProjectInfo.INSPECTED_LINK_MAN, StringUtils.isNotNull(project) ? project.getInspectedLinkMan() : "");
        dr.set(IReportPlaceholder.ProjectInfo.INSPECTED_LINK_PHONE, StringUtils.isNotNull(project) ? project.getInspectedLinkPhone() : "");
        dr.set(IReportPlaceholder.ProjectInfo.INSPECTED_ADDRESS, StringUtils.isNotNull(project) ? project.getInspectedAddress() : "");
        //项目委托单位数据
        dr.set(IReportPlaceholder.ProjectInfo.CUSTOMER_NAME, StringUtils.isNotNull(project) ? project.getCustomerName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.LEADER, StringUtils.isNotNull(project) ? project.getLeader() : "");
        dr.set(IReportPlaceholder.ProjectInfo.LINK_MAN, StringUtils.isNotNull(project) ? project.getLinkMan() : "");
        dr.set(IReportPlaceholder.ProjectInfo.LINK_PHONE, StringUtils.isNotNull(project) ? project.getLinkPhone() : "");
        dr.set(IReportPlaceholder.ProjectInfo.CUSTOMER_ADDRESS, StringUtils.isNull(project) ? "" : StringUtils.isNotEmpty(project.getCustomerAddress()) ? project.getCustomerAddress().replace("\n", " ") : "");
        //项目基础信息数据
        dr.set(IReportPlaceholder.ProjectInfo.PROJECT_NAME, StringUtils.isNotNull(project) ? project.getProjectName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.PROJECT_TYPE, StringUtils.isNotNull(project) ? project.getProjectTypeName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_MAKER, StringUtils.isNotNull(project) ? project.getReportMakerName() : "");
        dr.set(IReportPlaceholder.ProjectInfo.MONITOR_PURPOSE, StringUtils.isNotNull(project) ? project.getMonitorPurp() : "");
        dr.set(IReportPlaceholder.ProjectInfo.PROJECT_CODE, StringUtils.isNotNull(project) ? project.getProjectCode() : "");
        //报告基础信息数据
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_CODE, StringUtils.isNull(report) ? "" : report.getCode());
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_DATE, StringUtils.isNull(report) ? "" : StringUtils.isNotNull(report.getReportDate()) ? DateUtil.dateToString(report.getReportDate(), "yyyy.MM.dd") : "");
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_DATE_YEAR, StringUtils.isNull(report) ? "" : StringUtils.isNotNull(report.getReportDate()) ? DateUtil.dateToString(report.getReportDate(), DateUtil.YEAR) : "");
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_DATE_CN, StringUtils.isNull(report) ? "" : StringUtils.isNotNull(report.getReportDate()) ? DateUtil.dateToString(report.getReportDate(), DateUtil.YEAR_ZH_CN) : "");
        dr.set(IReportPlaceholder.ProjectInfo.REPORT_DATE_CN_BIG, StringUtils.isNull(report) ? "" : StringUtils.isNotNull(report.getReportDate()) ? date2Chinese(report.getReportDate()) : "");
        dr.set(IReportPlaceholder.ProjectInfo.SIGN_DATE, report.getSignDate());
        //TODO: 系统基础信息 SystemConfig
        dr.set(IReportPlaceholder.SystemInfo.ENTERPRISE_NAME, "江苏远大信息股份有限公司");
        dr.set(IReportPlaceholder.SystemInfo.ENTERPRISE_ENGLISH, "Sinoyd");
        dr.set(IReportPlaceholder.SystemInfo.ENTERPRISE_ADDRESS, "沙洲西路122号天霸商务馆A幢4F");
        dr.set(IReportPlaceholder.SystemInfo.ENTERPRISE_ZIP_CODE, "215600");
        dr.set(IReportPlaceholder.SystemInfo.ENTERPRISE_PHONE, "0558-430-3411");
        dr.set(IReportPlaceholder.SystemInfo.SYSTEM_CODE, "LIMS6.0Report");
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    @Override
    public List<String> getDataSourceStrategy() {
        List<String> strategyList= new ArrayList<>();
        strategyList.add(IDataSourceStrategy.PROJECT);
        strategyList.add(IDataSourceStrategy.REPORT);
        return strategyList;
    }
}
