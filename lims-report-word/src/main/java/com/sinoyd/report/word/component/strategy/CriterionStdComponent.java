package com.sinoyd.report.word.component.strategy;


import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoInstrumentUseRecord;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.report.constants.IReportGenerateConstants.IReportComponentStrategy;
import static com.sinoyd.report.constants.IReportGenerateConstants.IReportPlaceholder;


/**
 * 标准版报告技术说明组件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/03
 */
@Component(IReportComponentStrategy.CRITERION_STD_DATA)
@SuppressWarnings("unchecked")
public class CriterionStdComponent extends AbsReportComponent {
    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        initDataRow(reportComponentVo, reportInfo, new HashMap<>(), ds, 1, 1, false, false);
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    @Override
    public int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, Map<String, Object> pageReportInfo, DataSet ds, int idNumber, int dataId, boolean filterFlag, boolean evaFlag) {
        DataTable dtTable = ds.getTables().get(reportComponentVo.getTableName());
        DataTable dtSource = ds.getTables().get(reportComponentVo.getSourceTableName());
        //分析数据
        List<DtoAnalyseData> analyseDataList = (List<DtoAnalyseData>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        List<String> testIds = (List<String>) reportInfo.get(IDataSourceKey.TEST_IDS);
        Map<String, DtoAnalyseData> testId2AnaData = new HashMap<>();
        for (String testId : testIds) {
            Optional<DtoAnalyseData> anaDataOp = analyseDataList.stream().filter(p -> testId.equals(p.getTestId())).findFirst();
            anaDataOp.ifPresent(p->testId2AnaData.put(testId, p));
        }
        //仪器使用记录数据
        List<DtoInstrumentUseRecord> useRecordList = (List<DtoInstrumentUseRecord>) reportInfo.get(IDataSourceKey.INSTRUMENT_USE_RECORD_DATA_LIST);
        //获取测试项目关联仪器数据
        Map<String, List<DtoInstrumentUseRecord>> testId2InstUseRecordMap = getInstUseRecordMap(useRecordList, testId2AnaData);
        //获取技术说明数据行集合
        List<List<String>> assembleInstInfoList = assembleInstInfoList(testId2InstUseRecordMap, analyseDataList, useRecordList, testIds);
        return initInstInfoTable(assembleInstInfoList, dtTable, dtSource, reportComponentVo.getSampleCount(), idNumber, dataId);
    }

    /**
     * 组装仪器数据信息表
     *
     * @param rowList           表行数据对象列表
     * @param dtRst             仪器数据表对
     * @param dtCriterionSource 仪器数据行对象
     * @param sampleCnt         每页条数
     */
    private int initInstInfoTable(List<List<String>> rowList, DataTable dtRst, DataTable dtCriterionSource, int sampleCnt, int idNumber, int dataId) {
        if (StringUtils.isNotEmpty(rowList)) {
            for (int i = 0; i < rowList.size(); i += sampleCnt) {
                List<List<String>> pageRowList = rowList.stream().skip(i).limit(sampleCnt).collect(Collectors.toList());
                DataRow drRst = dtRst.newRow();
                drRst.set(IReportPlaceholder.TABLE_ID, idNumber);
                drRst.set(IReportPlaceholder.ID, dataId);
                drRst.set("End", i + sampleCnt < rowList.size() ? "\f" : "");
                dtRst.getRows().add(drRst);
                for (List<String> row : pageRowList) {
                    DataRow dataRow = dtCriterionSource.newRow();
                    dataRow.set(IReportPlaceholder.ID, dataId);
                    dataRow.set(IReportPlaceholder.TABLE_ID, idNumber);
                    dataRow.set("TestName", row.get(0));
                    dataRow.set("AnalyseMethod", row.get(1));
                    dataRow.set("LoadType", row.get(2));
                    dataRow.set("InstrumentNameModel", row.get(3));
                    dataRow.set("InstrumentCode", row.get(4));
                    dtCriterionSource.getRows().add(dataRow);
                }
                dataId++;
            }
        }
        return dataId;
    }

    /**
     * 组装仪器数据信息列表
     *
     * @param testId2InstUseRcdInfoListMap 每个测试项目id对应的仪器使用记录信息对象列表
     * @param analyseDataList              所有的分析数据
     * @param instUseRcdInfoList           仪器使用记录数据
     * @return 仪器数据信息列表
     */
    private List<List<String>> assembleInstInfoList(Map<String, List<DtoInstrumentUseRecord>> testId2InstUseRcdInfoListMap,
                                                    List<DtoAnalyseData> analyseDataList,
                                                    List<DtoInstrumentUseRecord> instUseRcdInfoList,
                                                    List<String> testIds) {
        List<List<String>> rowList = new ArrayList<>();
        //添加一个默认的采样方法
        //过滤出所有采样仪器
        List<String> samplingInstNameList = instUseRcdInfoList.stream().filter(p -> new Integer(2).equals(p.getObjectType()))
                .map(DtoInstrumentUseRecord::getInstrumentName).distinct().collect(Collectors.toList());
        String samplingInstNames = StringUtils.isNotEmpty(samplingInstNameList) ? String.join("，", samplingInstNameList) : "-";
        rowList.add(Arrays.asList("采样", "—", "—", samplingInstNames, "—"));
        for (String testId : testIds) {
            Optional<DtoAnalyseData> anaDataOp = analyseDataList.stream().filter(p -> testId.equals(p.getTestId())).findFirst();
            if (anaDataOp.isPresent()) {
                List<DtoInstrumentUseRecord> loopInfoList = testId2InstUseRcdInfoListMap.getOrDefault(testId, new ArrayList<>());
                loopInfoList.sort(Comparator.comparing(DtoInstrumentUseRecord::getInstrumentName));
                List<String> insNames = loopInfoList.stream().map(p -> p.getInstrumentName() + "-" + p.getInstrumentModel()).collect(Collectors.toList());
                List<String> insSerialNoList = loopInfoList.stream().map(DtoInstrumentUseRecord::getInstrumentSerialNo).collect(Collectors.toList());
                List<String> row = new ArrayList<>();
                row.add(anaDataOp.get().getRedAnalyzeItemName());
                row.add(anaDataOp.get().getRedAnalyzeMethodName());
                row.add("玻璃瓶运输");
                row.add(StringUtils.isNotEmpty(insNames) ? String.join(";", insNames) : "—");
                row.add(StringUtils.isNotEmpty(insSerialNoList) ? String.join(";", insSerialNoList) : "—");
                rowList.add(row);
            }
        }
        return rowList;
    }

    /**
     * 获取所有仪器使用记录,每个 testId 对应多个仪器使用记录（仪器名称，仪器型号，仪器出厂编号）
     *
     * @param instUseRcdInfoList 仪器使用记录信息列表
     * @param analyseDataMap     分析数据映射
     * @return 样品dto对象列表
     */
    public static Map<String, List<DtoInstrumentUseRecord>> getInstUseRecordMap(List<DtoInstrumentUseRecord> instUseRcdInfoList, Map<String, DtoAnalyseData> analyseDataMap) {
        Map<String, List<DtoInstrumentUseRecord>> testId2InstUseRcdInfoListMap = new HashMap<>();
        //每个testId对应的仪器id列表映射关系，用于去除同一个testId下重复的仪器使用记录
        Map<String, List<String>> testId2InstIdListMap = new HashMap<>();
        for (DtoInstrumentUseRecord dto : instUseRcdInfoList) {
            if (StringUtils.isNotEmpty(dto.getTestIds())) {
                String[] loopTestIds = dto.getTestIds().split(",");
                for (String testId : loopTestIds) {
                    DtoAnalyseData analyseData = analyseDataMap.getOrDefault(testId, null);
                    if (StringUtils.isNotNull(analyseData)) {
                        //如果是实验室指标则只获取实验室仪器,如果是现场指标则获取现场分析仪器
                        if ((analyseData.getIsCompleteField() && new Integer(4).equals(dto.getObjectType()))
                                || (!analyseData.getIsCompleteField() && new Integer(1).equals(dto.getObjectType()))) {
                            if (StringUtils.isNotEmpty(testId2InstUseRcdInfoListMap.get(testId))) {
                                if (!testId2InstIdListMap.get(testId).contains(dto.getInstrumentId())) {
                                    testId2InstUseRcdInfoListMap.get(testId).add(dto);
                                    testId2InstIdListMap.get(testId).add(dto.getInstrumentId());
                                }
                            } else {
                                List<DtoInstrumentUseRecord> tmpList = new ArrayList<>(Collections.singletonList(dto));
                                testId2InstUseRcdInfoListMap.put(testId, tmpList);
                                List<String> instIdList = new ArrayList<>(Collections.singletonList(dto.getInstrumentId()));
                                testId2InstIdListMap.put(testId, instIdList);
                            }
                        }
                    }
                }
            }
        }
        //每个测试项目id 的仪器使用记录按照仪器名称，仪器型号，仪器出厂编号排序
        for (Map.Entry<String, List<DtoInstrumentUseRecord>> entry : testId2InstUseRcdInfoListMap.entrySet()) {
            List<DtoInstrumentUseRecord> loopList = entry.getValue();
            loopList.sort(Comparator.comparing(DtoInstrumentUseRecord::getInstrumentName));
        }
        return testId2InstUseRcdInfoListMap;
    }

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return new ArrayList<>(Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID, "End"));
    }

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return new ArrayList<>(Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID,
                "TestName", "AnalyseMethod", "LoadType", "InstrumentNameModel", "InstrumentCode"));
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    @Override
    public List<String> getDataSourceStrategy() {
        List<String> strategy = new ArrayList<>();
        strategy.add(IDataSourceStrategy.ANALYSE_DATA);
        strategy.add(IDataSourceStrategy.PROJECT);
        strategy.add(IDataSourceStrategy.INS_USE_RECORD);
        return strategy;
    }
}
