package com.sinoyd.report.word.component.strategy;

import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoProject;
import com.sinoyd.report.dto.DtoReportBaseInfo;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.utils.ReportGenerateUtils;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.sinoyd.report.constants.IReportGenerateConstants.IReportComponentStrategy;
import static com.sinoyd.report.constants.IReportGenerateConstants.IReportPlaceholder;

/**
 * 废水检测表头数据组件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/29
 */
@Component(IReportComponentStrategy.HEAD_WATER_STD_TABLE)
public class HeadWaterStdComponent extends AbsReportComponent {
    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        Map<String, Object> pageReportInfo = new HashMap<>();
        pageReportInfo.put(IDataSourceKey.SAMPLE_DATA_LIST, reportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST));
        //初始化行对象
        initDataRow(reportComponentVo, reportInfo, pageReportInfo, ds, 1, 1, false, false);
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    @Override
    @SuppressWarnings("unchecked")
    public int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, Map<String, Object> pageReportInfo, DataSet ds, int idNumber, int dataId, boolean filterFlag, boolean evaFlag) {
        DataTable dtTable = ds.getTables().get(reportComponentVo.getTableName());
        List<DtoAnalyseData> analyseDataList = (List<DtoAnalyseData>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        List<DtoSample> sampleList = (List<DtoSample>) reportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST);
        DtoReportBaseInfo reportBaseInfo = (DtoReportBaseInfo) reportInfo.get(IDataSourceKey.REPORT_BASE_INFO_DATA);
        reportBaseInfo = StringUtils.isNotNull(reportBaseInfo) ? reportBaseInfo : new DtoReportBaseInfo();
        DtoProject project = (DtoProject) reportInfo.get(IDataSourceKey.PROJECT_DATA);
        //采样日期
        String samplingTime = ReportGenerateUtils.getSamplingTime(sampleList, "yyyy-MM-dd", "~");

        String receiveTime = StringUtils.isNotEmpty(sampleList) ? (StringUtils.isNotNull(sampleList.get(0).getReceiveSampleDate())
                ? DateUtil.dateToString(sampleList.get(0).getReceiveSampleDate(), DateUtil.YEAR_ZH_CN) : "") : "";
        //分析日期
        List<Date> analyzeTimeList = ReportGenerateUtils.getAnalyzeFinishTimeList(analyseDataList);
        String analyzeTime = ReportGenerateUtils.getTimeRange(analyzeTimeList, "yyyy-MM-dd", "~", "");
        ReportGenerateUtils.initAddRow(dtTable, getTableColumnList(reportComponentVo, reportInfo), Arrays.asList(dataId, idNumber, project.getInspectedEnt(), samplingTime, project.getInspectedAddress(), analyzeTime,
                project.getInspectedLinkMan(), project.getInspectedLinkPhone(), reportBaseInfo.getInspectedEnt(),
                reportBaseInfo.getInspectedAddress(), receiveTime, reportBaseInfo.getCustomerName()));
        return dataId;
    }

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID,
                "InspectedEnt", "SamplingTime", "InspectedAddress", "AnalyzeTime", "LinkMan", "LinkPhone",
                "ElectronicInspectedEnt", "ElectronicInspectedAddress", "ReceiveTime", "ElectronicCustomerName");
    }

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        return null;
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    @Override
    public List<String> getDataSourceStrategy() {
        List<String> strategyList = new ArrayList<>();
        strategyList.add(IDataSourceStrategy.PROJECT);
        strategyList.add(IDataSourceStrategy.REPORT);
        strategyList.add(IDataSourceStrategy.SAMPLE);
        strategyList.add(IDataSourceStrategy.ANALYSE_DATA);
        return strategyList;
    }
}
