package com.sinoyd.report.word.component.strategy;

import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.constants.IDataSourceKey;
import com.sinoyd.report.constants.IDataSourceStrategy;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoReportFolderInfo;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.utils.ReportGenerateUtils;
import com.sinoyd.report.word.vo.ReportComponentVo;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.report.constants.IReportGenerateConstants.*;

/**
 * 废水检测数据组件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
@Component(IReportComponentStrategy.WASTE_WATER_STD_SAMPLE_DATA)
@SuppressWarnings({"unchecked"})
public class WasteWaterStdComponent extends AbsReportComponent {

    private final String HORIZONTAL = "—";

    /**
     * 生成数据表
     *
     * @param reportComponentVo 报表组件VO
     * @param reportInfo        数据源
     * @param ds                数据
     */
    @Override
    public void createTable(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, DataSet ds) {
        initTableAndSetRelation(reportComponentVo, reportInfo, ds);
        initDataRow(reportComponentVo, reportInfo, new HashMap<>(), ds, 1, 1, false, true);
    }

    /**
     * 初始化数据表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报表数据集
     * @param pageReportInfo    分页报表数据集
     * @param ds                报告数据集
     * @param idNumber          表索引
     * @param dataId            行数据表索引
     * @param filterFlag        是否按照样品过滤
     * @param evaFlag           是否在每页最后加一行评价值
     * @return 样品数据行索引
     */
    @Override
    public int initDataRow(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo, Map<String, Object> pageReportInfo, DataSet ds, int idNumber, int dataId, boolean filterFlag, boolean evaFlag) {
        //分页样品数量与测试项目数量
        int testCount = reportComponentVo.getTestCount();
        int sampleCount = reportComponentVo.getSampleCount();
        //初始化表
        DataTable dtTable = ds.getTables().get(reportComponentVo.getTableName());
        DataTable dtSource = ds.getTables().get(reportComponentVo.getSourceTableName());
        //获取分页数据
        List<DtoAnalyseData> pageAnalyseDataList = (List<DtoAnalyseData>) pageReportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        List<DtoSample> sampleList = (List<DtoSample>) pageReportInfo.get(IDataSourceKey.SAMPLE_DATA_LIST);
        //获取全部数据
        List<DtoAnalyseData> analyseDataList = (List<DtoAnalyseData>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_LIST);
        sampleList.sort(Comparator.comparing(DtoSample::getRedFolderName).thenComparing(DtoSample::getSamplingTimeBegin));
        //获取报告点位数据
        List<DtoReportFolderInfo> reportFolderInfoList = (List<DtoReportFolderInfo>) reportInfo.get(IDataSourceKey.REPORT_FOLDER_INFO_LIST);
        //初始化表数据
        dataId = initRow(reportComponentVo, idNumber, dataId, reportInfo,
                sampleList, pageAnalyseDataList,
                dtTable, dtSource,
                analyseDataList,
                reportFolderInfoList,
                testCount, sampleCount);
        return dataId;
    }

    /**
     * 初始化行数据
     *
     * @param reportComponentVo    组件属性对象
     * @param idNumber             表索引
     * @param dataId               行数据表索引
     * @param reportInfo           全局数据
     * @param sampleList           分页所有样品数据
     * @param pageAnalyseDataList  分页的分析项目数据
     * @param dtTable              数据表
     * @param dtSource             数据行表
     * @param analyseDataList      所有分析项目数据
     * @param reportFolderInfoList 报告点位数据
     * @param testCount            每页测试项目个数
     * @param sampleCount          每页样品个数
     * @return 初始化的页索引（与外层表行索引关联, 用于分页）
     */
    private int initRow(ReportComponentVo reportComponentVo, int idNumber, int dataId, Map<String, Object> reportInfo,
                        List<DtoSample> sampleList, List<DtoAnalyseData> pageAnalyseDataList,
                        DataTable dtTable, DataTable dtSource,
                        List<DtoAnalyseData> analyseDataList,
                        List<DtoReportFolderInfo> reportFolderInfoList,
                        int testCount, int sampleCount) {
        List<String> anaTestIds = pageAnalyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        for (int i = 0; i < sampleList.size(); i += sampleCount) {
            List<DtoSample> pageSampleList = sampleList.stream().skip(i).limit(sampleCount).collect(Collectors.toList());
            for (int j = 0; j < anaTestIds.size(); j += testCount) {
                List<String> pageTestIds = anaTestIds.stream().skip(j).limit(testCount).collect(Collectors.toList());
                //获取当前点位数据
                String eleEvaluationRemark = "";
                if (StringUtils.isNotEmpty(pageSampleList)){
                    String folderId = pageSampleList.get(0).getSampleFolderId();
                    Optional<DtoReportFolderInfo> folderInfoOp = reportFolderInfoList.stream().filter(p -> folderId.equals(p.getFolderId())).findFirst();
                    if (folderInfoOp.isPresent()){
                        eleEvaluationRemark = folderInfoOp.get().getFolderRemark();
                    }
                }
                //初始化表行
                ReportGenerateUtils.initAddRow(dtTable, getTableColumnList(reportComponentVo, reportInfo),
                        getTableValueList(dataId, idNumber, reportInfo, sampleCount, testCount, pageSampleList, pageTestIds, eleEvaluationRemark));
                for (int k = 0; k < testCount; k++) {
                    String testId = k < pageTestIds.size() ? pageTestIds.get(k) : "";
                    setRowData(dtSource, reportComponentVo, reportInfo, analyseDataList, pageSampleList, testId, idNumber, dataId);
                }
                dataId++;
            }
        }
        return dataId;
    }

    /**
     * 渲染常规水样品数据行
     *
     * @param dtWaterSource     行数据表对象
     * @param reportComponentVo 组件对象
     * @param reportInfo        报告数据
     * @param analyseDataList   所有分析数据
     * @param pageSampleList    当前页的样品数据
     * @param testId            当前行的测试项目id
     * @param idNumber          表索引
     * @param dataId            行索引
     */
    public void setRowData(DataTable dtWaterSource, ReportComponentVo reportComponentVo, Map<String, Object> reportInfo,
                           List<DtoAnalyseData> analyseDataList, List<DtoSample> pageSampleList, String testId,
                           int idNumber, int dataId) {
        //获取满足测试项目的分析数据 TODO:后续改成测试项目获取
        DtoAnalyseData analyseData = analyseDataList.stream().filter(p -> testId.equals(p.getTestId())).findFirst().orElse(null);
        //获取行的数据列
        List<String> sourceColumns = getSourceColumnList(reportComponentVo, reportInfo);
        Map<String, List<DtoAnalyseData>> analyseDataMap = (Map<String, List<DtoAnalyseData>>) reportInfo.get(IDataSourceKey.ANALYSE_DATA_MAP_GROUP);
        List<Object> valList = new ArrayList<>(Arrays.asList(dataId, idNumber));
        if (analyseData != null) {
            valList.add(StringUtils.isEmpty(analyseData.getRedAnalyzeItemName()) ? HORIZONTAL : analyseData.getRedAnalyzeItemName());
            valList.add(StringUtils.isEmpty(analyseData.getDimension()) ? HORIZONTAL : analyseData.getDimension());
            valList.add(StringUtils.isEmpty(analyseData.getExamLimitValue()) ? HORIZONTAL : analyseData.getExamLimitValue());
            //分析时间
            String analyseTime = HORIZONTAL;
            if (StringUtils.isNotNull(analyseData.getAnalyzeTime())) {
                analyseTime = DateUtil.dateToString(analyseData.getAnalyzeTime(), DateUtil.FULL);
            }
            valList.add(analyseTime);
        } else {
            valList.add(HORIZONTAL);
            valList.add(HORIZONTAL);
            valList.add(HORIZONTAL);
            valList.add(HORIZONTAL);
        }
        //赋值
        for (DtoSample sample : pageSampleList) {
            List<DtoAnalyseData> analyseDataForSample = analyseDataMap.getOrDefault(sample.getSampleId(), new ArrayList<>());
            DtoAnalyseData analyseDataOfSample = analyseDataForSample.stream().filter(p -> testId.equals(p.getTestId())).findFirst().orElse(null);
            if (analyseDataOfSample != null) {
                valList.add(analyseDataOfSample.getTestValue());
            } else {
                valList.add(HORIZONTAL);
            }
        }
        if (pageSampleList.size() < reportComponentVo.getSampleCount()) {
            for (int i = 0; i < reportComponentVo.getSampleCount() - pageSampleList.size(); i++) {
                valList.add(HORIZONTAL);
            }
        }
        //TODO:平均值
        valList.add("5.5");
        ReportGenerateUtils.initAddRow(dtWaterSource, sourceColumns, valList);
    }


    /**
     * 获取采样开始时间~结束时间
     *
     * @param pageSampleList 样品列表
     * @param paramsDataList 参数列表
     * @param idx            索引
     * @return 列名称列表
     */
    public String getTestTimeStr(List<DtoSample> pageSampleList, List<DtoParamsData> paramsDataList, int idx) {
        String slash = "/";
        String testTime = HORIZONTAL;
        if (idx < pageSampleList.size()) {
            String startTime = ReportGenerateUtils.getParamValByName(paramsDataList, "采样开始时间", "");
            String endTime = ReportGenerateUtils.getParamValByName(paramsDataList, "采样结束时间", "");
            testTime = startTime;
            if (!testTime.equals(endTime) && StringUtils.isNotEmpty(endTime) && !slash.equals(endTime)) {
                testTime = testTime + "~" + endTime;
            }
            if (StringUtils.isEmpty(testTime) && StringUtils.isNotEmpty(endTime) && !slash.equals(endTime)) {
                testTime = endTime;
            }
        }
        return testTime;
    }


    /**
     * 获取表行值列表
     *
     * @param dataId              行id
     * @param idNumber            表id
     * @param reportInfo          数据集合
     * @param sampleCount         每页样品个数
     * @param testCount           每页测试项目条数
     * @param pageSampleList      当前页的样品数据
     * @param pageTestIds         当前页的测试项目数据
     * @param eleEvaluationRemark 点位备注
     * @return 列名称列表
     */
    public List<Object> getTableValueList(int dataId, int idNumber, Map<String, Object> reportInfo, int sampleCount, int testCount, List<DtoSample> pageSampleList, List<String> pageTestIds, String eleEvaluationRemark) {
        Map<String, List<DtoParamsData>> paramsDataMap = (Map<String, List<DtoParamsData>>) reportInfo.get(IDataSourceKey.PARAMS_MAP_GROUP);
        List<Object> valueList = new ArrayList<>(Arrays.asList(dataId, idNumber));
        valueList.add(pageSampleList.get(0).getRedFolderName());
        for (int i = 0; i < sampleCount; i++) {
            List<DtoParamsData> paramsDataList = i < pageSampleList.size() ? paramsDataMap.getOrDefault(pageSampleList.get(i).getSampleId(), new ArrayList<>()) : new ArrayList<>();
            String shape = ReportGenerateUtils.getParamValByName(paramsDataList, "样品性状", HORIZONTAL);
            DtoSample loopSample = i < pageSampleList.size() ? pageSampleList.get(i) : null;
            String sampleCode = loopSample != null ? loopSample.getSampleCode() : HORIZONTAL;
            String testTime = getTestTimeStr(pageSampleList, paramsDataList, i);
            valueList.add(shape);
            valueList.add(sampleCode);
            valueList.add(StringUtils.isEmpty(testTime) ? HORIZONTAL : testTime);

        }
        valueList.add(eleEvaluationRemark);
        valueList.add(pageSampleList.size() < sampleCount && pageTestIds.size() < testCount ? "" : "\f");
        return valueList;
    }

    /**
     * 获取组件表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getTableColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        List<String> columnList = new ArrayList<>(Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID, "WatchSpot"));
        for (int i = 0; i < reportComponentVo.getSampleCount(); i++) {
            columnList.add("Shape" + i);
            columnList.add("SampleCode" + i);
            columnList.add("TestTime" + i);
        }
        columnList.add("ElectronicFolderRemark");
        columnList.add("End");
        return columnList;
    }

    /**
     * 获取数据行表列名称列表
     *
     * @param reportComponentVo 组件属性对象
     * @param reportInfo        报告数据源信息集合
     * @return 列名称列表
     */
    @Override
    public List<String> getSourceColumnList(ReportComponentVo reportComponentVo, Map<String, Object> reportInfo) {
        List<String> smpFieldList = new ArrayList<>(Arrays.asList(IReportPlaceholder.ID, IReportPlaceholder.TABLE_ID, "TestName", "Dimension", "LimitValue", "AnalyzeTime"));
        for (int i = 0; i < reportComponentVo.getSampleCount(); i++) {
            smpFieldList.add("val" + i);
        }
        smpFieldList.add("AvgValue");
        return smpFieldList;
    }

    /**
     * 获取当前组件需要用到的数据源策略
     *
     * @return 组件所需数据源策略
     */
    @Override
    public List<String> getDataSourceStrategy() {
        List<String> strategyList = new ArrayList<>();
        strategyList.add(IDataSourceStrategy.PROJECT);
        strategyList.add(IDataSourceStrategy.REPORT);
        strategyList.add(IDataSourceStrategy.PARAMS_DATA);
        strategyList.add(IDataSourceStrategy.SAMPLE);
        strategyList.add(IDataSourceStrategy.ANALYSE_DATA);
        strategyList.add(IDataSourceStrategy.REPORT_FOLDER_INFO);
        return strategyList;
    }
}
