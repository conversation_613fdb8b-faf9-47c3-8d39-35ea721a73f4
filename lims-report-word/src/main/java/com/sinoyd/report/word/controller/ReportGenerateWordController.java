package com.sinoyd.report.word.controller;

import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.report.sync.SyncProjectService;
import com.sinoyd.report.word.service.GenerateWordReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 报表:报告接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/22
 */
@RestController
@RequestMapping("api/report/generate/word")
public class ReportGenerateWordController extends ExceptionHandlerController<GenerateWordReportService> {

    private SyncProjectService syncProjectService;

    /**
     * 报告生成接口
     *
     * @param reportCode 报表编码
     * @param map        业务传参
     * @param response   响应体
     */
    @PostMapping("/{reportCode}")
    public void generate(@PathVariable("reportCode") String reportCode,
                         @RequestBody Map<String, Object> map,
                         HttpServletResponse response) {
        map.put("reportId", "66911430-4932-47e1-856c-0a5ee74dd84b");
        service.generate(reportCode, map, response);
    }

    /**
     * 临时数据同步接口
     *
     * @param projectId 项目id
     */
    @PostMapping("/sync/{projectId}")
    public void syncProjectData(@PathVariable("projectId") String projectId) {
        syncProjectService.sync(projectId);
    }

    @Autowired
    public void setSyncProjectService(SyncProjectService syncProjectService) {
        this.syncProjectService = syncProjectService;
    }
}
