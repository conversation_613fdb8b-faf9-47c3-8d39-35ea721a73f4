package com.sinoyd.report.word.groupType.factory;

import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.groupType.strategy.AbsGroupType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 报告样品分页类型工厂
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
@Component
public class ReportGroupTypeFactory {

    private final Map<String, AbsGroupType> groupTypeMap = new ConcurrentHashMap<>();

    @Autowired
    public ReportGroupTypeFactory(Map<String, AbsGroupType> strategyMap) {
        this.groupTypeMap.putAll(strategyMap);
    }

    /**
     * 对样品进行分页
     *
     * @param beanName     状态bean名称
     * @param sampleList   参数
     * @param propertyName 属性名称
     */
    public Map<String, List<DtoSample>> groupSample(String beanName, List<DtoSample> sampleList, String propertyName) {
        //赋值数据源
        return this.groupTypeMap.get(beanName).group(sampleList, propertyName);
    }
}
