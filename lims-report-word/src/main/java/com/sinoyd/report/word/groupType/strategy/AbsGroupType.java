package com.sinoyd.report.word.groupType.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.entity.Sample;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 报告样品分页方式
 *
 * <AUTHOR>
 * @version V1.0.0 2021/01/27
 * @since V100R001
 */
public abstract class AbsGroupType {

    /**
     * 对样品进行分组
     *
     * @param sampleList   样品列表
     * @param propertyName 属性名称
     */
    public abstract Map<String, List<DtoSample>> group(List<DtoSample> sampleList, String propertyName);

    /**
     * 获取对象属性值
     *
     * @param sample       样品对象
     * @param dataPropName 属性名称
     * @return 行对象
     */
    public String getFieldValString(DtoSample sample, String dataPropName) {
        Field field = getFieldByName(dataPropName);
        try {
            Type type = field.getType();
            if (String.class.equals(type)) {
                return (String) field.get(sample);
            } else if (Integer.class.equals(type)) {
                return String.valueOf(field.get(sample));
            } else {
                throw new BaseException("不支持按照属性：" + dataPropName + "进行分页!");
            }
        } catch (IllegalAccessException e) {
            throw new BaseException("分页属性名称配置错误!");
        }
    }

    /**
     * 获取对象属性值
     *
     * @param sample       样品对象
     * @param dataPropName 属性名称
     * @return 行对象
     */
    public Date getFieldValDate(DtoSample sample, String dataPropName) {
        Field field = getFieldByName(dataPropName);
        try {
            return (Date) field.get(sample);
        } catch (Exception e) {
            throw new BaseException("分页属性名称配置错误!");
        }
    }

    /**
     * 按照日期分组
     *
     * @param fieldVal   属性值
     * @param dateFormat 日期格式
     * @return 处理后的值
     */
    public String getGroupValDate(Date fieldVal, String dateFormat) {
        return DateUtil.dateToString(fieldVal, dateFormat);
    }

    /**
     * 根据属性名称获取属性对象
     *
     * @param propertyName 属性名称
     * @return 属性对象
     */
    private Field getFieldByName(String propertyName) {
        try {
            Field field = Sample.class.getDeclaredField(propertyName);
            field.setAccessible(true);
            return field;
        } catch (Exception e) {
            throw new BaseException("分页属性名称配置错误!");
        }
    }
}
