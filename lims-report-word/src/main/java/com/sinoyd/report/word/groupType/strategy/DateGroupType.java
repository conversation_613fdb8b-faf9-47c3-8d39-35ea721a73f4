package com.sinoyd.report.word.groupType.strategy;

import com.sinoyd.report.constants.IReportGenerateConstants;
import com.sinoyd.report.dto.DtoSample;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 按照样品类的某一个date类型属性的值（yyyy-MM-dd）进行分页
 * <AUTHOR>
 * @version V1.0.0 2021/01/27
 * @since V100R001
 * */
@Component(IReportGenerateConstants.IGroupTypeStrategy.DATE_GROUP_TYPE)
public class DateGroupType extends AbsGroupType {


    @Override
    public Map<String, List<DtoSample>> group(List<DtoSample> sampleList, String propertyName) {
        return sampleList.stream().collect(Collectors.groupingBy(p -> getGroupValDate(getFieldValDate(p, propertyName), "yyyy-MM-dd")));
    }

}
