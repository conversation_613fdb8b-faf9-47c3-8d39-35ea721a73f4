package com.sinoyd.report.word.service.impl;

import com.aspose.words.Document;
import com.aspose.words.net.System.Data.DataSet;
import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.service.BaseConfigService;
import com.sinoyd.report.utils.ReportFileUtil;
import com.sinoyd.report.word.builder.WordParamBuilder;
import com.sinoyd.report.word.service.AbsReportService;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.vo.ReportComponentVo;
import com.sinoyd.report.word.vo.WordParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 报告生成接口基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/27
 */
@Service
public abstract class AbsReportServiceImpl implements AbsReportService {

    private BaseConfigService baseConfigService;

    private WordReportService wordReportService;

    private FilePropertyConfig filePropertyConfig;

    protected final ThreadLocal<List<ReportComponentVo>> voList = new ThreadLocal<>();

    /**
     * 根据报表编码生成报告
     *
     * @param reportCode 报表编码
     * @param map        请求参数
     * @param response   响应体
     */
    @Override
    public void generate(String reportCode, Map<String, Object> map, HttpServletResponse response) {
        DtoBaseConfig baseConfig = baseConfigService.findByReportCode(reportCode);
        if (baseConfig == null) {
            throw new BaseException("报表编码" + reportCode + "未配置，请确认");
        }
        //准备数据集合
        DataSet ds = prepareDataSet(baseConfig, map);
        //获取报表模板
        String absPath = filePropertyConfig.getTemplatePath() + FileUtil.FILE_SEPARATOR + baseConfig.getTemplatePath();
        WordParamVO wordParamVO = WordParamBuilder.init(absPath, true).setDataSet(ds).isFormatCell(false)
                .getInstance();
        //获取赋值后的文档对象
        Document doc = wordReportService.generateDoc(wordParamVO);
        //处理合并区域
        mergeData(doc, ds);
        //处理科学计数法区域
        setSciData(doc, ds);

        String templateName = getWordReportName(baseConfig, reportCode, map);
        //下载文件
        ReportFileUtil.downloadWord(templateName, doc, response);
        //清除线程变量
        voList.remove();
    }

    /**
     * 准备数据集
     *
     * @param reportConfig 当前报表配置数据
     * @param map          请求参数
     * @return 处理好的数据集
     */
    public abstract DataSet prepareDataSet(DtoBaseConfig reportConfig, Map<String, Object> map);

    /**
     * 处理合并区域
     *
     * @param doc 文档对象
     * @param ds  数据集合
     */
    protected void mergeData(Document doc, DataSet ds) {
    }

    /**
     * 处理科学计数法区域
     *
     * @param doc 文档对象
     * @param ds  数据集合
     */
    protected void setSciData(Document doc, DataSet ds) {
    }

    /**
     * 获取报表文件名称
     *
     * @param baseConfig 基础配置对象
     * @param reportCode 报表编码
     * @param map        数据源映射
     * @return 报表文件名称
     */
    protected String getWordReportName(DtoBaseConfig baseConfig, String reportCode, Map<String, Object> map) {
        return "";
    }


    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }

    @Autowired
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }
}
