package com.sinoyd.report.word.service.impl;

import com.aspose.words.Document;
import com.aspose.words.net.System.Data.DataSet;
import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.*;
import com.sinoyd.base.enums.EnumReportConfigType;
import com.sinoyd.report.service.BaseConfig2ModuleService;
import com.sinoyd.report.service.GlobalConfigService;
import com.sinoyd.report.service.ReportModule2GroupTypeService;
import com.sinoyd.report.service.ReportModuleService;
import com.sinoyd.report.strategy.dataSource.context.ReportDataSourceContext;
import com.sinoyd.report.vo.ReportInfoVO;
import com.sinoyd.report.word.component.factory.ComponentGenerateFactory;
import com.sinoyd.report.word.service.GenerateWordReportService;
import com.sinoyd.report.word.strategy.reportFileName.context.WordReportFileNameContext;
import com.sinoyd.report.word.vo.ReportComponentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 监测报告报表通用接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/29
 */
@Service
@Slf4j
public class GenerateWordReportServiceImpl extends AbsReportServiceImpl implements GenerateWordReportService {

    private BaseConfig2ModuleService baseConfig2ModuleService;

    private ReportModule2GroupTypeService reportModule2GroupTypeService;

    private ReportModuleService reportModuleService;

    private ComponentGenerateFactory componentGenerateFactory;

    private ReportDataSourceContext reportDataSourceContext;
    private GlobalConfigService globalConfigService;
    private WordReportFileNameContext wordReportFileNameContext;

    private static final Pattern PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");


    /**
     * 准备数据集
     *
     * @param reportConfig 当前报表配置数据
     * @param map          请求参数
     * @return 处理好的数据集
     */
    @Override
    public DataSet prepareDataSet(DtoBaseConfig reportConfig, Map<String, Object> map) {
        String reportConfigId = StringUtils.isNotNull(reportConfig) ? reportConfig.getId() : UUIDHelper.guidEmpty();
        //获取配置的报告组件信息
        if (StringUtils.isNull(voList.get())) {
            voList.set(getReportComponents(reportConfigId));
        }
        DataSet ds = new DataSet();
        //处理数据源参数
        ReportInfoVO reportInfo = new ReportInfoVO();
        reportInfo.setReportId(map.get("reportId").toString());
        reportInfo.setReportType(EnumReportConfigType.报告.getValue());
        //获取所需数据源策略集合
        List<String> dsStrategyList = new ArrayList<>();
        //遍历每个组件，初始化组件数据表
        for (ReportComponentVo vo : voList.get()) {
            componentGenerateFactory.fillingDSStrategy(vo, dsStrategyList);
        }
        if (StringUtils.isNotEmpty(dsStrategyList)) {
            dsStrategyList = dsStrategyList.stream().distinct().collect(Collectors.toList());
        }
        //所需的数据源字典集合
        Map<String, Object> dataInfo = new HashMap<>();
        //数据源字典集合填充
        for (String dsStrategy : dsStrategyList) {
            reportDataSourceContext.dataSource(dsStrategy, reportInfo, dataInfo);
        }
        //报告组件排序
        sortReportTypeVo(voList.get());
        //遍历每个组件，初始化组件数据表
        for (ReportComponentVo vo : voList.get()) {
            componentGenerateFactory.createTable(vo.getModuleCode(), vo, dataInfo, ds);
        }
        return ds;
    }

    /**
     * 报告组件排序
     *
     * @param voList 组件列表
     */
    private void sortReportTypeVo(List<ReportComponentVo> voList) {
        //过滤出基础信息组件，排在第一位
        ReportComponentVo basicVo = voList.stream().filter(p -> "basic".equals(p.getTableName())).findFirst().orElse(null);
        if (StringUtils.isNotNull(basicVo)) {
            voList.remove(basicVo);
            List<ReportComponentVo> tempVoList = new ArrayList<>(voList);
            voList.clear();
            voList.add(basicVo);
            voList.addAll(tempVoList);
        }
    }

    /**
     * 获取报告配置的组件信息
     *
     * @param reportConfigId 报告模板配置id
     * @return 组件信息
     */
    private List<ReportComponentVo> getReportComponents(String reportConfigId) {
        List<ReportComponentVo> voList = new ArrayList<>();
        List<DtoBaseConfig2Module> config2Modules = baseConfig2ModuleService.queryByBaseConfigId(reportConfigId);
        List<String> config2ModuleIdList = config2Modules.stream().map(DtoBaseConfig2Module::getId).collect(Collectors.toList());
        //获取所有的报告组件数据
        List<DtoReportModule> allReportModuleList = reportModuleService.findAll();
        Map<String, DtoReportModule> allReportModuleMap = allReportModuleList.stream().collect(Collectors.toMap(DtoReportModule::getModuleCode, module -> module));
        //获取当前报表配置关联的报告组件
        List<String> moduleIdList = config2Modules.stream().map(DtoBaseConfig2Module::getReportModuleId).distinct().collect(Collectors.toList());
        List<DtoReportModule> moduleList = allReportModuleList.stream().filter(p -> moduleIdList.contains(p.getId())).collect(Collectors.toList());
        Map<String, DtoReportModule> reportModuleMap = moduleList.stream().collect(Collectors.toMap(DtoReportModule::getId, module -> module));
        //获取报告组件分页数据
        List<DtoReportModule2GroupType> module2GroupTypeList = reportModule2GroupTypeService.findByBaseConfigModuleIdIn(config2ModuleIdList);
        Map<String, List<DtoReportModule2GroupType>> module2GroupTypeMap = module2GroupTypeList.stream()
                .collect(Collectors.groupingBy(DtoReportModule2GroupType::getBaseConfigModuleId));
        //获取子组件信息
        Map<String, DtoReportModule> allConfigModuleMap = getAllConfigModuleMap(moduleList, allReportModuleMap);
        if (StringUtils.isNotEmpty(config2Modules)) {
            for (DtoBaseConfig2Module config2Module : config2Modules) {
                DtoReportModule module = reportModuleMap.get(config2Module.getReportModuleId());
                if (StringUtils.isNotNull(module)) {
                    List<DtoReportModule2GroupType> loopModule2GroupTypeList = module2GroupTypeMap.getOrDefault(config2Module.getId(), new ArrayList<>())
                            .stream().sorted(Comparator.comparing(DtoReportModule2GroupType::getPriority).reversed()).collect(Collectors.toList());
                    List<String> propertyList = loopModule2GroupTypeList.stream().map(DtoReportModule2GroupType::getGroupTypeName).collect(Collectors.toList());
                    module2GroupTypeMap.getOrDefault(config2Module.getId(), new ArrayList<>());
                    //如果是复合组件需要收集对应的子组件信息
                    ReportComponentVo vo = initReportTypeVo(module, allConfigModuleMap, propertyList);
                    voList.add(vo);
                }
            }
        }
        return voList;
    }

    /**
     * 初始化组件类型对象
     *
     * @param module             组件对象
     * @param allConfigModuleMap 所有与配置的组件相关联的组件映射
     * @return 组件类型对象
     */
    private ReportComponentVo initReportTypeVo(DtoReportModule module, Map<String, DtoReportModule> allConfigModuleMap, List<String> propertyList) {
        List<ReportComponentVo> sonTableTypeVoList = new ArrayList<>();
        if (module.getIsCompound()) {
            String json = module.getSonTableJson();
            if (StringUtils.isNotEmpty(json)) {
                List<String> sonTableList = JsonIterator.deserialize(json, new TypeLiteral<List<String>>() {
                });
                if (StringUtils.isNotEmpty(sonTableList)) {
                    for (String sonTable : sonTableList) {
                        if (allConfigModuleMap.containsKey(sonTable)) {
                            sonTableTypeVoList.add(initReportTypeVo(allConfigModuleMap.get(sonTable), allConfigModuleMap, propertyList));
                        }
                    }
                }
            }
        }
        return new ReportComponentVo(module.getModuleCode(), module.getTableName(), module.getSourceTableName(),
                module.getSampleCount(), module.getTestCount(), propertyList, sonTableTypeVoList, module.getIsCompound());
    }

    /**
     * 获取所有组件信息映射
     *
     * @param configModuleList 报告配置的组件列表
     * @param allModuleMap     所有组件映射
     * @return 所有组件信息映射
     */
    private Map<String, DtoReportModule> getAllConfigModuleMap(List<DtoReportModule> configModuleList, Map<String, DtoReportModule> allModuleMap) {
        List<DtoReportModule> allConfigModuleList = getAllConfigModuleList(configModuleList, allModuleMap);
        return allConfigModuleList.stream().collect(Collectors.toMap(DtoReportModule::getModuleCode, dto -> dto, (r1, r2) -> r1));
    }

    /**
     * 获取所有与配置的组件相关联的组件信息
     *
     * @param configModuleList 报告配置的组件列表
     * @return 所有组件信息
     */
    private List<DtoReportModule> getAllConfigModuleList(List<DtoReportModule> configModuleList, Map<String, DtoReportModule> allModuleMap) {
        List<DtoReportModule> allConfigModuleList = new ArrayList<>();
        for (DtoReportModule configModule : configModuleList) {
            allConfigModuleList.add(configModule);
            if (configModule.getIsCompound()) {
                String json = configModule.getSonTableJson();
                if (StringUtils.isNotEmpty(json)) {
                    List<String> sonTableList = JsonIterator.deserialize(json, new TypeLiteral<List<String>>() {
                    });
                    if (StringUtils.isNotEmpty(sonTableList)) {
                        for (String sonTable : sonTableList) {
                            if (allModuleMap.containsKey(sonTable)) {
                                allConfigModuleList.addAll(getAllConfigModuleList(Collections.singletonList(allModuleMap.get(sonTable)), allModuleMap));
                            }
                        }
                    }
                } else {
                    allConfigModuleList.add(configModule);
                }
            }
        }
        return allConfigModuleList;
    }

    /**
     * 处理合并区域
     *
     * @param doc 文档对象
     * @param ds  数据集合
     */
    @Override
    protected void mergeData(Document doc, DataSet ds) {
        //遍历每个组件，进行合并单元格
        List<ReportComponentVo> reportTypeVoList = voList.get();
        if (StringUtils.isNotNull(reportTypeVoList)) {
            try {
                for (ReportComponentVo vo : reportTypeVoList) {
                    componentGenerateFactory.mergeCells(vo, doc, ds);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("执行区域合并出错!");
            }
        }
    }

    /**
     * 处理科学计数法区域
     *
     * @param doc 文档对象
     * @param ds  数据集合
     */
    @Override
    protected void setSciData(Document doc, DataSet ds) {
        //遍历每个组件，进行科学计数法格式转换等操作
        List<ReportComponentVo> reportTypeVoList = voList.get();
        if (StringUtils.isNotNull(reportTypeVoList)) {
            try {
                for (ReportComponentVo vo : reportTypeVoList) {
                    componentGenerateFactory.sciData(vo, doc, ds);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("执行区域合并出错!");
            }
        }
    }

    /**
     * 设置word报表名称
     *
     * @param reportCode 报表编码
     */
    @Override
    public String getWordReportName(DtoBaseConfig baseConfig, String reportCode, Map<String, Object> map) {
        DtoGlobalConfig globalConfig = globalConfigService.findByReportCode(reportCode);
        String namingMethod = globalConfig.getNamingMethod(), namingRules = globalConfig.getNameRules();
        String templateName = baseConfig.getTemplateName();
        if (StringUtils.isNotEmpty(namingMethod) && StringUtils.isNotEmpty(namingRules)) {
            Map<String, String> fileNameDataMap = wordReportFileNameContext.getFileNameDataMap(baseConfig, map, namingMethod);
            Matcher matcher = PATTERN.matcher(namingRules);
            while (matcher.find()) {
                String groupValue = matcher.group();
                String value = "";
                if (StringUtils.isNotNull(fileNameDataMap.get(groupValue))) {
                    value = fileNameDataMap.get(groupValue);
                }
                namingRules = namingRules.replace("[" + groupValue + "]", value);
            }
            int dotIdx = templateName.lastIndexOf("."), pathIdx = templateName.lastIndexOf(System.lineSeparator());
            String suffix = (dotIdx != -1) ? templateName.substring(dotIdx) : "", path = (pathIdx != -1) ? templateName.substring(0, pathIdx + 1) : "";
            templateName = path + namingRules + suffix;
        }
        return templateName;
    }

    @Autowired
    @Lazy
    public void setBaseConfig2ModuleService(BaseConfig2ModuleService baseConfig2ModuleService) {
        this.baseConfig2ModuleService = baseConfig2ModuleService;
    }

    @Autowired
    @Lazy
    public void setReportModule2GroupTypeService(ReportModule2GroupTypeService reportModule2GroupTypeService) {
        this.reportModule2GroupTypeService = reportModule2GroupTypeService;
    }

    @Autowired
    @Lazy
    public void setReportModuleService(ReportModuleService reportModuleService) {
        this.reportModuleService = reportModuleService;
    }

    @Autowired
    public void setComponentGenerateFactory(ComponentGenerateFactory componentGenerateFactory) {
        this.componentGenerateFactory = componentGenerateFactory;
    }

    @Autowired
    public void setReportDataSourceFactory(ReportDataSourceContext reportDataSourceContext) {
        this.reportDataSourceContext = reportDataSourceContext;
    }

    @Autowired
    @Lazy
    public void setGlobalConfigService(GlobalConfigService globalConfigService) {
        this.globalConfigService = globalConfigService;
    }

    @Autowired
    @Lazy
    public void setWordReportFileNameContext(WordReportFileNameContext wordReportFileNameContext) {
        this.wordReportFileNameContext = wordReportFileNameContext;
    }
}
