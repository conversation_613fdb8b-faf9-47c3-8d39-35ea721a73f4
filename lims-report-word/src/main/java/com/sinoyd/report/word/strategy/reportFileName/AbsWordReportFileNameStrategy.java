package com.sinoyd.report.word.strategy.reportFileName;

import com.sinoyd.report.dto.DtoBaseConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 报表文件名处理基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbsWordReportFileNameStrategy {

    /**
     * 获取报表文件命名规则数据
     *
     * @param baseConfig 基础配置对象
     * @param map        数据源映射
     * @return 报表文件命名规则数据
     */
    public abstract Map<String, String> getFileNameDataMap(DtoBaseConfig baseConfig, Map<String, Object> map);

    /**
     * 获取报表文件命名方法
     *
     * @return 报表文件命名方法
     */
    public abstract String getFileNamingMethod();
}
