package com.sinoyd.report.word.strategy.reportFileName;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoProject;
import com.sinoyd.report.dto.DtoReport;
import com.sinoyd.report.dto.DtoWorksheetFolder;
import com.sinoyd.report.enums.EnumNameRules;
import com.sinoyd.report.enums.EnumNamingMethod;
import com.sinoyd.report.service.ProjectService;
import com.sinoyd.report.service.ReportService;
import com.sinoyd.report.service.WorkSheetFolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * 项目报表文件名称策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class ProjectFileNameStrategy extends AbsWordReportFileNameStrategy {

    private ReportService reportService;
    private ProjectService projectService;

    /**
     * 获取报表文件命名规则数据
     *
     * @param baseConfig 基础配置对象
     * @param map        数据源映射
     * @return 报表文件命名规则数据
     */
    @Override
    public Map<String, String> getFileNameDataMap(DtoBaseConfig baseConfig, Map<String, Object> map) {
        String reportId = map.getOrDefault("reportId", "").toString();
        DtoReport report = reportService.findOne(reportId);
        String projectId = StringUtils.isNotNull(report) ? report.getProjectId() : "";
        DtoProject project = projectService.findOne(projectId);
        String projectCode = StringUtils.isNotNull(project) ? project.getProjectCode() : "";
        return Collections.singletonMap(EnumNameRules.项目编号.getCode(), projectCode);
    }

    @Override
    public String getFileNamingMethod() {
        return EnumNamingMethod.项目.getCode();
    }

    @Autowired
    @Lazy
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }
}
