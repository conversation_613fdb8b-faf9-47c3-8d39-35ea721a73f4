package com.sinoyd.report.word.strategy.reportFileName.context;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.word.strategy.reportFileName.AbsWordReportFileNameStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报表文件名策略管理类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Component
public class WordReportFileNameContext {

    private List<AbsWordReportFileNameStrategy> fileNameStrategyList;

    /**
     * 处理报表文件名称策略
     *
     * @param baseConfig   前端传参对象
     * @param map          数据源映射
     * @param namingMethod 命名方法
     */
    public Map<String, String> getFileNameDataMap(DtoBaseConfig baseConfig, Map<String, Object> map, String namingMethod) {
        Map<String, String> dataMap = new HashMap<>();
        AbsWordReportFileNameStrategy reportFileNameStrategy = fileNameStrategyList.stream().filter(p -> namingMethod.equals(p.getFileNamingMethod())).findFirst().orElse(null);
        if (StringUtils.isNotNull(reportFileNameStrategy)) {
            dataMap = reportFileNameStrategy.getFileNameDataMap(baseConfig, map);
        }
        return dataMap;
    }

    @Autowired
    public void setFileNameStrategyList(List<AbsWordReportFileNameStrategy> fileNameStrategyList) {
        this.fileNameStrategyList = fileNameStrategyList;
    }
}
