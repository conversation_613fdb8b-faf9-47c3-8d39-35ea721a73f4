package com.sinoyd.report.word.utils;

import com.aspose.words.net.System.Data.DataColumn;
import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAnalyseData;
import com.sinoyd.report.dto.DtoParamsData;
import com.sinoyd.report.dto.DtoSample;
import com.sinoyd.report.word.util.MathUtil;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告生成所用到的工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public class ReportGenerateUtils {

    private static final char[] cnArr = new char[]{'〇', '一', '二', '三', '四', '五', '六', '七', '八', '九'};

    /**
     * 添加数据表
     *
     * @param columnNameList 列名称集合
     * @param tableName      表名
     * @param ds             表格数据集
     * @return 数据表
     */
    public static DataTable initAddDataTable(List<String> columnNameList, String tableName, DataSet ds) {
        DataTable table = initDataTable(columnNameList, tableName);
        ds.getTables().add(table);
        return table;
    }

    /**
     * 初始化数据表
     *
     * @param columnNameList 列名称集合
     * @param tableName      表名
     * @return 数据表
     */
    public static DataTable initDataTable(List<String> columnNameList, String tableName) {
        DataTable table = new DataTable();
        columnNameList.forEach(p -> addTableColumn(table, p));
        table.setTableName(tableName);
        return table;
    }

    /**
     * 初始化并添加一个行对象
     *
     * @param dataTable  报告表对象
     * @param columnList 列名称对象
     * @param valueList  行数据列表
     */
    public static DataRow initAddRow(DataTable dataTable, List<String> columnList, List<Object> valueList) {
        DataRow row = initRow(dataTable, columnList, valueList);
        dataTable.getRows().add(row);
        return row;
    }

    /**
     * 初始化一个行对象
     *
     * @param dataTable  报告表对象
     * @param columnList 列名称对象
     * @param valueList  行数据列表
     * @return 行对象
     */
    public static DataRow initRow(DataTable dataTable, List<String> columnList, List<Object> valueList) {
        DataRow dataRow = dataTable.newRow();
        for (int i = 0; i < columnList.size(); i++) {
            Object value = "";
            if (valueList.size() > i) {
                value = valueList.get(i);
            }
            dataRow.set(columnList.get(i), value);
        }
        return dataRow;
    }

    /**
     * 给表格添加列
     *
     * @param dt         table
     * @param columnName 列名
     */
    public static void addTableColumn(DataTable dt, String columnName) {
        DataColumn column = new DataColumn();
        column.setColumnName(columnName);
        dt.getColumns().add(column);
    }

    /**
     * 日期转大写(二〇一五年七月五日)
     *
     * @param datetime 日期
     * @return 大写日期
     */
    public static String getCapDate(Date datetime) {
        String letter = "〇一二三四五六七八九";
        StringBuilder capDate = new StringBuilder();
        Calendar ca = Calendar.getInstance();
        ca.setTime(datetime);
        String year = String.valueOf(ca.get(Calendar.YEAR));
        for (int i = 0; i < year.length(); i++) {
            String index = year.substring(i, i + 1);
            capDate.append(letter.charAt(Integer.parseInt(index)));
        }
        int month = ca.get(Calendar.MONTH) + 1;
        int day = ca.get(Calendar.DAY_OF_MONTH);
        capDate.append("年").append(getCapMonthDay(month)).append("月").append(getCapMonthDay(day)).append("日");
        return capDate.toString();
    }

    /**
     * 日月转大写
     *
     * @param num 日、月数字
     * @return 大写数字
     */
    public static String getCapMonthDay(int num) {
        String letter = "〇一二三四五六七八九";
        String capNum = "";
        //余数
        int remainder = num % 10;
        //十位
        int tenSit = num / 10;
        if (tenSit > 1) {
            capNum += letter.charAt(tenSit);
        }
        if (num >= 10) {
            capNum += "十";
        }
        if (remainder != 0) {
            capNum += letter.charAt(remainder);
        }
        return capNum;
    }

    /**
     * 日期类型转为数字大写类型 (例如：2023-02-25 转为：二〇二三年二月二十五日)
     *
     * @param date 日期对象
     * @return 日期数字大写类型
     */
    public static String date2Chinese(Date date) {
        if (StringUtils.isNull(date)) {
            return "";
        }
        String dateStr = DateUtil.dateToString(date, DateUtil.YEAR);
        String[] dateStrArr = dateStr.split("-");
        String yearStr = dateStrArr[0];
        StringBuilder yearBigStr = new StringBuilder();
        for (int i = 0; i < yearStr.length(); i++) {
            yearBigStr.append(cnArr[yearStr.charAt(i) - '0']);
        }
        return yearBigStr + "年" + MathUtil.toChinese(Integer.parseInt(dateStrArr[1])).replace("零", "〇") + "月"
                + MathUtil.toChinese(Integer.parseInt(dateStrArr[2])).replace("零", "〇") + "日";
    }

    /**
     * 获取给定日期列表中的最大最小日期范围字符串
     *
     * @param dateList      日期列表
     * @param dateFormat    日期格式
     * @param connectSymbol 范围连接符
     */
    public static String getTimeRange(List<Date> dateList, String dateFormat, String connectSymbol, String defaultValue) {
        String rangeStr = defaultValue;
        if (StringUtils.isNotEmpty(dateList)) {
            String maxTime = DateUtil.dateToString(Collections.max(dateList), dateFormat);
            String minTime = DateUtil.dateToString(Collections.min(dateList), dateFormat);
            if (maxTime.equals(minTime)) {
                rangeStr = maxTime;
            } else {
                rangeStr = minTime + connectSymbol + maxTime;
            }
        }
        return rangeStr;
    }

    /**
     * 获取采样时间区间
     *
     * @param sampleList    样品数据列表
     * @param dateFormat    日期格式
     * @param connectSymbol 连接符
     */
    public static String getSamplingTime(List<DtoSample> sampleList, String dateFormat, String connectSymbol) {
        String time;
        List<String> dateStrList = getMinMaxSamplingTime(sampleList, dateFormat);
        String samplingTimeMin = dateStrList.get(0);
        String samplingTimeMax = dateStrList.get(1);
        if (samplingTimeMin.equals(samplingTimeMax)) {
            time = samplingTimeMin;
        } else {
            time = samplingTimeMin + connectSymbol + samplingTimeMax;
        }
        return time;
    }

    /**
     * 获取最早和最晚的样品采样时间字符串
     *
     * @param sampleList 样品列表
     * @param dateFormat 日期格式
     */
    public static List<String> getMinMaxSamplingTime(List<DtoSample> sampleList, String dateFormat) {
        List<String> resList = new ArrayList<>();
        List<String> samplingTimeList = getSamplingTimeStrList(sampleList, dateFormat);
        resList.add(StringUtils.isNotEmpty(samplingTimeList) ? Collections.min(samplingTimeList) : "");
        resList.add(StringUtils.isNotEmpty(samplingTimeList) ? Collections.max(samplingTimeList) : "");
        return resList;
    }

    /**
     * 获取样品采样时间字符串列表
     *
     * @param sampleList 样品列表
     * @param dateFormat 日期格式
     */
    public static List<String> getSamplingTimeStrList(List<DtoSample> sampleList, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        List<String> samplingTimeList = new ArrayList<>();
        List<Date> sampleTimeList = sampleList.stream().map(DtoSample::getSamplingTimeBegin).distinct().sorted().collect(Collectors.toList());
        for (Date sampleTime : sampleTimeList) {
            samplingTimeList.add(sdf.format(sampleTime));
        }
        return samplingTimeList;
    }

    /**
     * 根据参数名称获取参数值（任意取一个）
     *
     * @param paramsDataList 参数对象列表
     * @param name           参数名称
     * @param defVal         默认值
     * @return 参数值
     */
    public static String getParamValByName(List<DtoParamsData> paramsDataList, String name, String defVal) {
        DtoParamsData paramsData = paramsDataList.stream().filter(p -> name.equals(p.getParamsConfigName())
                && StringUtils.isNotEmpty(p.getParamsValue())).findFirst().orElse(null);
        if (paramsData != null) {
            return StringUtils.isNotEmpty(paramsData.getParamsValue()) ? paramsData.getParamsValue() : "";
        }
        return defVal;
    }


    /**
     * 从分析数据列表中获取分析日期及分析完成日期列表
     *
     * @param analyseDataList 分析数据列表
     * @return 分析日期及分析完成日期列表
     */
    public static List<Date> getAnalyzeFinishTimeList(List<DtoAnalyseData> analyseDataList) {
        //分析日期
        List<Date> analyzeTimeList = analyseDataList.stream().filter(p -> StringUtils.isNotNull(p.getAnalyzeTime())
                && !DateUtil.dateToString(p.getAnalyzeTime(), "yyyy.MM.dd").contains("1753"))
                .map(DtoAnalyseData::getAnalyzeTime).distinct().collect(Collectors.toList());
        List<Date> finishTimeList = analyseDataList.stream().filter(p -> StringUtils.isNotNull(p.getFinishTime())
                && !DateUtil.dateToString(p.getFinishTime(), "yyyy.MM.dd").contains("1753"))
                .map(DtoAnalyseData::getFinishTime).distinct().collect(Collectors.toList());
        if (StringUtils.isNotEmpty(finishTimeList)) {
            analyzeTimeList.addAll(finishTimeList);
        }
        return analyzeTimeList;
    }
}
