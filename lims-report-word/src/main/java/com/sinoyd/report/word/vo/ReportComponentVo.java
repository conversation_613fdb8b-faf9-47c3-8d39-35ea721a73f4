package com.sinoyd.report.word.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 报告组件生成VO
 *
 * <AUTHOR>
 * @version V1.0.0 2022/07/01
 * @since V100R001
 */
@Data
public class ReportComponentVo {
    /**
     * 组件编码
     */
    private String moduleCode;

    /**
     * 组件表名
     */
    private String tableName;

    /**
     * 组件数据行表名(适用于检测结果表组件)
     */
    private String sourceTableName;

    /**
     * 组件每页样品数量
     */
    private Integer sampleCount;

    /**
     * 组件每页测试项目数量
     */
    private Integer testCount;

    /**
     * 分页属性列表
     */
    private List<String> pagePropertyList;

    /**
     * 组件下的子组件
     */
    private List<ReportComponentVo> sonTypeVos;

    /**
     * 是否复合组件
     */
    private Boolean isCompound;

    public ReportComponentVo() {
    }

    public ReportComponentVo(String moduleCode, String tableName, String sourceTableName, Integer sampleCount, Integer testCount,
                             List<String> pagePropertyList, List<ReportComponentVo> sonTypeVos, Boolean isCompound) {
        this.moduleCode = moduleCode;
        this.tableName = tableName;
        this.sourceTableName = sourceTableName;
        this.sampleCount = sampleCount;
        this.testCount = testCount;
        this.pagePropertyList = pagePropertyList;
        this.sonTypeVos = sonTypeVos;
        this.isCompound = isCompound;
    }

    public ReportComponentVo(String moduleCode, String tableName, String sourceTableName, Integer sampleCount, Integer testCount) {
        this.moduleCode = moduleCode;
        this.tableName = tableName;
        this.sourceTableName = sourceTableName;
        this.sampleCount = sampleCount;
        this.testCount = testCount;
    }
}
