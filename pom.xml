<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sinoyd-boot-starter-report-word</artifactId>
    <version>${report.word.version}-SNAPSHOT</version>
    <name>sinoyd-boot-starter-report-word</name>
    <description>Word报表模块</description>

    <build>
        <finalName>sinoyd-boot-starter-report-word</finalName>
    </build>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <properties>
        <spring-boot.version>2.7.0</spring-boot.version>
        <report.base.version>5.2.4-SNAPSHOT</report.base.version>
        <report.word.version>5.2.5</report.word.version>
    </properties>
    <dependencies>
       <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-report</artifactId>
			<version>${report.base.version}</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
