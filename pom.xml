<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.lims</groupId>
    <artifactId>report-base-config</artifactId>
    <version>${report.config.version}-SNAPSHOT</version>
    <name>report-base-config</name>
    <description>报表基础配置模块</description>

    <build>
        <finalName>report-base-config</finalName>
    </build>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <properties>
        <report.config.version>6.0.21</report.config.version>
        <frame-arch.version>6.0.0-SNAPSHOT</frame-arch.version>
        <report.base.version>5.2.4-SNAPSHOT</report.base.version>
        <lims.base.version>6.0.33-MS-SNAPSHOT</lims.base.version>
        <rms.version>6.0.33</rms.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.frame</groupId>
            <artifactId>frame-arch</artifactId>
            <version>${frame-arch.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-report</artifactId>
            <version>${report.base.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims</groupId>
            <artifactId>lims-rms-client</artifactId>
            <version>${rms.version}-MS-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims</groupId>
            <artifactId>lims-base-impl</artifactId>
            <version>${lims.base.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
