<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sinoyd-boot-starter-report</artifactId>
    <version>${report.base.version}-SNAPSHOT</version>
    <name>sinoyd-boot-starter-report</name>
    <description>报表基础模块</description>

    <build>
        <finalName>sinoyd-boot-starter-report</finalName>
    </build>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <properties>
        <spring-boot.version>2.7.0</spring-boot.version>
        <aspose.cell.version>9.0.0</aspose.cell.version>
        <aspose.words.version>19.5.0</aspose.words.version>
        <aspose.pdf.version>18.9.0</aspose.pdf.version>
        <commons.io.version>2.7</commons.io.version>
        <org.jfree.version>1.0.19</org.jfree.version>
        <report.base.version>5.2.4</report.base.version>
        <lims.commons.version>6.0.55-SNAPSHOT</lims.commons.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-cells</artifactId>
            <version>${aspose.cell.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>${aspose.words.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-pdf</artifactId>
            <version>${aspose.pdf.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>${org.jfree.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-common</artifactId>
            <version>5.2.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sinoyd.lims</groupId>
            <artifactId>lims-commons</artifactId>
            <version>${lims.commons.version}</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
