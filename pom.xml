<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.lims</groupId>
    <artifactId>lims-report</artifactId>
    <version>${lims.report.version}-SNAPSHOT</version>
    <name>lims-report</name>
    <description>LIMS 报表模块</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <modules>
        <!-- word报告 -->
        <module>lims-report-word</module>
        <!-- LIMS通用excel模块 -->
        <module>lims-report-excel</module>
        <!-- 采样单 -->
        <module>lims-report-sampling</module>
        <!-- 原始记录单 -->
        <module>lims-report-worksheet</module>
        <!-- 数据层 -->
        <module>lims-report-ds</module>
        <!-- client层 -->
        <module>lims-report-client</module>
    </modules>

    <properties>
        <lims.report.version>6.0.21</lims.report.version>
        <report.config.version>6.0.21-SNAPSHOT</report.config.version>
        <sinoyd.report.word.version>5.2.5-SNAPSHOT</sinoyd.report.word.version>
        <sinoyd.report.excel.version>5.2.7-SNAPSHOT</sinoyd.report.excel.version>
        <sinoydframework.version>5.2.1-SNAPSHOT</sinoydframework.version>
        <frame-arch.version>6.0.0-SNAPSHOT</frame-arch.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-report-word</artifactId>
                <version>${sinoyd.report.word.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-report-excel</artifactId>
                <version>${sinoyd.report.excel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-report-word</artifactId>
                <version>${lims.report.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-report-excel</artifactId>
                <version>${lims.report.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-report-sampling</artifactId>
                <version>${lims.report.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-report-worksheet</artifactId>
                <version>${lims.report.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-report-ds</artifactId>
                <version>${lims.report.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>report-base-config</artifactId>
                <version>${report.config.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-frame-client</artifactId>
                <version>${sinoydframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.frame</groupId>
                <artifactId>frame-arch</artifactId>
                <version>${frame-arch.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>lims-report</finalName>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
