<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.lims</groupId>
    <artifactId>lims-base</artifactId>
    <version>${base.version}-MS-SNAPSHOT</version>
    <name>lims-base</name>
    <description>LIMS 基础模块</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <frame-arch.version>6.0.0-SNAPSHOT</frame-arch.version>
        <base.version>6.0.33</base.version>
        <cglib.version>2.2.2</cglib.version>
        <commons.version>6.0.63-SNAPSHOT</commons.version>
        <mapstrut.version>1.5.3.Final</mapstrut.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <sinoyd-boot-starter.excel.version>5.2.7-SNAPSHOT</sinoyd-boot-starter.excel.version>
        <sinoyd-boot-starter.word.version>5.2.5-SNAPSHOT</sinoyd-boot-starter.word.version>
    </properties>

    <modules>
        <module>lims-base-public</module>
        <module>lims-base-arch</module>
        <module>lims-base-impl</module>
        <module>lims-base-log</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-base-public</artifactId>
                <version>${base.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-base-arch</artifactId>
                <version>${base.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-base-impl</artifactId>
                <version>${base.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-commons</artifactId>
                <version>${commons.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.frame</groupId>
                <artifactId>frame-arch</artifactId>
                <version>${frame-arch.version}</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstrut.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstrut.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok.mapstruct.binding.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-report-excel</artifactId>
                <version>${sinoyd-boot-starter.excel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-report-word</artifactId>
                <version>${sinoyd-boot-starter.word.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
