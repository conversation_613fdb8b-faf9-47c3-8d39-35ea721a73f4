package com.sinoyd;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.frame.client.annotation.EnableFrameClient;
import com.sinoyd.frame.base.configuration.JdkDateSupport;
import com.sinoyd.frame.base.configuration.JdkTimestampSupport;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.web.WebApplicationInitializer;

/**
 * 应用入口
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2023-07-20
 */
@SpringBootApplication
@EnableFrameClient
@EnableDiscoveryClient
@EnableJpaAuditing(auditorAwareRef = "principalContextUser")
@EnableCaching
@EnableFeignClients(basePackages = "com.sinoyd")
public class ApplicationRunner extends SpringBootServletInitializer implements WebApplicationInitializer {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationRunner.class, args);
        //启用json时间序列化
        JdkTimestampSupport.enable(DateUtil.FULL);
        //启用json时间序列化格式
        JdkDateSupport.enable(DateUtil.FULL);
    }

}
