package com.sinoyd.lims.report.configuration;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 数据源配置类
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2023-07-20
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "limEntityManagerFactory",
        transactionManagerRef = "reportTransactionManager",
        basePackages = {"com.sinoyd.report.repository"},
        repositoryFactoryBeanClass = ReportRepositoryFactoryBean.class
)
public class DataSourceConfig {

    @Resource
    private JpaProperties jpaProperties;

    @Bean(name = "dataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource limsDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "reportJdbcTemplate")
    public JdbcTemplate primaryJdbcTemplate(@Qualifier("dataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Primary
    @Bean(name = "entityManagerPrimary")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return limsEntityManagerFactory(builder, limsDataSource()).getObject().createEntityManager();
    }

    @Bean(name = "limEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean limsEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("dataSource") DataSource limsDataSource) {
        return builder
                .dataSource(limsDataSource)
                .packages("com.sinoyd.report.dto")
                //后续如果要引用pro的那basePackages要继续加pro的底层数据包，如果引用到视图也要加进来
                .properties(jpaProperties.getProperties())
                .persistenceUnit("report")
                .build();
    }

    @Bean(name = "reportTransactionManager")
    @Primary
    public PlatformTransactionManager baseTransactionManager(
            @Qualifier("limEntityManagerFactory") EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }
}