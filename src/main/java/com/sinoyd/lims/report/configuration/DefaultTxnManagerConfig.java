package com.sinoyd.lims.report.configuration;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;

/**
 * 默认的事务管理器
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2023-07-20
 */
@Configuration
@AutoConfigureAfter(DataSourceConfig.class)
public class DefaultTxnManagerConfig implements TransactionManagementConfigurer{

    @Resource(name = "reportTransactionManager")
    private PlatformTransactionManager defaultTxnManager;

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return defaultTxnManager;
    }
}
