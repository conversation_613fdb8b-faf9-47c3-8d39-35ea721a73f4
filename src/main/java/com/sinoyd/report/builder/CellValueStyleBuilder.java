package com.sinoyd.report.builder;

import com.sinoyd.report.utils.SciUtil;

import java.awt.*;

/**
 * 单元格值样式构建器，这里只是单元格值的样式，并非单元格样式，比如字体颜色、是否加粗、是否显示科学计数法等等
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/8/2
 */
public class CellValueStyleBuilder {

    /**
     * 拼接的HTML
     */
    private StringBuilder HTML = new StringBuilder();

    /**
     * 拼接的整体的CSS样式
     */
    private final StringBuilder CSS = new StringBuilder();

    /**
     * 私有化构造方法
     */
    private CellValueStyleBuilder() {
    }

    /**
     * 初始化
     *
     * @return 构建器实例
     */
    public static CellValueStyleBuilder init() {
        CellValueStyleBuilder builder = new CellValueStyleBuilder();
        builder.HTML = new StringBuilder();
        return builder;
    }

    /**
     * 初始化
     *
     * @param cellValue 单元格初始值
     * @return 构建器实例
     */
    public static CellValueStyleBuilder init(String cellValue) {
        CellValueStyleBuilder builder = new CellValueStyleBuilder();
        builder.HTML = new StringBuilder(cellValue);
        return builder;
    }

    /**
     * 初始化
     *
     * @param cellValue  单元格初始值
     * @param fontFamily 单元格整体字体样式
     * @return 构建器实例
     */
    public static CellValueStyleBuilder init(String cellValue, String fontFamily) {
        CellValueStyleBuilder builder = new CellValueStyleBuilder();
        builder.HTML = new StringBuilder(cellValue);
        builder.CSS.append("font-family: ").append(fontFamily).append(";");
        return builder;
    }

    /**
     * 初始化
     *
     * @param cellValue 单元格初始值
     * @param isBold    单元格整体字体是否加粗
     * @return 构建器实例
     */
    public static CellValueStyleBuilder init(String cellValue, Boolean isBold) {
        CellValueStyleBuilder builder = new CellValueStyleBuilder();
        builder.HTML = new StringBuilder(cellValue);
        if (isBold) {
            builder.CSS.append("font-weight: bold;");
        }
        return builder;
    }

    /**
     * 初始化
     *
     * @param cellValue  单元格初始值
     * @param isBold     单元格整体字体是否加粗
     * @param fontFamily 单元格整体字体样式
     * @return 构建器实例
     */
    public static CellValueStyleBuilder init(String cellValue, Boolean isBold, String fontFamily) {
        CellValueStyleBuilder builder = new CellValueStyleBuilder();
        builder.HTML = new StringBuilder(cellValue);
        if (isBold) {
            builder.CSS.append("font-weight: bold;");
        }
        if (fontFamily != null && !"".equals(fontFamily)) {
            builder.CSS.append("font-family: ").append(fontFamily).append(";");
        }
        return builder;
    }

    /**
     * 设置单元格整体数值的颜色
     *
     * @param color 颜色，十六进制颜色代码，比如 #FF9B00
     * @return 构建器实例
     */
    public CellValueStyleBuilder setColor(String color) {
        this.CSS.append("color:").append(color).append(";");
        return this;
    }

    /**
     * 设置单元格增体数值的大小
     *
     * @param px 大小，比如【小四】大小为16px
     * @return 构建器实例
     */
    public CellValueStyleBuilder setFontSize(Integer px) {
        this.CSS.append("font-size:").append(px).append("px;");
        return this;
    }


    /**
     * 拼接值
     *
     * @param cellValue 单元格值
     * @return 构造器实例
     */
    public CellValueStyleBuilder append(String cellValue) {
        //拼接数据
        this.HTML.append("<span>").append(cellValue).append("</span>");
        return this;
    }

    /**
     * 拼接值
     *
     * @param cellValue 值
     * @param color     颜色 十六进制颜色 例如#FF9B00
     * @return 构造器实例
     */
    public CellValueStyleBuilder append(String cellValue, String color) {
        //拼接数据
        this.HTML.append("<span style = 'color:").append(color).append("'>").append(cellValue).append("</span>");
        return this;
    }

    /**
     * 拼接复选框
     *
     * @param value      复选框前面的值
     * @param isChecked  是否勾选
     * @param fontFamily 复选框及文字的字体样式
     * @return 构造器实例
     */
    public CellValueStyleBuilder appendCheckBox(String value, Boolean isChecked, String fontFamily) {
        //拼接数据
        this.HTML.append("<span style = 'font-family: ").append(fontFamily).append(";'>").append(value).append("</span>").append(isChecked ? "☑" : "□");
        return this;
    }

    /**
     * 拼接复选框
     *
     * @param value     复选框前面的值
     * @param isChecked 是否勾选
     * @return 构造器实例
     */
    public CellValueStyleBuilder appendCheckBox(String value, Boolean isChecked) {
        //拼接数据
        this.HTML.append(value).append(isChecked ? "☑" : "□");
        return this;
    }

    /**
     * 拼接转换后的科学计数法字符
     *
     * @param sciStr 需要转换科学记数法的字符串
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSci(String sciStr) {
        //获取科学记数法转换数据
        sciStr = SciUtil.formatSci(sciStr);
        this.HTML.append(sciStr);
        return this;
    }

    /**
     * 拼接转换后的科学计数法字符
     *
     * @param sciStr 需要转换科学记数法的字符串
     * @param color  科学记数法的颜色 十六进制颜色代码 例如:#FF00FF
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSci(String sciStr, String color) {
        //获取科学记数法转换数据
        sciStr = SciUtil.formatSci(sciStr);
        if (color != null) {
            this.HTML.append("<span style = 'color:").append(color).append("'>").append(sciStr).append("</span>");
        } else {
            this.HTML.append(sciStr);
        }
        return this;
    }

    /**
     * 拼接转换后的下标数据
     *
     * @param supStr 需要转换下标的字符串数据
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSup(String supStr) {
        //拼接下标数据
        this.HTML.append("<sup>").append(supStr).append("</sup>");
        return this;
    }

    /**
     * 拼接转换后的下标数据
     *
     * @param supStr 需要转换下标的字符串数据
     * @param color  下标文本的颜色 十六进制颜色数值 例如：#FF9B00
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSup(String supStr, String color) {
        //拼接上标数据
        this.HTML.append("<sup style = 'color:").append(color).append(";'>").append(supStr).append("</sup>");
        return this;
    }

    /**
     * 拼接转换后的上标数据
     *
     * @param subStr 需要转换上标的字符串数据
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSub(String subStr) {
        //拼接上标数据
        this.HTML.append("<sub>").append(subStr).append("</sub>");
        return this;
    }

    /**
     * 拼接转换后的上标数据
     *
     * @param subStr 需要转换上标的字符串数据
     * @param color  上标文本的颜色 十六进制颜色 例如#FF9B00
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSub(String subStr, String color) {
        //拼接上标数据
        this.HTML.append("<sub style = 'color:").append(color).append(";'>").append(subStr).append("</sub>");
        return this;
    }

    /**
     * 拼接加粗文本
     *
     * @param boldStr 需要加粗的文本
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendBold(String boldStr) {
        //拼接上标数据
        this.HTML.append("<b>").append(boldStr).append("</b>");
        return this;
    }

    /**
     * 拼接加粗文本
     *
     * @param boldStr 需要加粗的文本
     * @param color   字体颜色 十六进制颜色代码 例如:#FF00FF
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendBold(String boldStr, String color) {
        //拼接上标数据
        this.HTML.append("<b style = 'color:").append(color).append("'>").append(boldStr).append("</b>");
        return this;
    }

    /**
     * 拼接加粗文本
     *
     * @param boldStr 需要加粗的文本
     * @param color   字体颜色 十六进制颜色代码 例如 #FF00FF
     * @param size    字体大小
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendBold(String boldStr, String color, Integer size) {
        //拼接上标数据
        this.HTML.append("<b style = 'color:").append(color)
                .append(";font-size:").append(size).append("px").append("'>").append(boldStr).append("</b>");
        return this;
    }

    /**
     * 拼接特殊字体大小文本
     *
     * @param text 需要更改字体大小的文本
     * @param px   需要更改字体的大小【px】
     * @return 构建器实例
     */
    public CellValueStyleBuilder appendSizeFont(String text, Integer px) {
        //拼接上标数据
        this.HTML.append("<span style = 'font-size:").append(px).append("px;font-family: 宋体'>").append(text).append("</b>");
        return this;
    }

    /**
     * 获取将样式拼装成html格式的单元格值
     *
     * @return 将样式拼装成html格式的单元格值
     */
    public String getHtmlVal() {
        return "<span style = '" + this.CSS + "'>" + this.HTML + "</span>";
    }

    /**
     * 获取颜色的16进制
     *
     * @param color 颜色对象
     * @return 16进制颜色
     */
    private String getRGB16(Color color) {
        int red = color.getRed();
        int green = color.getGreen();
        int blue = color.getBlue();
        return String.format("#%02x%02x%02x", red, green, blue);
    }

}