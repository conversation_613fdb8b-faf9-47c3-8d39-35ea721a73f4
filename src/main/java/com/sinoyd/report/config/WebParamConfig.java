package com.sinoyd.report.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 业务参数配置
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/30
 */
@Configuration
@RefreshScope
@Getter
public class WebParamConfig {

    /**
     * API接口请求地址
     */
    @Value("${report.api.requestHost: localhost}")
    private String apiHost;
}
