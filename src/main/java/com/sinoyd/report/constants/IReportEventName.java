package com.sinoyd.report.constants;

/**
 * 事件名
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
public interface IReportEventName {
    /**
     * 报表基础配置事件名
     */
    String BASE_CONFIG = "报表基础配置";

    /**
     * 报告组件配置事件名称
     */
    String REPORT_MODULE = "报告组件配置";

    /**
     * 数据集配置事件名称
     */
    String REPORT_DATA_SET = "数据集配置";

    /**
     * API接口管理事件名称
     */
    String REPORT_API = "API接口管理配置";

    /**
     * 数据集应用配置事件名称
     */
    String DATA_SET_APPLY_COLUMN = "数据集应用配置";

    /**
     * 采样单配置事件名称
     */
    String SAMPLING_CONFIG = "采样单配置";

    /**
     * 采样单参数配置事件名称
     */
    String SAMPLING_PARAM = "采样单参数配置";

    /**
     * 原始记录单配置事件名称
     */
    String WORKSHEET_CONFIG = "原始记录单配置";

    /**
     * 原始记录单参数配置事件名称
     */
    String WORKSHEET_PARAM = "原始记录单参数配置";

    /**
     * 测试项目公式配置事件名称
     */
    String TEST_FORMULA = "测试项目公式配配置";

    /**
     * 测试项目公式方程配置事件名称
     */
    String TEST_FORMULA_EQUATION = "测试项目公式方程配置";

    /**
     * 测试项目公式方程参数配置事件名称
     */
    String TEST_FORMULA_EQUATION_PARAM = "测试项目公式方程参数配置";
}
