package com.sinoyd.report.constants;

/**
 * 报告生成使用的常量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/05
 */
public interface IReportGenerateConstants {

    /**
     * 报告组件策略编码（组件生成策略的注入的Bean名称）
     */
    interface IReportComponentStrategy {

        //region 通用组件
        /**
         * 报告基础信息组件
         */
        String REPORT_BASIC = "basic";

        /**
         * 主表复合组件
         */
        String COMPOUND_COMPONENT = "compoundComponent";
        //endregion

        //region 水报告组件
        /**
         * 废水报告检测结果表组件
         */
        String WASTE_WATER_STD_SAMPLE_DATA = "dtWasteWaterStdTable";

        /**
         * 常规水主表组件
         */
        String MAIN_WASTE_WATER_STD_DATA = "mainWasteWaterStdData";

        /**
         * 常规水主表测试表2组件
         */
        String MAIN_WASTE_WATER_SCD_STD_DATA = "mainWasteWaterSecondStdData";

        /**
         * 水报告数据表头数据
         */
        String HEAD_WATER_STD_TABLE = "dtWaterHeadStdTable";
        //endregion

        //region 技术依据表
        /**
         * 技术依据表
         */
        String CRITERION_STD_DATA = "dtCriterionStdTable";
        //endregion
    }

    /**
     * 报告样品分页方式策略编码（报告样品分页方式的注入的Bean名称）
     */
    interface IGroupTypeStrategy{
        /**
         * 普通字段类型分页方式
         */
        String FIELD_GROUP_TYPE = "fieldGroupType";

        /**
         * 日期字段类型分页方式
         */
        String DATE_GROUP_TYPE = "dateGroupType";
    }

    /**
     * 报告生成占位符
     */
    interface IReportPlaceholder{

        /**
         * 关联id
         */
        String ID = "Id";

        /**
         * 关联表id
         */
        String TABLE_ID = "TableId";

        /**
         * 项目信息占位符
         */
        interface ProjectInfo{
            /**
             * 报告编号
             */
            String REPORT_CODE = "ReportCode";

            /**
             * 报告编制日期(yyyy.MM.dd)
             */
            String REPORT_DATE = "ReportDate";

            /**
             * 报告编制日期(yyyy-MM-dd)
             */
            String REPORT_DATE_YEAR = "ReportDateYear";

            /**
             * 报告编制日期(yyyy年MM月dd日)
             */
            String REPORT_DATE_CN = "ReportDateCN";

            /**
             * 报告编制日期(二0二三年二月)
             */
            String REPORT_DATE_CN_BIG = "ReportDateCNBig";

            /**
             * 项目类型
             */
            String PROJECT_TYPE = "ProjectType";

            /**
             * 项目名称
             */
            String PROJECT_NAME = "ProjectName";

            /**
             * 项目名称（电子报告）
             */
            String ELECTRONIC_PROJECT_NAME = "ElectronicProjectName";

            /**
             * 系统编号（电子报告）
             */
            String ELECTRONIC_SYSTEM_CODE = "ElectronicSystemCode";

            /**
             * 项目编号
             */
            String PROJECT_CODE = "ProjectCode";

            /**
             * 委托单位
             */
            String CUSTOMER_NAME = "CustomerName";

            /**
             * 委托单位（电子报告）
             */
            String ELECTRONIC_CUSTOMER_NAME = "ElectronicCustomerName";

            /**
             * 受检单位
             */
            String INSPECTED_ENT = "InspectedEnt";

            /**
             * 受检单位（电子报告）
             */
            String ELECTRONIC_INSPECTED_ENT = "ElectronicInspectedEnt";

            /**
             * 项目负责人
             */
            String LEADER = "Leader";

            /**
             * 编制报告人
             */
            String REPORT_MAKER = "ReportMaker";

            /**
             * 项目登记时间
             */
            String REGISTER_TIME = "RegisterTime";

            /**
             * 地址
             */
            String CUSTOMER_ADDRESS = "CustomerAddress";

            /**
             * 地址（电子报告）
             */
            String ELECTRONIC_CUSTOMER_ADDRESS = "ElectronicCustomerAddress";

            /**
             * 受检方地址
             */
            String INSPECTED_ADDRESS = "InspectedAddress";

            /**
             * 受检方地址（电子报告）
             */
            String ELECTRONIC_INSPECTED_ADDRESS = "ElectronicInspectedAddress";

            /**
             * 联系人
             */
            String LINK_MAN = "LinkMan";

            /**
             * 受检方联系人
             */
            String INSPECTED_LINK_MAN = "InspectedLinkMan";

            /**
             * 电话
             */
            String LINK_PHONE = "LinkPhone";

            /**
             * 受检单位电话
             */
            String INSPECTED_LINK_PHONE = "InspectedLinkPhone";

            /**
             * 当前日期
             */
            String CAPITAL_DATE = "CapitalDate";

            /**
             * 当前日期
             */
            String CREATE_DATE = "CreateDate";

            /**
             * 监测目的
             */
            String MONITOR_PURPOSE = "MonitorPurpose";

            /**
             * 报告签发日期
             */
            String SIGN_DATE = "SignDate";
        }

        /**
         * 样品信息及数据信息
         */
        interface SampleInfo{
            /**
             * 送养人
             */
            String SENDER = "Sender";

            /**
             * 检测类型
             */
            String SAMPLE_TYPE = "SampleType";

            /**
             * 样品性状
             */
            String SAMPLE_STATUS_DESCRIBE = "SampleStatusDescribe";

            /**
             * 采样时间
             */
            String SAMPLING_TIME = "SamplingTime";

            /**
             * 分析时间
             */
            String ANALYZE_TIME = "AnalyzeTime";

            /**
             * 采样人
             */
            String SAMPLING_PERSON = "SamplingPerson";

            /**
             * 分析项目
             */
            String ANALYZE_ITEM = "AnalyzeItem";

            /**
             * 分析方法
             */
            String ANALYZE_METHOD = "AnalyzeMethod";

            /**
             * 仪器编号及名称
             */
            String INSTR_MODEL_NAME_LIST_FOR_ITEM = "InstrModelNameListForItem";
        }

        /**
         * 系统基础信息
         */
        interface SystemInfo{
            /**
             * 企业名称
             */
            String ENTERPRISE_NAME = "EnterpriseName";

            /**
             * 企业英文名称
             */
            String ENTERPRISE_ENGLISH = "EnterpriseEnglish";

            /**
             * 企业地址
             */
            String ENTERPRISE_ADDRESS = "EnterpriseAddress";

            /**
             * 企业邮编
             */
            String ENTERPRISE_ZIP_CODE = "EnterpriseZipCode";

            /**
             * 企业电话
             */
            String ENTERPRISE_PHONE = "EnterprisePhone";

            /**
             * 系统编码
             */
            String SYSTEM_CODE = "SystemCode";
        }
    }
}
