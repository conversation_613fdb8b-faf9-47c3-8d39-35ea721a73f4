package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.ApiColumnCriteria;
import com.sinoyd.report.dto.DtoApiColumn;
import com.sinoyd.report.service.ApiColumnService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * API接口列配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@RestController
@RequestMapping("api/report/apiColumn")
public class ApiColumnController extends BaseJpaController<DtoApiColumn, String, ApiColumnService> {


    /**
     * 分页查询列数据
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoApiColumn>> findByPage(ApiColumnCriteria criteria){
        RestResponse<List<DtoApiColumn>> restResponse = new RestResponse<>();
        PageBean<DtoApiColumn> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoApiColumn> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }
}
