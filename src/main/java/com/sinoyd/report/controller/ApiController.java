package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.ApiCriteria;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiColumn;
import com.sinoyd.report.service.ApiService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * API接口管理接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@RestController
@RequestMapping("api/report/api")
public class ApiController extends BaseJpaController<DtoApi, String, ApiService> {


    /**
     * 分页查询
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoApi>> findByPage(ApiCriteria criteria) {
        RestResponse<List<DtoApi>> restResponse = new RestResponse<>();
        PageBean<DtoApi> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoApi> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 校验接口
     *
     * @param api 数据集对象
     * @return 接口请求结果列
     */
    @PostMapping("/verify")
    public RestResponse<List<DtoApiColumn>> verify(@RequestBody DtoApi api) {
        RestResponse<List<DtoApiColumn>> restResponse = new RestResponse<>();
        restResponse.setData(service.verifyApi(api));
        restResponse.setMsg("验证成功!");
        return restResponse;
    }


    /**
     * 新增API接口管理配置
     *
     * @param api 数据源实体数据
     * @return 保存后的数据源数据
     */
    @PostMapping
    public RestResponse<DtoApi> save(@RequestBody DtoApi api) {
        RestResponse<DtoApi> restResponse = new RestResponse<>();
        restResponse.setData(service.save(api));
        restResponse.setMsg("保存成功!");
        return restResponse;
    }

    /**
     * 更新API接口管理配置
     *
     * @param api 数据源实体数据
     * @return 更新后的数据源数据
     */
    @PutMapping
    public RestResponse<DtoApi> update(@RequestBody DtoApi api) {
        RestResponse<DtoApi> restResponse = new RestResponse<>();
        restResponse.setData(service.update(api));
        restResponse.setMsg("更新成功!");
        return restResponse;
    }

    /**
     * 查询API接口管理配置详情
     *
     * @param id API接口管理配置id
     * @return API接口管理配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoApi> findOne(@PathVariable("id") String id) {
        RestResponse<DtoApi> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        restResponse.setMsg("查询成功!");
        return restResponse;
    }

    /**
     * 批量删除API接口管理配置
     *
     * @param ids 需要删除的API接口数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setMsg("删除成功!");
        return restResponse;
    }
}
