package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.ApplyConfigCriteria;
import com.sinoyd.report.dto.DtoApplyConfig;
import com.sinoyd.report.service.ApplyConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 基础配置应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/applyConfig")
public class ApplyConfigController extends BaseJpaController<DtoApplyConfig, String, ApplyConfigService> {


    /**
     * 分页动态条件查询ApplyConfig
     *
     * @param applyConfigCriteria 条件参数
     * @return RestResponse<List < DtoApplyConfig>>
     */
    @GetMapping
    public RestResponse<List<DtoApplyConfig>> findByPage(ApplyConfigCriteria applyConfigCriteria) {
        PageBean<DtoApplyConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoApplyConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, applyConfigCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ApplyConfig
     *
     * @param id 主键id
     * @return RestResponse<DtoApplyConfig>
     */
    @GetMapping(path = "/{id}")
    public RestResponse<DtoApplyConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoApplyConfig> restResponse = new RestResponse<>();
        DtoApplyConfig applyConfig = service.findOne(id);
        restResponse.setData(applyConfig);
        restResponse.setRestStatus(StringUtils.isNull(applyConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ApplyConfig
     *
     * @param applyConfig 实体列表
     * @return RestResponse<DtoApplyConfig>
     */
    @PostMapping
    public RestResponse<DtoApplyConfig> create(@RequestBody DtoApplyConfig applyConfig) {
        RestResponse<DtoApplyConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(applyConfig));
        return restResponse;
    }

    /**
     * 新增ApplyConfig
     *
     * @param applyConfig 实体列表
     * @return RestResponse<DtoApplyConfig>
     */
    @PutMapping
    public RestResponse<DtoApplyConfig> update(@RequestBody DtoApplyConfig applyConfig) {
        RestResponse<DtoApplyConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(applyConfig));
        return restResponse;
    }

    /**
     * 根据id批量删除ApplyConfig
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}