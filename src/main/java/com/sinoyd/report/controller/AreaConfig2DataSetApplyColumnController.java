package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.criteria.AreaConfig2DataSetApplyColumnCriteria;
import com.sinoyd.report.dto.DtoAreaConfig2DataSetApplyColumn;
import com.sinoyd.report.service.AreaConfig2DataSetApplyColumnService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 区域配置与数据集列应用映射数据接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/18
 */
@RestController
@RequestMapping("api/report/areaConfig2DataSetApplyColumn")
public class AreaConfig2DataSetApplyColumnController extends BaseJpaController<DtoAreaConfig2DataSetApplyColumn, String, AreaConfig2DataSetApplyColumnService> {

    /**
     * 分页查询
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoAreaConfig2DataSetApplyColumn>> findByPage(AreaConfig2DataSetApplyColumnCriteria criteria){
        RestResponse<List<DtoAreaConfig2DataSetApplyColumn>> restResponse = new RestResponse<>();
        PageBean<DtoAreaConfig2DataSetApplyColumn> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoAreaConfig2DataSetApplyColumn> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存数据
     *
     * @param dataSetApplyColumns 数据
     * @return 数据
     */
    @PostMapping
    public RestResponse<List<DtoAreaConfig2DataSetApplyColumn>> batchSave(@RequestBody List<DtoAreaConfig2DataSetApplyColumn> dataSetApplyColumns) {
        RestResponse<List<DtoAreaConfig2DataSetApplyColumn>> response = new RestResponse<>();
        dataSetApplyColumns = dataSetApplyColumns.stream().filter(p -> StringUtils.isNotEmpty(p.getAreaConfigId()) && !UUIDHelper.guidEmpty().equals(p.getAreaConfigId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(dataSetApplyColumns)) {
            response.setData(service.save(dataSetApplyColumns));
        }
        return response;
    }

    /**
     * 批量删除数据集应用列配置
     *
     * @param ids 需要删除的数据集数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }

}
