package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.service.AreaConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 报表区域配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@RestController
@RequestMapping("api/report/areaConfig")
public class AreaConfigController extends BaseJpaController<DtoAreaConfig, String, AreaConfigService> {


    /**
     * 批量保存报表配置下的区域配置数据
     *
     * @param entities 区域配置数据
     * @return 已保存的数据
     */
    @PostMapping("/batch")
    public RestResponse<List<DtoAreaConfig>> saveBatch(@RequestBody List<DtoAreaConfig> entities) {
        RestResponse<List<DtoAreaConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.save(entities));
        return restResponse;
    }

    /**
     * 保存区域配置信息
     *
     * @param areaConfig 区域配置
     * @return RestResponse<DtoAreaConfig>
     */
    @PostMapping()
    public RestResponse<DtoAreaConfig> save(@RequestBody DtoAreaConfig areaConfig) {
        RestResponse<DtoAreaConfig> response = new RestResponse<>();
        response.setData(service.save(areaConfig));
        return response;
    }

    /**
     * 更新区域配置信息
     *
     * @param areaConfig 区域配置
     * @return RestResponse<DtoAreaConfig>
     */
    @PutMapping()
    public RestResponse<DtoAreaConfig> update(@RequestBody DtoAreaConfig areaConfig) {
        RestResponse<DtoAreaConfig> response = new RestResponse<>();
        response.setData(service.update(areaConfig));
        return response;
    }

    /**
     * 删除区域配置信息
     *
     * @param ids id集合
     * @return RestResponse<Integer>
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }

    /**
     * 根据报表模板查询区域配置数据
     *
     * @param reportCode 报表模板
     * @return 区域配置数据
     */
    @GetMapping("/{reportCode}")
    public RestResponse<List<DtoAreaConfig>> findByReportCode(@PathVariable("reportCode") String reportCode) {
        RestResponse<List<DtoAreaConfig>> restResponse = new RestResponse<>();
        List<DtoAreaConfig> data = service.findByReportCode(reportCode);
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        return restResponse;
    }

    /**
     * 获取区域配置类型
     *
     * @return 区域类型
     */
    @GetMapping("/areaTypes")
    public RestResponse<List<String>> findAreaTypes(){
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAreaTypes());
        return restResponse;
    }

    /**
     * 获取区域配置列表
     *
     * @return RestResponse<Map>
     */
    @GetMapping("/getAreaConfigMap")
    public RestResponse<Map<String, Object>> getAreaConfigMap(DtoAreaConfig areaConfig) {
        RestResponse<Map<String, Object>> response = new RestResponse<>();
        response.setData(service.getAreaConfigMap(areaConfig.getReportCode()));
        return response;
    }

    /**
     * 获取区域配置详情
     *
     * @param id  主键
     * @return    RestResponse<DtoAreaConfig>
     */
    @GetMapping("/find/{id}")
    public RestResponse<DtoAreaConfig> findOne(@PathVariable(name = "id") String id) {
        RestResponse<DtoAreaConfig> response = new RestResponse<>();
        response.setData(service.findOne(id));
        return response;
    }
}
