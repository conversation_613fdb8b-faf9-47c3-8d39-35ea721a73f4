package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoAreaExpandConfig;
import com.sinoyd.report.service.AreaExpandConfigService;
import org.springframework.web.bind.annotation.*;

/**
 * 报表区域扩展配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@RestController
@RequestMapping("api/report/areaExpandConfig")
public class AreaExpandConfigController extends BaseJpaController<DtoAreaExpandConfig, String, AreaExpandConfigService> {


    /**
     * 保存区域扩展配置信息
     *
     * @param areaExpandConfig 区域扩展配置
     * @return RestResponse<DtoAreaExpandConfig>
     */
    @PostMapping()
    public RestResponse<DtoAreaExpandConfig> save(@RequestBody DtoAreaExpandConfig areaExpandConfig) {
        RestResponse<DtoAreaExpandConfig> response = new RestResponse<>();
        response.setData(service.save(areaExpandConfig));
        return response;
    }

    /**
     * 更新区域扩展配置信息
     *
     * @param areaExpandConfig 区域扩展配置
     * @return RestResponse<DtoAreaExpandConfig>
     */
    @PutMapping()
    public RestResponse<DtoAreaExpandConfig> update(@RequestBody DtoAreaExpandConfig areaExpandConfig) {
        RestResponse<DtoAreaExpandConfig> response = new RestResponse<>();
        response.setData(service.update(areaExpandConfig));
        return response;
    }

    /**
     * 获取区域扩展配置详情
     *
     * @param id 主键
     * @return RestResponse<DtoAreaConfig>
     */
    @GetMapping("/{id}")
    public RestResponse<DtoAreaExpandConfig> findOne(@PathVariable(name = "id") String id) {
        RestResponse<DtoAreaExpandConfig> response = new RestResponse<>();
        response.setData(service.findOne(id));
        return response;
    }

    /**
     * 按照区域id获取区域扩展配置
     *
     * @param areaConfigId 区域id
     * @return RestResponse<DtoAreaConfig>
     */
    @GetMapping
    public RestResponse<DtoAreaExpandConfig> findByArea(String areaConfigId) {
        RestResponse<DtoAreaExpandConfig> response = new RestResponse<>();
        response.setData(service.findByAreaId(areaConfigId));
        return response;
    }
}
