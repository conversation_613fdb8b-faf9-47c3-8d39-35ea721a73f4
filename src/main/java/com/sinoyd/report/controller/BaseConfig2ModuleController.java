package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoBaseConfig2Module;
import com.sinoyd.report.dto.DtoReportModule;
import com.sinoyd.report.service.BaseConfig2ModuleService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BaseConfig2Module服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@RestController
@RequestMapping("api/report/baseConfig2Module")
public class BaseConfig2ModuleController extends BaseJpaController<DtoBaseConfig2Module, String, BaseConfig2ModuleService> {

    /**
     * 根据recordConfigId查询
     *
     * @param baseConfigId 报告配置id
     * @return RestResponse<DtoBaseConfig2Module>
     */
    @GetMapping(path = "/{baseConfigId}")
    public RestResponse<List<DtoBaseConfig2Module>> find(@PathVariable(name = "baseConfigId") String baseConfigId) {
        RestResponse<List<DtoBaseConfig2Module>> restResponse = new RestResponse<>();
        List<DtoBaseConfig2Module> reportConfig2Module = service.queryByBaseConfigId(baseConfigId);
        restResponse.setData(reportConfig2Module);
        restResponse.setRestStatus(StringUtils.isNull(reportConfig2Module) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按主键查询组件信息及关联的分页配置信息
     *
     * @param id 主键id
     * @return RestResponse<DtoReportModule>
     */
    @GetMapping(path = "/moduleInfo/{id}")
    public RestResponse<DtoReportModule> findModuleInfo(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        DtoReportModule reportModule = service.findModuleInfo(id);
        restResponse.setData(reportModule);
        restResponse.setRestStatus(StringUtils.isNull(reportModule) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 新增ReportConfig2Module
     *
     * @param reportConfig2Module 实体列表
     * @return RestResponse<DtoBaseConfig2Module>
     */
    @PostMapping
    public RestResponse<DtoBaseConfig2Module> create(@RequestBody DtoBaseConfig2Module reportConfig2Module) {
        RestResponse<DtoBaseConfig2Module> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportConfig2Module));
        return restResponse;
    }

    /**
     * 修改ReportConfig2Module
     *
     * @param ReportConfig2Module 实体列表
     * @return RestResponse<DtoBaseConfig2Module>
     */
    @PutMapping
    public RestResponse<DtoBaseConfig2Module> update(@RequestBody DtoBaseConfig2Module ReportConfig2Module) {
        RestResponse<DtoBaseConfig2Module> restResponse = new RestResponse<>();
        restResponse.setData(service.update(ReportConfig2Module));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportConfig2Module
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        int count = service.deleteConfig2Module(ids);
        restResp.setCount(count);
        return restResp;
    }

}
