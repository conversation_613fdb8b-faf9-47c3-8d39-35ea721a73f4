package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.BaseConfigCriteria;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoDocument;
import com.sinoyd.report.service.BaseConfigService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 报表基础配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@RestController
@RequestMapping("api/report/baseConfig")
public class BaseConfigController extends BaseJpaController<DtoBaseConfig, String, BaseConfigService> {

    /**
     * 分页动态条件查询人员
     *
     * @param criteria 条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoBaseConfig>> findByPage(BaseConfigCriteria criteria) {
        RestResponse<List<DtoBaseConfig>> restResp = new RestResponse<>();
        PageBean<DtoBaseConfig> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtils.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 保存报表基础配置数据
     *
     * @param entity 报表基础配置数据
     * @return 保存后的数据
     */
    @PostMapping
    public RestResponse<DtoBaseConfig> save(@RequestBody DtoBaseConfig entity) {
        RestResponse<DtoBaseConfig> restResp = new RestResponse<>();
        restResp.setData(service.save(entity));
        return restResp;
    }

    /**
     * 保存报表基础配置数据
     *
     * @param entity 报表基础配置数据
     * @return 保存后的数据
     */
    @PutMapping
    public RestResponse<DtoBaseConfig> update(@RequestBody DtoBaseConfig entity) {
        RestResponse<DtoBaseConfig> restResp = new RestResponse<>();
        restResp.setData(service.update(entity));
        return restResp;
    }

    /**
     * 查询数据详细
     *
     * @param id 数据id
     * @return 数据详细
     */
    @GetMapping("/{id}")
    public RestResponse<DtoBaseConfig> findOne(@PathVariable("id") String id) {
        RestResponse<DtoBaseConfig> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }

    /**
     * 根据报表编码查询数据
     *
     * @param reportCode 报表编码
     * @return 数据详细
     */
    @GetMapping("/code/{reportCode}")
    public RestResponse<DtoBaseConfig> findByCode(@PathVariable("reportCode") String reportCode) {
        RestResponse<DtoBaseConfig> restResp = new RestResponse<>();
        restResp.setData(service.findByReportCode(reportCode));
        return restResp;
    }


    /**
     * 保存报表基础配置数据
     *
     * @param ids 需要删除的数据id集合
     * @return 保存后的数据
     */
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setData(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 上传文件
     *
     * @param request 请求
     * @return 结果
     */
    @PostMapping("/upload")
    public RestResponse<List<DtoDocument>> upload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setData(service.upload(request));
        return restResponse;
    }

    /**
     * 文件下载
     *
     * @param configId 下载的Id
     * @param response 响应流
     * @return 返回数据
     */
    @GetMapping("/download/{configId}")
    public RestResponse<String> fileDownload(@PathVariable String configId, HttpServletResponse response) {
        service.downloadReport(configId, response);
        return new RestResponse<>();
    }

    /**
     * 文件预览
     *
     * @param response 输出流
     */
    @GetMapping("/preview/{configId}")
    public void previewDocument(@PathVariable("configId") String configId, HttpServletResponse response) {
        service.preview(configId, response);
    }
}
