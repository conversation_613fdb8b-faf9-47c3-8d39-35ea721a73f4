package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.service.CustomParamConfigService;
import com.sinoyd.report.vo.CustomParamConfigSaveVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 报表全局配置-配置信息接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/14
 */
@RestController
@RequestMapping("api/report/customParamConfig")
public class CustomParamConfigController extends BaseJpaController<DtoCustomParamConfig, String, CustomParamConfigService> {

    /**
     * 根据报表全局配置查询对应的配置信息
     *
     * @param globalConfigId 全局配置id
     * @return 配置数据
     */
    @GetMapping("/{globalConfigId}")
    public RestResponse<List<DtoCustomParamConfig>> findForGlobalConfig(@PathVariable("globalConfigId") String globalConfigId) {
        RestResponse<List<DtoCustomParamConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.findByGlobalConfigId(globalConfigId));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 根据报表全局配置id查询待选择的配置信息
     *
     * @param globalConfigId 全局配置id
     * @return 待选择的配置信息
     */
    @GetMapping("/alternative/{globalConfigId}")
    public RestResponse<List<DtoCustomParamConfig>> findAlternativeConfig(@PathVariable("globalConfigId") String globalConfigId) {
        RestResponse<List<DtoCustomParamConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAlternativeConfig(globalConfigId));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 保存选择的配置信息
     *
     * @param customParamSaveVo 需要保存的数据
     * @return 保存后的数据
     */
    @PostMapping
    public RestResponse<List<DtoCustomParamConfig>> save(@RequestBody CustomParamConfigSaveVO customParamSaveVo) {
        RestResponse<List<DtoCustomParamConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.saveCheckedConfig(customParamSaveVo.getCustomParamConfigList(), customParamSaveVo.getGlobalConfigId()));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
