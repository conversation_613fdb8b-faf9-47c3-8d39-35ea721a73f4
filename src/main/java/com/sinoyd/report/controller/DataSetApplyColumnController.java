package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.DataSetApplyColumnCriteria;
import com.sinoyd.report.dto.DtoDataSetApplyColumn;
import com.sinoyd.report.service.DataSetApplyColumnService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * Sql数据集列配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@RestController
@RequestMapping("api/report/dataSetApplyColumn")
public class DataSetApplyColumnController extends BaseJpaController<DtoDataSetApplyColumn, String, DataSetApplyColumnService> {

    /**
     * 分页查询
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoDataSetApplyColumn>> findByPage(DataSetApplyColumnCriteria criteria){
        RestResponse<List<DtoDataSetApplyColumn>> restResponse = new RestResponse<>();
        PageBean<DtoDataSetApplyColumn> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoDataSetApplyColumn> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }



    /**
     * 新增数据集应用列配置
     *
     * @param dataSetColumn 数据源实体数据
     * @return 保存后的数据源数据
     */
    @PostMapping
    public RestResponse<DtoDataSetApplyColumn> save(@RequestBody DtoDataSetApplyColumn dataSetColumn) {
        RestResponse<DtoDataSetApplyColumn> restResponse = new RestResponse<>();
        restResponse.setData(service.save(dataSetColumn));
        return restResponse;
    }

    /**
     * 更新数据集应用列配置
     *
     * @param dataSetColumn 数据源实体数据
     * @return 更新后的数据源数据
     */
    @PutMapping
    public RestResponse<DtoDataSetApplyColumn> update(@RequestBody DtoDataSetApplyColumn dataSetColumn) {
        RestResponse<DtoDataSetApplyColumn> restResponse = new RestResponse<>();
        restResponse.setData(service.update(dataSetColumn));
        return restResponse;
    }

    /**
     * 查询数据集应用列配置详情
     *
     * @param id 数据集应用列配置id
     * @return 数据集应用列配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoDataSetApplyColumn> findOne(@PathVariable("id") String id) {
        RestResponse<DtoDataSetApplyColumn> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 批量删除数据集应用列配置
     *
     * @param ids 需要删除的数据集数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }
}
