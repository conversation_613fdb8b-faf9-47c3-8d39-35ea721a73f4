package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.DataSetColumnCriteria;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.service.DataSetColumnService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Sql数据集列配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@RestController
@RequestMapping("api/report/dataSetColumn")
public class DataSetColumnController extends BaseJpaController<DtoDataSetColumn, String, DataSetColumnService> {


    /**
     * 根据数据集Id查询列数据
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoDataSetColumn>> findByPage(DataSetColumnCriteria criteria){
        RestResponse<List<DtoDataSetColumn>> restResponse = new RestResponse<>();
        PageBean<DtoDataSetColumn> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoDataSetColumn> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

}
