package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.DataSetCriteria;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.service.DataSetService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * Sql数据集配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@RestController
@RequestMapping("api/report/dataSet")
public class DataSetController extends BaseJpaController<DtoDataSet, String, DataSetService> {

    /**
     * 分页查询
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoDataSet>> findByPage(DataSetCriteria criteria) {
        RestResponse<List<DtoDataSet>> restResponse = new RestResponse<>();
        PageBean<DtoDataSet> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        List<DtoDataSet> data = pageBean.getData();
        restResponse.setRestStatus(StringUtils.isEmpty(data) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 校验SQL
     *
     * @param dataSet 数据集对象
     * @return SQL执行结果列
     */
    @PostMapping("/verify")
    public RestResponse<DtoDataSet> verify(@RequestBody DtoDataSet dataSet) {
        RestResponse<DtoDataSet> restResponse = new RestResponse<>();
        restResponse.setData(service.verifySql(dataSet));
        restResponse.setMsg("验证成功!");
        return restResponse;
    }


    /**
     * 新增数据集配置
     *
     * @param dataSet 数据源实体数据
     * @return 保存后的数据源数据
     */
    @PostMapping
    public RestResponse<DtoDataSet> save(@RequestBody DtoDataSet dataSet) {
        RestResponse<DtoDataSet> restResponse = new RestResponse<>();
        restResponse.setData(service.save(dataSet));
        restResponse.setMsg("保存成功!");
        return restResponse;
    }

    /**
     * 更新数据集配置
     *
     * @param dataSet 数据源实体数据
     * @return 更新后的数据源数据
     */
    @PutMapping
    public RestResponse<DtoDataSet> update(@RequestBody DtoDataSet dataSet) {
        RestResponse<DtoDataSet> restResponse = new RestResponse<>();
        restResponse.setData(service.update(dataSet));
        restResponse.setMsg("更新成功!");
        return restResponse;
    }

    /**
     * 查询数据集配置详情
     *
     * @param id 数据集配置id
     * @return 数据集配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoDataSet> findOne(@PathVariable("id") String id) {
        RestResponse<DtoDataSet> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        restResponse.setMsg("查询成功!");
        return restResponse;
    }

    /**
     * 批量删除数据集配置
     *
     * @param ids 需要删除的数据集数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setMsg("删除成功!");
        return restResponse;
    }
}
