package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.DataSourceCriteria;
import com.sinoyd.report.dto.DtoDataSource;
import com.sinoyd.report.service.DataSourceService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * Sql数据源配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/30
 */
@RestController
@RequestMapping("api/report/dataSource")
public class DataSourceController extends BaseJpaController<DtoDataSource, String, DataSourceService> {

    /**
     * 分页动态条件查询数据源配置
     *
     * @param criteria 条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoDataSource>> findByPage(DataSourceCriteria criteria) {
        RestResponse<List<DtoDataSource>> restResp = new RestResponse<>();
        PageBean<DtoDataSource> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtils.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }


    /**
     * 验证数据源连接
     *
     * @param dataSource 数据源连接信息
     * @return 是否校验成功
     */
    @PostMapping("/verify")
    public RestResponse<String> verifyConnect(@RequestBody DtoDataSource dataSource) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.verifyConnection(dataSource);
        restResponse.setMsg("校验成功!");
        return restResponse;
    }

    /**
     * 新增数据源配置
     *
     * @param dataSource 数据源实体数据
     * @return 保存后的数据源数据
     */
    @PostMapping
    public RestResponse<DtoDataSource> save(@RequestBody DtoDataSource dataSource) {
        RestResponse<DtoDataSource> restResponse = new RestResponse<>();
        restResponse.setData(service.save(dataSource));
        return restResponse;
    }

    /**
     * 更新数据源配置
     *
     * @param dataSource 数据源实体数据
     * @return 更新后的数据源数据
     */
    @PutMapping
    public RestResponse<DtoDataSource> update(@RequestBody DtoDataSource dataSource) {
        RestResponse<DtoDataSource> restResponse = new RestResponse<>();
        restResponse.setData(service.update(dataSource));
        return restResponse;
    }

    /**
     * 查询数据源配置详情
     *
     * @param id 数据源配置id
     * @return 数据源配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoDataSource> findOne(@PathVariable("id") String id) {
        RestResponse<DtoDataSource> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 删除单条数据源配置
     *
     * @param id 需要删除的数据源数据id
     * @return 删除的数据
     */
    @DeleteMapping("/{id}")
    public RestResponse<Integer> delete(@PathVariable("id") String id) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(id));
        return restResponse;
    }

    /**
     * 批量删除数据源配置
     *
     * @param ids 需要删除的数据源数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }
}
