package com.sinoyd.report.controller;

import com.sinoyd.base.vo.DocumentPathVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.DocumentCriteria;
import com.sinoyd.report.dto.DtoDocument;
import com.sinoyd.report.service.DocumentService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 文档用户访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-10-08
 */
@RestController
@RequestMapping("/api/report/document")
public class DocumentController extends BaseJpaController<DtoDocument, String, DocumentService> {

    /**
     * 上传文件
     *
     * @param request 请求
     * @return 结果
     */
    @PostMapping("/upload")
    public RestResponse<List<DtoDocument>> upload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setData(service.upload(request));
        return restResponse;
    }

    /**
     * 文件下载
     *
     * @param documentId 文档Id
     * @param response   响应流
     * @return 返回数据
     */
    @GetMapping("/download/{id}")
    public RestResponse<String> download(@PathVariable("id") String documentId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        service.download(documentId, response);
        return restResp;
    }


    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @GetMapping("/path/{code}")
    public RestResponse<DocumentPathVO> getDocumentPath(@PathVariable("code") String code, @RequestParam Map<String, Object> map) {
        RestResponse<DocumentPathVO> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPathFromXml(code, map));
        return restResp;
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @GetMapping
    public RestResponse<List<DtoDocument>> findByPage(DocumentCriteria c) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        PageBean<DtoDocument> page = super.getPageBean();
        service.findByPage(page, c);
        restResp.setRestStatus(StringUtils.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 查询详情
     *
     * @param id 主键id
     * @return 结果
     */
    @GetMapping("/{id}")
    public RestResponse<DtoDocument> findOne(@PathVariable("id") String id) {
        RestResponse<DtoDocument> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 单个删除
     *
     * @param id 主键id
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable("id") String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setCount(service.logicDeleteById(id));
        return restResponse;
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览实体
     * @param response 输出流
     */
    @PostMapping("/preview")
    public void previewDocument(@RequestBody DocumentPreviewVO vo, HttpServletResponse response) {
        service.previewDocument(vo, response);
    }

    /**
     * 批量删除
     *
     * @param ids 需要删除的id集合
     * @return 删除的记录数
     */

    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }
}