package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoGlobalConfig;
import com.sinoyd.report.service.GlobalConfigService;
import org.springframework.web.bind.annotation.*;


/**
 * 报表全局参数配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@RestController
@RequestMapping("api/report/globalConfig")
public class GlobalConfigController extends BaseJpaController<DtoGlobalConfig, String, GlobalConfigService> {

    /**
     * 根据报表编码查询全局参数配置数据
     *
     * @param reportCode 报表编码
     * @return 全局参数配置数据
     */
    @GetMapping("/{reportCode}")
    public RestResponse<DtoGlobalConfig> findByReportCode(@PathVariable("reportCode") String reportCode){
        RestResponse<DtoGlobalConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.findByReportCode(reportCode));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 保存全局参数配置数据
     *
     * @param entity 需要保存的数据
     * @return 保存后的数据
     */
    @PostMapping
    public RestResponse<DtoGlobalConfig> save(@RequestBody DtoGlobalConfig entity){
        RestResponse<DtoGlobalConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(entity));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 更新全局参数配置数据
     *
     * @param entity 需要保存的数据
     * @return 保存后的数据
     */
    @PutMapping
    public RestResponse<DtoGlobalConfig> update (@RequestBody DtoGlobalConfig entity){
        RestResponse<DtoGlobalConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(entity));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
