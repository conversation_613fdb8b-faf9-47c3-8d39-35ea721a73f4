package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoReportModule2GroupType;
import com.sinoyd.report.dto.customer.DtoModuleGroupTypeTemp;
import com.sinoyd.report.service.ReportModule2GroupTypeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ReportModule2GroupType 服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@RestController
@RequestMapping("api/report/reportModule2GroupType")
public class ReportModule2GroupTypeController extends BaseJpaController<DtoReportModule2GroupType, String, ReportModule2GroupTypeService> {

    /**
     * 新增 ReportModule2GroupType
     *
     * @param moduleGroupTypeTemp 请求实体
     * @return RestResponse<ReportModule2GroupType>
     */
    @PostMapping
    public RestResponse<List<DtoReportModule2GroupType>> create(@RequestBody DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        RestResponse<List<DtoReportModule2GroupType>> restResponse = new RestResponse<>();
        restResponse.setData(service.saveGroupType(moduleGroupTypeTemp));
        return restResponse;
    }

    /**
     * 修改 ReportModule2GroupType
     *
     * @param moduleGroupTypeTemp 实体列表
     * @return RestResponse<DtoReportConfig2Module>
     */
    @PutMapping
    public RestResponse<List<DtoReportModule2GroupType>> update(@RequestBody DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        RestResponse<List<DtoReportModule2GroupType>> restResponse = new RestResponse<>();
        restResponse.setData(service.updateGroupType(moduleGroupTypeTemp));
        return restResponse;
    }

}
