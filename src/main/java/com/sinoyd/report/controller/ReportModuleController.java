package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.ReportModuleCriteria;
import com.sinoyd.report.dto.DtoReportModule;
import com.sinoyd.report.service.ReportModuleService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ReportModule服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@RestController
@RequestMapping("api/report/reportModule")
public class ReportModuleController  extends BaseJpaController<DtoReportModule, String, ReportModuleService> {

    /**
     * 分页动态条件查询ReportModule
     *
     * @param reportModuleCriteria 条件参数
     * @return RestResponse<List < ReportModule>>
     */
    @GetMapping
    public RestResponse<List<DtoReportModule>> findByPage(ReportModuleCriteria reportModuleCriteria) {
        PageBean<DtoReportModule> pageBean = super.getPageBean();
        RestResponse<List<DtoReportModule>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportModuleCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ReportModule
     *
     * @param id 主键id
     * @return RestResponse<DtoReportModule>
     */
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReportModule> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        DtoReportModule reportModule = service.findOne(id);
        restResponse.setData(reportModule);
        restResponse.setRestStatus(StringUtils.isNull(reportModule) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 新增ReportModule
     *
     * @param reportModule 实体列表
     * @return RestResponse<DtoReportModule>
     */
    @PostMapping
    public RestResponse<DtoReportModule> create(@RequestBody DtoReportModule reportModule) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportModule));
        return restResponse;
    }

    /**
     * 修改ReportModule
     *
     * @param ReportModule 实体列表
     * @return RestResponse<DtoReportModule>
     */
    @PutMapping
    public RestResponse<DtoReportModule> update(@RequestBody DtoReportModule ReportModule) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        restResponse.setData(service.update(ReportModule));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportModule
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteModule(ids);
        restResp.setCount(count);
        return restResp;
    }

}
