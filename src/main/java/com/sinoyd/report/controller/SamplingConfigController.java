package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.SamplingConfigCriteria;
import com.sinoyd.report.dto.DtoSamplingConfig;
import com.sinoyd.report.service.SamplingConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * SamplingConfig应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/samplingConfig")
public class SamplingConfigController extends BaseJpaController<DtoSamplingConfig, String, SamplingConfigService> {

    /**
     * 分页动态条件查询SamplingConfig
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoSamplingConfig>>
     */
    @GetMapping
    public RestResponse<List<DtoSamplingConfig>> findByPage(SamplingConfigCriteria criteria) {
        PageBean<DtoSamplingConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoSamplingConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询单条采样单配置
     *
     * @param id 采样单配置id
     * @return 采样单配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoSamplingConfig> findOne(@PathVariable String id) {
        RestResponse<DtoSamplingConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 新增samplingConfig
     *
     * @param samplingConfig 实体列表
     * @return RestResponse<DtoSamplingConfig>
     */
    @PostMapping
    public RestResponse<DtoSamplingConfig> create(@RequestBody DtoSamplingConfig samplingConfig) {
        RestResponse<DtoSamplingConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(samplingConfig));
        return restResponse;
    }

    /**
     * 复制samplingConfig
     *
     * @param samplingConfig 实体列表
     * @return RestResponse<DtoSamplingConfig>
     */
    @PostMapping("/copy")
    public RestResponse<DtoSamplingConfig> copy(@RequestBody DtoSamplingConfig samplingConfig) {
        RestResponse<DtoSamplingConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.copy(samplingConfig));
        return restResponse;
    }

    /**
     * 修改samplingConfig
     *
     * @param samplingConfig 实体列表
     * @return RestResponse<DtoSamplingConfig>
     */
    @PutMapping
    public RestResponse<DtoSamplingConfig> update(@RequestBody DtoSamplingConfig samplingConfig) {
        RestResponse<DtoSamplingConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(samplingConfig));
        return restResponse;
    }

    /**
     * "根据id批量删除samplingConfig
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据ids获取采样单配置
     *
     * @param ids ids
     * @return 采样单配置
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoSamplingConfig>> findAllByIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoSamplingConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll(ids, Boolean.FALSE));
        return restResponse;
    }
}
