package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.SamplingParamCriteria;
import com.sinoyd.report.dto.DtoSamplingParam;
import com.sinoyd.report.service.SamplingParamService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * SamplingParam应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/samplingParam")
public class SamplingParamController extends BaseJpaController<DtoSamplingParam, String, SamplingParamService> {

    /**
     * 分页动态条件查询SamplingParam
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoSamplingParam>>
     */
    @GetMapping
    public RestResponse<List<DtoSamplingParam>> findByPage(SamplingParamCriteria criteria) {
        PageBean<DtoSamplingParam> pageBean = super.getPageBean();
        RestResponse<List<DtoSamplingParam>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增SamplingParam
     *
     * @param samplingParam 实体列表
     * @return RestResponse<DtoSamplingParam>
     */
    @PostMapping
    public RestResponse<DtoSamplingParam> create(@Validated @RequestBody DtoSamplingParam samplingParam) {
        RestResponse<DtoSamplingParam> restResponse = new RestResponse<>();
        restResponse.setData(service.save(samplingParam));
        return restResponse;
    }

    /**
     * 修改SamplingParam
     *
     * @param samplingParam 实体列表
     * @return RestResponse<DtoSamplingParam>
     */
    @PutMapping
    public RestResponse<DtoSamplingParam> update(@Validated @RequestBody DtoSamplingParam samplingParam) {
        RestResponse<DtoSamplingParam> restResponse = new RestResponse<>();
        restResponse.setData(service.update(samplingParam));
        return restResponse;
    }

    /**
     * "根据id批量删除SamplingParam
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param ids 采样单配置Ids
     * @return 参数集合
     */
    @PostMapping("/samplingConfigIds")
    public RestResponse<List<DtoSamplingParam>> findBySamplingConfigIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoSamplingParam>> restResp = new RestResponse<>();
        restResp.setData(service.findBySamplingConfigIdIn(ids));
        return restResp;
    }

    /**
     * 根据ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoSamplingParam>> findByIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoSamplingParam>> restResp = new RestResponse<>();
        restResp.setData(service.findByIds(ids));
        return restResp;
    }
}
