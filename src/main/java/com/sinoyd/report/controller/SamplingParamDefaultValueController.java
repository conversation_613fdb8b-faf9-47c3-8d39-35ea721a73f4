package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;
import com.sinoyd.report.service.SamplingParamDefaultValueService;
import com.sinoyd.report.vo.SamplingParamDefaultValueQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SamplingParamDefaultValue应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/samplingParamDefaultValue")
public class SamplingParamDefaultValueController extends BaseJpaController<DtoSamplingParamDefaultValue, String, SamplingParamDefaultValueService> {

    /**
     * 分页动态条件查询SamplingParamDefaultValue
     * @param queryVO 条件参数
     * @return RestResponse<List<DtoSamplingParamDefaultValue>>
     */
    @GetMapping("/findList")
    public RestResponse<List<DtoSamplingParamDefaultValue>> findList(SamplingParamDefaultValueQueryVO queryVO) {
        RestResponse<List<DtoSamplingParamDefaultValue>> restResponse = new RestResponse<>();
        restResponse.setData(service.findList(queryVO));
        return restResponse;
    }

    /**
     * 批量修改SamplingParamDefaultValue
     * @param samplingParamDefaultValueList 数据载体
     * @return RestResponse<List<DtoSamplingParamDefaultValue>>
     */
    @PutMapping
    public RestResponse<List<DtoSamplingParamDefaultValue>> batchUpdate(@RequestBody List<DtoSamplingParamDefaultValue> samplingParamDefaultValueList) {
        RestResponse<List<DtoSamplingParamDefaultValue>> response = new RestResponse<>();
        response.setData(service.save(samplingParamDefaultValueList));
        return response;
    }

}
