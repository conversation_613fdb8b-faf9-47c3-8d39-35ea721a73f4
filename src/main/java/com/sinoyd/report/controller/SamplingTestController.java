package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.SamplingTestCriteria;
import com.sinoyd.report.dto.DtoSamplingTest;
import com.sinoyd.report.service.SamplingTestService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SamplingTest应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/samplingTest")
public class SamplingTestController extends BaseJpaController<DtoSamplingTest, String, SamplingTestService> {

    /**
     * 分页动态条件查询SamplingTest
     * @param criteria 条件参数
     * @return RestResponse<List<DtoSamplingTest>>
     */
    @GetMapping
    public RestResponse<List<DtoSamplingTest>> findByPage(SamplingTestCriteria criteria) {
        PageBean<DtoSamplingTest> pageBean = super.getPageBean();
        RestResponse<List<DtoSamplingTest>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 批量保存SamplingTest
     * @param samplingTestList 数据载体
     * @return RestResponse<List<DtoSamplingTest>>
     */
    @PostMapping
    public RestResponse<List<DtoSamplingTest>> batchSave(@RequestBody List<DtoSamplingTest> samplingTestList) {
        RestResponse<List<DtoSamplingTest>> response = new RestResponse<>();
        response.setData(service.save(samplingTestList));
        return response;
    }

    /**
     * "根据id批量删除SamplingTest
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


}
