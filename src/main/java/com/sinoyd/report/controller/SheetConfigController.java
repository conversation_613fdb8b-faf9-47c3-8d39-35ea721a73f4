package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.service.SheetConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 报表模板sheet页配置接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
@RestController
@RequestMapping("api/report/sheetConfig")
public class SheetConfigController extends BaseJpaController<DtoSheetConfig, String, SheetConfigService> {


    /**
     * 保存sheet页配置信息
     *
     * @param sheetConfig sheet页配置
     * @return RestResponse<DtoAreaConfig>
     */
    @PostMapping()
    public RestResponse<DtoSheetConfig> save(@RequestBody DtoSheetConfig sheetConfig) {
        RestResponse<DtoSheetConfig> response = new RestResponse<>();
        response.setData(service.save(sheetConfig));
        return response;
    }

    /**
     * 修改sheet页配置信息
     *
     * @param sheetConfig sheet页配置
     * @return RestResponse<DtoAreaConfig>
     */
    @PutMapping()
    public RestResponse<DtoSheetConfig> update(@RequestBody DtoSheetConfig sheetConfig) {
        RestResponse<DtoSheetConfig> response = new RestResponse<>();
        response.setData(service.update(sheetConfig));
        return response;
    }

    /**
     * 删除sheet页配置信息
     *
     * @param ids sheet页配置
     * @return RestResponse<Integer>
     */
    @DeleteMapping()
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        return response;
    }

}
