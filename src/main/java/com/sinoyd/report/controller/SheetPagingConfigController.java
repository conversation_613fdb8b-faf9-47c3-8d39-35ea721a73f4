package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoSheetPagingConfig;
import com.sinoyd.report.service.SheetPagingConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 模板sheet页配置分页依据接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
@RestController
@RequestMapping("api/report/sheetPagingConfig")
public class SheetPagingConfigController extends BaseJpaController<DtoSheetPagingConfig, String, SheetPagingConfigService> {


    /**
     * 新增分页依据数据
     *
     * @param sheetPagingConfig 分页依据信息
     * @return 数据
     */
    @PostMapping
    public RestResponse<DtoSheetPagingConfig> save(@RequestBody DtoSheetPagingConfig sheetPagingConfig) {
        RestResponse<DtoSheetPagingConfig> response = new RestResponse<>();
        response.setData(service.save(sheetPagingConfig));
        return response;
    }

    /**
     * 修改分页依据数据
     *
     * @param sheetPagingConfig 分页依据信息
     * @return 数据
     */
    @PutMapping
    public RestResponse<DtoSheetPagingConfig> update(@RequestBody DtoSheetPagingConfig sheetPagingConfig) {
        RestResponse<DtoSheetPagingConfig> response = new RestResponse<>();
        response.setData(service.update(sheetPagingConfig));
        return response;
    }

    /**
     * 删除sheet页分页依据配置
     *
     * @param ids 需要删除的数据集数据id集合
     * @return 删除的数据
     */
    @DeleteMapping
    public RestResponse<Integer> deleteBatch(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }



}
