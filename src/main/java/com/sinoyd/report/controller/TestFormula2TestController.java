package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.TestFormula2TestCriteria;
import com.sinoyd.report.dto.DtoTestFormula2Test;
import com.sinoyd.report.service.TestFormula2TestService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TestFormula2Test应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormula2Test")
public class TestFormula2TestController extends BaseJpaController<DtoTestFormula2Test, String, TestFormula2TestService> {

    /**
     * 分页动态条件查询TestFormula2Test
     * @param criteria 条件参数
     * @return RestResponse<List<DtoTestFormula2Test>>
     */
    @GetMapping
    public RestResponse<List<DtoTestFormula2Test>> findByPage(TestFormula2TestCriteria criteria) {
        PageBean<DtoTestFormula2Test> pageBean = super.getPageBean();
        RestResponse<List<DtoTestFormula2Test>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 批量保存TestFormula2Test
     * @param testFormula2Tests 数据载体
     * @return RestResponse<List<DtoTestFormula2Test>>
     */
    @PostMapping
    public RestResponse<List<DtoTestFormula2Test>> batchSave(@RequestBody List<DtoTestFormula2Test> testFormula2Tests) {
        RestResponse<List<DtoTestFormula2Test>> response = new RestResponse<>();
        response.setData(service.save(testFormula2Tests));
        return response;
    }

    /**
     * "根据id批量删除TestFormula2Test
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


}
