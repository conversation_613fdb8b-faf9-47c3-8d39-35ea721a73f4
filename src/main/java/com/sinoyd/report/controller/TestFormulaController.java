package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.TestFormulaCriteria;
import com.sinoyd.report.dto.DtoTestFormula;
import com.sinoyd.report.service.TestFormulaService;
import com.sinoyd.report.vo.FormulaQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * TestFormula应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormula")
public class TestFormulaController extends BaseJpaController<DtoTestFormula, String, TestFormulaService> {

    /**
     * 分页动态条件查询TestFormula
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoTestFormula>>
     */
    @GetMapping
    public RestResponse<List<DtoTestFormula>> findByPage(TestFormulaCriteria criteria) {
        PageBean<DtoTestFormula> pageBean = super.getPageBean();
        RestResponse<List<DtoTestFormula>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增TestFormula
     *
     * @param testFormula 实体列表
     * @return RestResponse<DtoTestFormula>
     */
    @PostMapping
    public RestResponse<DtoTestFormula> create(@RequestBody DtoTestFormula testFormula) {
        RestResponse<DtoTestFormula> restResponse = new RestResponse<>();
        restResponse.setData(service.save(testFormula));
        return restResponse;
    }

    /**
     * 新增TestFormula
     *
     * @param testFormula 实体列表
     * @return RestResponse<DtoTestFormula>
     */
    @PutMapping
    public RestResponse<DtoTestFormula> update(@RequestBody DtoTestFormula testFormula) {
        RestResponse<DtoTestFormula> restResponse = new RestResponse<>();
        restResponse.setData(service.update(testFormula));
        return restResponse;
    }

    /**
     * "根据id批量删除TestFormula
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 批量设置公式启用
     *
     * @param ids      主键
     * @param isEnable 是否启用
     * @return RestResponse<Void>
     */
    @PostMapping("/setEnable/{isEnable}")
    public RestResponse<Void> setEnable(@RequestBody List<String> ids, @PathVariable(name = "isEnable") Boolean isEnable) {
        service.setEnable(ids, isEnable);
        return new RestResponse<>();
    }

    /**
     * 复制测试项目公式
     *
     * @param ids id集合
     * @return RestResponse<Void>
     */
    @PostMapping("/copy")
    public RestResponse<Void> copy(@RequestBody List<String> ids) {
        service.copy(ids);
        return new RestResponse<>();
    }

    /**
     * 判断采样单或者原始记录单是否被使用
     *
     * @param objectIds 对象id集合
     * @return RestResponse<Boolean>
     */
    @PostMapping("/bindFormulaFlag")
    public RestResponse<String> bindFormulaFlag(@RequestBody List<String> objectIds) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.bindFormulaFlag(objectIds));
        return restResponse;
    }

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param ids 采样单配置Ids
     * @return 参数集合
     */
    @PostMapping("/objectIds")
    public RestResponse<List<DtoTestFormula>> findByObjectIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        restResp.setData(service.findByObjectIds(ids, true));
        return restResp;
    }

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param formulaId 公式id
     * @param testId    测试项目id
     * @return 参数集合
     */
    @PostMapping("/formula/{formulaId}/{testId}")
    public RestResponse<List<DtoTestFormula>> findByFormulaIdAndTestId(@PathVariable(name = "formulaId") String formulaId,
                                                                       @PathVariable(name = "testId") String testId) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        restResp.setData(service.findByFormulaIdAndTestId(formulaId, testId));
        return restResp;
    }

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param queryVO 表单配置id
     * @return 参数集合
     */
    @PostMapping("/formula")
    public RestResponse<List<DtoTestFormula>> findSheetConfigFormula(@RequestBody FormulaQueryVO queryVO) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        restResp.setData(service.findSheetConfigFormula(queryVO));
        return restResp;
    }

    /**
     * 根据采样单配置Ids获取参数集合
     *
     * @param ids 采样单配置Ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoTestFormula>> findAllByIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        restResp.setData(service.findAllByIds(ids));
        return restResp;
    }

    /**
     * 根据 测试项目ids 获取公式
     *
     * @param testIds 测试项目ids
     * @return 公式集合
     */
    @PostMapping("/testIds")
    public RestResponse<List<DtoTestFormula>> findByTestIds(@RequestBody Collection<String> testIds) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        restResp.setData(service.findByTestIds(testIds, true));
        return restResp;
    }
}
