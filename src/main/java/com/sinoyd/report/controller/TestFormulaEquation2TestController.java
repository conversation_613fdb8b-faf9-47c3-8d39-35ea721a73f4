package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoTestFormulaEquation2Test;
import com.sinoyd.report.service.TestFormulaEquation2TestService;
import com.sinoyd.report.vo.TestFormulaEquation2TestQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TestFormulaEquation2Test应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormulaEquation2Test")
public class TestFormulaEquation2TestController extends BaseJpaController<DtoTestFormulaEquation2Test, String, TestFormulaEquation2TestService> {

    /**
     * 分页动态条件查询DtoTestFormulaEquation2Test
     * @param queryVO 条件参数
     * @return RestResponse<List<DtoTestFormulaEquation2Test>>
     */
    @GetMapping("/findList")
    public RestResponse<List<DtoTestFormulaEquation2Test>> findList(TestFormulaEquation2TestQueryVO queryVO) {
        RestResponse<List<DtoTestFormulaEquation2Test>> restResponse = new RestResponse<>();
        restResponse.setData(service.findList(queryVO.getFormulaEquationId(), queryVO.getTestFormulaId()));
        return restResponse;
    }

    /**
     * 批量修改DtoTestFormulaEquation2Test
     * @param testFormulaEquation2TestList 数据载体
     * @return RestResponse<List<DtoTestFormulaEquation2Test>>
     */
    @PutMapping
    public RestResponse<List<DtoTestFormulaEquation2Test>> batchUpdate(@RequestBody List<DtoTestFormulaEquation2Test> testFormulaEquation2TestList) {
        RestResponse<List<DtoTestFormulaEquation2Test>> response = new RestResponse<>();
        response.setData(service.save(testFormulaEquation2TestList));
        return response;
    }

}
