package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.TestFormulaEquationCriteria;
import com.sinoyd.report.dto.DtoTestFormulaEquation;
import com.sinoyd.report.service.TestFormulaEquationService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * TestFormulaEquation应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormulaEquation")
public class TestFormulaEquationController extends BaseJpaController<DtoTestFormulaEquation, String, TestFormulaEquationService> {

    /**
     * 分页动态条件查询TestFormulaEquation
     * @param criteria 条件参数
     * @return RestResponse<List<DtoTestFormulaEquation>>
     */
    @GetMapping
    public RestResponse<List<DtoTestFormulaEquation>> findByPage(TestFormulaEquationCriteria criteria) {
        PageBean<DtoTestFormulaEquation> pageBean = super.getPageBean();
        RestResponse<List<DtoTestFormulaEquation>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增TestFormulaEquation
     *
     * @param testFormulaEquation 实体列表
     * @return RestResponse<TestFormulaEquation>
     */
    @PostMapping
    public RestResponse<DtoTestFormulaEquation> create(@RequestBody DtoTestFormulaEquation testFormulaEquation) {
        RestResponse<DtoTestFormulaEquation> restResponse = new RestResponse<>();
        restResponse.setData(service.save(testFormulaEquation));
        return restResponse;
    }

    /**
     * 新增TestFormula
     *
     * @param testFormulaEquation 实体列表
     * @return RestResponse<TestFormulaEquation>
     */
    @PutMapping
    public RestResponse<DtoTestFormulaEquation> update(@RequestBody DtoTestFormulaEquation testFormulaEquation) {
        RestResponse<DtoTestFormulaEquation> restResponse = new RestResponse<>();
        restResponse.setData(service.update(testFormulaEquation));
        return restResponse;
    }

    /**
     * 批量保存TestFormulaEquation
     *
     * @param testFormulaEquations 数据载体
     * @param testFormulaId 测试公式id
     * @return RestResponse<Void>
     */
    @PostMapping("batchSave/{testFormulaId}")
    public RestResponse<Void> batchSave(@RequestBody List<DtoTestFormulaEquation> testFormulaEquations, @PathVariable(name = "testFormulaId") String testFormulaId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.batchSave(testFormulaEquations, testFormulaId);
        return restResponse;
    }

    /**
     * "根据id批量删除TestFormulaEquation
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据测试公式id批量查询TestFormulaEquation
     * @param testFormulaIds 测试公式id列表
     *
     * @return RestResponse<List<DtoTestFormulaEquation>>
     */
    @PostMapping("/testFormulaIds")
    public RestResponse<List<DtoTestFormulaEquation>> findByTestFormulaIdIn(@RequestBody Collection<String> testFormulaIds) {
        return new RestResponse<List<DtoTestFormulaEquation>>().setData(service.findByTestFormulaIdIn(testFormulaIds));
    }
}
