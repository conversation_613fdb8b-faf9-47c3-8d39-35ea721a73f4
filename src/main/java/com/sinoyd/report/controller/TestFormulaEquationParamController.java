package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.TestFormulaEquationParamCriteria;
import com.sinoyd.report.dto.DtoTestFormulaEquationParam;
import com.sinoyd.report.service.TestFormulaEquationParamService;
import com.sinoyd.report.vo.TestFormulaEquationParamQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TestFormulaEquationParam应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormulaEquationParam")
public class TestFormulaEquationParamController extends BaseJpaController<DtoTestFormulaEquationParam, String, TestFormulaEquationParamService> {

    /**
     * 分页动态条件查询TestFormulaEquationParam
     * @param criteria 条件参数
     * @return RestResponse<List<DtoTestFormulaEquationParam>>
     */
    @GetMapping
    public RestResponse<List<DtoTestFormulaEquationParam>> findByPage(TestFormulaEquationParamCriteria criteria) {
        PageBean<DtoTestFormulaEquationParam> pageBean = super.getPageBean();
        RestResponse<List<DtoTestFormulaEquationParam>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 获取数据参数列表
     *
     * @param queryVO 查询条件
     * @return 返回数据列表
     */
    @PostMapping("/paramList")
    public RestResponse<List<DtoTestFormulaEquationParam>> paramList(@RequestBody TestFormulaEquationParamQueryVO queryVO) {
        RestResponse<List<DtoTestFormulaEquationParam>> response = new RestResponse<>();
        response.setData(service.paramList(queryVO));
        return response;
    }

    /**
     * 新增TestFormulaEquationParam
     *
     * @param testFormulaEquationParam 实体列表
     * @return RestResponse<DtoTestFormulaEquationParam>
     */
    @PostMapping
    public RestResponse<DtoTestFormulaEquationParam> create(@RequestBody DtoTestFormulaEquationParam testFormulaEquationParam) {
        RestResponse<DtoTestFormulaEquationParam> restResponse = new RestResponse<>();
        restResponse.setData(service.save(testFormulaEquationParam));
        return restResponse;
    }

    /**
     * 新增TestFormulaEquationParam
     *
     * @param testFormulaEquationParam 实体列表
     * @return RestResponse<DtoTestFormulaEquationParam>
     */
    @PutMapping
    public RestResponse<DtoTestFormulaEquationParam> update(@RequestBody DtoTestFormulaEquationParam testFormulaEquationParam) {
        RestResponse<DtoTestFormulaEquationParam> restResponse = new RestResponse<>();
        restResponse.setData(service.update(testFormulaEquationParam));
        return restResponse;
    }

    /**
     * "根据id批量删除TestFormulaEquationParam
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


}
