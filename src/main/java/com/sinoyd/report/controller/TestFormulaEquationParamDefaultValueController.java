package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.TestFormulaEquationParamDefaultValueCriteria;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;
import com.sinoyd.report.dto.DtoTestFormulaEquationParamDefaultValue;
import com.sinoyd.report.service.TestFormulaEquationParamDefaultValueService;
import com.sinoyd.report.vo.SamplingParamDefaultValueQueryVO;
import com.sinoyd.report.vo.TestFormulaEquationParamDefaultValueQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TestFormulaEquationParamDefaultValue应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/testFormulaEquationParamDefaultValue")
public class TestFormulaEquationParamDefaultValueController extends BaseJpaController<DtoTestFormulaEquationParamDefaultValue, String, TestFormulaEquationParamDefaultValueService> {

    /**
     * 分页动态条件查询TestFormulaEquationParamDefaultValue
     * @param queryVO 条件参数
     * @return RestResponse<List<DtoTestFormulaEquationParamDefaultValue>>
     */
    @GetMapping("/findList")
    public RestResponse<List<DtoTestFormulaEquationParamDefaultValue>> findList(TestFormulaEquationParamDefaultValueQueryVO queryVO) {
        RestResponse<List<DtoTestFormulaEquationParamDefaultValue>> restResponse = new RestResponse<>();
        restResponse.setData(service.findList(queryVO));
        return restResponse;
    }

    /**
     * 批量修改TestFormulaEquationParamDefaultValue
     * @param testFormulaEquationParamDefaultValues 数据载体
     * @return RestResponse<List<DtoSamplingParamDefaultValue>>
     */
    @PutMapping
    public RestResponse<List<DtoTestFormulaEquationParamDefaultValue>> batchUpdate(@RequestBody List<DtoTestFormulaEquationParamDefaultValue> testFormulaEquationParamDefaultValues) {
        RestResponse<List<DtoTestFormulaEquationParamDefaultValue>> response = new RestResponse<>();
        response.setData(service.save(testFormulaEquationParamDefaultValues));
        return response;
    }

}
