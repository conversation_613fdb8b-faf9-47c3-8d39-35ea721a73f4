package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.WorkSheetConfigCriteria;
import com.sinoyd.report.dto.DtoWorkSheetConfig;
import com.sinoyd.report.service.WorkSheetConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * WorkSheetConfig应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/workSheetConfig")
public class WorkSheetConfigController extends BaseJpaController<DtoWorkSheetConfig, String, WorkSheetConfigService> {

    /**
     * 分页动态条件查询workSheetConfig
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoSamplingConfig>>
     */
    @GetMapping
    public RestResponse<List<DtoWorkSheetConfig>> findByPage(WorkSheetConfigCriteria criteria) {
        PageBean<DtoWorkSheetConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增workSheetConfig
     *
     * @param workSheetConfig 实体列表
     * @return RestResponse<DtoWorkSheetConfig>
     */
    @PostMapping
    public RestResponse<DtoWorkSheetConfig> create(@RequestBody DtoWorkSheetConfig workSheetConfig) {
        RestResponse<DtoWorkSheetConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(workSheetConfig));
        return restResponse;
    }

    /**
     * 根据id查询原始记录单配置
     *
     * @param ids 实体列表
     * @return 原始记录单配置
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoWorkSheetConfig>> findByIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoWorkSheetConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll(ids));
        return restResponse;
    }


    /**
     * 根据id查询原始记录单配置
     *
     * @param testIds 实体列表
     * @return 原始记录单配置
     */
    @PostMapping("/test/ids")
    public RestResponse<List<DtoWorkSheetConfig>> findByTestIds(@RequestBody Collection<String> testIds) {
        RestResponse<List<DtoWorkSheetConfig>> restResponse = new RestResponse<>();
        restResponse.setData(service.findByTestIds(testIds));
        return restResponse;
    }

    /**
     * 修改workSheetConfig
     *
     * @param workSheetConfig 实体列表
     * @return RestResponse<DtoWorkSheetConfig>
     */
    @PutMapping
    public RestResponse<DtoWorkSheetConfig> update(@RequestBody DtoWorkSheetConfig workSheetConfig) {
        RestResponse<DtoWorkSheetConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(workSheetConfig));
        return restResponse;
    }

    /**
     * "根据id批量删除workSheetConfig
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

}
