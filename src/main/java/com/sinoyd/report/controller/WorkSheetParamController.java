package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.WorkSheetParamCriteria;
import com.sinoyd.report.dto.DtoWorkSheetParam;
import com.sinoyd.report.service.WorkSheetParamService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * WorkSheetParam应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/workSheetParam")
public class WorkSheetParamController extends BaseJpaController<DtoWorkSheetParam, String, WorkSheetParamService> {

    /**
     * 分页动态条件查询WorkSheetParam
     * @param criteria 条件参数
     * @return RestResponse<List<DtoWorkSheetParam>>
     */
    @GetMapping
    public RestResponse<List<DtoWorkSheetParam>> findByPage(WorkSheetParamCriteria criteria) {
        PageBean<DtoWorkSheetParam> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetParam>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增WorkSheetParam
     * @param workSheetParam 实体列表
     * @return RestResponse<DtoWorkSheetParam>
     */
    @PostMapping
    public RestResponse<DtoWorkSheetParam> create(@Validated @RequestBody DtoWorkSheetParam workSheetParam) {
        RestResponse<DtoWorkSheetParam> restResponse = new RestResponse<>();
        restResponse.setData(service.save(workSheetParam));
        return restResponse;
    }

    /**
     * 修改WorkSheetParam
     * @param workSheetParam 实体列表
     * @return RestResponse<DtoWorkSheetParam>
     */
    @PutMapping
    public RestResponse<DtoWorkSheetParam> update(@Validated @RequestBody DtoWorkSheetParam workSheetParam) {
        RestResponse<DtoWorkSheetParam> restResponse = new RestResponse<>();
        restResponse.setData(service.update(workSheetParam));
        return restResponse;
    }

    /**
     * "根据id批量删除SamplingParam
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据原始记录单配置Ids获取参数集合
     *
     * @param ids 配置Ids
     * @return 参数集合
     */
    @PostMapping("/workSheetConfigIds")
    public RestResponse<List<DtoWorkSheetParam>> findByWorkSheetConfigIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoWorkSheetParam>> restResp = new RestResponse<>();
        restResp.setData(service.findByWorkSheetConfigIds(ids));
        return restResp;
    }

    /**
     * 根据ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoWorkSheetParam>> findByIds(@RequestBody Collection<String> ids) {
        RestResponse<List<DtoWorkSheetParam>> restResp = new RestResponse<>();
        restResp.setData(service.findByIds(ids));
        return restResp;
    }
}
