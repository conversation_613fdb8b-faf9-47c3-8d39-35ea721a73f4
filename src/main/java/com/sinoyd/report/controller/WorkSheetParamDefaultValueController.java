package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.dto.DtoWorkSheetParamDefaultValue;
import com.sinoyd.report.service.WorkSheetParamDefaultValueService;
import com.sinoyd.report.vo.WorkSheetParamDefaultValueQueryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * WorkSheetParamDefaultValue应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/workSheetParamDefaultValue")
public class WorkSheetParamDefaultValueController extends BaseJpaController<DtoWorkSheetParamDefaultValue, String, WorkSheetParamDefaultValueService> {

    /**
     * 分页动态条件查询WorkSheetParamDefaultValue
     * @param queryVO 条件参数
     * @return RestResponse<List<DtoWorkSheetParamDefaultValue>>
     */
    @GetMapping("/findList")
    public RestResponse<List<DtoWorkSheetParamDefaultValue>> findByPage(WorkSheetParamDefaultValueQueryVO queryVO) {
        RestResponse<List<DtoWorkSheetParamDefaultValue>> restResponse = new RestResponse<>();
        restResponse.setData(service.findList(queryVO));
        return restResponse;
    }

    /**
     * 批量保存WorkSheetParamDefaultValue
     * @param workSheetParamDefaultValues 数据载体
     * @return RestResponse<List<DtoWorkSheetParamDefaultValue>>
     */
    @PutMapping
    public RestResponse<List<DtoWorkSheetParamDefaultValue>> batchUpdate(@RequestBody List<DtoWorkSheetParamDefaultValue> workSheetParamDefaultValues) {
        RestResponse<List<DtoWorkSheetParamDefaultValue>> response = new RestResponse<>();
        response.setData(service.save(workSheetParamDefaultValues));
        return response;
    }

}
