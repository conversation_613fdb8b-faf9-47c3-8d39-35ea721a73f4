package com.sinoyd.report.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.report.criteria.WorkSheetTestCriteria;
import com.sinoyd.report.dto.DtoWorkSheetTest;
import com.sinoyd.report.service.WorkSheetTestService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * WorkSheetTest应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@RestController
@RequestMapping("api/report/workSheetTest")
public class WorkSheetTestController extends BaseJpaController<DtoWorkSheetTest, String, WorkSheetTestService> {

    /**
     * 分页动态条件查询WorkSheetTest
     * @param criteria 条件参数
     * @return RestResponse<List<DtoWorkSheetTest>>
     */
    @GetMapping
    public RestResponse<List<DtoWorkSheetTest>> findByPage(WorkSheetTestCriteria criteria) {
        PageBean<DtoWorkSheetTest> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetTest>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 批量保存WorkSheetTest
     * @param workSheetTestList 数据载体
     * @return RestResponse<List<DtoWorkSheetTest>>
     */
    @PostMapping
    public RestResponse<List<DtoWorkSheetTest>> batchSave(@RequestBody List<DtoWorkSheetTest> workSheetTestList) {
        RestResponse<List<DtoWorkSheetTest>> response = new RestResponse<>();
        response.setData(service.save(workSheetTestList));
        return response;
    }

    /**
     * "根据id批量删除WorkSheetTest
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


}
