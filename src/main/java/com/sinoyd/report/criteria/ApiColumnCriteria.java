package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API列配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiColumnCriteria extends LIMSBaseCriteria {

    /**
     * API接口管理Id
     */
    private String apiId;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.apiId)){
            condition.append(" and ac.apiId = :apiId");
            values.put("apiId", this.apiId);
        }
        return condition.toString();
    }
}
