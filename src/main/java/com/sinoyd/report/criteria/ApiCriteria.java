package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API接口管理查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiCriteria extends LIMSBaseCriteria {

    /**
     * 查询条件(编码，名称)
     */
    private String key;

    /**
     * API接口类型
     */
    private Integer apiType;

    /**
     * 需要过滤的Id
     */
    private String excludedId;


    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.key)){
            condition.append(" and (a.apiCode like :key or a.apiName like :key)");
            values.put("key", super.appendPercent(this.key));
        }
        if (StringUtils.isNotNull(this.apiType)){
            condition.append(" and a.apiType = :apiType");
            values.put("apiType", this.apiType);
        }
        if (StringUtils.isNotEmpty(this.excludedId)){
            condition.append(" and a.id != :excludedId");
            values.put("excludedId", this.excludedId);
        }
        return condition.toString();
    }
}
