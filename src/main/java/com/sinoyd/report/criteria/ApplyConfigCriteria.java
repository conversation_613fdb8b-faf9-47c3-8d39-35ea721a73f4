package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础配置应用查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApplyConfigCriteria extends LIMSBaseCriteria {

    /**
     * 主键id
     */
    private String id;

    /**
     * 报表配置编码
     */
    private String reportCode;

    /**
     * 所属应用id
     */
    private String webAppId;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 是否启用
     */
    private Boolean enabledFlag;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.reportCode = c.reportCode ");
        if (StringUtils.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id");
            values.put("id", this.id);
        }
        if (StringUtils.isNotEmpty(this.reportCode)) {
            condition.append(" and (a.reportCode like :reportCode or c.templateName like :reportCode)");
            values.put("reportCode", appendPercent(this.reportCode));
        }
        if (StringUtils.isNotNullAndEmpty(this.webAppId)) {
            condition.append(" and a.webAppId = :webAppId");
            values.put("webAppId", this.webAppId);
        }
        if (StringUtils.isNotNullAndEmpty(this.moduleCode)) {
            condition.append(" and a.moduleCode = :moduleCode");
            values.put("moduleCode", this.moduleCode);
        }
        if (StringUtils.isNotNull(this.enabledFlag)) {
            condition.append(" and a.isEnabled = :enabledFlag");
            values.put("enabledFlag", this.enabledFlag);
        }
        return condition.toString();
    }

}
