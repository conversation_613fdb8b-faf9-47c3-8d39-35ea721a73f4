package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 区域配置与数据集列应用映射数据查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaConfig2DataSetApplyColumnCriteria extends LIMSBaseCriteria {

    /**
     * 区域配置id
     */
    private String areaConfigId;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.areaConfigId)) {
            condition.append(" and a.areaConfigId = :areaConfigId");
            values.put("areaConfigId", this.areaConfigId);
        }
        return condition.toString();
    }
}
