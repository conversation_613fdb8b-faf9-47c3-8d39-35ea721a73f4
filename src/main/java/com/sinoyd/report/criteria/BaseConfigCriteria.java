package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报表基础配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseConfigCriteria extends LIMSBaseCriteria {

    /**
     * 报表类型
     */
    private Integer reportTypeValue;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 报表名称
     */
    private String templateName;

    /**
     * 获取查询条件
     *
     * @return 查询条件
     */
    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        //报表类型
        if (StringUtils.isNotNullAndEmpty(this.reportTypeValue)) {
            condition.append(" and bc.reportTypeValue = :reportTypeValue");
            values.put("reportTypeValue", this.reportTypeValue);
        }
        //报表编码
        if (StringUtils.isNotEmpty(reportCode)) {
            condition.append(" and bc.reportCode like :reportCode");
            values.put("reportCode", appendPercent(this.reportCode));
        }
        //报表名称
        if (StringUtils.isNotEmpty(this.templateName)){
            condition.append(" and bc.templateName like :templateName");
            values.put("templateName", appendPercent(this.templateName));
        }
        return condition.toString();
    }
}
