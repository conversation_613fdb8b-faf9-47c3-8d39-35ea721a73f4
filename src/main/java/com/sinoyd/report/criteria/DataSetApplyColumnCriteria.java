package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据集应用列查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataSetApplyColumnCriteria extends LIMSBaseCriteria {

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 查询条件关键字
     */
    private String key;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.businessType)) {
            condition.append(" and dac.businessType = :businessType");
            values.put("businessType", this.businessType);
        }
        if (StringUtils.isNotEmpty(this.key)) {
            condition.append(" and (dac.columnName like :key or dac.placeholderName like :key or " +
                    "exists(select 1 from DtoDataSet ds where ds.id = dac.dataSetId and ds.dataSetName like :key) or " +
                    "exists(select 1 from DtoApi api where api.id = dac.dataSetId and api.apiName like :key))");
            values.put("key", appendPercent(this.key));
        }
        return condition.toString();
    }
}
