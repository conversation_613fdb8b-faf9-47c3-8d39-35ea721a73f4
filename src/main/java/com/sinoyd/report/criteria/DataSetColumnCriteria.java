package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Sql数据集列配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataSetColumnCriteria extends LIMSBaseCriteria {

    /**
     * 数据集Id
     */
    private String dataSetId;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.dataSetId)){
            condition.append(" and dsc.dataSetId = :dataSetId");
            values.put("dataSetId", this.dataSetId);
        }
        return condition.toString();
    }
}
