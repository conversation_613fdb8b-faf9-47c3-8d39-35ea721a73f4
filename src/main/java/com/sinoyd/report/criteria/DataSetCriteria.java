package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Sql数据集配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataSetCriteria extends LIMSBaseCriteria {

    private String key;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.key)){
            condition.append(" and (ds.dataSetCode like :key or ds.dataSetName like :key)");
            values.put("key", super.appendPercent(this.key));
        }
        return condition.toString();
    }
}
