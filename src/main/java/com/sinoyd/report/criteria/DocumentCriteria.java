package com.sinoyd.report.criteria;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 文档查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentCriteria extends BaseCriteria {
    /**
     * 上传的开始日期
     */
    private String dtBegin;

    /**
     * 上传的结束日期
     */
    private String dtEnd;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 上传人的Id
     */
    private String creator;

    /**
     * 上传人的姓名
     */
    private String creatorName;

    /**
     * 存储路径
     */
    private String path;

    /**
     * 对象Id
     */
    private String folderId;

    /**
     * 对象Id
     */
    private List<String> folderIds = new ArrayList<>();

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 文件类型Id
     */
    private String docTypeId;

    /**
     * 文件类型Id集合
     */
    private List<String> docTypeIds = new ArrayList<>();

    /**
     * 是否文件管理查询
     */
    private Boolean isFolder = false;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(fileName)) {
            condition.append(" and (fileName like :fileName)");
            values.put("fileName", "%" + this.fileName + "%");
        }

        if (StringUtils.isNotNullAndEmpty(folderName)) {
            condition.append(" and (folderName like :folderName)");
            values.put("folderName", "%" + this.folderName + "%");
        }

        if (StringUtils.isNotNullAndEmpty(path)) {
            condition.append(" and (path like :path)");
            values.put("path", "%" + this.path + "%");
        }

        if (StringUtils.isNotNullAndEmpty(folderId) && !folderId.equals(UUIDHelper.guidEmpty())) {
            condition.append(" and objectId = :folderId");
            values.put("folderId", folderId);
        } else if (StringUtils.isNotNull(isFolder) && isFolder) { //说明要根据folder表进行过滤
            condition.append(" and exists (select 1 from DtoFolder b where d.objectId = b.id)");
        }
        if (StringUtils.isNotNullAndEmpty(docTypeId) && !docTypeId.equals(UUIDHelper.guidEmpty())) {
            condition.append(" and docType = :docTypeId");
            values.put("docTypeId", docTypeId);
        }
        if (folderIds.size() > 0) {
            condition.append(" and objectId in :folderIds");
            values.put("folderIds", folderIds);
        }
        if (docTypeIds.size() > 0) {
            condition.append(" and docType in :docTypeIds");
            values.put("docTypeIds", docTypeIds);
        }
        if (StringUtils.isNotNullAndEmpty(creatorName)) {
            condition.append(" and (uploadPerson like :creatorName)");
            values.put("creatorName", "%" + this.creatorName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(creator) && !creator.equals(UUIDHelper.guidEmpty())) {
            condition.append(" and uploadPersonId = :creator");
            values.put("creator", creator);
        }

        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and ").append("createDate >= :from");
            values.put("from", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(to);
            endCalendar.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and ").append("createDate < :to");
            this.values.put("to", endCalendar.getTime());
        }
        condition.append(" and isDeleted = 0");

        return condition.toString();
    }
}