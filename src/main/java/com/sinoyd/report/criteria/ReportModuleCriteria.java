package com.sinoyd.report.criteria;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * ReportModule查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportModuleCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组件名称编码
     */
    private String moduleCodeName;

    /**
     * 主表名称，数据行表名
     */
    private String tableName;

    /**
     * 基础配置id
     */
    private String baseConfigId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.moduleCodeName)) {
            condition.append(" and (moduleCode like :moduleCodeName or moduleName like :moduleCodeName) ");
            values.put("moduleCodeName",  "%" + this.moduleCodeName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.tableName)) {
            condition.append(" and (tableName like :tableName or sourceTableName like :tableName) ");
            values.put("tableName",  "%" + this.tableName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.baseConfigId)) {
            condition.append(" and not exists (select 1 from DtoBaseConfig2Module b where a.id = b.reportModuleId and b.baseConfigId = :baseConfigId)");
            values.put("baseConfigId", this.baseConfigId);
        }
        return condition.toString();
    }

}
