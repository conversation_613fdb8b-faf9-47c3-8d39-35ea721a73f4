package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingConfigCriteria extends LIMSBaseCriteria {

    /**
     * 记录单名称
     */
    private String formName;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.formName)) {
            condition.append(" and formName like :formName");
            values.put("formName",  super.appendPercent(this.formName));
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId)) {
            condition.append(" and (sampleTypeId like :sampleTypeId or bigSampleTypeId like :sampleTypeId)");
            values.put("sampleTypeId",  super.appendPercent(this.sampleTypeId));
        }
        return condition.toString();
    }
}
