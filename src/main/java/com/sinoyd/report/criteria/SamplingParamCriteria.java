package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采样单参数查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingParamCriteria extends LIMSBaseCriteria {

    /**
     * 参数种类，枚举管理，如样品参数、数据参数
     */
    private Integer paramCategory;

    /**
     * 采样单配置id
     */
    private String samplingConfigId;

    /**
     * 参数名称
     */
    private String paramName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.paramCategory)) {
            condition.append(" and paramCategory = :paramCategory");
            values.put("paramCategory",  this.paramCategory);
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingConfigId)) {
            condition.append(" and samplingConfigId = :samplingConfigId");
            values.put("samplingConfigId",  this.samplingConfigId);
        }
        if (StringUtils.isNotNullAndEmpty(this.paramName)) {
            condition.append(" and (paramName like :paramName or aliasName like :paramName)");
            values.put("paramName",  super.appendPercent(this.paramName));
        }
        return condition.toString();
    }

}
