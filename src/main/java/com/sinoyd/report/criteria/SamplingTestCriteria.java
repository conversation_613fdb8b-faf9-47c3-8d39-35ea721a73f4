package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 采样单关联测试项目查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingTestCriteria extends LIMSBaseCriteria {

    /**
     * 检测类型id
     */
    private String samplingConfigId;

    /**
     * 分析项目
     */
    private String itemKey;

    /**
     * 分析方法
     */
    private String methodKey;

    /**
     * 测试项目id结婚
     */
    private List<String> testIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.samplingConfigId)) {
            condition.append(" and samplingConfigId = :samplingConfigId");
            values.put("samplingConfigId",  this.samplingConfigId);
        }
        if (StringUtils.isNotEmpty(testIds)) {
            condition.append(" and testId in :testIds");
            values.put("testIds",  this.testIds);
        }
        return condition.toString();
    }

}
