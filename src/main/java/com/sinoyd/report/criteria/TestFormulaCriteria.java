
package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 测试项目公式查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestFormulaCriteria extends LIMSBaseCriteria {

    /**
     * 对象名称
     */
    private String objectName;

    /**
     * 公式名称
     */
    private String formulaName;

    /**
     * 分析项目
     */
    private String itemName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 测试项目id结婚
     */
    private List<String> testIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(objectName)) {
            condition.append(" and exists(select 1 from DtoSamplingConfig s, DtoWorkSheetConfig w where (s.id = a.objectId and s.formName like :objectName) or (w.id = a.objectId and w.formName like :objectName)) ");
            values.put("objectName", super.appendPercent(this.objectName));
        }
        if (StringUtils.isNotNullAndEmpty(formulaName)) {
            condition.append(" and a.formulaName like :formulaName ");
            values.put("formulaName", super.appendPercent(this.formulaName));
        }
        if (StringUtils.isNotEmpty(testIds)) {
            condition.append(" and exists(select 1 from DtoTestFormula2Test t where t.testFormulaId = a.id and t.testId in :testIds)");
            values.put("testIds",  this.testIds);
        }
        return condition.toString();

    }
}
