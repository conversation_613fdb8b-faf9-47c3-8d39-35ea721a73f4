package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试项目公式关联测试项目查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquation2TestCriteria extends LIMSBaseCriteria {

    /**
     * 公式方程id
     */
    private String formulaEquationId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.formulaEquationId)) {
            condition.append(" and formulaEquationId = :formulaEquationId ");
            values.put("formulaEquationId", this.formulaEquationId);
        }
        return condition.toString();
    }

}
