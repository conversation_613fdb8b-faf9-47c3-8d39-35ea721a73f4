package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试项目公式方程查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquationCriteria extends LIMSBaseCriteria {

    /**
     * 测试项目公式id
     */
    private String testFormulaId;

    /**
     * 方程种类，枚举管理
     */
    private Integer equationCategory;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.testFormulaId)) {
            condition.append(" and testFormulaId = :testFormulaId ");
            values.put("testFormulaId", this.testFormulaId);
        }
        if (StringUtils.isNotNullAndEmpty(this.equationCategory)) {
            condition.append(" and equationCategory = :equationCategory ");
            values.put("equationCategory", this.equationCategory);
        }
        return condition.toString();

    }

}
