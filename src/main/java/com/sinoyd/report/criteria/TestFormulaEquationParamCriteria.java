package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试项目公式方程参数查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquationParamCriteria extends LIMSBaseCriteria {

    /**
     * 测试项目公式方程id
     */
    private String testFormulaId;

    /**
     * 参数种类
     */
    private Integer paramCategory;

    /**
     * 参数名称
     */
    private String paramName;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.testFormulaId)) {
            condition.append(" and testFormulaId = :testFormulaId ");
            values.put("testFormulaId", this.testFormulaId);
        }
        if (StringUtils.isNotNullAndEmpty(this.paramCategory)) {
            condition.append(" and paramCategory = :paramCategory ");
            values.put("paramCategory", this.paramCategory);
        }
        if (StringUtils.isNotNullAndEmpty(this.paramName)) {
            condition.append(" and paramName like :paramName ");
            values.put("paramName", super.appendPercent(this.paramName));
        }
        return condition.toString();

    }

}
