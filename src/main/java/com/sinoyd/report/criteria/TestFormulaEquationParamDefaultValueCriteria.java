package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试项目公式方程参数查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquationParamDefaultValueCriteria extends LIMSBaseCriteria {

    /**
     * 测试项目公式方程参数id
     */
    private String equationParamId;

    /**
     * 参数类型
     */
    private Integer paramCategory;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.equationParamId)) {
            condition.append(" and equationParamId = :equationParamId ");
            values.put("equationParamId", this.equationParamId);
        }
        if (StringUtils.isNotNullAndEmpty(this.paramCategory)) {
            condition.append(" and paramCategory = :paramCategory ");
            values.put("paramCategory", this.paramCategory);
        }
        return condition.toString();

    }

}
