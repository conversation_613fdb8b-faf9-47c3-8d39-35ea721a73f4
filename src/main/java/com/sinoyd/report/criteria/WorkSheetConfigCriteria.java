package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单配置查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetConfigCriteria extends LIMSBaseCriteria {

    /**
     * 原始记录单名称
     */
    private String formName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.formName)) {
            condition.append(" and formName like :formName");
            values.put("formName",  super.appendPercent(this.formName));
        }
        return condition.toString();
    }

}
