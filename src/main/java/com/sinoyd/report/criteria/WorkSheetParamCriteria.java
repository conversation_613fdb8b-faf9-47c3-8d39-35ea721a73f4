package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单参数查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetParamCriteria extends LIMSBaseCriteria {

    /**
     * 检测类型id
     */
    private String workSheetConfigId;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数类型
     */
    private Integer paramCategory;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.workSheetConfigId)) {
            condition.append(" and workSheetConfigId = :workSheetConfigId");
            values.put("workSheetConfigId",  this.workSheetConfigId);
        }
        if (StringUtils.isNotNullAndEmpty(this.paramName)) {
            condition.append(" and (paramName like :paramName or aliasName like :paramName)");
            values.put("paramName",  super.appendPercent(this.paramName));
        }
        if (StringUtils.isNotNullAndEmpty(this.paramCategory)) {
            condition.append(" and paramCategory = :paramCategory");
            values.put("paramCategory",  this.paramCategory);
        }
        return condition.toString();
    }

}
