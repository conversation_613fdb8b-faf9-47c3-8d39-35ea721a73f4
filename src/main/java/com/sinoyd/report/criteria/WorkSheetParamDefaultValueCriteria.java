package com.sinoyd.report.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单参数默认值查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetParamDefaultValueCriteria extends LIMSBaseCriteria {

    /**
     * 原始记录单参数id
     */
    private String workSheetParamId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.workSheetParamId)) {
            condition.append(" and workSheetParamId = :workSheetParamId");
            values.put("workSheetParamId",  this.workSheetParamId);
        }
        return condition.toString();
    }

}
