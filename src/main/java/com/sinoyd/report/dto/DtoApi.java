package com.sinoyd.report.dto;

import com.sinoyd.report.entity.Api;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报表模板基础配置实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_Api")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoApi extends Api {

    /**
     * API接口类型名称
     */
    @Transient
    private String apiTypeName;

    /**
     * 请求参数
     */
    @Transient
    private List<DtoApiParam> apiParams;

    /**
     * 接口执行结果列
     */
    @Transient
    private List<DtoApiColumn> apiColumns;
}
