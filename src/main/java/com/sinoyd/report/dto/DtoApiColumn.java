package com.sinoyd.report.dto;

import com.sinoyd.report.entity.ApiColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * API数据列表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_ApiColumn")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoApiColumn extends ApiColumn {

}