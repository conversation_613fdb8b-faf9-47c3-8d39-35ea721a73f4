package com.sinoyd.report.dto;

import com.sinoyd.base.enums.EnumReportConfigType;
import com.sinoyd.report.entity.ApplyConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 报表配置应用实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_ApplyConfig")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoApplyConfig extends ApplyConfig {

    /**
     * 排序值
     */
    @Transient
    private Integer orderNum;

    /**
     * 报表名称
     */
    @Transient
    private String reportName;

    /**
     * 报表类型编码，枚举维护：原始记录单 1、采样单 2、报告 3、报表 4、标签 5
     * {@link EnumReportConfigType}
     */
    @Transient
    private Integer reportTypeValue;

}
