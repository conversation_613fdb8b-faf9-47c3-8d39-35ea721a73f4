package com.sinoyd.report.dto;

import com.sinoyd.report.entity.AreaConfig;
import com.sinoyd.report.enums.EnumAreaType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报表模板区域配置实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_AreaConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoAreaConfig extends AreaConfig {

    /**
     *区域配置数据列应用列表
     */
    @Transient
    private List<DtoAreaConfig2DataSetApplyColumn> dataSetApplyColumns;

    /**
     *区域扩展配置对象
     */
    @Transient
    private DtoAreaExpandConfig areaExpandConfig;

    /**
     * 初始化一个新的区域配置对象
     *
     * @param areaConfig  区域配置对象
     * @param sheetRowCnt sheet页的行数
     * @param i           索引
     * @param rangValue0  起始位置0
     * @param rangValue1  起始位置1
     * @param rangValue2  结束位置0
     * @param rangValue3  结束位置1
     */
    public DtoAreaConfig(DtoAreaConfig areaConfig, int sheetRowCnt, int i, String rangValue0,
                         String rangValue1, String rangValue2, String rangValue3) {
        DtoAreaConfig newAreaConfig = new DtoAreaConfig();
        BeanUtils.copyProperties(areaConfig, newAreaConfig, "id", "orderNum", "areaStart", "areaEnd");
        newAreaConfig.setExpandPageSize(areaConfig.getExpandPageSize());
        newAreaConfig.setAreaType(areaConfig.getAreaType());
        newAreaConfig.setExpandType(areaConfig.getExpandType());
        newAreaConfig.setSheetName(areaConfig.getSheetName());
        newAreaConfig.setExpandAreaSize(areaConfig.getExpandAreaSize());
        newAreaConfig.setOrderNum(areaConfig.getOrderNum());
        newAreaConfig.setItemIndex(!EnumAreaType.全局固定区域.name().equals(areaConfig.getAreaType()) ? areaConfig.getItemIndex() + i
                : areaConfig.getItemIndex());
        newAreaConfig.setAreaStart(rangValue0 + (Integer.parseInt(rangValue1) + i * sheetRowCnt));
        newAreaConfig.setAreaEnd(rangValue2 + (Integer.parseInt(rangValue3) + i * sheetRowCnt));
    }

    public DtoAreaConfig() {

    }
}
