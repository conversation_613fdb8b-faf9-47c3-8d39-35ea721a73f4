package com.sinoyd.report.dto;

import com.sinoyd.report.entity.AreaConfig2DataSetApplyColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 区域配置与数据集列应用映射表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_AreaConfig2DataSetApplyColumn")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoAreaConfig2DataSetApplyColumn extends AreaConfig2DataSetApplyColumn {

    /**
     * 业务类型，常量管理
     */
    @Transient
    private String businessType;

    /**
     * 业务类型，常量管理
     */
    @Transient
    private String businessTypeName;

    /**
     * 数据列编码
     */
    @Transient
    private String columnCode;

    /**
     * 数据列名称
     */
    @Transient
    private String columnName;

    /**
     * 模版占位符
     */
    @Transient
    private String placeholderName;

    /**
     * 说明
     */
    @Transient
    private String remark;

}
