package com.sinoyd.report.dto;

import com.sinoyd.report.entity.AreaExpandConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报表模板区域扩展配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_AreaExpandConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoAreaExpandConfig extends AreaExpandConfig {

    /**
     * 区域扩展合并配置列表
     */
    @Transient
    private List<DtoAreaExpandMergeConfig> areaExpandMergeConfigList;

}
