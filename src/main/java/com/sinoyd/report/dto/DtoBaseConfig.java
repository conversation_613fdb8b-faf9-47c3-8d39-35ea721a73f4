package com.sinoyd.report.dto;

import com.sinoyd.report.entity.BaseConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 报表模板基础配置实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_BaseConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoBaseConfig extends BaseConfig {

    /**
     * 模版附件id
     */
    @Transient
    private String documentId;
}
