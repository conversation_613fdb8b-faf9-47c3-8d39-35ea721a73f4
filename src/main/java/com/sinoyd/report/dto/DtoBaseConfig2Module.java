package com.sinoyd.report.dto;

import com.sinoyd.report.entity.BaseConfig2Module;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报告组件与报表基础配置关联表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Report_BaseConfig2Module")
@Data
@Where(clause = "isDeleted = 0")
@DynamicInsert
public class DtoBaseConfig2Module extends BaseConfig2Module {

    private static final long serialVersionUID = 1L;

    /**
     * 组件编码
     */
    @Transient
    private String moduleCode;

    /**
     * 组件名称
     */
    @Transient
    private String moduleName;

    /**
     * 组件主表名称
     */
    @Transient
    private String tableName;


    /**
     * 组件数据行表名称
     */
    @Transient
    private String sourceTableName;

    /**
     * 报告组件分页配置列表
     */
    @Transient
    private List<DtoReportModule2GroupType> reportModule2GroupTypeList;
}
