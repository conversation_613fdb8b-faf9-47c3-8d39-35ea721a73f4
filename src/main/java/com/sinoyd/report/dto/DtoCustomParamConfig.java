package com.sinoyd.report.dto;

import com.sinoyd.report.entity.CustomParamConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 报表模板配置全局配置自定义参数dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/31
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Report_CustomParamConfig")
@Data
@DynamicInsert
public class DtoCustomParamConfig extends CustomParamConfig {
}
