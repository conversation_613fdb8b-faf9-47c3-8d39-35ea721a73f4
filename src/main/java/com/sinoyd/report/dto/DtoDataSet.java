package com.sinoyd.report.dto;

import com.sinoyd.report.entity.DataSet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 数据集表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_DataSet")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoDataSet extends DataSet {

    /**
     * 数据源名称
     */
    @Transient
    private String dataSourceName;

    /**
     * Sql参数
     */
    @Transient
    private List<DtoDataSetParam> dataSetParams;

    /**
     * Sql执行结果返回列
     */
    @Transient
    private List<DtoDataSetColumn> dataSetColumns;

    /**
     * 保存标记（保存数据集配置调用sql验证接口时，该属性传参为true）
     */
    @Transient
    private Boolean saveFlag;
}