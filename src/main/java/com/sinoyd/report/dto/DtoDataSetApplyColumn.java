package com.sinoyd.report.dto;

import com.sinoyd.report.entity.DataSetApplyColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 数据集应用数据列表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_DataSetApplyColumn")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoDataSetApplyColumn extends DataSetApplyColumn {

    /**
     * 数据集类型名称
     */
    @Transient
    private String dataSetTypeName;

    /**
     * 数据集/API接口名称
     */
    @Transient
    private String dataSetName;

    /**
     * 业务类型名称
     */
    @Transient
    private String businessTypeName;
}