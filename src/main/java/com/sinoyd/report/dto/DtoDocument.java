package com.sinoyd.report.dto;

import com.sinoyd.report.entity.Document;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 文档传输实体
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022/9/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Report_Document")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoDocument extends Document {
}