package com.sinoyd.report.dto;

import com.sinoyd.report.entity.GlobalConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报表模板配置全局配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_GlobalConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoGlobalConfig extends GlobalConfig {

    @Transient
    private List<DtoCustomParamConfig> customParamConfigList;
}
