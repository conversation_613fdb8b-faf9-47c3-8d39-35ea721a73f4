package com.sinoyd.report.dto;

import com.sinoyd.report.entity.ReportModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报告组件信息表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Report_ReportModule")
@Data
@Where(clause = "isDeleted = 0")
@DynamicInsert
public class DtoReportModule extends ReportModule {

    @Transient
    private List<DtoReportModule2GroupType> reportModule2GroupTypeList;

}
