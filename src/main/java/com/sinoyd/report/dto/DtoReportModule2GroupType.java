package com.sinoyd.report.dto;

import com.sinoyd.report.entity.ReportModule2GroupType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 报告各个组件配置的分页方式表实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Report_ReportModule2GroupType")
@Data
@Where(clause = "isDeleted = 0")
@DynamicInsert
public class DtoReportModule2GroupType extends ReportModule2GroupType {
}
