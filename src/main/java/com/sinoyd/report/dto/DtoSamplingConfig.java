package com.sinoyd.report.dto;

import com.sinoyd.report.entity.SamplingConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 表单配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_SamplingConfig")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoSamplingConfig extends SamplingConfig {

    /**
     * 参数配置id集合
     */
    @Transient
    private List<String> paramsConfigIds;

    /**
     * 报表路径
     */
    @Transient
    private String workPath;

    /**
     * 报表名称
     */
    @Transient
    private String workName;

    /**
     * 源配置id
     */
    @Transient
    private String sourceRecordConfigId;

    /**
     * 基础配置id
     */
    @Transient
    private String baseConfigId;

    /**
     * 基础报表配置名称
     */
    @Transient
    private String baseConfigName;

    /**
     * 关联的测试项目id集合
     */
    @Transient
    private List<String> testIds;
}