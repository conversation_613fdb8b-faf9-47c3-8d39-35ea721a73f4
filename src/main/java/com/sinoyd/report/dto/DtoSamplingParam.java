package com.sinoyd.report.dto;

import com.sinoyd.report.entity.SamplingParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 采样单参数实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_SamplingParam")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoSamplingParam extends SamplingParam {

    /**
     * 创建复制实例
     *
     * @return 复制的采样单参数实例
     */
    public DtoSamplingParam createCopyInstance() {
        DtoSamplingParam copy = new DtoSamplingParam();
        // 使用BeanUtils复制属性，排除id、创建时间、修改时间等字段
        String[] ignoreFields = {"id", "createDate", "modifyDate", "creator", "modifier"};
        try {
            BeanUtils.copyProperties(this, copy, ignoreFields);
        } catch (Exception e) {
            throw new RuntimeException("复制采样单参数实例失败", e);
        }
        return copy;
    }
}
