package com.sinoyd.report.dto;

import com.sinoyd.report.entity.SheetConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 报表模板sheet页配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_SheetConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSheetConfig extends SheetConfig {

    /**
     * sheet页分页依据信息列表
     */
    @Transient
    private List<DtoSheetPagingConfig> sheetPagingConfigList;

}
