package com.sinoyd.report.dto;

import com.sinoyd.report.entity.SheetPagingConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * sheet页分页配置明细实体Dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_SheetPagingConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSheetPagingConfig extends SheetPagingConfig {

    /**
     * 数据列名称
     */
    @Transient
    private String columnName;
}
