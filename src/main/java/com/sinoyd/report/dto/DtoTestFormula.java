package com.sinoyd.report.dto;

import com.sinoyd.report.entity.TestFormula;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 测试项目公式实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_TestFormula")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoTestFormula extends TestFormula {

    /**
     * 对象名称
     */
    @Transient
    private String objectName;

    /**
     * 测试项目数量
     */
    @Transient
    private Integer testNum;

    /**
     * 样品出证公式
     */
    @Transient
    private String sampleFormula;

    /**
     * 测试项目名称
     */
    @Transient
    private String testNames;

    /**
     * 测试项目ids
     */
    @Transient
    private List<String> testIds;

    /**
     * 公式集合
     */
    @Transient
    private List<DtoTestFormulaEquation> testFormulaEquationList;

    /**
     * 公式参数集合
     */
    @Transient
    private List<DtoTestFormulaEquationParam> testFormulaEquationParamList;
}
