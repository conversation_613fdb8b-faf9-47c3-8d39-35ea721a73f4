package com.sinoyd.report.dto;

import com.sinoyd.report.entity.TestFormulaEquation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 测试项目公式方程实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_TestFormulaEquation")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoTestFormulaEquation extends TestFormulaEquation {
}
