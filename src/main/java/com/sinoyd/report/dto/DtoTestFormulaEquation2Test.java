package com.sinoyd.report.dto;

import com.sinoyd.report.entity.TestFormulaEquation2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 测试项目公式方程关联测试项目配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_TestFormulaEquation2Test")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoTestFormulaEquation2Test extends TestFormulaEquation2Test {

    /**
     * 分析方法名称
     */
    @Transient
    private String analyzeMethodName;

    /**
     * 分析方法标准编号
     */
    @Transient
    private String methodStandardNo;

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 分析项目化学符号
     */
    @Transient
    private String chemicalSymbol;

}
