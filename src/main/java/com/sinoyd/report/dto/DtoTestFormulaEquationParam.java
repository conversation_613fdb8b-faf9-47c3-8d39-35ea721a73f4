package com.sinoyd.report.dto;

import com.sinoyd.report.entity.TestFormulaEquationParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 测试项目公式方程参数实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_TestFormulaEquationParam")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoTestFormulaEquationParam extends TestFormulaEquationParam {

    /**
     * 参数种类名称
     */
    @Transient
    private String paramCategoryLabel;

    @Transient
    private List<DtoTestFormulaEquationParamDefaultValue> defaultValueList;
}
