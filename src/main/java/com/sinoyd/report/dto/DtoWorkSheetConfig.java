package com.sinoyd.report.dto;

import com.sinoyd.report.entity.WorkSheetConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 原始记录单配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_WorkSheetConfig")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoWorkSheetConfig extends WorkSheetConfig {

    /**
     * 报表路径
     */
    @Transient
    private String workPath;

    /**
     * 报表名称
     */
    @Transient
    private String workName;

    /**
     * 基础配置id
     */
    @Transient
    private String baseConfigId;

    /**
     * 当前配置下的
     */
    @Transient
    private List<DtoWorkSheetParam> paramList;

}
