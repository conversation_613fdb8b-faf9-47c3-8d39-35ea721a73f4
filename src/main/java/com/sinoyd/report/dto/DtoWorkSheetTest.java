package com.sinoyd.report.dto;

import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.report.entity.WorkSheetTest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 采样单关联测试项目实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_REPORT_WorkSheetTest")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
public class DtoWorkSheetTest extends WorkSheetTest {

    /**
     * 分析方法名称
     */
    @Transient
    private String analyzeMethodName;

    /**
     * 分析方法标准编号
     */
    @Transient
    private String methodStandardNo;

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 分析项目化学符号
     */
    @Transient
    private String chemicalSymbol;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 是否cma
     */
    @Transient
    private Boolean needCma;

    /**
     * 是否cnas
     */
    @Transient
    private Boolean needCnas;

    /**
     * 根据测试项目初始化数据
     *
     * @param test 测试项目
     */
    public void initFromTest(DtoTest test) {
        setAnalyzeItemName(test.getAnalyzeItemName());
        setAnalyzeMethodName(test.getAnalyzeMethodName());
        setMethodStandardNo(test.getMethodStandardNo());
        setChemicalSymbol(test.getChemicalSymbol());
        setNeedCma(test.getNeedCma());
        setNeedCnas(test.getNeedCnas());
    }

}
