package com.sinoyd.report.dto.customer;

import com.sinoyd.report.dto.DtoReportModule2GroupType;
import lombok.Data;

import java.util.List;

/**
 * 报告组件和分页方式关联关系传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Data
public class DtoModuleGroupTypeTemp {


    /**
     * 报告组件配置关联关系id
     */
    private String baseConfigModuleId;

    /**
     * 分页方式列表
     */
    List<DtoReportModule2GroupType> groupTypeList;
}
