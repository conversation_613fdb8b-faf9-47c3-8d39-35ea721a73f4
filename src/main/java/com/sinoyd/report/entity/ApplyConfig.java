package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 报表配置应用实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class ApplyConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public ApplyConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 配置编码
     */
    private String reportCode;

    /**
     * 所属应用id
     */
    private String webAppId;

    /**
     * 所属应用名称
     */
    private String webAppName;

    /**
     * 所属模块
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 控件编码
     */
    private String controlCode;

    /**
     * 控件位置
     */
    private String controlSite;

    /**
     * 控件类型
     */
    private Integer controlType;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 是否可编辑
     */
    private Boolean isEditable;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 是否空白填充
     */
    private Boolean blankFill;

    /**
     * 假删标记
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
