package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;

/**
 * 报表模板区域配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class AreaConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public AreaConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * sheet名称
     */
    private String sheetName;

    /**
     * 区域类型，枚举
     * {@link com.sinoyd.report.enums.EnumAreaType}
     */
    private String areaType;

    /**
     * 开始位置
     */
    private String areaStart;

    /**
     * 结束位置
     */
    private String areaEnd;

    /**
     * 扩展方式，枚举(1：行扩展 2：列扩展 3：不扩展)
     * {@link com.sinoyd.report.enums.EnumExpandType}
     */
    private Integer expandType;

    /**
     * 每次扩展的行、列数
     */
    private String expandAreaSize;

    /**
     * 字体
     */
    private String fontFamily;

    /**
     * 字体大小
     */
    private Integer fontSize;

    /**
     * 最大字体大小，用于缩放
     */
    private Integer maxFontSize;

    /**
     * 每页总行、列数
     */
    private Integer expandPageSize;

    /**
     * 每页分页属性行/列数（例如：按照分析项目分页,每页每个分析项目放2个样品，pageColumnSize = 2）
     */
    private Integer pageColumnSize;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 数据索引
     */
    private Integer itemIndex;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 空白区域标识符
     */
    private String blankIdentifier;

    /**
     * 空白区域占位符
     */
    private String emptyPlaceHolder;

    /**
     * 空白区域模板占位符
     */
    private String emptyTemplatePlaceHolder;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
