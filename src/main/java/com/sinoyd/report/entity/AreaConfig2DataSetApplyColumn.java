package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * 区域配置与数据集列应用映射表实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class AreaConfig2DataSetApplyColumn extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 区域配置id
     */
    private String areaConfigId;

    /**
     * 应用数据列id
     */
    private String dataSetApplyColumnId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

}
