package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 报表模板区域扩展合并配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class AreaExpandMergeConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public AreaExpandMergeConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 区域扩展配置id
     */
    private String areaExpandConfigId;

    /**
     * 合并规则
     */
    private String mergeRules;

    /**
     * 开始位置
     */
    private String startPosition;

    /**
     * 结束位置
     */
    private String endPosition;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
