package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.base.enums.EnumReportConfigType;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * 报表模板基础配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class BaseConfig extends LimsBaseEntity {

    public BaseConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 报表id
     */
    @Id
    @Column(length = 50, nullable = false)
    private String id = UUIDHelper.newId();

    /**
     * 报表编码
     */
    @Column(length = 50, nullable = false)
    private String reportCode;

    /**
     * 报表类型编码，枚举维护：原始记录单 1、采样单 2、报告 3、报表 4、标签 5
     * {@link EnumReportConfigType}
     */
    @Column(nullable = false)
    private Integer reportTypeValue;

    /**
     * 模板名称
     */
    @Column(nullable = false)
    private String templateName;

    /**
     * 模板位置
     */
    @Column(nullable = false)
    private String templatePath;

    /**
     * 排序值
     */
    @Column(nullable = false)
    private Integer orderNum;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    private Boolean isDeleted = false;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 受控编号
     */
    private String controlNum;

    /**
     * 配置报表名称
     */
    private String definedFileName;

    /**
     * 分页字段
     */
    private String pageFields;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    private Date modifyDate;
}
