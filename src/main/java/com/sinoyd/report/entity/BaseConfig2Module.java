package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;


/**
 * 报告组件与报表基础配置关联表
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class BaseConfig2Module extends LimsBaseEntity {

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 报表基础配置id
     */
    private String baseConfigId;

    /**
     * 报告组件id（常量维护）
     */
    private String reportModuleId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

}
