package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;

/**
 * 数据集表实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class DataSet extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;

    public DataSet() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 数据源id
     */
    private String dsId;

    /**
     * 数据集名称
     */
    private String dataSetName;

    /**
     * 数据集编码
     */
    private String dataSetCode;

    /**
     * 是否集合
     */
    private Boolean isCollection;

    /**
     * sql内容
     */
    private String sqlContent;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 假删标记
     */
    private Boolean isDeleted = false;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;


}