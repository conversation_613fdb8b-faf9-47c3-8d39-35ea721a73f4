package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;

/**
 * 数据集的数据列表实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class DataSetColumn extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;

    public DataSetColumn() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 数据集id
     */
    private String dataSetId;

    /**
     * 数据列编码
     */
    private String columnCode;

    /**
     * 数据列名称
     */
    private String columnName;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 说明
     */
    private String remark;

    /**
     * 假删标记
     */
    private Boolean isDeleted = false;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;


}