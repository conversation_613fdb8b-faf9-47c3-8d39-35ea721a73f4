package com.sinoyd.report.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;

/**
 * 数据源配置表实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class DataSource extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;

    public DataSource() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 数据源名称
     */
    private String dsName;

    /**
     * 数据库类型字典编码，字典管理
     */
    private String dbTypeCode;

    /**
     * 数据库驱动字典编码，字典管理，和dbTypeCode联动
     */
    private String dbDriverCode;

    /**
     * 数据库地址
     */
    private String dbHost;

    /**
     * 数据库端口，需要根据dbTypeCode给出默认值
     */
    private Integer dbPort;

    /**
     * 数据库Schema名称
     */
    private String dbName;

    /**
     * 数据库用户名
     */
    private String dbUserName;

    /**
     * 数据库密码，加密存储
     */
    private String dbPassword;

    /**
     * 备注
     */
    private String remark;

    /**
     * 假删标记
     */
    private Boolean isDeleted = false;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;
}