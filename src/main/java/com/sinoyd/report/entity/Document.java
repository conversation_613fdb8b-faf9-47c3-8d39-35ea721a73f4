package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;


/**
 * 文档实体
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2023/11/15
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class Document extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Document() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 关联对象id（Guid）
     */
    private String objectId;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 物理文件名
     */
    private String physicalName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 是否副本
     */
    private Boolean isTranscript = false;

    /**
     * 文件类型
     */
    private String docType;

    /**
     * 文件类型描述
     */
    private String docTypeDesc;

    /**
     * 文件大小
     */
    private Long docSize;

    /**
     * 文件后缀
     */
    private String docSuffix;

    /**
     * 下载次数
     */
    private Integer downloadTimes = 0;

    /**
     * 排序值
     */
    private Integer orderNum = 0;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上传人Id
     */
    private String uploadPersonId;

    /**
     * 上传人姓名
     */
    private String uploadPerson;

    /**
     * 是否文件置顶
     */
    private Boolean isTop = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}