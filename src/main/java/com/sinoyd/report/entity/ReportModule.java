package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import java.util.Date;

/**
 * 报告组件信息表
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class ReportModule extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public ReportModule() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 组件编码
     */
    private String moduleCode;

    /**
     * 组件名称
     */
    private String moduleName;

    /**
     * 组件主表名称
     */
    private String tableName;

    /**
     * 组件数据行表名称
     */
    private String sourceTableName;

    /**
     * 组件每页样品数量
     */
    private Integer sampleCount;

    /**
     * 组件每页测试项目数量
     */
    private Integer testCount;


    /**
     * 子组件配置信息（适用于复合组件）
     */
    private String sonTableJson;

    /**
     * 是否复合组件
     */
    private Boolean isCompound;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

}
