package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;


/**
 * 报告各个组件配置的分页方式表
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class ReportModule2GroupType extends LimsBaseEntity {

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 报告组件配置id
     */
    private String baseConfigModuleId;

    /**
     * 分页类型名称（包含数据源，属性名称，分页方式）
     */
    private String groupTypeName;

    /**
     * 优先级（最外层分页的优先级最高）
     */
    private Integer priority;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

}
