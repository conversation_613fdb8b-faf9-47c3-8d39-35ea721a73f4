package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;


/**
 * 表单配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class SamplingConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public SamplingConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();

    /**
     * 名称
     */
    private String formName;

    /**
     * 报表模板编码
     */
    private String reportCode;

    /**
     * 检测类型Id(小类)
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 录入方式，枚举管理{@link com.sinoyd.base.enums.EnumSamplingFormFillType}
     */
    private Integer fillType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否仅关联测试项目显示
     */
    private Boolean isRelatedTest;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;

}