package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * sheet页分页配置明细
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class SheetPagingConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public SheetPagingConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * sheet页配置id
     */
    private String sheetConfigId;

    /**
     * 应用数据列id
     */
    private String dataSetApplyColumnId;

    /**
     * 优先级（值越大，优先级越高）
     */
    private Integer priority;

    /**
     * 每页数量（例如按分析项目分页，每页放3个分析项目，countPerPage = 3）
     */
    private Integer countPerPage;

    /**
     * 区域数据是否跟随分页属性展示
     */
    private Boolean correspondFlag;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;

}
