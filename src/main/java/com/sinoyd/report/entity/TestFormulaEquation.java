package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 测试项目公式方程实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquation extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public TestFormulaEquation() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();

    /**
     * 测试项目公式id
     */
    private String testFormulaId;

    /**
     * 方程种类，枚举管理
     */
    private Integer equationCategory;

    /**
     * 是否前置判定
     */
    private Boolean isPreJudge;

    /**
     * 前置判定方程
     */
    private String preJudgeEquation;

    /**
     * 计算方程
     */
    private String calculateEquation;

    /**
     * 有效位数
     */
    private Integer significantDigit;

    /**
     * 小数位数
     */
    private Integer decimalDigit;

    /**
     * 检出限
     */
    private String detectionValue;

    /**
     * 小于检出限显示字典编码，字典管理
     */
    private String detectionDisplayCode;

    /**
     * 修约规则字典编码，字典管理
     */
    private String reviseRuleCode;

    /**
     * 计算方式，枚举管理
     */
    private Integer calculateWay;

    /**
     * 计算优先级
     */
    private Integer calculatePriority;

    /**
     * 计算参数id
     */
    private String calculateParamId;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;

}
