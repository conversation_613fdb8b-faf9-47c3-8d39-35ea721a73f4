package com.sinoyd.report.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 测试项目公式参数实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class TestFormulaEquationParam extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public TestFormulaEquationParam() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();

    /**
     * 测试项目公式方程id
     */
    private String testFormulaId;

    /**
     * 参数种类，枚举管理，如表头参数
     */
    private Integer paramCategory;

    /**
     * 参数id
     */
    private String paramId;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数别名
     */
    private String aliasName;

    /**
     * 控件类型，枚举管理
     */
    private Integer controlType;

    /**
     * 是否必填
     */
    private Boolean isMandatory;

    /**
     * 是否出证
     */
    private Boolean isCert;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 有效位数
     */
    private Integer significantDigit;

    /**
     * 小数位数
     */
    private Integer decimalDigit;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否显示
     */
    private Boolean isDisplay;

    /**
     * 参考文本
     */
    private String reference;

    /**
     * 数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置
     */
    private Integer dsType;

    /**
     * 当dsType是接口时，该字段存储接口参数及值
     */
    private String apiDsParams;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;

}
