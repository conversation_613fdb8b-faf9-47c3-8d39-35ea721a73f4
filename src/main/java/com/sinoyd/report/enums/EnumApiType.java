package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API接口类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@Getter
@AllArgsConstructor
public enum EnumApiType {

    数据集(1),

    其他(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumApiType getByValue(Integer value) {
        for (EnumApiType c : EnumApiType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的API接口返回类型枚举值[%d]", value));
    }
}
