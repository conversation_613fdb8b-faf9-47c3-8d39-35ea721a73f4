package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表区域合并个性化处理类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/14
 */
@Getter
@AllArgsConstructor
public enum EnumAreaMergeIndType {

    /**
     * 原样与串联样合并个性化
     */
    原样与串联样合并("clMerge");


    /**
     * 类型编码
     */
    private final String value;

    /**
     * 根据编码获取枚举
     *
     * @param value 编码
     * @return 枚举
     */
    public static EnumAreaMergeIndType getByValue(String value) {
        for (EnumAreaMergeIndType e : EnumAreaMergeIndType.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("区域合并个性化处理类型枚举值[%s]!", value));
    }

}
