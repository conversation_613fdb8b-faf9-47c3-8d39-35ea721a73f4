package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@Getter
@AllArgsConstructor
public enum EnumAreaType {

    /**
     * 固定区域
     */
    全局固定区域("Info", 10),

    /**
     * 分页表头区域
     */
    分页表头区域("Add", 20),

    /**
     * 数据区域
     */
    拓展数据区域("Data", 30),

    /**
     * 平行数据区域
     */
    平行数据区域("Parallel", 40),

    /**
     * 加标数据区域
     */
    加标数据区域("Addition", 50),

    /**
     *  标样数据区域
     */
    标样数据区域("Standard", 60),

    /**
     * 全程序空白数据区域
     */
    全程序空白数据区域("OutBlank", 70),

    /**
     * 室内空白数据区域
     */
    室内空白数据区域("InnerBlank", 80),

    /**
     * 曲线校核数据区域
     */
    曲线校核数据区域("JHCurve", 90),

    /**
     * 校正系数检验数据区域
     */
    校正系数检验数据区域("CorrectionFactor", 100),

    /**
     * 分析项目数据区域
     */
    分析项目数据区域("AnalyzeItem", 110),

    /**
     * 点位数据区域
     */
    点位数据区域("SampleFolder", 120),

    /**
     * 合并数据区域
     */
    合并数据区域("Merge", 130),

    /**
     * 页码数据区域
     */
    页码数据区域("PageReveal", 140),

    /**
     * 曲线校准数据区域
     */
    曲线校准数据区域("JZCurve", 140);

    /**
     * 区域编码
     */
    private final String value;

    /**
     * 排序值
     */
    private final Integer orderNum;


    /**
     * 根据编码获取枚举
     *
     * @param value 编码
     * @return 枚举
     */
    public static EnumAreaType getByValue(String value) {
        for (EnumAreaType e : EnumAreaType.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的区域类型枚举值[%s]!", value));
    }

}
