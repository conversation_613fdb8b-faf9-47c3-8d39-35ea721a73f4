package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全局配置自定义参数编码枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Getter
@AllArgsConstructor
public enum EnumCustomParamCode {

    /**
     * 报表数据源个性化
     */
    报表数据源个性化("DataSourceInd"),

    /**
     * 质控页是否显示现场平行
     */
    质控页是否显示现场平行("QCOutParallel"),

    /**
     * 数据排序规则
     */
    数据排序规则("DataSortRules"),

    /**
     * 数据排序规则
     */
    区域数据主从配置("AreaDataMainSubConfig");

    /**
     * 规则编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumCustomParamCode getByValue(String code) {
        for (EnumCustomParamCode e : EnumCustomParamCode.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("自定义参数编码枚举值[%s]!", code));
    }

}
