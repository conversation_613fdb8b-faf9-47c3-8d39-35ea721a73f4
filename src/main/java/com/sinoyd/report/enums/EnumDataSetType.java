package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 数据集应用所选数据集类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@Getter
@AllArgsConstructor
public enum EnumDataSetType {

    SQL数据集(1),

    API接口(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumDataSetType getByValue(Integer value){
        for (EnumDataSetType c : EnumDataSetType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的数据集应用类型枚举值[%d]!", value));
    }
}
