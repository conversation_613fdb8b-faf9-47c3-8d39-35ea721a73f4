package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表数据排序处理类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/17
 */
@Getter
@AllArgsConstructor
public enum EnumDataSortType {

    /**
     * 默认排序规则
     */
    默认排序("DefaultSort");


    /**
     * 排序类型编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumDataSortType getByValue(String code) {
        for (EnumDataSortType e : EnumDataSortType.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("数据排序处理类型枚举值[%s]!", code));
    }

}
