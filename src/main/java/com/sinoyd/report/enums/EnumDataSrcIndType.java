package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表数据源个性化处理类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Getter
@AllArgsConstructor
public enum EnumDataSrcIndType {

    /**
     * 区域数据个性化
     */
    区域数据个性化("AreaDataInd"),

    /**
     * 根据不同分析项目对数据进行不同处理
     */
    分析项目数据处理个性化("AnalyzeItemDataInd");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumDataSrcIndType getByValue(String code) {
        for (EnumDataSrcIndType e : EnumDataSrcIndType.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("数据源个性化处理类型枚举值[%s]!", code));
    }

}
