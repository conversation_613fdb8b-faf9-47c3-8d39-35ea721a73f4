package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表下载方式枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/31
 */
@Getter
@AllArgsConstructor
public enum EnumDownloadMethod {

    /**
     * 导出报表
     */
    export(1),

    /**
     * 生成报表
     */
    generate(2);

    private final Integer value;

    /**
     * 根据编码获取枚举
     *
     * @param value 编码
     * @return 枚举
     */
    public static EnumDownloadMethod getByValue(String value) {
        for (EnumDownloadMethod e : EnumDownloadMethod.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的报表下载方式枚举值[%s]!", value));
    }

}
