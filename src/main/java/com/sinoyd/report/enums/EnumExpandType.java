package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域拓展类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@Getter
@AllArgsConstructor
public enum EnumExpandType {

    行拓展(1),
    列拓展(2),
    不拓展(3);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumExpandType getByValue(Integer value) {
        for (EnumExpandType c : EnumExpandType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的区域拓展类型枚举值[%s]!", value));
    }
}
