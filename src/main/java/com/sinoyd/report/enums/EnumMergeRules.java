package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表单元格合并处理类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/14
 */
@Getter
@AllArgsConstructor
public enum EnumMergeRules {

    /**
     * 单元格合并个性化
     */
    串联样单元格合并("ClMerge");


    /**
     * 合并规则编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumMergeRules getByValue(String code) {
        for (EnumMergeRules e : EnumMergeRules.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("单元格合并处理类型枚举值[%s]!", code));
    }

}
