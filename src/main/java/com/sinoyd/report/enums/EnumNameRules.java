package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表文件名称规则枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Getter
@AllArgsConstructor
public enum EnumNameRules {

    /**
     * 检测单编码名称规则
     */
    检测单编号("WorkSheetCode"),

    /**
     * 送样单号名称规则
     */
    送样单号("RecordCode"),

    /**
     * 采样时间名称规则
     */
    采样时间("SamplingTime"),

    /**
     * 项目编号名称规则
     */
    项目编号("ProjectCode");


    /**
     * 名称规则编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumNameRules getByValue(String code) {
        for (EnumNameRules e : EnumNameRules.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的命名方法枚举值[%s]!", code));
    }

}
