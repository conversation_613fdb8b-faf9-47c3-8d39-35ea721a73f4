package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表文件命名方法枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/12
 */
@Getter
@AllArgsConstructor
public enum EnumNamingMethod {

    /**
     * 原始记录单命名方法
     */
    原始记录单("WorkSheet"),

    /**
     * 采样单命名方法
     */
    采样单("Sampling"),

    /**
     * 项目命名方法
     */
    项目("Project");

    /**
     * 命名方法编码
     */
    private final String code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static EnumNamingMethod getByValue(String code) {
        for (EnumNamingMethod e : EnumNamingMethod.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的命名方法枚举值[%s]!", code));
    }

}
