package com.sinoyd.report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测试项目公式对象类型
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor
@Getter
public enum EnumObjectType {

    采样单(1),

    原始记录单(2);

    /**
     * 枚举值
     */
    private Integer value;

    /**
     * 根据枚举值获取枚举名称
     *
     * @param value 枚举值
     * @return 枚举名称
     */
    public static String EnumObjectType(Integer value) {
        for (EnumObjectType c : EnumObjectType.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        return "";
    }

}
