package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据集/Api接口参数类型
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@AllArgsConstructor
@Getter
public enum EnumParamType {

    项目ID("projectId"),

    项目ID集合("projectIds"),

    样品ID("sampleId"),

    样品ID集合("sampleIds"),

    工作单ID("workSheetFolderId"),

    工作单ID集合("workSheetFolderIds"),

    送样单ID("receiveSampleRecordId"),

    送样单ID集合("receiveSampleRecordIds"),

    固定值("fixedValue");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumParamType getByValue(String value){
        for (EnumParamType e : EnumParamType.values()) {
            if (e.getValue().equals(value)){
                return e;
            }
        }
        throw new BaseException(String.format("非法的业务参数枚举值[%s]!", value));
    }

}
