package com.sinoyd.report.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告用到的枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/26
 */
public class EnumReport {

    /**
     * 合并方式
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMergeModel {

        /**
         * 按行合并
         */
        Row,

        /**
         * 按列合并
         */
        Cell
    }
}
