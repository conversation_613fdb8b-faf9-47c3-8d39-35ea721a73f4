package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Api请求方式
 *
 * <AUTHOR>
 * @version L
 */
@AllArgsConstructor
@Getter
public enum EnumRequestMethod {

    GET(1),

    POST(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumRequestMethod getByValue(Integer value) {
        for (EnumRequestMethod c : EnumRequestMethod.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        throw new BaseException(String.format("非法的请求方式枚举值[%d]!", value));
    }
}
