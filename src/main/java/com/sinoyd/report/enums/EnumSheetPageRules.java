package com.sinoyd.report.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表模板页属性页码规则枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
@Getter
@AllArgsConstructor
public enum EnumSheetPageRules {

    /**
     * 保持连续
     */
    continuous(1),

    /**
     * 独立分页
     */
    independent(2);

    private final Integer value;

    /**
     * 根据编码获取枚举
     *
     * @param value 编码
     * @return 枚举
     */
    public static EnumSheetPageRules getByValue(Integer value) {
        for (EnumSheetPageRules e : EnumSheetPageRules.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的报表页码规则枚举值[%s]!", value));
    }

}
