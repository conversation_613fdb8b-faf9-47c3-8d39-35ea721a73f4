package com.sinoyd.report.excel.builder;

import com.sinoyd.boot.common.util.StringUtils;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

import static com.sinoyd.report.excel.util.GenerateExcelReportUtil.*;

/**
 * excel报表单元格值的样式构建器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/12/5
 */
public class ExcelCellValueStyleBuilder {

    /**
     * 拼接的HTML列表
     */
    private List<StringBuilder> htmlList;

    /**
     * CSS样式列表,与HTML列表对应
     */
    private List<StringBuilder> cssList;

    /**
     * 私有化构造方法
     */
    private ExcelCellValueStyleBuilder() {
    }

    /**
     * 初始化
     *
     * @return 构建器实例
     */
    public static ExcelCellValueStyleBuilder init() {
        ExcelCellValueStyleBuilder builder = new ExcelCellValueStyleBuilder();
        builder.htmlList = new ArrayList<>();
        builder.cssList = new ArrayList<>();
        return builder;
    }

    /**
     * 设置单元格值及样式
     *
     * @param cellValue 传入的单元格值
     * @param isFormat  是否转换科学计数法
     */
    public ExcelCellValueStyleBuilder setStyleVal(String cellValue, boolean isFormat) {
        String[] valueArr = cellValue.split(System.lineSeparator());
        for (String val : valueArr) {
            if (checkColor(val)) {
                String[] valArr = val.split(":");
                addColor(valueArr[0]);
                addValue(checkAndFormatSci(valArr.length > 1 ? valArr[1] : "", isFormat));
            } else if (checkFont(val)) {
                String[] valArr = val.split(":");
                addFontFamily(valArr[1]);
                addValue(checkAndFormatSci(valArr.length > 2 ? valArr[2] : "", isFormat));
            } else {
                addColor("");
                addValue(checkAndFormatSci(val, isFormat));
            }
        }
        return this;
    }

    /**
     * 设置单元格整体数值的颜色
     *
     * @param color 颜色，十六进制颜色代码，比如 #FF9B00
     */
    public void addColor(String color) {
        if (StringUtils.isNotEmpty(color)) {
            StringBuilder sb = new StringBuilder("color:");
            sb.append(color).append(";");
            this.cssList.add(sb);
        } else {
            this.cssList.add(new StringBuilder(""));
        }
    }

    /**
     * 设置单元格整体数值的字体
     *
     * @param font 字体
     */
    public void addFontFamily(String font) {
        if (StringUtils.isNotEmpty(font)) {
            StringBuilder sb = new StringBuilder("font-family: ");
            sb.append(font).append(";");
            this.cssList.add(sb);
        } else {
            this.cssList.add(new StringBuilder(""));
        }
    }

    /**
     * 拼接值
     *
     * @param cellValue 单元格值
     */
    public void addValue(String cellValue) {
        //拼接数据
        this.htmlList.add(new StringBuilder(cellValue));
    }


    /**
     * 获取将样式拼装成html格式的单元格值
     *
     * @return 将样式拼装成html格式的单元格值
     */
    public String getHtmlVal() {
        StringBuilder resHtml = new StringBuilder();
        for (int i = 0; i < this.htmlList.size(); i++) {
            if (StringUtils.isNotEmpty(this.cssList.get(i).toString())) {
                resHtml.append("<span style=").append("\"").append(this.cssList.get(i)).append("\"").append(">").append(this.htmlList.get(i)).append("</span>");
            } else {
                resHtml.append("<span>").append(this.htmlList.get(i)).append("</span>");
            }
            if (i < this.htmlList.size() - 1) {
                resHtml.append("<br/>");
            }
        }
        return resHtml.toString();
    }

    /**
     * 获取颜色的16进制
     *
     * @param color 颜色对象
     * @return 16进制颜色
     */
    private String getRGB16(Color color) {
        int red = color.getRed();
        int green = color.getGreen();
        int blue = color.getBlue();
        return String.format("#%02x%02x%02x", red, green, blue);
    }

}