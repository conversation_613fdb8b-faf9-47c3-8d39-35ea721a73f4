package com.sinoyd.report.excel.enums;

/**
 * 区域拓展类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
public enum EnumExcelExpandType {

    行拓展(1),
    列拓展(2),
    不拓展(3);

    private final Integer value;

    EnumExcelExpandType(Integer value) {
        this.value = value;
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumExcelExpandType getByValue(Integer value) {
        for (EnumExcelExpandType c : EnumExcelExpandType.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        return null;
    }
}
