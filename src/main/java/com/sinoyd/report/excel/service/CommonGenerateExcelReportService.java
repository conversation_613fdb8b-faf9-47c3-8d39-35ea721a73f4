package com.sinoyd.report.excel.service;


import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.report.excel.vo.CommonExcelReportParamVO;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * 通用excel报表导出接口(采样单，原始记录单)
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/09
 * @since V100R001
 */
public interface CommonGenerateExcelReportService {

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param response           响应对象
     */
    void generateExcelReport(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, HttpServletResponse response);

    /**
     * 生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param outputStream       输出流
     */
    void generateExcelReportStream(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, OutputStream outputStream);

    /**
     * 获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    WorkbookDesigner generateDesigner(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap);

    /**
     * 根据报表配置获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    WorkbookDesigner generateDesignerByParam(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap);

}
