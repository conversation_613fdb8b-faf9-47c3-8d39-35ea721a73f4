package com.sinoyd.report.excel.service;


import com.sinoyd.report.excel.vo.ExcelParamVO;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 通用aspose导出接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/14
 * @since V100R001
 */
public interface ExcelExportService {

    /**
     * 导出excel（单个sheet）
     *
     * @param paramVO  数据源
     * @param response 响应体
     * @return 返回导出的文件流
     */
    void exportExcel(ExcelParamVO paramVO, HttpServletResponse response);

    /**
     * 生成文档到输出流中
     *
     * @param paramVO  数据源
     * @param outputStream  输出流
     */
    void generateStream(ExcelParamVO paramVO, OutputStream outputStream);

}
