package com.sinoyd.report.excel.service.impl;

import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.report.excel.service.CommonGenerateExcelReportService;
import com.sinoyd.report.excel.util.GenerateExcelReportUtil;
import com.sinoyd.report.excel.vo.CommonExcelReportParamVO;
import com.sinoyd.report.utils.ReportFileUtil;
import com.sinoyd.report.utils.ReportLicenseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import static com.sinoyd.report.excel.util.GenerateExcelReportUtil.*;

/**
 * 通用excel报表导出接口(采样单，原始记录单)
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/09
 * @since V100R001
 */
@Service
@Slf4j
public class CommonGenerateExcelReportServiceImpl implements CommonGenerateExcelReportService {

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param response           响应对象
     */
    @Override
    public void generateExcelReport(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, HttpServletResponse response) {
        //获取designer对象
        WorkbookDesigner designer = generateDesigner(excelReportParamVO, dsMap);
        //保存报表文件
        String path = saveReportFile(designer, excelReportParamVO);
        //下载文件
        FileUtil.download(path, excelReportParamVO.getOutPutName(), response);
    }

    /**
     * 根据报表基础配置生成excel报表
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @param outputStream       输出流
     */
    @Override
    public void generateExcelReportStream(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, OutputStream outputStream) {
        //获取designer对象
        WorkbookDesigner designer = generateDesigner(excelReportParamVO, dsMap);
        //保存文件至输出流
        try {
            designer.getWorkbook().save(outputStream, SaveFormat.XLSX);
        } catch (Exception e) {
            log.error("保存文档到输出流中出错！", e);
            throw new RuntimeException("无法保存文档到输出流中!", e);
        }
    }

    /**
     * 根据报表基础配置获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    @Override
    public WorkbookDesigner generateDesigner(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap) {
        String reportCode = StringUtils.isNotEmpty(excelReportParamVO.getReportCode()) ? excelReportParamVO.getReportCode() : "";
        log.info("开始导出通用excel报表(采样单，原始记录单) 报表编码：" + reportCode);
        ReportLicenseUtil.isLicense();
        return generateDesignerByParam(excelReportParamVO, dsMap);
    }

    /**
     * 获取Designer对象
     *
     * @param excelReportParamVO excel报表生成数据传输对象
     * @param dsMap              报表数据源
     * @return Designer对象
     */
    @Override
    public WorkbookDesigner generateDesignerByParam(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap) {
        WorkbookDesigner designer = getDesigner(excelReportParamVO);
        //设置页眉页脚
        setPageHeadFoot(excelReportParamVO, designer);
        //设置报表表头名称
        setSheetReportName(excelReportParamVO, dsMap, designer);
        //每个sheet页配置的所有区域的最大页数的映射
        Map<String, Integer> sheetName2MaxPageCntMap = GenerateExcelReportUtil.getSheet2MaxPageCntMap(dsMap, excelReportParamVO);
        if (excelReportParamVO.getGlobalConfig().getIsPageable()) {
            //按照分页生成
            generateByPage(excelReportParamVO, sheetName2MaxPageCntMap, designer, dsMap);
        } else {
            //不分页生成
            generateBySinglePage(excelReportParamVO, sheetName2MaxPageCntMap, designer, dsMap);
        }
        //报表以100%的缩放模式打开并设置打印区域
        setPrintArea(designer, sheetName2MaxPageCntMap, dsMap);
        //合并区域
        merge(designer.getWorkbook(), dsMap, excelReportParamVO.getAreaConfigList());
        return designer;
    }

    /**
     * 获取designer对象
     *
     * @param excelReportParamVO 前端传参接收对象
     * @return designer对象
     */
    private WorkbookDesigner getDesigner(CommonExcelReportParamVO excelReportParamVO) {
        String templatePath = excelReportParamVO.getBaseConfig().getTemplatePath() + "/" + excelReportParamVO.getBaseConfig().getTemplateName();
        log.info("开始获取designer对象，模板路径：" + templatePath);
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.getWorkbook().getWorksheets().clear();
        setWorkbookForDesigner(templatePath, designer);
        return designer;
    }

    /**
     * 设置workBook对象
     *
     * @param templatePath 模板路径
     * @param designer     excel工作单
     */
    private void setWorkbookForDesigner(String templatePath, WorkbookDesigner designer) {
        InputStream stream = null;
        try {
            File file = new File(templatePath);
            stream = file.exists() ? new FileInputStream(file)
                    : this.getClass().getClassLoader().getResourceAsStream(templatePath);
            if (stream == null) {
                throw new BaseException("报表模板输入流创建失败，请确认模板是否存在: " + templatePath);
            }
            designer.setWorkbook(new Workbook(stream));
        } catch (Exception e) {
            log.error("设置workBook对象出错：", e);
            throw new BaseException("设置workBook对象出错");
        } finally {
            ReportFileUtil.closeStream(stream);
        }
    }
}
