package com.sinoyd.report.excel.service.impl;

import com.aspose.cells.SaveFormat;
import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.common.utils.AsposeLicenseUtil;
import com.sinoyd.report.excel.service.ExcelExportService;
import com.sinoyd.report.excel.util.AsposeExcelUtil;
import com.sinoyd.report.excel.vo.ExcelParamVO;
import com.sinoyd.report.utils.ReportFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 通用aspose导出接口
 * 文件生成代码处理包含
 * 1.表头固定区域赋值，ExcelFixedConfigVO，定义了开始结束列和行，数据部分Map<String, Object> dataMaps，模板中占位符需要以'&='开头，map的key对应模板中占位符
 * 2.拓展列处理，模板中占位符以'expandColumn='开头，拓展列数据放在List<Map<String, Object>> expandList中，一个map相当于一列，map中包含拓展列的列名数据和对应列数据占位符，模板中对应列数据的占位符为map中的key值加'&='
 * 3.行数据的处理，数据部分List<Map<String, Object>> mapList，一个map为一行，模板中占位符为 '&=' + map中的key值，如果map中的value值为 'Red:'开头，则对应单元格标红
 * 4.excel合并区域部分，设置为List<ExcelMergeAreaVO> excelMergeAreas，每个Vo设置开始合并的行数和列数，以及总行数和总列数，以及按行合并还是按列合并
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/14
 * @since V100R001
 */
@Service
@Slf4j
public class ExcelExportServiceImpl implements ExcelExportService {

    @Override
    public void exportExcel(ExcelParamVO paramVO, HttpServletResponse response) {
        WorkbookDesigner designer = generate(paramVO);
        ReportFileUtil.downloadExcel(paramVO.getFileName(), designer, response);
        AsposeExcelUtil.saveOutPutFile(designer, paramVO.getOutPutPath(), paramVO.getFileName());
    }

    @Override
    public void generateStream(ExcelParamVO paramVO, OutputStream outputStream) {
        WorkbookDesigner designer = generate(paramVO);
        try {
            designer.getWorkbook().save(outputStream, SaveFormat.XLSX);
        } catch (Exception e) {
            throw new RuntimeException("无法保存文档到输出流中!");
        }
    }

    /**
     * 生成excel designer
     *
     * @param paramVO excel生成参数
     * @return WorkbookDesigner
     */
    public WorkbookDesigner generate(ExcelParamVO paramVO) {
        if (!AsposeLicenseUtil.isLicense()) {
            return null;
        }
        WorkbookDesigner designer = AsposeExcelUtil.getWorkbookDesigner(paramVO);
        if (paramVO.getIsMultiData() != null && paramVO.getIsMultiData()) {
            AsposeExcelUtil.exportExcel(paramVO.getMultiDataList(), paramVO.getExcelFixedConfigList(), paramVO.getExcelMergeAreas(), designer, paramVO.getSheetIndex());
        } else {
            AsposeExcelUtil.exportExcel(paramVO.getMapList(), paramVO.getExcelFixedConfigList(), paramVO.getExcelMergeAreas(), paramVO.getExpandList(),
                    designer, paramVO.getSheetIndex());
        }
        return designer;
    }


}
