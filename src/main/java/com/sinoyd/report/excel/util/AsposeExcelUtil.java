package com.sinoyd.report.excel.util;

import com.aspose.cells.*;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.enums.EnumReport;
import com.sinoyd.report.excel.vo.ExcelFixedConfigVO;
import com.sinoyd.report.excel.vo.ExcelMergeAreaVO;
import com.sinoyd.report.excel.vo.ExcelParamVO;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AsposeExcelUtil生成工具类
 *
 * <AUTHOR>
 * @version V5.2.1
 * @since 2023/10/10
 */
@Slf4j
public class AsposeExcelUtil {

    private final static Pattern ruler = Pattern.compile("\\{\\{(.+?)\\}\\}");

    /**
     * 多数据集导出,不支持列拓展
     *
     * @param multiDataList           数据集
     * @param excelFixedConfigList    固定区域赋值
     * @param excelMergeAreas         合并区域
     * @param designer                工作单
     * @param sheetIndex              sheet索引
     */
    public static void exportExcel(List<List<Map<String, Object>>> multiDataList,
                                   List<ExcelFixedConfigVO> excelFixedConfigList,
                                   List<ExcelMergeAreaVO> excelMergeAreas,
                                   WorkbookDesigner designer, Integer sheetIndex) {
        //默认第一个
        Worksheet sheet = designer.getWorkbook().getWorksheets().get(sheetIndex);
        //对表头固定区域进行赋值
        if (StringUtils.isNotEmpty(excelFixedConfigList)) {
            writeExcelFixedConfigData(excelFixedConfigList.get(0), sheet);
        }
        int num = 1;
        for (List<Map<String, Object>> dataList : multiDataList) {
            int size = dataList.size();
            //最大行数
            int rows = sheet.getCells().getMaxDataRow();
            //最大的列数
            int maxColumn = sheet.getCells().getMaxColumn();
            int i;
            Boolean flag = false;
            for (i = num; i <= rows; i++) {
                for (int j = 0; j <= maxColumn; j ++) {
                    Cell cell = sheet.getCells().get(i, j);
                    if (cell != null) {
                        String value = cell.getStringValue();
                        if (ruler.matcher(value).find()) {
                            flag = true;
                            break;
                        }
                    }
                }
                if (flag) {
                    break;
                }
            }
            sheet.getCells().insertRows(i + 1, size - 1);
            Cells thisCells = sheet.getCells();
            for (int k = 1; k <= size -1; k++) {
                try {
                    thisCells.copyRows(thisCells, i, i + k, 1);
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            num = i + size;

            Row row = sheet.getCells().getRows().get(i);
            //循环列数据
            for (int columnIndex = 0; columnIndex <= maxColumn; columnIndex++) {
                Cell cell = row.get(columnIndex);
                if (cell != null) {//只有格子是{{}} 才进行数据处理
                    Matcher m = ruler.matcher(cell.getStringValue());
                    if (m.find()) {
                        String name = m.group(1);
                        if (size == 0) {
                            Cell cellValue = sheet.getCells().get(i, columnIndex);
                            cellValue.putValue("", false, true);
                        }
                        //循环行数据
                        for (int j = 0; j < size; j++) {
                            Map<String, Object> map1 = dataList.get(j);
                            Cell cellValue = sheet.getCells().get(j + i, columnIndex);
                            String value = "";
                            if (map1.containsKey(name)) {
                                value = String.valueOf(map1.get(name));
                            }
                            cellValue.setHtmlString(value);
                        }
                    }
                }
            }
        }
        //对需要合并的单元格进行处理
        merge(sheet, excelMergeAreas);
        if (!excelFixedConfigList.isEmpty() && excelFixedConfigList.size() >= 2) {
            writeExcelFixedConfigData(excelFixedConfigList.get(1), sheet);
        }
    }

    /**
     * 通用的导出方法
     *
     * @param mapList      key为占位符名称，value为对应单元格数据，一行数据为一个map
     * @param expandList   扩展列
     * @param designer     工作单
     * @param sheetIndex   索引
     */
    public static void exportExcel(List<Map<String, Object>> mapList,
                                   List<ExcelFixedConfigVO> excelFixedConfigList,
                                   List<ExcelMergeAreaVO> excelMergeAreas,
                                   List<Map<String, Object>> expandList,
                                   WorkbookDesigner designer, Integer sheetIndex
    ) {
        //默认第一个
        Worksheet sheet = designer.getWorkbook().getWorksheets().get(sheetIndex);
        //对表头固定区域进行赋值
        if (StringUtils.isNotEmpty(excelFixedConfigList)) {
            writeExcelFixedConfigData(excelFixedConfigList.get(0), sheet);
        }
        //最大行数
        int rows = sheet.getCells().getMaxDataRow();
        //最大的列数
        int maxColumn = sheet.getCells().getMaxColumn();
        //把sheet里面的列补充完整
        for (int column = 0; column <= maxColumn; column++) {
            for (int m = 0; m <= rows; m++) {
                Cell cell = sheet.getCells().get(m, column);
                if (cell != null) {
                    String value = cell.getStringValue();
                    if (value.contains("expandColumn=")) {
                        setExtendList(column, m, sheet, expandList);
                        break;
                    }
                }
            }
        }
        //默认是第一行
        int i;
        int size = mapList.size();
        for (i = 1; i <= rows; i++) {
            Cell cell = sheet.getCells().get(i, 1);
            if (cell != null) {
                String value = cell.getStringValue();
                if (ruler.matcher(value).find()) {
                    break;
                }
            }
        }

        sheet.getCells().insertRows(i + 1, size - 1);
        Row row = sheet.getCells().getRows().get(i);
        //重新获取最大的列数
        maxColumn = sheet.getCells().getMaxColumn();
        //循环列数据
        for (int columnIndex = 0; columnIndex <= maxColumn; columnIndex++) {
            Cell cell = row.get(columnIndex);
            if (cell != null) {//只有格子是{{}} 才进行数据处理
                Matcher m = ruler.matcher(cell.getStringValue());
                if (m.find()) {
                    String name = m.group(1);
                    if (size == 0) {
                        Cell cellValue = sheet.getCells().get(i, columnIndex);
                        cellValue.putValue("", false, true);
                    }
                    //循环行数据
                    for (int j = 0; j < size; j++) {
                        Map<String, Object> map1 = mapList.get(j);
                        Cell cellValue = sheet.getCells().get(j + i, columnIndex);
                        String value = "";
                        if (map1.containsKey(name)) {
                            value = String.valueOf(map1.get(name));
                        }
                        cellValue.setHtmlString(value);
                    }
                }
            }
        }
        //对需要合并的单元格进行处理
        merge(sheet, excelMergeAreas);
    }

    /**
     * 设置拓展集合
     *
     * @param column     列
     * @param m          行
     * @param sheet      表
     * @param expandList 拓展集合
     */
    private static void setExtendList(Integer column, Integer m, Worksheet sheet, List<Map<String, Object>> expandList) {
        //对应行数与拓展列数据Map中的key值映射
        Map<Integer, String> row2Key = new HashMap<>();
        //含有'='占位符的最后一行,默认最后一行是'{{}}'
        int rowIndex = m;
        while (true) {
            Cell cell = sheet.getCells().get(rowIndex, column);
            Matcher matcher = ruler.matcher(cell.getStringValue());
            if (matcher.find()) {
                row2Key.put(rowIndex, matcher.group(1));
            }
            if (!cell.getStringValue().contains("expandColumn=")) {
                break;
            }
            rowIndex++;
        }

        sheet.getCells().insertColumns(column + 1, expandList.size() - 1);
        int k = column;
        for (Map<String, Object> map : expandList) {
            for (int i = m; i <= rowIndex; i++) {
                Cell cell = sheet.getCells().get(i, k);
                cell.putValue(i == rowIndex ? "{{" + map.get(row2Key.get(i)) + "}}" : map.get(row2Key.get(i)));
            }
            k++;
        }
    }

    /**
     * 渲染Excel中的固定区域数据
     *
     * @param excelFixedConfig 固定区域对象
     * @param sheet            Excel Sheet对象
     */
    private static void writeExcelFixedConfigData(ExcelFixedConfigVO excelFixedConfig, Worksheet sheet) {
        //固定区域复制数据集，map中key为占位符名称，value为对应值
        Map<String, Object> dataMap = excelFixedConfig.getDataMaps();
        for (Integer row = excelFixedConfig.getStartRow(); row <= excelFixedConfig.getEndRow(); row++) {
            for (Integer col = excelFixedConfig.getStartCol(); col <= excelFixedConfig.getEndCol(); col++) {
                Cell cell = sheet.getCells().get(row, col);
                String mark = "{{";
                if (cell != null) {//只有格子是{{}} 才进行数据处理
                    Matcher m = ruler.matcher(cell.getStringValue());
                    if (m.find()) {
                        int index = cell.getStringValue().indexOf(mark);
                        String name = m.group(1);
                        String value = cell.getStringValue().substring(0, index);
                        if (dataMap.containsKey(name)) {
                            value += dataMap.get(name) == null ? "" : String.valueOf(dataMap.get(name));
                        }
                        cell.setHtmlString(value);
                    }
                }
            }
        }
    }

    /**
     * 合并单元格
     *
     * @param sheet           工作页
     * @param excelMergeAreas 合并区域
     */
    //#region 合并的处理方法
    public static void merge(Worksheet sheet,
                             List<ExcelMergeAreaVO> excelMergeAreas) {
        if (StringUtils.isNotNull(excelMergeAreas)) {
            // 单元格合并(1.默认不合并)
            Cells worksheetCells = sheet.getCells();
            Integer rowDataSize = worksheetCells.getMaxDataRow();
            Integer cellDataSize = worksheetCells.getMaxColumn();
            for (ExcelMergeAreaVO excelMergeArea : excelMergeAreas) {
                if (excelMergeArea.getEnumMergeModel().name().equals(EnumReport.EnumMergeModel.Row.name())) {
                    mergeByRow(sheet, worksheetCells, excelMergeArea, rowDataSize, excelMergeArea.getIsFollowRange());
                } else {
                    mergeByCell(sheet, worksheetCells, excelMergeArea, cellDataSize, excelMergeArea.getIsFollowRange());
                }
            }
        }
    }

    /**
     * 按列数据进行合并
     *
     * @param sheet          excel sheet页
     * @param worksheetCells 当前页
     * @param excelMergeArea 合并区域
     * @param cellDataSize   列的数据
     * @param isFollowRange  是否跟随区域
     */
    public static void mergeByCell(Worksheet sheet, Cells worksheetCells,
                                   ExcelMergeAreaVO excelMergeArea, Integer cellDataSize, Boolean isFollowRange) {
        // 单元格合并(1.默认不合并)
        Integer mergeCount = 1;
        Integer cellsMergeStart = 0;
        cellDataSize = isFollowRange ? excelMergeArea.getTotalColumns() : cellDataSize;
        for (int j = 0; j < cellDataSize; j++) {
            Integer rows = excelMergeArea.getRows();
            Integer cells = excelMergeArea.getCells();
            // 本次的单元格
            Cell cell = sheet.getCells().get(rows,
                    cells + j);
            // 下一个单元格
            Cell nextCell = sheet.getCells().get(rows,
                    cells + j + 1);
            // 判断下一个单元格是否存在
            if (StringUtils.isNotNullAndEmpty(nextCell)) {
                // 如果下一个格子的值和本次的格子值相同则合并单元格数mergeCount+1
                if (StringUtils.isNotEmpty(cell.getStringValue())
                        && cell.getStringValue().equals(nextCell.getStringValue())) {
                    if (mergeCount.equals(1)) {
                        cellsMergeStart = cells + j;
                    }
                    mergeCount += 1;
                } else {
                    // 如果下一个格子的值和本次的格子值不通,则检测mergeCount是否为1,不为1则进行合并
                    if (!mergeCount.equals(1)) {
                        worksheetCells.merge(rows, cellsMergeStart, excelMergeArea.getTotalRows(), mergeCount);
                        // 合并完之后还原mergeCount
                        mergeCount = 1;
                    }
                }
            }
        }
    }

    /**
     * 按行数据进行合并(根据区域进行合并)
     *
     * @param sheet          excel sheet页
     * @param worksheetCells 当前页
     * @param excelMergeArea 合并区域
     * @param rowDataSize    行数据
     * @param isFollowRange  是否根据行区域合并
     */
    public static void mergeByRow(Worksheet sheet, Cells worksheetCells,
                                  ExcelMergeAreaVO excelMergeArea, Integer rowDataSize, Boolean isFollowRange) {
        // 单元格合并(1.默认不合并)
        Integer mergeCount = 1;
        rowDataSize = isFollowRange ? excelMergeArea.getTotalRows() : rowDataSize;
        for (Integer j = 0; j < rowDataSize; j++) {
            Integer rows = excelMergeArea.getRows();
            Integer cells = excelMergeArea.getCells();
            //循环需要合并单元格的范围，必须保证范围之后上下单元格的值都是一致的，才能进行合并
            boolean isMerge = true;
            for (Integer i = 0; i <= excelMergeArea.getCellRange(); i++) {
                // 本次的单元格
                Cell cellValue = sheet.getCells().get(j + rows,
                        cells + i);
                // 下一个单元格
                Cell cellNextValue = sheet.getCells().get(j + rows + 1,
                        cells + i);
                // 判断下一个单元格是否存在
                if (isFollowRange && j.equals(rowDataSize - 1)) {
                    isMerge = false;
                } else {
                    if (StringUtils.isNotNullAndEmpty(cellNextValue) && isMerge) {
                        //说明是需要合并的
                        isMerge = StringUtils.isNotNull(cellValue.getStringValue())
                                && cellValue.getStringValue().equals(cellNextValue.getStringValue());
                    } else {
                        //不存在也不进行数据合并
                        isMerge = false;
                    }
                }
            }
            // 如果下一个格子的值和本次的格子值相同则合并单元格数mergeCount+1,说明下一笔数据还可以进行合并
            if (isMerge) {
                mergeCount += 1;
            } else if (!mergeCount.equals(1)) {
                for (Integer i = 0; i <= excelMergeArea.getCellRange(); i++) {
                    // 如果下一个格子的值和本次的格子值不通,则检测mergeCount是否为1,不为1则进行合并
                    worksheetCells.merge(j + rows - mergeCount + 1,
                            cells + i,
                            mergeCount, excelMergeArea.getTotalColumns());
                }
                // 合并完之后还原mergeCount
                mergeCount = 1;
            }
        }
    }


    /**
     * 保存文件
     *
     * @param designer      文档对象
     * @param outPutAbsPath 输出绝对路径
     * @param fileName      文件名称
     * @return 下载路径
     */
    public static String saveOutPutFile(WorkbookDesigner designer, String outPutAbsPath, String fileName) {
        if (outPutAbsPath == null || "".equals(outPutAbsPath)) {
            return "";
        }
        String outFullPath;
        try {
            File outputFile = new File(outPutAbsPath);
            outFullPath = outPutAbsPath + fileName;
            if (!outputFile.exists()) {
                outputFile.mkdirs();
            }
            designer.getWorkbook().save(outFullPath);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("保存输出文件失败，请监测数据路径是否正确");
        }
        return outFullPath;
    }

    /**
     * 获取路径
     *
     * @param isAbs        是否绝对路径
     * @param templateName 模板名称
     * @param designer     excel工作单
     */
    public static void getWorkbookDesigner(Boolean isAbs, String templateName, WorkbookDesigner designer) {
        InputStream stream = null;
        try {
            if (isAbs) {
                File file = new File(templateName);
                stream = Files.newInputStream(file.toPath());
            } else {
                stream = AsposeExcelUtil.class.getResourceAsStream(templateName);
                if (stream == null) {
                    throw new RuntimeException("相对路径输入流创建失败，请确认Resource下是否有此路径: " + templateName);
                }
            }
            designer.setWorkbook(new Workbook(stream));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException(e);
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
    }

    /**
     * 获取excel工作区域
     *
     * @param paramVO excel参数
     * @return 返回工作区域
     */
    public static WorkbookDesigner getWorkbookDesigner(ExcelParamVO paramVO) {
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.getWorkbook().getWorksheets().clear();//清除工作区域
        boolean isAbs = (paramVO.getTemplatePath() != null && !"".equals(paramVO.getTemplatePath()));
        String templatePath = isAbs ? paramVO.getTemplatePath() : paramVO.getRelativeTemplatePath();
        getWorkbookDesigner(isAbs, templatePath, designer);
        Style style = designer.getWorkbook().getDefaultStyle();
        //文字字体
        style.getFont().setName("宋体");
        //文字大小
        style.getFont().setSize(12);
        //单元格解锁
        style.setLocked(false);
        //单元格自动换行
        style.setTextWrapped(false);
        //应用边界线 左边界线
        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        //应用边界线 右边界线
        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
        //应用边界线 上边界线
        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        //应用边界线 下边界线
        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        return designer;
    }

}
