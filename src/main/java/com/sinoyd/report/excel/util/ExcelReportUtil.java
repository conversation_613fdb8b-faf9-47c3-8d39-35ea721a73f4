package com.sinoyd.report.excel.util;

import com.sinoyd.boot.common.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * excel类报表工具类
 *
 * <AUTHOR>
 * @version V5.2.1
 * @since 2024/06/14
 */
public class ExcelReportUtil {

    /**
     * 获取数据区数据
     *
     * @param dsMap   报表数据集
     * @param secName 数据区域名称
     * @return add区数据列表
     */
    public static Map<String, List<Map<String, Object>>> getAreaTypeData(Map<String, List<Map<String, Object>>> dsMap, String secName) {
        Map<String, List<Map<String, Object>>> secResult = new HashMap<>();
        dsMap.forEach((k, v) -> {
            if (k.startsWith(secName)) {
                secResult.put(k, v);
            }
        });
        return secResult;
    }

    /**
     * 获取单元格行列索引
     *
     * @param position 单元格位置
     * @return 单元格行列索引
     */
    public static int[] getCellRowCol(String position) {
        char[] arr = position.toCharArray();
        List<Character> colList = new ArrayList<>();
         StringBuilder rowBuilder = new StringBuilder();
        for (char c : arr) {
            if (c >= 'A' && c <= 'Z') {
                colList.add(c);
            } else {
                rowBuilder.append(c);
            }
        }
        int weight = 1, col = 0;
        for (int i = 0; i < colList.size(); i++) {
            col += ((colList.get(colList.size() - 1 - i) - 'A') + 1) * weight;
            weight *= 26;
        }
        return new int[]{Integer.parseInt(rowBuilder.toString()) - 1, col -1};
    }

    /**
     * 从行数据中获取integer值
     *
     * @param rowMap     行数据映射
     * @param key        key值
     * @param defaultVal 默认值
     * @return int类型值
     */
    public static Integer getRowMapIntVal(Map<String, Object> rowMap, String key, Integer defaultVal) {
        return (Integer) rowMap.getOrDefault(key, -1);
    }

    /**
     * 从行数据中获取字符串值
     *
     * @param rowMap     行数据映射
     * @param key        key值
     * @param defaultVal 默认值
     * @return 字符串类型值
     */
    public static String getRowMapStringVal(Map<String, Object> rowMap, String key, String defaultVal) {
        return rowMap.getOrDefault(key, defaultVal).toString();
    }

    /**
     * 根据质控类型质控等级过滤行数据
     *
     * @param rowMapList 行数据列表
     * @param qcType     质控类型
     * @param qcGrade    质控等级
     * @return 过滤后的行数据
     */
    public static List<Map<String, Object>> filterMapList(List<Map<String, Object>> rowMapList, Integer qcType, Integer qcGrade, Integer category) {
        return rowMapList.stream().filter(p -> (StringUtils.isNull(qcType) || qcType.equals(getRowMapIntVal(p, "qcType", -1)))
                && (StringUtils.isNull(qcGrade) || qcGrade.equals(getRowMapIntVal(p, "qcGrade", -1))) && (StringUtils.isNull(category)
                || category.equals(getRowMapIntVal(p, "sampleCategory", -1)))).collect(Collectors.toList());
    }

    /**
     * 根据质控类型质控等级判断行数据对象是否满足条件
     *
     * @param rowMap  行数据
     * @param qcType  质控类型
     * @param qcGrade 质控等级
     * @return 是否满足条件
     */
    public static boolean checkRowMap(Map<String, Object> rowMap, Integer qcType, Integer qcGrade, Integer category) {
        return (StringUtils.isNull(qcType) || qcType.equals(getRowMapIntVal(rowMap, "qcType", -1)))
                && (StringUtils.isNull(qcGrade) || qcGrade.equals(getRowMapIntVal(rowMap, "qcGrade", -1))) && (StringUtils.isNull(category)
                || category.equals(getRowMapIntVal(rowMap, "sampleCategory", -1)));
    }

    /**
     * 根据给定的属性名创建并返回一个空白map
     *
     * @param fldList 属性名列表
     * @param kbFld   要设置为以下空白的属性名
     * @param kbName  空白标识名称 “以下空白”，“以右空白”等。。
     */
    public static Map<String, Object> createKbMap(List<String> fldList, String kbFld, String kbName) {
        Map<String, Object> kbMap = new HashMap<>();
        fldList.forEach(p -> kbMap.put(p, ""));
        if (StringUtils.isNotEmpty(kbFld)) {
            kbMap.put(kbFld, kbName);
        }
        return kbMap;
    }

    /**
     * 检查给定值是否为空，为空则返回默认值，不为空返回原值
     *
     * @param obj        原始值
     * @param defaultVal 默认值
     * @return 校验后的值
     */
    public static String checkValEmpty(Object obj, String defaultVal) {
        return (StringUtils.isNotNull(obj) && StringUtils.isNotEmpty(obj.toString())) ? obj.toString() : defaultVal;
    }
}
