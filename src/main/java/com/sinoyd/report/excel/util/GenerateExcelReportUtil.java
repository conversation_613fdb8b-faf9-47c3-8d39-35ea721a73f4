package com.sinoyd.report.excel.util;

import com.aspose.cells.*;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.excel.builder.ExcelCellValueStyleBuilder;
import com.sinoyd.report.excel.enums.EnumExcelAreaType;
import com.sinoyd.report.excel.enums.EnumExcelExpandType;
import com.sinoyd.report.excel.enums.EnumExcelSheetPageRules;
import com.sinoyd.report.excel.vo.*;
import com.sinoyd.report.utils.SciUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sinoyd.report.excel.constant.ExcelReportConstants.*;
import static com.sinoyd.report.excel.enums.EnumExcelSheetPageRules.independent;

/**
 * excel报表生成工具类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/11/09
 */
@Slf4j
public class GenerateExcelReportUtil {

    //数字正则
    private static final Pattern r0 = Pattern.compile("\\d");
    //排除数字正则
    private static final Pattern r1 = Pattern.compile("[^\\d]*");

    private static final Pattern colorReg = Pattern.compile("^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$");

    /**
     * 获取报表每个sheet页对应的区域类型
     *
     * @param excelReportParamVO 前端传参接收对象
     * @return 每个sheet页对应的区域类型
     */
    public static Map<String, Set<String>> getSheetName2AreaTypesMap(CommonExcelReportParamVO excelReportParamVO) {
        Map<String, Set<String>> sheet2AreaTypeMap = new HashMap<>();
        for (AreaConfigVO areaConfig : excelReportParamVO.getAreaConfigList()) {
            String sheetName = areaConfig.getSheetName();
            if (!sheet2AreaTypeMap.containsKey(sheetName)) {
                sheet2AreaTypeMap.put(sheetName, new HashSet<>());
            }
            sheet2AreaTypeMap.get(sheetName).add(areaConfig.getAreaType());
        }
        return sheet2AreaTypeMap;
    }

    /**
     * 获取每个sheet页配置的所有区域的最大页数的映射
     *
     * @param dsMap              报表数据源
     * @param excelReportParamVO 前端传参接收对象
     * @return 每个sheet页配置的所有区域的最大页数的映射
     */
    public static Map<String, Integer> getSheet2MaxPageCntMap(Map<String, List<Map<String, Object>>> dsMap, CommonExcelReportParamVO excelReportParamVO) {
        Map<String, Set<String>> sheetName2AreaTypesMap = getSheetName2AreaTypesMap(excelReportParamVO);
        Map<String, Integer> sheetName2MaxPageCntMap = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : sheetName2AreaTypesMap.entrySet()) {
            Set<String> areaTypeSet = sheetName2AreaTypesMap.get(entry.getKey());
            int count = getMaxPageCntForSheet(dsMap, areaTypeSet);
            sheetName2MaxPageCntMap.put(entry.getKey(), count);
        }
        return sheetName2MaxPageCntMap;
    }

    /**
     * 每个sheet页名称和其对应的行数的映射
     *
     * @param areaConfigList 区域配置列表
     * @return 每个sheet页名称和其对应的行数的映射
     */
    public static Map<String, Integer> getSheetName2RowCntMap(List<AreaConfigVO> areaConfigList) {
        Map<String, Integer> sheetName2RowCntMap = new HashMap<>();
        //过滤出sheet页配置项
        List<AreaConfigVO> sheetAreaConfigList = areaConfigList.stream().filter(p -> EnumExcelAreaType.Sheet.name().equals(p.getAreaType()))
                .collect(Collectors.toList());
        sheetAreaConfigList.forEach(p -> sheetName2RowCntMap.put(p.getSheetName(), p.getExpandPageSize()));
        return sheetName2RowCntMap;
    }


    /**
     * 替换掉正则表达式匹配到的字符串
     *
     * @param rangeValue 范围值
     * @param pattern    正则表达式
     * @return 返回数据
     */
    public static String getRangRegexValue(String rangeValue, Pattern pattern) {
        String value = rangeValue;
        Matcher m = pattern.matcher(rangeValue);
        while (m.find()) {
            String matchWord = m.group(0);
            value = value.replace(matchWord, "");
        }
        return value;
    }

    /**
     * 获取各个sheet页配置的行数和列数的映射
     *
     * @param sheetConfigList sheet页配置列表
     * @return 各个sheet页配置的行数和列数的映射
     */
    public static Map<String, int[]> getSheetName2PageRowColCntMap(List<SheetConfigVO> sheetConfigList) {
        Map<String, int[]> sheetName2PageRowColumnCntMap = new HashMap<>();
        for (SheetConfigVO sheetConfigVO : sheetConfigList) {
            sheetName2PageRowColumnCntMap.put(sheetConfigVO.getSheetName(), new int[]{sheetConfigVO.getSheetRows(), sheetConfigVO.getSheetColumns()});
        }
        return sheetName2PageRowColumnCntMap;
    }

    /**
     * 设置单元格字体颜色,及格式化为科学计数法
     *
     * @param cell     单元格对象
     * @param value    单元格值
     * @param isFormat 是否转换科学计数法
     */
    public static void setHtmlValue(Cell cell, String value, boolean isFormat) {
        ExcelCellValueStyleBuilder builder = ExcelCellValueStyleBuilder.init().setStyleVal(value, isFormat);
        String htmlVal = builder.getHtmlVal();
//        String ht = "<span style=\"font-family: Times New Roman;\">WQ240320001</span>";
//        String ht = "<span style=\"font-family: 宋体;\">WQ240320001</span>";
        cell.setHtmlString(htmlVal);
    }

    /**
     * 校验并转换科学计数法
     *
     * @param value    原始值
     * @param isFormat 是否需要转换
     * @return 转换后的值
     */
    public static String checkAndFormatSci(String value, boolean isFormat) {
        return isFormat ? SciUtil.formatSci(value) : value;
    }

    /**
     * 获取sheet页配置的所有区域的最大页数
     *
     * @param dsMap       报表数据源
     * @param areaTypeSet 区域配置列表
     * @return 每个sheet页配置的所有区域的最大页数
     */
    public static int getMaxPageCntForSheet(Map<String, List<Map<String, Object>>> dsMap, Set<String> areaTypeSet) {
        int count = 0;
        for (String areaType : areaTypeSet) {
            if (!EnumExcelAreaType.Add.name().equals(areaType) && !EnumExcelAreaType.Page.name().equals(areaType) && !EnumExcelAreaType.PageReveal.name().equals(areaType)) {
                int pValue = (int) dsMap.keySet().parallelStream().filter(p -> p.startsWith(areaType)).count();
                count = Math.max(count, pValue);
            }
        }
        return count;
    }

    /**
     * 按指定的页数拷贝sheet页的指定区域，并设置行高
     *
     * @param worksheet     报表sheet页对象
     * @param i             页数索引
     * @param pageRowCnt    每页行数
     * @param pageColumnCnt 每页列数
     */
    public static void copySheetRange(Worksheet worksheet, int i, int pageRowCnt, int pageColumnCnt) {
        Cells cells = worksheet.getCells();
        Range rangeCopy = cells.createRange(0, 0, pageRowCnt, pageColumnCnt);
        Range range = cells.createRange(i * pageRowCnt, 0, pageRowCnt, pageColumnCnt);
        try {
            range.copy(rangeCopy);
        } catch (Exception e) {
            log.error("报表Sheet页复制区域出错，SheetName:" + worksheet.getName() + " 行数：" + pageRowCnt + " 列数：" + pageColumnCnt + " 页数：" + i, e);
            throw new RuntimeException("报表Sheet页复制区域出错，SheetName:" + worksheet.getName() + " 行数：" + pageRowCnt + " 列数：" + pageColumnCnt + " 页数：" + i);
        }
        for (int j = 0; j < pageRowCnt; j++) {
            cells.setRowHeight(i * pageRowCnt + j, cells.getRows().get(j).getHeight());
        }
    }

    /**
     * 进行字体缩写
     *
     * @param designer     单元格对象
     * @param areaConfig   区域配置
     * @param pageNum      页码
     * @param pageRowCount 行数
     */
    public static void resizeArea(WorkbookDesigner designer, AreaConfigVO areaConfig, int pageNum, int pageRowCount) {
        Worksheet worksheet = designer.getWorkbook().getWorksheets().get(areaConfig.getSheetName());
        Cells cells = worksheet.getCells();
        Range range = cells.createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
        for (int p = 1; p <= pageNum; p++) {
            for (int r = range.getFirstRow() + pageRowCount * (p - 1); r < range.getFirstRow() + range.getRowCount() + pageRowCount * (p - 1); r++) {
                for (int c = range.getFirstColumn(); c < range.getFirstColumn() + range.getColumnCount(); c++) {
                    Cell cell = worksheet.getCells().get(r, c);
                    if (cell != null) {
                        byte[] byteArray = cell.getStringValue().getBytes();
                        //#region 单元格长宽
                        BigDecimal width = BigDecimal.ZERO, height = BigDecimal.ZERO;
                        Range mergedRange = cell.getMergedRange();
                        if (mergedRange != null) {
                            for (int mc = mergedRange.getFirstColumn(); mc < mergedRange.getFirstColumn() + mergedRange.getColumnCount(); mc++) {
                                width = width.add(new BigDecimal(String.valueOf(cells.getColumns().get(mc).getWidth())));
                            }
                            for (int mr = mergedRange.getFirstRow(); mr < mergedRange.getFirstRow() + mergedRange.getRowCount(); mr++) {
                                height = height.add(new BigDecimal(String.valueOf(cells.getRows().get(mr).getHeight())));
                            }
                        } else {
                            width = new BigDecimal(String.valueOf(cells.getColumns().get(c).getWidth()));
                            height = new BigDecimal(String.valueOf(cells.getRows().get(r).getHeight()));
                        }
                        int maxFontSize = (areaConfig.getMaxFontSize() == null || areaConfig.getMaxFontSize() <= 0) ? MAX_FONT_SIZE : areaConfig.getMaxFontSize();
                        int reFontSize = resizeFont(width, height, maxFontSize, byteArray.length);
                        if (reFontSize > 0) {
                            resetCellFontSize(cell, reFontSize);
                        }
                        if (mergedRange != null) {
                            c += mergedRange.getFirstColumn() + mergedRange.getColumnCount() - c - 1;
                        }
                    }
                }
            }
        }
    }

    /**
     * 重新设置单元格字体大小
     *
     * @param cell     单元格对象
     * @param fontSize 字体大小
     */
    public static void resetCellFontSize(Cell cell, int fontSize) {
        Style style = cell.getStyle();
        style.getFont().setSize(fontSize);
        cell.setStyle(style);
    }

    /**
     * 重新字体大写
     *
     * @param width       宽度
     * @param height      高度
     * @param fontSizeMax 最大字体
     * @param byteLength  字节长度
     * @return 返回字体大小
     */
    public static int resizeFont(BigDecimal width, BigDecimal height, int fontSizeMax, int byteLength) {
        log.info("开始字体缩放，width：" + width + " height：" + height + "fontSizeMax：" + fontSizeMax);
        //相关的高度
        double[] heightArray = {14.25, 13.5, 12, 11.25, 10.5, 9.75, 9, 8.25, 6.75, 6};
        //相关的宽度
        double[] widthArray = {0.93, 0.9, 0.75, 0.66, 0.65, 0.48, 0.42, 0.33, 0.28, 0.22};
        int newFontSize = fontSizeMax;
        int i;
        try {
            for (i = fontSizeMax; i >= 3; i--) {
                //该高度可以容纳的行数
                BigDecimal rowCount = height.divide(new BigDecimal(String.valueOf(heightArray[12 - i])), 10, RoundingMode.FLOOR);
                log.info("rowCount：" + rowCount.toPlainString());
                BigDecimal colCount = width.divide(new BigDecimal(String.valueOf(widthArray[12 - i])), 10, RoundingMode.FLOOR);
                log.info("colCount：" + colCount.stripTrailingZeros().toPlainString());
                BigDecimal cnt = new BigDecimal(String.valueOf(byteLength)).divide(rowCount, 10, RoundingMode.FLOOR);
                log.info("cnt：" + cnt.stripTrailingZeros().toPlainString());
                //比较每行字符数
                if (cnt.compareTo(colCount) <= 0) {
                    newFontSize = i;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("计算缩小后的字体出错 width：" + width + " height：" + height + "fontSizeMax：" + fontSizeMax, e);
            throw new RuntimeException("计算缩小后的字体出错 width：" + width + " height：" + height + "fontSizeMax：" + fontSizeMax);
        }

        return i != 2 ? newFontSize : 3;
    }

    /**
     * 保存生成的报表文件
     *
     * @param excelReportParamVO 前端传参
     */
    public static String saveReportFile(WorkbookDesigner designer, CommonExcelReportParamVO excelReportParamVO) {
        String outPutPath = excelReportParamVO.getOutPutPath(), outPutName = excelReportParamVO.getOutPutName();
        String fileName = outPutPath + "/" + outPutName;
        log.info("开始保存报表文件，报表输出路径：" + outPutPath + " 输出报表文件名：" + outPutName + " 报表文件路径：" + fileName);
        File outputFile = new File(outPutPath);
        if (!outputFile.exists()) {
            boolean dirFlag = outputFile.mkdirs();
            if (!dirFlag) {
                log.error("创建文件失败，文件路径：" + outPutPath);
                throw new RuntimeException("创建文件失败！");
            }
        }
        try {
            designer.getWorkbook().save(fileName);
        } catch (Exception e) {
            log.error("保存生成的报表文件失败！报表文件路径：" + fileName, e);
            throw new RuntimeException("保存生成的报表文件失败！报表文件路径：" + fileName, e);
        }
        return fileName;
    }

    /**
     * 合并区域，只有当区域全部加装完毕之后对需要的区域进行合并处理
     *
     * @param workbook       工作单
     * @param dataMap        数据集合
     * @param areaConfigList 区域配置对象列表
     */
    public static void merge(Workbook workbook, Map<String, List<Map<String, Object>>> dataMap, List<AreaConfigVO> areaConfigList) {
        Set<String> dataMapKeySet = dataMap.keySet();
        for (AreaConfigVO areaConfig : areaConfigList) {
            List<String> mergeKeysForArea = dataMapKeySet.stream().filter(p -> p.contains(areaConfig.getId() + EnumExcelAreaType.Merge.name())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(mergeKeysForArea)) {
                for (String mergeKey : mergeKeysForArea) {
                    List<Map<String, Object>> mergeMapList = dataMap.get(mergeKey);
                    Cells cells = workbook.getWorksheets().get(areaConfig.getSheetName()).getCells();
                    for (Map<String, Object> map : mergeMapList) {
                        int startRow = (Integer) map.getOrDefault("startRow", 0), startColumn = (Integer) map.getOrDefault("startColumn", 0),
                                totalRows = (Integer) map.getOrDefault("totalRows", 0), totalColumns = (Integer) map.getOrDefault("totalColumns", 0);
                        log.info("开始合并区域：startRow：" + startRow + " startColumn：" + startColumn + " totalRows：" + totalRows + " totalColumns：" + totalColumns);
                        cells.merge(startRow, startColumn, totalRows, totalColumns);
                    }
                }
            }
        }
    }

    /**
     * 报表以100%的缩放模式打开设置打印区域
     *
     * @param designer                Designer对象
     * @param sheetName2MaxPageCntMap sheet页配置的所有区域的最大页数的映射
     * @param dsMap                   报表数据源
     */
    public static void setPrintArea(WorkbookDesigner designer, Map<String, Integer> sheetName2MaxPageCntMap, Map<String, List<Map<String, Object>>> dsMap) {
        try {
            for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
                Worksheet worksheet = designer.getWorkbook().getWorksheets().get(i);
                worksheet.setZoom(100);
                List<Map<String, Object>> pageConfigs = dsMap.get(EnumExcelAreaType.Page.name() + "1");
                if (StringUtils.isNotEmpty(pageConfigs)) {
                    int pageNum = sheetName2MaxPageCntMap.getOrDefault(worksheet.getName(), 1);
                    if (worksheet.getPageSetup().getPrintArea() != null) {
                        String areaEnd = worksheet.getPageSetup().getPrintArea().split(":")[1];
                        String rangValue0 = GenerateExcelReportUtil.getRangRegexValue(areaEnd, r0);
                        String rangValue1 = GenerateExcelReportUtil.getRangRegexValue(areaEnd, r1);
                        areaEnd = rangValue0 + Integer.parseInt(rangValue1) * pageNum;
                        worksheet.getPageSetup().setPrintArea(worksheet.getPageSetup().getPrintArea().split(":")[0] + ":" + areaEnd);
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置报表打印区域失败！", e);
            throw new RuntimeException("设置报表打印区域失败！");
        }
    }

    /**
     * 生成报表不分页
     *
     * @param excelReportParamVO      前端传参接收对象
     * @param sheetName2MaxPageCntMap 获取每个sheet页配置的所有区域的最大页数的映射
     * @param designer                Design对象
     * @param dsMap                   报表数据源
     */
    public static void generateBySinglePage(CommonExcelReportParamVO excelReportParamVO, Map<String, Integer> sheetName2MaxPageCntMap,
                                            WorkbookDesigner designer, Map<String, List<Map<String, Object>>> dsMap) {
        reConfigPage(excelReportParamVO, sheetName2MaxPageCntMap, false, dsMap);
        List<AreaConfigVO> extraAreaConfigs = new ArrayList<>();
        List<AreaConfigVO> areaConfigs = excelReportParamVO.getAreaConfigList();
        boolean isFormat = StringUtils.isNotNull(excelReportParamVO.getFormatSci()) && excelReportParamVO.getFormatSci();
        for (AreaConfigVO areaConfig : areaConfigs) {
            if (EnumExcelAreaType.Data.name().equals(areaConfig.getAreaType())) {
                String dataKey = areaConfig.getAreaType() + areaConfig.getItemIndex();
                Cells thisCells = designer.getWorkbook().getWorksheets().get(areaConfig.getSheetName()).getCells();
                Range thisRange = thisCells.createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
                if (EnumExcelExpandType.行拓展.getValue().equals(areaConfig.getExpandType()) && dsMap.containsKey(dataKey)
                        && dsMap.get(dataKey).size() > thisRange.getRowCount()) {
                    extraAreaConfigs.add(areaConfig);
                } else {
                    loadArea(designer.getWorkbook(), dsMap, areaConfig, excelReportParamVO, isFormat);
                }
            } else {
                loadArea(designer.getWorkbook(), dsMap, areaConfig, excelReportParamVO, isFormat);
            }
        }
        int[] addRowInfoArr = loadExtraArea(designer, extraAreaConfigs, excelReportParamVO, dsMap, isFormat);
        //字体缩放
        resizeFontForSinglePage(designer, excelReportParamVO, addRowInfoArr[0], addRowInfoArr[1]);
    }

    /**
     * 字体缩放（不分页）
     *
     * @param designer           Design对象
     * @param excelReportParamVO 前端传参接收对象
     * @param startRow           超出配置区域的起始行
     * @param rows               超出配置区域的行数
     */
    private static void resizeFontForSinglePage(WorkbookDesigner designer, CommonExcelReportParamVO excelReportParamVO, int startRow, int rows) {
        List<AreaConfigVO> fontConfigList = excelReportParamVO.getAreaConfigList().stream().filter(p -> !EnumExcelAreaType.Sheet.name().equals(p.getAreaType())
                && !EnumExcelAreaType.PageReveal.name().equals(p.getAreaType()) && StringUtils.isNotNull(p.getMaxFontSize())).collect(Collectors.toList());
        for (AreaConfigVO areaConfig : fontConfigList) {
            Worksheet worksheet = designer.getWorkbook().getWorksheets().get(areaConfig.getSheetName());
            Cells cells = worksheet.getCells();
            Range range = cells.createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
            if (rows != 0 && startRow >= range.getFirstRow() && startRow <= range.getFirstRow() + range.getRowCount()) {
                String rangValue0 = GenerateExcelReportUtil.getRangRegexValue(areaConfig.getAreaEnd(), r0);
                String rangValue1 = GenerateExcelReportUtil.getRangRegexValue(areaConfig.getAreaEnd(), r1);
                areaConfig.setAreaStart(rangValue0 + (Integer.parseInt(rangValue1) + rows));
                range = cells.createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
            }
            for (int r = range.getFirstRow(); r < range.getFirstRow() + range.getRowCount(); r++) {
                for (int c = range.getFirstColumn(); c < range.getFirstColumn() + range.getColumnCount(); c++) {
                    Cell cell = cells.get(r, c);
                    if (cell != null) {
                        BigDecimal height = BigDecimal.ZERO, width = BigDecimal.ZERO;
                        byte[] byteArray = cell.getStringValue().getBytes();
                        Range mergedRange = cell.getMergedRange();
                        if (mergedRange != null) {
                            for (int mr = mergedRange.getFirstRow(); mr < mergedRange.getFirstRow() + mergedRange.getRowCount(); mr++) {
                                height = height.add(new BigDecimal(String.valueOf(cells.getRows().get(mr).getHeight())));
                            }
                            for (int mc = mergedRange.getFirstColumn(); mc < mergedRange.getFirstColumn() + mergedRange.getColumnCount(); mc++) {
                                width = width.add(new BigDecimal(String.valueOf(cells.getColumns().get(mc).getWidth())));
                            }
                        } else {
                            width = new BigDecimal(String.valueOf(cells.getColumns().get(c).getWidth()));
                            height = new BigDecimal(String.valueOf(cells.getRows().get(r).getHeight()));
                        }
                        int reFontSize = GenerateExcelReportUtil.resizeFont(width, height, areaConfig.getMaxFontSize(), byteArray.length);
                        Style style = cell.getStyle();
                        style.getFont().setSize(reFontSize);
                        cell.setStyle(style);
                        if (mergedRange != null) {
                            c += mergedRange.getFirstColumn() + mergedRange.getColumnCount() - c - 1;
                        }
                    }
                }
            }
        }
    }

    /**
     * 数据超出配置区域的赋值
     *
     * @param designer           Design对象
     * @param extraAreaConfigs   超出区域配置列表
     * @param excelReportParamVO 前端传参接收对象
     * @param dsMap              报表数据源
     * @param isFormat           是否转换科学计数法
     * @return 超出区域配置的行数量及开始行索引
     */
    public static int[] loadExtraArea(WorkbookDesigner designer, List<AreaConfigVO> extraAreaConfigs, CommonExcelReportParamVO excelReportParamVO,
                                      Map<String, List<Map<String, Object>>> dsMap, boolean isFormat) {
        int startRow = 0, rows = 0, dataCount = 0;
        try {
            for (AreaConfigVO areaConfig : extraAreaConfigs) {
                String dataKey = areaConfig.getAreaType() + areaConfig.getItemIndex();
                Cells thisCells = designer.getWorkbook().getWorksheets().get(areaConfig.getSheetName()).getCells();
                Range thisRange = thisCells.createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
                if (EnumExcelExpandType.行拓展.getValue().equals(areaConfig.getExpandType())) {
                    startRow = thisRange.getFirstRow() + 1;
                    rows = dsMap.get(dataKey).size() - thisRange.getRowCount();
                    dataCount = rows;
                    thisCells.insertRows(thisRange.getFirstRow() + 1, dataCount);
                    for (int i = 1; i <= dataCount; i++) {
                        thisCells.copyRows(thisCells, thisRange.getFirstRow(), thisRange.getFirstRow() + i, Integer.parseInt(areaConfig.getExpandAreaSize()));
                    }
                    String rangValue0 = GenerateExcelReportUtil.getRangRegexValue(areaConfig.getAreaEnd(), r0);
                    String rangValue1 = GenerateExcelReportUtil.getRangRegexValue(areaConfig.getAreaEnd(), r1);
                    areaConfig.setAreaEnd(rangValue0 + (Integer.parseInt(rangValue1) + rows));
                }
                loadArea(designer.getWorkbook(), dsMap, areaConfig, excelReportParamVO, isFormat);
            }
        } catch (Exception e) {
            log.error("数据超出配置区域的赋值出错，startRow：" + startRow + "  dataCount：" + dataCount, e);
            throw new RuntimeException("数据超出配置区域的赋值出错，startRow：" + startRow + "  dataCount：" + dataCount);
        }
        return new int[]{startRow, rows};
    }

    /**
     * 按照分页生成报表
     *
     * @param excelReportParamVO      前端传参接收对象
     * @param sheetName2MaxPageCntMap 获取每个sheet页配置的所有区域的最大页数的映射
     * @param designer                Design对象
     * @param dsMap                   报表数据源
     */
    public static void generateByPage(CommonExcelReportParamVO excelReportParamVO, Map<String, Integer> sheetName2MaxPageCntMap,
                                      WorkbookDesigner designer, Map<String, List<Map<String, Object>>> dsMap) {
        //获取报表总页数
        int pageCnt = getPageCnt(dsMap, sheetName2MaxPageCntMap);
        log.info("分页生成的报表页数：" + pageCnt);
        //设置页数和页码
        reConfigPage(excelReportParamVO, sheetName2MaxPageCntMap, true, dsMap);
        List<AreaConfigVO> areaConfigList = excelReportParamVO.getAreaConfigList();
        Map<String, Integer> sheetName2RowCntMap = GenerateExcelReportUtil.getSheetName2RowCntMap(areaConfigList);
        Map<String, int[]> sheetName2PageRowColCntMap = GenerateExcelReportUtil.getSheetName2PageRowColCntMap(excelReportParamVO.getSheetConfigList());
        WorksheetCollection worksheetCollection = designer.getWorkbook().getWorksheets();
        boolean isFormat = StringUtils.isNotNull(excelReportParamVO.getFormatSci()) && excelReportParamVO.getFormatSci();
        //给配置区域赋值-分页
        if (pageCnt != 1) {
            List<AreaConfigVO> newConfigList = addNewAreaConfigList(worksheetCollection, areaConfigList, sheetName2MaxPageCntMap, sheetName2PageRowColCntMap, pageCnt);
            newConfigList.forEach(p -> loadArea(designer.getWorkbook(), dsMap, p, excelReportParamVO, isFormat));
        } else {
            areaConfigList.forEach(p -> loadArea(designer.getWorkbook(), dsMap, p, excelReportParamVO, isFormat));
        }
        //字体缩小
        resizeFontForAreas(designer, excelReportParamVO, sheetName2MaxPageCntMap, sheetName2RowCntMap, pageCnt);
    }

    /**
     * 对配置字体缩放的区域进行所犯字体
     *
     * @param designer                aspose Design对象
     * @param excelReportParamVO      前端传参接收对象
     * @param sheetName2MaxPageCntMap 每个sheet页配置的所有区域的最大页数的映射
     * @param sheetName2RowCntMap     每个sheet页名称和其对应的行数的映射
     * @param pageCnt                 报表页数
     */
    public static void resizeFontForAreas(WorkbookDesigner designer, CommonExcelReportParamVO excelReportParamVO, Map<String, Integer> sheetName2MaxPageCntMap,
                                          Map<String, Integer> sheetName2RowCntMap, int pageCnt) {
        int globalRowCnt = excelReportParamVO.getGlobalConfig().getPageRows();
        List<AreaConfigVO> fontConfigList = excelReportParamVO.getAreaConfigList().stream().filter(p -> !EnumExcelAreaType.Sheet.name().equals(p.getAreaType())
                && !EnumExcelAreaType.PageReveal.name().equals(p.getAreaType()) && StringUtils.isNotNull(p.getMaxFontSize())).collect(Collectors.toList());
        Map<String, List<AreaConfigVO>> sheetName2AreaConfigListMap = getSheetName2AreaConfigMap(fontConfigList);
        //遍历每一个sheet的字体缩小配置进行缩小字体操作
        for (Map.Entry<String, List<AreaConfigVO>> entry : sheetName2AreaConfigListMap.entrySet()) {
            String sheetName = entry.getKey();
            List<AreaConfigVO> sheetFontConfigList = sheetName2AreaConfigListMap.getOrDefault(sheetName, new ArrayList<>());
            //获取每个sheet页的行数和总页数
            int sheetPageCnt = sheetName2MaxPageCntMap.getOrDefault(sheetName, pageCnt), sheetRowCnt = sheetName2RowCntMap.getOrDefault(sheetName, globalRowCnt);
            for (AreaConfigVO sheetFontConfig : sheetFontConfigList) {
                pageCnt = pageCnt == 0 ? 1 : pageCnt;
                GenerateExcelReportUtil.resizeArea(designer, sheetFontConfig, sheetPageCnt, sheetRowCnt);
            }
        }
    }

    /**
     * 按照每个worksheet页名称对需要调整字体的区域进行分组
     *
     * @param fontConfigList 字体缩放区域配置
     * @return worksheet页名称与调整字体的区域的映射
     */
    public static Map<String, List<AreaConfigVO>> getSheetName2AreaConfigMap(List<AreaConfigVO> fontConfigList) {
        Map<String, List<AreaConfigVO>> sheetName2AreaConfigListMap = new HashMap<>();
        for (AreaConfigVO fontConfig : fontConfigList) {
            String sheetName = fontConfig.getSheetName();
            if (StringUtils.isEmpty(sheetName)) {
                throw new BaseException("字体缩小未配置sheet页名称! sheet页名称：" + sheetName + " 开始位置：" + fontConfig.getAreaStart() + " 结束位置："
                        + fontConfig.getAreaEnd());
            }
            if (!sheetName2AreaConfigListMap.containsKey(sheetName)) {
                sheetName2AreaConfigListMap.put(sheetName, new ArrayList<>());
            }
            sheetName2AreaConfigListMap.get(sheetName).add(fontConfig);
        }
        return sheetName2AreaConfigListMap;
    }


    /**
     * 根据最终生成的报表的页数，补充每一页的区域配置（报表页数大于1时才需要进行补充）
     *
     * @param worksheetCollection        报表sheet页对象集合
     * @param areaConfigList             原有的区域配置列表
     * @param sheetName2MaxPageCntMap    每个sheet页配置的所有区域的最大页数的映射
     * @param sheetName2PageRowColCntMap 各个sheet页配置的行数和列数的映射
     * @param pageCnt                    最终生成的报表的页数
     * @return 补充完整的区域配置列表
     */
    public static List<AreaConfigVO> addNewAreaConfigList(WorksheetCollection worksheetCollection, List<AreaConfigVO> areaConfigList, Map<String, Integer> sheetName2MaxPageCntMap,
                                                          Map<String, int[]> sheetName2PageRowColCntMap, int pageCnt) {
        List<AreaConfigVO> newConfigList = new ArrayList<>(areaConfigList);
        for (int i = 1; i < pageCnt; i++) {
            for (AreaConfigVO areaConfig : areaConfigList) {
                //获取分页的行数和页数
                int[] rowColumnCntArr = sheetName2PageRowColCntMap.get(areaConfig.getSheetName());
                int sheetMaxPageCnt = sheetName2MaxPageCntMap.getOrDefault(areaConfig.getSheetName(), 1);
                if (i < sheetMaxPageCnt) {
                    String rangValue0 = getRangRegexValue(areaConfig.getAreaStart(), r0);
                    String rangValue1 = getRangRegexValue(areaConfig.getAreaStart(), r1);
                    String rangValue2 = getRangRegexValue(areaConfig.getAreaEnd(), r0);
                    String rangValue3 = getRangRegexValue(areaConfig.getAreaEnd(), r1);
                    AreaConfigVO newConfig = initAreaConfigVO(areaConfig, rowColumnCntArr[0], i, rangValue0, rangValue1, rangValue2, rangValue3);
                    newConfigList.add(newConfig);
                }
            }
            //复制模板每个sheet页的内容
            for (int k = 0; k < worksheetCollection.getCount(); k++) {
                Worksheet worksheet = worksheetCollection.get(k);
                int[] rowColCntArr = sheetName2PageRowColCntMap.getOrDefault(worksheet.getName(), new int[2]);
                int pageRowCnt = rowColCntArr[0], pageColumnCnt = rowColCntArr[1];
                int numCount = sheetName2MaxPageCntMap.getOrDefault(worksheet.getName(), 0);
                if (i < numCount) {
                    GenerateExcelReportUtil.copySheetRange(worksheet, i, pageRowCnt, pageColumnCnt);
                }
            }
        }
        return newConfigList;
    }

    /**
     * 初始化一个新的区域配置对象
     *
     * @param areaConfig  区域配置对象
     * @param sheetRowCnt sheet页的行数
     * @param i           索引
     * @param rangValue0  起始位置0
     * @param rangValue1  起始位置1
     * @param rangValue2  结束位置0
     * @param rangValue3  结束位置1
     */
    public static AreaConfigVO initAreaConfigVO(AreaConfigVO areaConfig, int sheetRowCnt, int i, String rangValue0,
                                                String rangValue1, String rangValue2, String rangValue3) {
        AreaConfigVO newAreaConfig = new AreaConfigVO();
        BeanUtils.copyProperties(areaConfig, newAreaConfig, "id", "areaStart", "areaEnd");
        newAreaConfig.setItemIndex(!EnumExcelAreaType.Info.name().equals(areaConfig.getAreaType()) ? areaConfig.getItemIndex() + i : areaConfig.getItemIndex());
        newAreaConfig.setExpandPageSize(areaConfig.getExpandPageSize());
        newAreaConfig.setAreaType(areaConfig.getAreaType());
        newAreaConfig.setExpandType(areaConfig.getExpandType());
        newAreaConfig.setSheetName(areaConfig.getSheetName());
        newAreaConfig.setExpandAreaSize(areaConfig.getExpandAreaSize());
        newAreaConfig.setAreaStart(rangValue0 + (Integer.parseInt(rangValue1) + i * sheetRowCnt));
        newAreaConfig.setAreaEnd(rangValue2 + (Integer.parseInt(rangValue3) + i * sheetRowCnt));
        return newAreaConfig;
    }

    /**
     * 按照区域配置及报表数据源映射，对报表单元格进行赋值
     *
     * @param workbook   aspose WorkBook对象
     * @param dsMap      报表数据源
     * @param areaConfig 区域配置信息
     * @param isFormat   是否转换科学计数法
     */
    public static void loadArea(Workbook workbook, Map<String, List<Map<String, Object>>> dsMap, AreaConfigVO areaConfig, CommonExcelReportParamVO excelReportParamVO, boolean isFormat) {
        int itemIdx = areaConfig.getItemIndex().equals(0) ? areaConfig.getItemIndex() + 1 : areaConfig.getItemIndex();
        String dataKey = areaConfig.getAreaType() + itemIdx;
        if (EnumExcelAreaType.PageReveal.name().equals(areaConfig.getAreaType())) {
            dataKey = areaConfig.getAreaType() + areaConfig.getSheetName() + itemIdx;
        }
        if (dsMap.containsKey(dataKey) && StringUtils.isNotEmpty(dsMap.get(dataKey))) {
            List<Map<String, Object>> mapList = dsMap.get(dataKey);
            Worksheet worksheet = workbook.getWorksheets().get(areaConfig.getSheetName());
            Range range = worksheet.getCells().createRange(areaConfig.getAreaStart(), areaConfig.getAreaEnd());
            if (EnumExcelExpandType.列拓展.getValue().equals(areaConfig.getExpandType())) {
                loadByColumn(worksheet, mapList, range, excelReportParamVO, areaConfig, isFormat);
            } else if (EnumExcelExpandType.行拓展.getValue().equals(areaConfig.getExpandType())) {
                loadByRow(worksheet, mapList, range, excelReportParamVO, areaConfig, isFormat);
            } else if (EnumExcelExpandType.不拓展.getValue().equals(areaConfig.getExpandType())) {
                loadNoExtend(worksheet, mapList, range, excelReportParamVO, areaConfig, isFormat);
            }
        }
    }

    /**
     * 不扩展赋值
     *
     * @param worksheet          Worksheet对象
     * @param mapList            数据源
     * @param range              区域对象
     * @param excelReportParamVO 前端传参接收对象
     * @param isFormat           是否转换科学计数法
     */
    public static void loadNoExtend(Worksheet worksheet, List<Map<String, Object>> mapList, Range range, CommonExcelReportParamVO excelReportParamVO,
                                    AreaConfigVO areaConfig, boolean isFormat) {
        Map<String, BufferedImage> imageMap = excelReportParamVO.getAreaConfig2ImageMap();
        AreaExpandConfigVO areaExpandConfigVO = areaConfig.getAreaExpandConfigVO();
        int picLeft = StringUtils.isNotNull(areaExpandConfigVO) ? areaExpandConfigVO.getPicLeft() : 0,
                picTop = StringUtils.isNotNull(areaExpandConfigVO) ? areaExpandConfigVO.getPicTop() : 0,
                picWidth = StringUtils.isNotNull(areaExpandConfigVO) ? areaExpandConfigVO.getQrCodeLength() : 0,
                picHeight = StringUtils.isNotNull(areaExpandConfigVO) ? areaExpandConfigVO.getQrCodeWidth() : 0;
        BufferedImage image = imageMap.get(areaConfig.getId());
        GlobalConfigVO globalConfigVO = excelReportParamVO.getGlobalConfig();
        Integer fontSize = StringUtils.isNotNull(areaConfig.getFontSize()) && areaConfig.getFontSize() > 0 ? areaConfig.getFontSize() : globalConfigVO.getFontsize();
        String fontFamily = StringUtils.isNotEmpty(areaConfig.getFontFamily()) ? areaConfig.getFontFamily() : globalConfigVO.getFontFamily();
        String val = "", cellStr = "";
        try {
            for (int r = range.getFirstRow(); r < range.getFirstRow() + range.getRowCount(); r++) {
                for (int c = range.getFirstColumn(); c < range.getFirstColumn() + range.getColumnCount(); c++) {
                    Cell cell = worksheet.getCells().get(r, c);
                    cellStr = cell.getStringValue();
                    Map<String, Object> map = mapList.get(0);
                    if (cell.getStringValue().equals("sampleQRCode")) {
                        try {
                            if (image == null) {
                                cell.setHtmlString("");
                            } else {
//                                saveImage(excelReportParamVO.getOutPutPath(), image, fileName);
                                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                                ImageIO.write(image, "png", outputStream);
                                ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

                                // #region 增加条形码
                                int firstColumn = cell.getMergedRange().getFirstColumn(), rangeNum = firstColumn + cell.getMergedRange().getColumnCount(),
                                        index = worksheet.getPictures().add(r, firstColumn, r + 1, rangeNum, inputStream);
                                Picture pic = worksheet.getPictures().get(index);
                                pic.setLeft(picLeft);
                                pic.setTop(picTop);
                                pic.setWidth(picWidth);
                                pic.setHeight(picHeight);
                                pic.setPlacement(PlacementType.MOVE_AND_SIZE);
                                setHtmlValue(cell, "", isFormat);
                            }
                        } catch (Exception e) {
                            log.error("二维码数据绑定出错, cellStr:" + cellStr + " val：" + val, e);
                        }
                    } else {
                        //常规的数据绑定
                        if (EnumExcelAreaType.Info.name().equals(areaConfig.getAreaType())) {
                            if (cell.getStringValue().contains(PLACEHOLDER_PREFIX) && StringUtils.isNotNull(map.get(cell.getStringValue()))) {
                                val = map.get(cell.getStringValue()).toString();
                                setHtmlValWithStyle(cell, val, fontFamily, fontSize, isFormat);
                            }
                        } else {
                            if (map.containsKey(cell.getStringValue())) {
                                val = map.get(cell.getStringValue()).toString();
                                setHtmlValWithStyle(cell, val, fontFamily, fontSize, isFormat);
                            }
                        }
                    }
                    Range mergedRange = cell.getMergedRange();
                    if (mergedRange != null) {
                        c += mergedRange.getFirstColumn() + mergedRange.getColumnCount() - c - 1;
                    }
                }
            }
        } catch (Exception e) {
            log.error("常规的数据绑定出错, cellStr:" + cellStr + " val：" + val, e);
            throw new RuntimeException("常规的数据绑定出错, cellStr:" + cellStr + " val：" + val);
        }
    }

//    /**
//     * 保存图片文件
//     */
//    public static void saveImage(String outputDir, BufferedImage source, String fileName) {
//        try {
//            File file = new File(outputDir);
//            if (!file.exists()) {
//                file.mkdirs();
//            }
//            File a = new File((new StringBuilder()).insert(0, outputDir).append(HttpServiceUrl.ALLATORIxDEMO("M")).append(fileName).toString());
//            ImageIO.write(source, SSOLoginController.ALLATORIxDEMO("HK_"), a);
//        } catch (Exception var5) {
//            System.out.println(var5.getMessage());
//        }
//    }

    /**
     * 按行扩展赋值
     *
     * @param worksheet  Worksheet对象
     * @param mapList    数据源
     * @param range      区域对象
     * @param areaConfig 区域配置
     * @param isFormat   是否转换科学计数法
     */
    public static void loadByRow(Worksheet worksheet, List<Map<String, Object>> mapList, Range range, CommonExcelReportParamVO excelReportParamVO,
                                 AreaConfigVO areaConfig, boolean isFormat) {
        GlobalConfigVO globalConfigVO = excelReportParamVO.getGlobalConfig();
        int step = Integer.parseInt(areaConfig.getExpandAreaSize()), count = Math.min(mapList.size() * step, range.getRowCount());
        Integer fontSize = StringUtils.isNotNull(areaConfig.getFontSize()) && areaConfig.getFontSize() > 0 ? areaConfig.getFontSize() : globalConfigVO.getFontsize();
        String fontFamily = StringUtils.isNotEmpty(areaConfig.getFontFamily()) ? areaConfig.getFontFamily() : globalConfigVO.getFontFamily();
        String value = "";
        try {
            for (int c = range.getFirstColumn(); c < range.getFirstColumn() + range.getColumnCount(); c++) {
                for (int s = range.getFirstRow(); s < range.getFirstRow() + step; s++) {
                    String cellStr = worksheet.getCells().get(s, c).getStringValue();
                    if (StringUtils.isNotEmpty(cellStr)) {
                        int dataIndex = 0;
                        for (int r = s; r < s + count; r += step) {
                            Cell cell1 = worksheet.getCells().get(r, c);
                            if (dataIndex < mapList.size()) {
                                Map<String, Object> map = mapList.get(dataIndex);
                                value = StringUtils.isNotNull(map.get(cellStr)) ? map.get(cellStr).toString() : getDefaultValue();
                                String formula = cell1.getFormula();
                                if (StringUtils.isEmpty(formula)) {
                                    setHtmlValWithStyle(cell1, value, fontFamily, fontSize, isFormat);
                                }
                            }
                            dataIndex++;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("单元格数据绑定出错，value=" + value, e);
            throw new RuntimeException("单元格数据绑定出错，value=" + value);
        }
    }

    /**
     * 重新设置默认值
     *
     * @return 返回设置的值
     */
    public static String getDefaultValue() {
        return CELL_DEFAULT_VALUE;
    }

    /**
     * 按列扩展赋值
     *
     * @param worksheet  Worksheet对象
     * @param mapList    数据源
     * @param range      区域对象
     * @param areaConfig 区域配置
     * @param isFormat   是否转换科学计数法
     */
    public static void loadByColumn(Worksheet worksheet, List<Map<String, Object>> mapList, Range range, CommonExcelReportParamVO excelReportParamVO,
                                    AreaConfigVO areaConfig, boolean isFormat) {
        int step = Integer.parseInt(areaConfig.getExpandAreaSize());
        GlobalConfigVO globalConfigVO = excelReportParamVO.getGlobalConfig();
        Integer fontSize = StringUtils.isNotNull(areaConfig.getFontSize()) && areaConfig.getFontSize() > 0 ? areaConfig.getFontSize() : globalConfigVO.getFontsize();
        String fontFamily = StringUtils.isNotEmpty(areaConfig.getFontFamily()) ? areaConfig.getFontFamily() : globalConfigVO.getFontFamily();
        for (int r = range.getFirstRow(); r < range.getFirstRow() + range.getRowCount(); r++) {
            for (int s = range.getFirstColumn(); s < range.getFirstColumn() + step; s++) {
                Cell cell = worksheet.getCells().get(r, s);
                String cellStr = cell.getStringValue();
                if (StringUtils.isNotEmpty(cell.getStringValue())) {
                    int dataIndex = 0;
                    for (int c = s; c < s + range.getColumnCount(); c += step) {
                        Cell cell1 = worksheet.getCells().get(r, c);
                        if (dataIndex < mapList.size()) {
                            Map<String, Object> map = mapList.get(dataIndex);
                            //数据源中不存在该集合，就不进行赋值处理
                            if (map.get(cellStr) != null) {
                                String val = map.get(cellStr).toString();
                                setHtmlValWithStyle(cell1, val, fontFamily, fontSize, isFormat);
                            }
                        }
                        //step=1,但有个别合并的列
                        if (step == 1 && StringUtils.isNotNull(cell1.getMergedRange())) {
                            c += cell1.getMergedRange().getColumnCount() - 1;
                        }
                        dataIndex++;
                    }
                }
            }
        }
    }

    /**
     * 设置cell单元格值并设置字体及颜色
     *
     * @param cell       单元格对象
     * @param val        单元格值
     * @param fontFamily 字体
     * @param fontSize   字体大小
     * @param isFormat   是否转换科学计数法
     */
    public static void setHtmlValWithStyle(Cell cell, String val, String fontFamily, Integer fontSize, boolean isFormat) {
        if (StringUtils.isNotEmpty(val) && StringUtils.isNotEmpty(fontFamily)) {
            val = "font-family: " + fontFamily + ":" + val;
        }
        setHtmlValue(cell, val, isFormat);
        if (StringUtils.isNotNull(fontSize) && fontSize > 0) {
            resetCellFontSize(cell, fontSize);
        }
    }


    /**
     * 重新设置页数和页码
     *
     * @param excelReportParamVO      前端传参接收对象
     * @param sheetName2MaxPageCntMap 每个sheet页配置的所有区域的最大页数的映射
     * @param isCount                 页码标记
     * @param dsMap                   报表数据源
     */
    public static void reConfigPage(CommonExcelReportParamVO excelReportParamVO, Map<String, Integer> sheetName2MaxPageCntMap,
                                    boolean isCount, Map<String, List<Map<String, Object>>> dsMap) {
        int addPageCount = 0, totalCount = 0;
        for (Map.Entry<String, Integer> entry : sheetName2MaxPageCntMap.entrySet()) {
            totalCount += entry.getValue();
        }
        //过滤出sheet配置项,按照对应的order值进行排序,避免多个worksheet的页码乱掉
        List<String> sortedSheetNames = excelReportParamVO.getSheetConfigList().stream().sorted(Comparator.comparing(SheetConfigVO::getOrderNum))
                .collect(Collectors.toList()).stream().map(SheetConfigVO::getSheetName).distinct().collect(Collectors.toList());
        for (String keyValue : sortedSheetNames) {
            int count = sheetName2MaxPageCntMap.getOrDefault(keyValue, 0);
            for (int k = 1; k <= count; k++) {
                Map<String, Object> dr = new HashMap<>();
                if (isCount) {
                    dr.put("页数", totalCount);
                    dr.put("页码", addPageCount + k);
                }
                dsMap.put(EnumExcelAreaType.PageReveal + keyValue + k, Collections.singletonList(dr));
            }
            if (isCount) {
                addPageCount += count;
            }
        }
        List<Map<String, Object>> infoList = dsMap.get(EnumExcelAreaType.Info.name() + 1);
        if (isCount && StringUtils.isNotEmpty(infoList)) {
            infoList.get(0).put("页数", totalCount);
            infoList.get(0).put("页码", addPageCount == 0 ? totalCount : 1);
        }

        //再次遍历每个sheet页,过滤出分页规则为独立分页的sheet页配置，单独设置这些sheet页的页数和页码，
        // 剩余连续分页的sheet页配置的总页数及页码需要重新设置（总页数需要扣除独立分页的sheet页的总页数，页码需要从第一个连续分页的sheet页开始重新编码）
        List<SheetConfigVO> sheetConfigVOList = excelReportParamVO.getSheetConfigList();
        if (StringUtils.isNotEmpty(sheetConfigVOList)) {
            List<SheetConfigVO> independentSheetConfigList = sheetConfigVOList.stream().filter(p -> independent.getValue().equals(p.getPageRules())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(independentSheetConfigList)) {
                //先计算原先的总页数
                int totalPageCnt = 0;
                for (SheetConfigVO configVO : sheetConfigVOList) {
                    totalPageCnt += sheetName2MaxPageCntMap.getOrDefault(configVO.getSheetName(), 0);
                }
                //计算独立分页的总页数
                int independentPageCnt = 0;
                for (SheetConfigVO vo : independentSheetConfigList) {
                    String sheetName = vo.getSheetName();
                    int count = sheetName2MaxPageCntMap.getOrDefault(sheetName, 0);
                    independentPageCnt += count;
                    if (isCount) {
                        for (int k = 1; k <= count; k++) {
                            Map<String, Object> dr = new HashMap<>();
                            dr.put("页数", count);
                            dr.put("页码", k);
                            dsMap.put(EnumExcelAreaType.PageReveal + sheetName + k, Collections.singletonList(dr));
                        }
                    }
                }
                List<SheetConfigVO> continuesSheetConfigList = sheetConfigVOList.stream().filter(p -> EnumExcelSheetPageRules.continuous.getValue().equals(p.getPageRules()))
                        .collect(Collectors.toList());
                if (StringUtils.isNotEmpty(continuesSheetConfigList)) {
                    //扣除独立分页的总页数后剩余的总页数
                    int continuesTotalCnt = totalPageCnt - independentPageCnt;
                    int pageNo = 1;
                    for (String keyValue : sortedSheetNames) {
                        SheetConfigVO loopConfigVo = continuesSheetConfigList.stream().filter(p -> keyValue.equals(p.getSheetName())).findFirst().orElse(null);
                        if (StringUtils.isNotNull(loopConfigVo)) {
                            if (isCount) {
                                int count = sheetName2MaxPageCntMap.getOrDefault(loopConfigVo.getSheetName(), 0);
                                for (int k = 1; k <= count; k++) {
                                    Map<String, Object> dr = new HashMap<>();
                                    dr.put("页数", continuesTotalCnt);
                                    dr.put("页码", pageNo);
                                    dsMap.put(EnumExcelAreaType.PageReveal + loopConfigVo.getSheetName() + k, Collections.singletonList(dr));
                                    pageNo++;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取报表总页数
     *
     * @param dsMap 报表数据源
     * @return 总页数
     */
    public static int getPageCnt(Map<String, List<Map<String, Object>>> dsMap, Map<String, Integer> sheetName2MaxPageCntMap) {
        List<Integer> maxPageCntList = new ArrayList<>(sheetName2MaxPageCntMap.values());
        int count = StringUtils.isNotEmpty(maxPageCntList) ? Collections.max(maxPageCntList) : 1;
        Map<String, Object> pageMap = new HashMap<>();
        pageMap.put("pageCount", count);
        dsMap.put(EnumExcelAreaType.Page.name() + 1, Collections.singletonList(pageMap));
        return count;
    }

    /**
     * 设置每个excel标签页的页眉页脚(版本号，受控编号)
     *
     * @param excelReportParamVO 前端传参接收对象
     * @param designer           Designer对象
     */
    public static void setPageHeadFoot(CommonExcelReportParamVO excelReportParamVO, WorkbookDesigner designer) {
        String controlNum = excelReportParamVO.getBaseConfig().getControlNum();
        String versionNum = excelReportParamVO.getBaseConfig().getVersionNum();
        WorksheetCollection worksheetCollection = designer.getWorkbook().getWorksheets();
        for (int i = 0; i < worksheetCollection.getCount(); i++) {
            PageSetup pageSetup = worksheetCollection.get(i).getPageSetup();
            if (StringUtils.isNotEmpty(controlNum)) {
                pageSetup.setHeader(2, "&\"Times New Roman,Bold\"&10 " + controlNum);
            }
            if (StringUtils.isNotEmpty(versionNum)) {
                pageSetup.setFooter(2, "&\"Times New Roman\"&10 " + versionNum);
            }
        }
    }

    /**
     * 统一设置每个Sheet页的报表名称
     *
     * @param excelReportParamVO 前端传参接收对象
     * @param dsMap              报表数据集
     * @param designer           WorkbookDesigner对象
     */
    public static void setSheetReportName(CommonExcelReportParamVO excelReportParamVO, Map<String, List<Map<String, Object>>> dsMap, WorkbookDesigner designer) {
        String reportName = excelReportParamVO.getBaseConfig().getDefinedFileName();
        String infoAreaName = EnumExcelAreaType.Info.name() + 1;
        if (!dsMap.containsKey(infoAreaName)) {
            dsMap.put(infoAreaName, Collections.singletonList(new HashMap<>()));
        }
        Map<String, Object> baseMap = dsMap.get(infoAreaName).get(0);
        WorksheetCollection worksheetCells = designer.getWorkbook().getWorksheets();
        if (StringUtils.isNotNull(worksheetCells)) {
            String[] nameArr = reportName.split(";");
            for (int i = 1; i <= worksheetCells.getCount(); i++) {
                baseMap.put("报表名称" + i, i <= nameArr.length ? nameArr[i - 1] : "");
            }
        }
    }

    /**
     * 判断字符串是否需要添加字体颜色样式
     *
     * @param value 字符串
     * @return 是否需要添加字体颜色样式
     */
    public static boolean checkColor(String value) {
        if (value.contains(":")) {
            return value.contains("red:") || colorReg.matcher(value.split(":")[0]).find();
        }
        return false;
    }

    /**
     * 判断字符串是否需要添加字体样式
     *
     * @param value 字符串
     * @return 是否需要添加字体样式
     */
    public static boolean checkFont(String value) {
        if (value.contains(":")) {
            return value.contains("font-family:");
        }
        return false;
    }

    /**
     * 检查给定值是否为空，为空则返回默认值，不为空返回原值
     *
     * @param val        原始值
     * @param defaultVal 默认值
     * @return 校验后的值
     */
    public static String checkValEmpty(String val, String defaultVal) {
        return StringUtils.isNotEmpty(val) ? val : defaultVal;
    }

    /**
     * 显示空白值
     *
     * @param kbType 空白类型
     * @return 空白值
     */
    public static String getKBStr(Integer kbType) {
        switch (kbType) {
            case 0:
                return "以下空白";
            case 1:
                return "以右空白";
            case 2:
                return "";
            case 3:
                return "/";
            default:
                return "--";
        }
    }

    /**
     * 数据赋值
     *
     * @param data  map数据
     * @param key   key
     * @param value value
     */
    public static void setMapValue(Map<String, Object> data, String key, Object value) {
        data.put(key, value);
    }

    /**
     * 重新格式化允许偏差范围 例如 [x] >= 1 and [x] <5 变为：1~5 （上下限值的绝对值一致时，返回 ：≤±10的形式）
     *
     * @param relLmt 质控范围
     * @return 格式化后的质控范围
     */
    public static String formatYxPc(String relLmt, String dftValue, String negPosStr, String conStr) {
        String lmtVal = dftValue;
        if (StringUtils.isNotEmpty(relLmt)) {
            lmtVal = relLmt.replace("[x]", "").replace(" ", "");
            if (lmtVal.contains("and")) {
                relLmt = relLmt.replace("[x]", "").replace(">", "").replace("<", "").replace("=", "");
                String[] relArr = relLmt.split("and");
                String leftAbs = relArr[0].trim().replace("-", "").replace("+", "");
                String rightAbs = relArr[1].trim().replace("-", "").replace("+", "");
                if (leftAbs.equals(rightAbs)) {
                    lmtVal = negPosStr + leftAbs;
                } else {
                    lmtVal = relArr[0].trim() + conStr + relArr[1].trim();
                    if (MathUtil.isNumeric(relArr[0].trim()) && MathUtil.isNumeric(relArr[1].trim())
                            && new BigDecimal(relArr[0].trim()).compareTo(new BigDecimal(relArr[1].trim())) > 0) {
                        lmtVal = relArr[1].trim() + conStr + relArr[0].trim();
                    }
                }
            } else {
                lmtVal = lmtVal.replace("<=", "≤").replace(">=", "≥");
            }
        }
        return lmtVal;
    }

    public static String formatPass(Boolean isPass, String dftValue) {
        String pass = dftValue;
        if (StringUtils.isNotNull(isPass)) {
            pass = isPass ? "是" : "否";
        }
        return pass;
    }

}
