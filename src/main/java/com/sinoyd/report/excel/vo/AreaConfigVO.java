package com.sinoyd.report.excel.vo;

import com.sinoyd.report.excel.enums.EnumExcelAreaType;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 报表区域配置实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/05
 * @since V100R001
 */
@Data
public class AreaConfigVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * sheet名称
     */
    private String sheetName;

    /**
     * 区域类型，枚举
     */
    private String areaType;

    /**
     * 开始位置
     */
    private String areaStart;

    /**
     * 结束位置
     */
    private String areaEnd;

    /**
     * 扩展方式，枚举(1：行扩展 2：列扩展 3：不扩展)
     */
    private Integer expandType;

    /**
     * 每次扩展的行、列数
     */
    private String expandAreaSize;

    /**
     * 字体
     */
    private String fontFamily;

    /**
     * 字体大小
     */
    private Integer fontSize;

    /**
     * 最大字体大小，用于缩放
     */
    private Integer maxFontSize;

    /**
     * 合并开始位置
     */
    private String mergeStart;

    /**
     * 合并结束位置
     */
    private String mergeEnd;

    /**
     * 每页总行、列数
     */
    private Integer expandPageSize;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 数据索引
     */
    private Integer itemIndex;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 区域扩展配置对象
     */
    private AreaExpandConfigVO areaExpandConfigVO;
}
