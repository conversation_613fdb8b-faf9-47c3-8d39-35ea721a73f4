package com.sinoyd.report.excel.vo;

import lombok.Data;

import java.util.List;

/**
 * 报表模板区域扩展配置传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/25
 */

@Data
public class AreaExpandConfigVO {

    private String id;

    /**
     * 区域配置id
     */
    private String areaConfigId;

    /**
     * 二维码长度
     */
    private Integer qrCodeLength;

    /**
     * 二维码宽度
     */
    private Integer qrCodeWidth;

    /**
     * 二维码开始位置
     */
    private Integer qrCodeStart;

    /**
     * 二维码结束位置
     */
    private Integer qrCodeEnd;

    /**
     * 二维码顶部间距
     */
    private Integer picTop;

    /**
     * 二维码左间距
     */
    private Integer picLeft;

    /**
     * 二维码大小
     */
    private Integer picSize;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 区域扩展合并配置列表
     */
    private List<AreaExpandMergeConfigVO> areaExpandMergeConfigVOList;

}
