package com.sinoyd.report.excel.vo;

import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.util.Date;

/**
 * 报表模板区域扩展合并配置传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */

@Data
public class AreaExpandMergeConfigVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 区域扩展配置id
     */
    private String areaExpandConfigId;

    /**
     * 合并规则
     */
    private String mergeRules;

    /**
     * 开始位置
     */
    private String startPosition;

    /**
     * 结束位置
     */
    private String endPosition;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
