package com.sinoyd.report.excel.vo;

import lombok.Data;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * excel报表生成数据传输VO
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/11/09
 */
@Data
public class CommonExcelReportParamVO {

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 报表基础配置
     */
    private BaseConfigVO baseConfig;

    /**
     * 报表模板全局配置
     */
    private GlobalConfigVO globalConfig;

    /**
     * 报表模板区域配置列表
     */
    private List<AreaConfigVO> areaConfigList;

    /**
     * 报表模板sheet页配置列表
     */
    private List<SheetConfigVO> sheetConfigList;

    /**
     * 是否转换科学计数法
     */
    private Boolean formatSci;

    /**
     * 生成报表的绝对路径地址
     */
    private String outPutPath;

    /**
     * 生成报表的文件名称
     */
    private String outPutName;

    /**
     * 区域配置和二维码图像对象的映射关系
     */
    private Map<String, BufferedImage> areaConfig2ImageMap;
}
