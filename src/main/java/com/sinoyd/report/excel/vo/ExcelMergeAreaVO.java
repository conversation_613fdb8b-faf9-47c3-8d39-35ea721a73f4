package com.sinoyd.report.excel.vo;


import com.sinoyd.report.enums.EnumReport;
import lombok.Data;

/**
 * excel的合并区域
 * <AUTHOR>
 * @version V1.0.0 2023/8/14
 * @since   V100R001
 */
@Data
public class ExcelMergeAreaVO {

    /**
     * 单元格数默认值
     */
    public static final Integer MERGE_CELL_DEFAULT_VAL = 0;

    /**
     * 默认合并的列数
     */
    public static final Integer MERGE_COLUMNS_DEFAULT_VAL = 1;

    /**
     * 默认合并的行数
     */
    public static final Integer MERGE_ROWS_DEFAULT_VAL = 1;

    /**
     * 开始合并的行
     */
    private Integer rows;

    /**
     * 开始合并的列
     */
    private Integer cells;

    /**
     * 单元格数 默认为0
     */
    private Integer cellRange = MERGE_CELL_DEFAULT_VAL;

    /**
     * 合并的列数 默认为1
     */
    private Integer totalColumns = MERGE_COLUMNS_DEFAULT_VAL;


    /**
     * 合并的行数 默认为1
     */
    private Integer totalRows = MERGE_ROWS_DEFAULT_VAL;

    /**
     * 是否按照区域进行合并
     */
    private Boolean isFollowRange = false;


    /**
     * 默认按行合并
     */
    private EnumReport.EnumMergeModel enumMergeModel = EnumReport.EnumMergeModel.Row;

    public ExcelMergeAreaVO(){}

    public ExcelMergeAreaVO(Integer rows, Integer cells, EnumReport.EnumMergeModel enumMergeModel){
        this.rows = rows;
        this.cells = cells;
        this.enumMergeModel = enumMergeModel;
    }


    public ExcelMergeAreaVO(Integer rows, Integer cells, Integer cellRange, Integer totalColumns, Integer totalRows, EnumReport.EnumMergeModel enumMergeModel, Boolean isFollowRange) {
        this.rows = rows;
        this.cells = cells;
        this.cellRange = cellRange;
        this.totalColumns = totalColumns;
        this.totalRows = totalRows;
        this.enumMergeModel = enumMergeModel;
        this.isFollowRange = isFollowRange;
    }
}
