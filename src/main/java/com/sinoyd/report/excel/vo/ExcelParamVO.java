package com.sinoyd.report.excel.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 报告生成数据传输VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/15
 */
@Data
public class ExcelParamVO {

    /**
     * 数据集
     */
    private List<Map<String, Object>> mapList = new ArrayList<>();

    /**
     * 扩展列
     */
    private List<Map<String, Object>> expandList = new ArrayList<>();

    /**
     * 模板相对路径
     */
    private String relativeTemplatePath;

    /**
     * 模板文件路径（绝对路径）
     */
    private String templatePath;

    /**
     * 生成的绝对路径地址
     */
    private String outPutPath;

    /**
     * 报告文件名称
     */
    private String fileName;


    /**
     * 表格中固定区域
     */
    private List<ExcelFixedConfigVO> excelFixedConfigList = new ArrayList<>();

    /**
     * 表格中合并区域
     */
    private List<ExcelMergeAreaVO> excelMergeAreas = new ArrayList<>();

    /**
     * 是否为特殊处理的统计表
     */
    private Boolean isStatistics = false;

    /**
     * sheet页序号
     */
    private Integer sheetIndex;

    /**
     * 单页有多个表格的数据集
     */
    private List<List<Map<String, Object>>> multiDataList = new ArrayList<>();

    /**
     * 是否多数据集
     */
    private Boolean isMultiData = false;

}
