package com.sinoyd.report.excel.vo;

import lombok.Data;

/**
 * 报表全局配置实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/05
 * @since V100R001
 */
@Data
public class GlobalConfigVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 是否分页
     */
    private Boolean isPageable;

    /**
     * 每页行数
     */
    private Integer pageRows;

    /**
     * 每页列数
     */
    private Integer pageColumns;

    /**
     * 字体
     */
    private String fontFamily;

    /**
     * 字体大小
     */
    private Integer fontsize;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;
}
