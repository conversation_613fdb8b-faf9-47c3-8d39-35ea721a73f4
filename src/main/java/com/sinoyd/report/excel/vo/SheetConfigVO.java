package com.sinoyd.report.excel.vo;

import lombok.Data;

/**
 * 报表模板sheet页配置实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Data
public class SheetConfigVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * sheet名称
     */
    private String sheetName;

    /**
     * sheet页行数
     */
    private Integer sheetRows;

    /**
     * sheet页列数
     */
    private Integer sheetColumns;

    /**
     * 页码规则，枚举(1：保持连续 2：独立分页)
     */
    private Integer pageRules;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;
}
