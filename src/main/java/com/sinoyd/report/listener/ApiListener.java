package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.ApiColumnService;
import com.sinoyd.report.service.ApiParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * API接口配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@Component
public class ApiListener {

    private ApiParamService apiParamService;

    private ApiColumnService apiColumnService;

    /**
     * 监听API接口管理配置删除
     *
     * @param event 数据集配置删除事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).REPORT_API")
    @Transactional
    public void listenApiDelete(LIMSEvent<String> event) {
        Collection<String> apiIds = event.getSourceList();
        //关联删除数据集参数
        apiParamService.deleteByApiIdIn(apiIds);
        //关联删除数据列
        apiColumnService.deleteByApiIdIn(apiIds);
    }

    @Autowired
    @Lazy
    public void setApiParamService(ApiParamService apiParamService) {
        this.apiParamService = apiParamService;
    }

    @Autowired
    @Lazy
    public void setApiColumnService(ApiColumnService apiColumnService) {
        this.apiColumnService = apiColumnService;
    }
}
