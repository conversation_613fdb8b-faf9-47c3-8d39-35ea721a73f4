package com.sinoyd.report.listener;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoAreaConfig;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoGlobalConfig;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.AreaConfigService;
import com.sinoyd.report.service.BaseConfigService;
import com.sinoyd.report.service.DocumentService;
import com.sinoyd.report.service.GlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报表基础配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@Component
@Slf4j
public class BaseConfigListener {

    private GlobalConfigService globalConfigService;

    private AreaConfigService areaConfigService;

    private BaseConfigService baseConfigService;

    private DocumentService documentService;


    /**
     * 监听报表基础配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).BASE_CONFIG")
    @Transactional
    public void listenBaseConfigDelete(LIMSEvent<String> event) {
        Collection<String> reportCodes = event.getSourceList();
        List<String> baseConfigIds = baseConfigService.findByReportCodes(reportCodes).stream().map(DtoBaseConfig::getId).collect(Collectors.toList());
        //删除全局参数配置
        Integer globalDeleteNum = globalConfigService.deleteByReportCodeIn(reportCodes);
        //打印后台日志
        log.info(String.format("======= 基础配置操作监听: 关联删除全局参数配置, 成功删除%d条全局参数配置数据(假删) =======", globalDeleteNum));
        //删除区域配置
        Integer areaDeleteNum = areaConfigService.deleteByReportCodeIn(reportCodes);
        //打印后台日志
        log.info(String.format("======= 基础配置操作监听: 关联删除报表区域配置, 成功删除%d条区域配置数据(假删) =======", areaDeleteNum));
        //删除模板文件数据以及附件记录
        if (StringUtils.isNotEmpty(baseConfigIds)){
            documentService.deleteDataAndFileByObjectIds(baseConfigIds);
        }
    }

    /**
     * 监听基础配置修改，需要修改关联数据的报表编码
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).UPDATE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).BASE_CONFIG")
    @Transactional
    public void listenBaseConfigUpdate(LIMSEvent<DtoBaseConfig> event) {
        //获取修改后的基础配置数据
        DtoBaseConfig baseConfig = event.getSource();
        //获取修改前的配置数据
        DtoBaseConfig oldBaseConfig = baseConfigService.findOne(baseConfig.getId());
        if (StringUtils.isNotNull(oldBaseConfig)) {
            String oldCode = oldBaseConfig.getReportCode();
            if (!oldCode.equals(baseConfig.getReportCode())) {
                //根据老的报表编码获取关联数据
                DtoGlobalConfig globalConfig = globalConfigService.findByReportCode(oldCode);
                List<DtoAreaConfig> areaConfigList = areaConfigService.findByReportCode(oldCode);
                //设置关联数据的报表编码
                if (StringUtils.isNotNull(globalConfig)){
                    globalConfig.setReportCode(baseConfig.getReportCode());
                    //保存关联数据
                    globalConfigService.update(globalConfig);
                }
                if (StringUtils.isNotEmpty(areaConfigList)){
                    areaConfigList.forEach(p -> p.setReportCode(baseConfig.getReportCode()));
                    areaConfigService.update(areaConfigList);
                }
            }
        }
    }

    @Autowired
    public void setGlobalConfigService(GlobalConfigService globalConfigService) {
        this.globalConfigService = globalConfigService;
    }

    @Autowired
    public void setAreaConfigService(AreaConfigService areaConfigService) {
        this.areaConfigService = areaConfigService;
    }

    @Autowired
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }
}
