package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 数据集应用配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/29
 */
@Component
@Slf4j
public class DataSetApplyColumnListener {

    private AreaConfig2DataSetApplyColumnService areaConfig2DataSetApplyColumnService;


    /**
     * 监听报表基础配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).DATA_SET_APPLY_COLUMN")
    @Transactional
    public void listenDataSetApplyColumnDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        //删除区域关联应用列数据
        areaConfig2DataSetApplyColumnService.deleteByDataSetApplyColumnIds(ids);
    }

    @Autowired
    @Lazy
    public void setAreaConfig2DataSetApplyColumnService(AreaConfig2DataSetApplyColumnService areaConfig2DataSetApplyColumnService) {
        this.areaConfig2DataSetApplyColumnService = areaConfig2DataSetApplyColumnService;
    }
}
