package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.DataSetColumnService;
import com.sinoyd.report.service.DataSetParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 数据集配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/18
 */
@Component
public class DataSetListener {

    private DataSetParamService dataSetParamService;

    private DataSetColumnService dataSetColumnService;

    /**
     * 监听数据集配置删除
     *
     * @param event 数据集配置删除事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).REPORT_DATA_SET")
    @Transactional
    public void listenDataSetDelete(LIMSEvent<String> event) {
        Collection<String> dataSetIds = event.getSourceList();
        //关联删除数据集参数
        dataSetParamService.deleteByDataSetIdIn(dataSetIds);
        //关联删除数据列
        dataSetColumnService.deleteByDataSetIdIn(dataSetIds);
    }

    @Autowired
    @Lazy
    public void setDataSetParamService(DataSetParamService dataSetParamService) {
        this.dataSetParamService = dataSetParamService;
    }

    @Autowired
    @Lazy
    public void setDataSetColumnService(DataSetColumnService dataSetColumnService) {
        this.dataSetColumnService = dataSetColumnService;
    }
}
