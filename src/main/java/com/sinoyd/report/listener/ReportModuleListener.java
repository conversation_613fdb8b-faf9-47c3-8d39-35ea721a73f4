package com.sinoyd.report.listener;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoBaseConfig2Module;
import com.sinoyd.report.dto.DtoReportModule2GroupType;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.BaseConfig2ModuleRepository;
import com.sinoyd.report.repository.ReportModule2GroupTypeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报告组件配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Component
@Slf4j
public class ReportModuleListener {

    private BaseConfig2ModuleRepository baseConfig2ModuleRepository;
    private ReportModule2GroupTypeRepository reportModule2GroupTypeRepository;

    /**
     * 监听报告组价删除
     * @param event 事件
     */
    @EventListener(value = LIMSEvent.class, condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
            + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).REPORT_MODULE")
    @Transactional
    public void listenReportModuleDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        //删除组件和报告关联关系
        List<DtoBaseConfig2Module> config2ModuleList = baseConfig2ModuleRepository.findByReportModuleIdIn(ids);
        if (StringUtils.isNotEmpty(config2ModuleList)) {
            List<String> config2ModuleIdList = config2ModuleList.stream().map(DtoBaseConfig2Module::getId).collect(Collectors.toList());
            baseConfig2ModuleRepository.deleteAll(config2ModuleList);
            //删除报告组件分页方式配置
            List<DtoReportModule2GroupType> reportModule2GroupTypeList = reportModule2GroupTypeRepository.findByBaseConfigModuleIdIn(config2ModuleIdList);
            if (StringUtils.isNotEmpty(reportModule2GroupTypeList)) {
                reportModule2GroupTypeRepository.deleteAll(reportModule2GroupTypeList);
            }
        }
    }

    @Autowired
    public void setBaseConfig2ModuleRepository(BaseConfig2ModuleRepository baseConfig2ModuleRepository) {
        this.baseConfig2ModuleRepository = baseConfig2ModuleRepository;
    }

    @Autowired
    public void setReportModule2GroupTypeRepository(ReportModule2GroupTypeRepository reportModule2GroupTypeRepository) {
        this.reportModule2GroupTypeRepository = reportModule2GroupTypeRepository;
    }
}
