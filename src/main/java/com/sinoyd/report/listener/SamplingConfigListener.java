package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoSamplingParam;
import com.sinoyd.report.service.SamplingParamService;
import com.sinoyd.report.service.SamplingTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采样单配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class SamplingConfigListener {

    private SamplingParamService samplingParamService;

    private SamplingTestService samplingTestService;

    /**
     * 监听采样单配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).SAMPLING_CONFIG")
    @Transactional
    public void listenSamplingConfigDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        samplingParamService.deleteBySamplingConfigIdIn(ids);
        samplingTestService.deleteBySamplingConfigIdIn(ids);
    }

    /**
     * 监听采样单配置复制，复制关联数据
     *
     * @param event 采样单配置复制事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).COPY "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).SAMPLING_CONFIG")
    @Transactional
    public void listenSamplingConfigCopy(LIMSEvent<Map<String, String>> event) {
        Map<String, String> copySourceMap = event.getSource();
        copySamplingParams(copySourceMap);
    }

    /**
     * 复制采样单参数
     *
     * @param copySourceMap 复制源映射（key：目标ID, value：源ID）
     */
    private void copySamplingParams(Map<String, String> copySourceMap) {
        //获取所有的源数据id集合
        List<String> sourceConfigIds = new ArrayList<>(copySourceMap.values());
        //查询所有的参数数据
        List<DtoSamplingParam> sourceParams = samplingParamService.findBySamplingConfigIdIn(sourceConfigIds);
        //参数根据采样单配置id进行分组
        Map<String, List<DtoSamplingParam>> sourceParamsGroup = sourceParams.stream().collect(Collectors.groupingBy(DtoSamplingParam::getSamplingConfigId));
        //需要保存的参数数集合
        List<DtoSamplingParam> saveParams = new ArrayList<>();
        //循环遍历复制源映射Map,进行参数复制
        copySourceMap.forEach((targetConfigId, sourceConfigId) -> {
            List<DtoSamplingParam> sourceParamsOfConfig = sourceParamsGroup.getOrDefault(sourceConfigId, new ArrayList<>());
            for (DtoSamplingParam sourceParam : sourceParamsOfConfig) {
                DtoSamplingParam copyParam = sourceParam.createCopyInstance();
                copyParam.setSamplingConfigId(targetConfigId);
                saveParams.add(copyParam);
            }
        });
        //批量保存参数复制数据（需要使用StringUtils进行空值判断）
        if (StringUtils.isNotEmpty(saveParams)) {
            samplingParamService.batchSave(saveParams);
        }
    }

    @Autowired
    @Lazy
    public void setSamplingParamService(SamplingParamService samplingParamService) {
        this.samplingParamService = samplingParamService;
    }

    @Autowired
    @Lazy
    public void setSamplingTestService(SamplingTestService samplingTestService) {
        this.samplingTestService = samplingTestService;
    }
}
