package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.SamplingParamDefaultValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 采样单参数配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class SamplingParamListener {

    private SamplingParamDefaultValueService samplingParamDefaultValueService;

    /**
     * 监听采样单配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).SAMPLING_PARAM")
    @Transactional
    public void listenSamplingParamDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        samplingParamDefaultValueService.deleteBySamplingParamIdIn(ids);
    }

    @Autowired
    @Lazy
    public void setSamplingParamDefaultValueService(SamplingParamDefaultValueService samplingParamDefaultValueService) {
        this.samplingParamDefaultValueService = samplingParamDefaultValueService;
    }
}
