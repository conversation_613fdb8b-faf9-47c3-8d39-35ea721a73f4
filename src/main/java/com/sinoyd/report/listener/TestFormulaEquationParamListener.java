package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.TestFormulaEquationParamDefaultValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 测试项目公式方程参数配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class TestFormulaEquationParamListener {

    private TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService;

    /**
     * 监听测试项目公式参数配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).TEST_FORMULA_EQUATION_PARAM")
    @Transactional
    public void listenTestFormulaEquationParamDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        testFormulaEquationParamDefaultValueService.deleteByEquationParamIdIn(ids);
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamDefaultValueService(TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService) {
        this.testFormulaEquationParamDefaultValueService = testFormulaEquationParamDefaultValueService;
    }
}
