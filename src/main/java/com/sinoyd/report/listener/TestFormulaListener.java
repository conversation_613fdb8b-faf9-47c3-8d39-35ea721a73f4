package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.dto.DtoTestFormula;
import com.sinoyd.report.service.TestFormula2TestService;
import com.sinoyd.report.service.TestFormulaEquationParamService;
import com.sinoyd.report.service.TestFormulaEquationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;

/**
 * 测试项目公式配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class TestFormulaListener {

    private TestFormula2TestService testFormula2TestService;

    private TestFormulaEquationService testFormulaEquationService;

    private TestFormulaEquationParamService testFormulaEquationParamService;

    /**
     * 监听测试项目公式配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).TEST_FORMULA")
    @Transactional
    public void listenTestFormulaDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        testFormula2TestService.deleteByTestFormulaIdIn(ids);
        testFormulaEquationService.deleteByTestFormulaIdIn(ids);
        testFormulaEquationParamService.deleteByTestFormulaIdIn(ids);
    }

    /**
     * 监听测试项目公式配置更新，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).UPDATE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).TEST_FORMULA")
    @Transactional
    public void listenTestFormulaUpdate(LIMSEvent<DtoTestFormula> event) {
        DtoTestFormula testFormula = event.getSource();
        testFormulaEquationService.deleteByTestFormulaIdIn(Collections.singleton(testFormula.getId()));
    }

    @Autowired
    @Lazy
    public void setTestFormula2TestService(TestFormula2TestService testFormula2TestService) {
        this.testFormula2TestService = testFormula2TestService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationService(TestFormulaEquationService testFormulaEquationService) {
        this.testFormulaEquationService = testFormulaEquationService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamService(TestFormulaEquationParamService testFormulaEquationParamService) {
        this.testFormulaEquationParamService = testFormulaEquationParamService;
    }
}
