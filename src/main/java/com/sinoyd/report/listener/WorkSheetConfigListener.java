package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.WorkSheetParamService;
import com.sinoyd.report.service.WorkSheetTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 原始记录单配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class WorkSheetConfigListener {

    private WorkSheetParamService workSheetParamService;
    private WorkSheetTestService workSheetTestService;

    /**
     * 监听原始记录单配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).WORKSHEET_CONFIG")
    @Transactional
    public void listenWorkSheetConfigDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        workSheetParamService.deleteByWorkSheetConfigIdIn(ids);
        workSheetTestService.deleteByWorkSheetConfigIdIn(ids);
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamService(WorkSheetParamService workSheetParamService) {
        this.workSheetParamService = workSheetParamService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetTestService(WorkSheetTestService workSheetTestService) {
        this.workSheetTestService = workSheetTestService;
    }
}
