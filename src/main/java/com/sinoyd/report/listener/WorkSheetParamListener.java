package com.sinoyd.report.listener;

import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.service.WorkSheetParamDefaultValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 原始记录单参数配置监听器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Component
@Slf4j
public class WorkSheetParamListener {

    private WorkSheetParamDefaultValueService workSheetParamDefaultValueService;

    /**
     * 监听原始记录单参数配置删除，删除关联数据
     *
     * @param event 基础配置修改事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.base.constants.IEventAction).DELETE "
                    + "and #root.event.name eq T(com.sinoyd.report.constants.IReportEventName).SAMPLING_PARAM")
    @Transactional
    public void listenWorkSheetParamDelete(LIMSEvent<String> event) {
        Collection<String> ids = event.getSourceList();
        workSheetParamDefaultValueService.deleteByWorkSheetParamIdIn(ids);
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamDefaultValueService(WorkSheetParamDefaultValueService workSheetParamDefaultValueService) {
        this.workSheetParamDefaultValueService = workSheetParamDefaultValueService;
    }
}
