package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoApiColumn;

import java.util.Collection;
import java.util.List;

/**
 * API数据列配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
public interface ApiColumnRepository extends IBaseJpaRepository<DtoApiColumn, String>, LimsRepository<DtoApiColumn, String> {

    /**
     * 根据Api接口ID集合查询数据列数据
     *
     * @param apiIds ApiID接口集合
     * @return 参数数据
     */
    List<DtoApiColumn> findByApiIdIn(Collection<String> apiIds);
}
