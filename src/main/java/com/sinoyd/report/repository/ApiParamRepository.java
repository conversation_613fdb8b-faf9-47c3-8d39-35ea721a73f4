package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoApiParam;

import java.util.Collection;
import java.util.List;

/**
 * API接口参数配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
public interface ApiParamRepository extends IBaseJpaRepository<DtoApiParam, String>, LimsRepository<DtoApiParam, String> {

    /**
     * 根据ApiID集合查询参数数据
     *
     * @param apiIds ApiID集合
     * @return 参数数据
     */
    List<DtoApiParam> findByApiIdIn(Collection<String> apiIds);
}
