package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoAreaConfig2DataSetApplyColumn;

import java.util.Collection;
import java.util.List;

/**
 * 区域配置与数据集列应用映射数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/08
 */
public interface AreaConfig2DataSetApplyColumnRepository extends IBaseJpaRepository<DtoAreaConfig2DataSetApplyColumn, String>, LimsRepository<DtoAreaConfig2DataSetApplyColumn, String> {

    /**
     * 根据区域配置id查询数据
     *
     * @param areaConfigId 区域配置id
     * @return 区域配置与数据集列应用映射数集合
     */
    List<DtoAreaConfig2DataSetApplyColumn> findByAreaConfigId(String areaConfigId);

    /**
     * 根据区域配置id查询数据
     *
     * @param areaConfigIds 区域配置id
     * @return 区域配置与数据集列应用映射数集合
     */
    List<DtoAreaConfig2DataSetApplyColumn> findByAreaConfigIdIn(Collection<String> areaConfigIds);

    /**
     * 根据数据集应用id删除数据
     *
     * @param applyIds 应用ids
     * @return 区域配置与数据集列应用映射数集合
     */
    List<DtoAreaConfig2DataSetApplyColumn> findByDataSetApplyColumnIdIn(Collection<String> applyIds);
}
