package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoAreaConfig;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 报表区域配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface AreaConfigRepository extends IBaseJpaRepository<DtoAreaConfig, String>,
        LimsRepository<DtoAreaConfig, String> {

    /**
     * 根据报表编码查询所有区域配置数据
     *
     * @param reportCode 报表编码
     * @return 区域配置数据
     */
    List<DtoAreaConfig> findByReportCode(String reportCode);

    /**
     * 根据报表编码集合查询所有区域配置数据
     *
     * @param reportCodes 报表编码集合
     * @return 区域配置数据
     */
    List<DtoAreaConfig> findByReportCodeIn(Collection<String> reportCodes);
}
