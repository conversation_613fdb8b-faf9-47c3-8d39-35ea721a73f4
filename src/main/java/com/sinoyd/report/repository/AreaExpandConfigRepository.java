package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoAreaExpandConfig;

import java.util.List;

/**
 * 报表模板区域扩展配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
public interface AreaExpandConfigRepository extends IBaseJpaRepository<DtoAreaExpandConfig, String>, LimsRepository<DtoAreaExpandConfig, String> {

    /**
     * 根据区域配置id查找
     *
     * @param areaConfigId 区域配置id
     * @return 区域扩展配置
     */
    DtoAreaExpandConfig findByAreaConfigId(String areaConfigId);

    /**
     * 根据区域配置id查找
     *
     * @param areaConfigIdList 区域配置id
     * @return 区域扩展配置
     */
    List<DtoAreaExpandConfig> findByAreaConfigIdIn(List<String> areaConfigIdList);

}
