package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoAreaExpandConfig;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;

import java.util.List;

/**
 * 报表模板区域扩展配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
public interface AreaExpandMergeConfigRepository extends IBaseJpaRepository<DtoAreaExpandMergeConfig, String>, LimsRepository<DtoAreaExpandMergeConfig, String> {

    /**
     * 根据扩展配置id查询
     *
     * @param expandConfigId 扩展配置id
     * @return 区域扩展合并配置列表
     */
    List<DtoAreaExpandMergeConfig> findByAreaExpandConfigId(String expandConfigId);

    /**
     * 根据扩展配置id列表查询
     *
     * @param expandConfigIdList 扩展配置id列表
     * @return 区域扩展合并配置列表
     */
    List<DtoAreaExpandMergeConfig> findByAreaExpandConfigIdIn(List<String> expandConfigIdList);

}
