package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoBaseConfig2Module;

import java.util.Collection;
import java.util.List;

/**
 * 报告组件与报表基础配置关联信息数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
public interface BaseConfig2ModuleRepository extends IBaseJpaRepository<DtoBaseConfig2Module, String>, LimsRepository<DtoBaseConfig2Module, String> {

    /**
     * 根据组件id获取相应的数据
     * @param moduleIds 组件id列表
     * @return 报告组件配置列表
     */
    List<DtoBaseConfig2Module> findByReportModuleIdIn(Collection<String> moduleIds);

    /**
     * 根据报表配置id获取相应的数据
     * @param baseConfigIdList 报告配置id列表
     * @return 报告组件配置列表
     */
    List<DtoBaseConfig2Module> findByBaseConfigIdIn(Collection<String> baseConfigIdList);

    /**
     * 根据报表模板配置id查询数据
     *
     * @param baseConfigId 报表模板配置id
     * @return List<DtoBaseConfig2Module>
     */
    List<DtoBaseConfig2Module> findByBaseConfigId(String baseConfigId);

}
