package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoBaseConfig;

import java.util.Collection;
import java.util.List;

/**
 * 报表基础配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface BaseConfigRepository extends IBaseJpaRepository<DtoBaseConfig, String>, LimsRepository<DtoBaseConfig, String> {

    /**
     * 查询同一个报表编号的数量
     *
     * @param reportCode 报表编码
     * @param id         排除的id
     * @return 想通报表编号的数量
     */
    Integer countByReportCodeAndIdNot( String reportCode, String id);

    /**
     * 根据报表编码查询
     *
     * @param reportCode 报表编码
     * @return 报表基础配置对象
     */
    DtoBaseConfig findByReportCode(String reportCode);


    /**
     * 根据报表编码查询报表配置数据
     *
     * @param reportCodes 报表编码
     * @return 报表配置数据
     */
    List<DtoBaseConfig> findByReportCodeIn(Collection<String> reportCodes);
}
