package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 报表全局参数配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
public interface CustomParamConfigRepository extends IBaseJpaRepository<DtoCustomParamConfig, String>, LimsRepository<DtoCustomParamConfig, String> {

    /**
     * 根据报表编码查询全局参数配置
     *
     * @param globalConfigId 全局配置id
     * @return 全局参数配置
     */
    List<DtoCustomParamConfig> findByGlobalConfigIdAndIsDeletedFalse(String globalConfigId);

    /**
     * 根据报表编码查询全局参数配置
     *
     * @param globalConfigIdList 全局配置id列表
     * @return 全局参数配置
     */
    List<DtoCustomParamConfig> findByGlobalConfigIdInAndIsDeletedFalse(List<String> globalConfigIdList);

    /**
     * 根据报表编码查询全局参数配置（包含已删除的）
     *
     * @param globalConfigId 全局配置id
     * @return 全局参数配置（包含已删除的）
     */
    List<DtoCustomParamConfig> findByGlobalConfigId(String globalConfigId);
}
