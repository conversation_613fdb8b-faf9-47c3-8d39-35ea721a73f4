package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoDataSetApplyColumn;

/**
 * 数据集应用列配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
public interface DataSetApplyColumnRepository
        extends IBaseJpaRepository<DtoDataSetApplyColumn, String>, LimsRepository<DtoDataSetApplyColumn, String> {

    /**
     * 用于保存时的重复判断
     *
     * @param placeholderName 模板占位符
     * @param id              数据集应用列Id
     * @return 重复的条数
     */
    Integer countByPlaceholderNameAndIdNotAndIsDeletedFalse(String placeholderName, String id);
}
