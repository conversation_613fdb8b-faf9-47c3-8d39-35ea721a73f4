package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoDataSetColumn;

import java.util.Collection;
import java.util.List;

/**
 * SQL数据列数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
public interface DataSetColumnRepository extends IBaseJpaRepository<DtoDataSetColumn, String>,
        LimsRepository<DtoDataSetColumn, String> {

    /**
     * 根据数据集Id集合查询数据列
     *
     * @param dataSetIds 数据集Id
     * @return 数据列
     */
    List<DtoDataSetColumn> findByDataSetIdIn(Collection<String> dataSetIds);
}
