package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoDataSetParam;

import java.util.Collection;
import java.util.List;

/**
 * 数据集参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
public interface DataSetParamRepository extends IBaseJpaRepository<DtoDataSetParam, String>,
        LimsRepository<DtoDataSetParam, String> {

    /**
     * 根据数据集配置id集合查询数据集参数
     *
     * @param dataSetIds 数据集配置id集合
     * @return 数据集参数
     */
    List<DtoDataSetParam> findByDataSetIdIn(Collection<String> dataSetIds);
}
