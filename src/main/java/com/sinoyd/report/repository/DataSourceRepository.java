package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoDataSource;

/**
 * 数据源配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
public interface DataSourceRepository extends IBaseJpaRepository<DtoDataSource, String>, LimsRepository<DtoDataSource, String> {

    /**
     * 根据数据源类型、数据源驱动、数据源地址、库名、端口坐唯一性校验
     */
    Integer countByDbTypeCodeAndDbDriverCodeAndDbHostAndDbNameAndDbPortAndIdNot(String dbTypeCode, String dbDriverCode, String dbHost, String dbName, Integer dbPort, String id);

    /**
     * 根据配置名称做唯一性校验
     */
    Integer countByDsNameAndIdNot(String dbName, String id);
}
