package com.sinoyd.report.repository;


import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoDocument;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 文件数据访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/30
 */
public interface DocumentRepository extends IBaseJpaRepository<DtoDocument, String>, LimsRepository<DtoDocument, String> {

    /**
     * 根据对象id 获取上传的相关文件
     *
     * @param folderId 对象id
     * @return 返回相应的文件信息
     */
    List<DtoDocument> findByObjectId(String folderId);

    /**
     * 根据外键id批量查询
     *
     * @param objectIds 外键id集合
     * @return 文件信息
     */
    List<DtoDocument> findByObjectIdIn(Collection<String> objectIds);
}