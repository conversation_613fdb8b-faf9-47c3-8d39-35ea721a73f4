package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoGlobalConfig;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 报表全局参数配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface GlobalConfigRepository extends IBaseJpaRepository<DtoGlobalConfig, String>, LimsRepository<DtoGlobalConfig, String> {

    /**
     * 根据报表编码查询全局参数配置
     *
     * @param reportCode 报表编码
     * @return 全局参数配置
     */
    DtoGlobalConfig findByReportCode(String reportCode);

    /**
     * 根据报表编码集合查询全局参数配置
     *
     * @param reportCodes 报表编码集合
     * @return 全局参数配置
     */
    List<DtoGlobalConfig> findByReportCodeIn(Collection<String> reportCodes);

    /**
     * 查询同一个报表编号的数量
     *
     * @param reportCode 报表编码
     * @param id         排除的id
     * @return 想通报表编号的数量
     */
    Integer countByReportCodeAndIdNot(String reportCode, String id);
}
