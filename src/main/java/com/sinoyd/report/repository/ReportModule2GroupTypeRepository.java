package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoReportModule2GroupType;

import java.util.Collection;
import java.util.List;

/**
 * 报告各个组件配置的分页方式信息数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
public interface ReportModule2GroupTypeRepository extends IBaseJpaRepository<DtoReportModule2GroupType, String>, LimsRepository<DtoReportModule2GroupType, String> {

    /**
     * 根据报表组件配置id获取相应的数据
     *
     * @param reportConfigModuleId 报告组件配置id
     * @return 报告组件分页方式配置列表
     */
    List<DtoReportModule2GroupType> findByBaseConfigModuleId(String reportConfigModuleId);

    /**
     * 根据报表组件配置id列表获取相应的数据
     *
     * @param reportConfigModuleIdList 报告组件配置id列表
     * @return 报告组件分页方式配置列表
     */
    List<DtoReportModule2GroupType> findByBaseConfigModuleIdIn(Collection<String> reportConfigModuleIdList);

}
