package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoReportModule;

/**
 * 报告组件信息数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
public interface ReportModuleRepository extends IBaseJpaRepository<DtoReportModule, String>, LimsRepository<DtoReportModule, String> {

    /**
     * 根据组件编码和id查询数据
     *
     * @param moduleCode 组件编码
     * @param id         主键
     * @return           数量
     */
    Integer countByModuleCodeAndIdNot(String moduleCode, String id);

}
