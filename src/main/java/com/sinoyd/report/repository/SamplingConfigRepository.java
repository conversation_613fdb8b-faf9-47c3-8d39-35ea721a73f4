package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSamplingConfig;

import java.util.Collection;
import java.util.List;

/**
 * 表单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingConfigRepository extends IBaseJpaRepository<DtoSamplingConfig, String>, LimsRepository<DtoSamplingConfig, String> {

    /**
     * 根据配置名称和配置类型查询
     *
     * @param formName 记录配置名称
     * @param id       id
     * @return 返回数据
     */
    Integer countByFormNameAndIdNot(String formName, String id);

    /**
     * 根据模板配置id和配置类型查询
     *
     * @param reportCode 记录配置编码
     * @param id         id
     * @return 返回数据
     */
    Integer countByReportCodeAndIdNot(String reportCode, String id);


    /**
     * 根据配置编码查询
     *
     * @param reportCodes 配置编码
     * @return 配置数据
     */
    List<DtoSamplingConfig> findByReportCodeIn(Collection<String> reportCodes);

}
