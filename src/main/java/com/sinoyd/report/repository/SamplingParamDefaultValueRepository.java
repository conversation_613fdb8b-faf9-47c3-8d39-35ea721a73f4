package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;

import java.util.Collection;
import java.util.List;

/**
 * 采样单参数默认值服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingParamDefaultValueRepository extends IBaseJpaRepository<DtoSamplingParamDefaultValue, String>, LimsRepository<DtoSamplingParamDefaultValue, String> {

    /**
     * 根据采样单参数id查询数据
     *
     * @param samplingParamIds 采样单参数id
     * @return List<DtoSamplingParamDefaultValue>
     */
    List<DtoSamplingParamDefaultValue> findBySamplingParamIdIn(Collection<String> samplingParamIds);

}
