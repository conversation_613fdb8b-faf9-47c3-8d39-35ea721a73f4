package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSamplingParam;

import java.util.Collection;
import java.util.List;

/**
 * 采样单参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingParamRepository extends IBaseJpaRepository<DtoSamplingParam, String>, LimsRepository<DtoSamplingParam, String> {

    /**
     * 根据名称，采样单配置id，id查询数据条数
     *
     * @param name               名称
     * @param samplingConfigId   采样单配置id
     * @param paramCategory      参数类别
     * @param id                 主键
     * @return  条数
     */
    Integer countByAliasNameAndSamplingConfigIdAndParamCategoryAndIdNot(String name, String samplingConfigId, Integer paramCategory, String id);

    /**
     * 根据采样单配置id查询参数数据
     *
     * @param samplingConfigIds  采样单配置id
     * @return  参数数据
     */
    List<DtoSamplingParam> findBySamplingConfigIdIn(Collection<String> samplingConfigIds);

    /**
     * 根据采样单配置id和参数类别查询参数数据
     *
     * @param samplingConfigId 采样单配置id
     * @param paramCategory    参数类别
     * @return 参数数据
     */
    List<DtoSamplingParam> findBySamplingConfigIdAndParamCategory(String samplingConfigId, Integer paramCategory);

}
