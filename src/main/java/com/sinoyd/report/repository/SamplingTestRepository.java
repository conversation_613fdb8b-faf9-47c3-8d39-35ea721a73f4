package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSamplingTest;

import java.util.Collection;
import java.util.List;

/**
 * 采样单关联测试项目服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingTestRepository extends IBaseJpaRepository<DtoSamplingTest, String>, LimsRepository<DtoSamplingTest, String> {

    /**
     * 根据采样单配置id查找数据
     *
     * @param samplingConfigIds  采样单配置id
     * @return  List<DtoSamplingTest>
     */
    List<DtoSamplingTest> findBySamplingConfigIdIn(Collection<String> samplingConfigIds);

}
