package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSheetConfig;

import java.util.Collection;
import java.util.List;

/**
 * 报表模板sheet页配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
public interface SheetConfigRepository extends IBaseJpaRepository<DtoSheetConfig, String>, LimsRepository<DtoSheetConfig, String> {

    /**
     * 根据报表编码查询模板sheet页配置
     *
     * @param reportCode 报表编码
     * @return 模板sheet页配置
     */
    List<DtoSheetConfig> findByReportCode(String reportCode);

    /**
     * 根据报表编码查询模板sheet页配置
     *
     * @param reportCodes 报表编码
     * @return 模板sheet页配置
     */
    List<DtoSheetConfig> findByReportCodeIn(Collection<String> reportCodes);
}
