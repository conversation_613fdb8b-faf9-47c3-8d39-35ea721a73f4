package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.dto.DtoSheetPagingConfig;

import java.util.List;

/**
 * 报表模板sheet页分页依据配置数据接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
public interface SheetPagingConfigRepository extends IBaseJpaRepository<DtoSheetPagingConfig, String>, LimsRepository<DtoSheetPagingConfig, String> {

    /**
     * 根据报表编码查询模板sheet页配置
     *
     * @param sheetConfigIdList 模板页配置id列表
     * @return 模板sheet页配置
     */
    List<DtoSheetPagingConfig> findBySheetConfigIdIn(List<String> sheetConfigIdList);

    /**
     * 根据报表编码查询模板sheet页配置
     *
     * @param sheetConfigId 模板页配置id
     * @return 模板sheet页配置
     */
    List<DtoSheetPagingConfig> findBySheetConfigId(String sheetConfigId);
}
