package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormula2Test;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式关联测试项目服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormula2TestRepository extends IBaseJpaRepository<DtoTestFormula2Test, String>, LimsRepository<DtoTestFormula2Test, String> {

    /**
     * 根据测试项目公式id查询关联的测试项目
     *
     * @param testFormulaIds 测试项目公式ids
     * @return List<DtoTestFormula2Test>
     */
    List<DtoTestFormula2Test> findByTestFormulaIdIn(Collection<String> testFormulaIds);

    /**
     * 根据测试项目ids获取关联集合
     *
     * @param testIds 测试项目ids
     * @return 关联集合
     */
    List<DtoTestFormula2Test> findByTestIdIn(Collection<String> testIds);
}
