package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormulaEquation2Test;

import java.util.Collection;
import java.util.List;

/**
 * 方程关联测试项目配置服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquation2TestRepository extends IBaseJpaRepository<DtoTestFormulaEquation2Test, String>, LimsRepository<DtoTestFormulaEquation2Test, String> {

    /**
     * 根据方程id查询配置信息
     *
     * @param formulaEquationId 方程id
     * @return 配置信息
     */
    List<DtoTestFormulaEquation2Test> findByFormulaEquationId(String formulaEquationId);

    /**
     * 根据方程id集合查询配置信息
     *
     * @param equationIds 方程id集合
     * @return 配置信息
     */
    List<DtoTestFormulaEquation2Test> findByFormulaEquationIdIn(Collection<String> equationIds);

}
