package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormulaEquationParamDefaultValue;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程参数默认值服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationParamDefaultValueRepository extends IBaseJpaRepository<DtoTestFormulaEquationParamDefaultValue, String>, LimsRepository<DtoTestFormulaEquationParamDefaultValue, String> {

    /**
     * 根据公式方程参数id查询
     *
     * @param paramIds 参数id集合
     * @return 返回查询结果
     */
    List<DtoTestFormulaEquationParamDefaultValue> findByEquationParamIdIn(Collection<String> paramIds);

}
