package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormulaEquationParam;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程参数服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationParamRepository extends IBaseJpaRepository<DtoTestFormulaEquationParam, String>, LimsRepository<DtoTestFormulaEquationParam, String> {

    /**
     * 根据条件查询数据数量
     *
     * @param aliasName             参数别名
     * @param testFormulaId         公式方程id
     * @param id                    id
     * @return 返回条数
     */
    Integer countByAliasNameAndTestFormulaIdAndIdNot(String aliasName, String testFormulaId, String id);

    /**
     * 根据公式id查询数据
     *
     * @param testFormulaIds 公式id
     * @return 数据
     */
    List<DtoTestFormulaEquationParam> findByTestFormulaIdIn(Collection<String> testFormulaIds);

}
