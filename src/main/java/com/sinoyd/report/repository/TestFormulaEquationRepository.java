package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormulaEquation;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationRepository extends IBaseJpaRepository<DtoTestFormulaEquation, String>, LimsRepository<DtoTestFormulaEquation, String> {

    /**
     * 根据测试项目公式id查询数据
     *
     * @param testFormulaIds 测试项目公式id集合
     * @return 数据
     */
    List<DtoTestFormulaEquation> findByTestFormulaIdIn(Collection<String> testFormulaIds);

}
