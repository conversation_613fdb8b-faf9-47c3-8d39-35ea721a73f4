package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoTestFormula;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaRepository extends IBaseJpaRepository<DtoTestFormula, String>, LimsRepository<DtoTestFormula, String> {

    /**
     * 根据对象id统计数量
     *
     * @param objectIds 对象id
     * @return  数量
     */
    Integer countByObjectIdIn(List<String> objectIds);

    /**
     * 根据对象id查询
     *
     * @param objectIds  对象id
     * @return 对象集合
     */
    List<DtoTestFormula> findByObjectIdIn(Collection<String> objectIds);

}
