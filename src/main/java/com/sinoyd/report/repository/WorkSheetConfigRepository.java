package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoSamplingConfig;
import com.sinoyd.report.dto.DtoWorkSheetConfig;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单应用数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetConfigRepository extends IBaseJpaRepository<DtoWorkSheetConfig, String>, LimsRepository<DtoWorkSheetConfig, String> {

    /**
     * 根据配置名称和配置类型查询
     *
     * @param formName 记录配置名称
     * @param id       id
     * @return 返回数据
     */
    Integer countByFormNameAndIdNot(String formName, String id);

    /**
     * 根据模板配置id和配置类型查询
     *
     * @param reportCode 记录配置编码
     * @param id         id
     * @return 返回数据
     */
    Integer countByReportCodeAndAndIdNot(String reportCode, String id);

    /**
     * 根据报告编号集合查询所有关联数据
     *
     * @param reportCodes           报告编号集合
     * @return 关联数据
     */
    List<DtoWorkSheetConfig> findByReportCodeIn(Collection<String> reportCodes);

}
