package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoWorkSheetParamDefaultValue;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数默认值数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetParamDefaultValueRepository extends IBaseJpaRepository<DtoWorkSheetParamDefaultValue, String>, LimsRepository<DtoWorkSheetParamDefaultValue, String> {

    /**
     * 根据原始记录单参数查找默认值信息
     *
     * @param workSheetParamIds 原始记录单参数ID集合
     * @return 原始记录单参数默认值信息集合
     */
    List<DtoWorkSheetParamDefaultValue> findByWorkSheetParamIdIn(Collection<String> workSheetParamIds);

}
