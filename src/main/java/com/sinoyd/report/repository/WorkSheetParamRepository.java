package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoWorkSheetParam;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetParamRepository extends IBaseJpaRepository<DtoWorkSheetParam, String>, LimsRepository<DtoWorkSheetParam, String> {

    /**
     * 根据名称，原始记录单配置id，id查询数据条数
     *
     * @param name              名称
     * @param paramCategory     参数类型
     * @param workSheetConfigId 原始记录单id
     * @param id                主键
     * @return 条数
     */
    Integer countByAliasNameAndParamCategoryAndWorkSheetConfigIdAndIdNot(String name, Integer paramCategory, String workSheetConfigId, String id);

    /**
     * 根据名称，原始记录单配置id，出证参数，id查询数据条数
     *
     * @param paramCategory     参数类型
     * @param workSheetConfigId 原始记录单id
     * @param id                主键
     * @return 条数
     */
    Integer countByParamCategoryAndWorkSheetConfigIdAndIsCertTrueAndIdNot(Integer paramCategory, String workSheetConfigId, String id);

    /**
     * 根据原始记录单配置id查询数据
     *
     * @param workSheetConfigIds 原始记录单配置id
     * @return 数据
     */
    List<DtoWorkSheetParam> findByWorkSheetConfigIdIn(Collection<String> workSheetConfigIds);

    /**
     * 根据原始记录单配置id和参数类型查询数据
     *
     * @param workSheetConfigId 原始记录单配置id
     * @param paramCategory     参数类型
     * @return 原始记录单参数数据
     */
    List<DtoWorkSheetParam> findByWorkSheetConfigIdAndParamCategory(String workSheetConfigId, Integer paramCategory);

}
