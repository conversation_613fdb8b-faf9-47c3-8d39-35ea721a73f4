package com.sinoyd.report.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.report.dto.DtoWorkSheetTest;

import java.util.Collection;
import java.util.List;

/**
 * 也是记录单测试项目服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetTestRepository extends IBaseJpaRepository<DtoWorkSheetTest, String>, LimsRepository<DtoWorkSheetTest, String> {

    /**
     * 根据配置id查询数据
     *
     * @param configIds 配置id集合
     * @return List<DtoWorkSheetTest>
     */
    List<DtoWorkSheetTest> findByWorkSheetConfigIdIn(Collection<String> configIds);

    /**
     * 根据测试项目id集合查询数据
     *
     * @param testIds 测试项目id集合
     * @return 原始记录单关联测试项目数据
     */
    List<DtoWorkSheetTest> findByTestIdIn(Collection<String> testIds);

}
