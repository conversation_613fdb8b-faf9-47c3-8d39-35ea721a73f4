package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiColumn;

import java.util.Collection;
import java.util.List;

/**
 * API数据列配置服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
public interface ApiColumnService extends LimsBaseService<DtoApiColumn, String> {

    /**
     * 根据Api接口ID集合查询关联数据列
     *
     * @param apiIds Api接口ID集合
     * @return 关联数据列
     */
    List<DtoApiColumn> findByApiIdIn(Collection<String> apiIds);

    /**
     * 根据API实体传参保存结果列
     *
     * @param api API实体
     * @return 保存后的结果列
     */
    List<DtoApiColumn> saveByApi(DtoApi api);

    /**
     * 根据Api接口ID集合删除关联数据列
     *
     * @param apiIds Api接口ID集合
     */
    void deleteByApiIdIn(Collection<String> apiIds);
}
