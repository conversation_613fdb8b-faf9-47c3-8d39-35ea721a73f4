package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiParam;

import java.util.Collection;
import java.util.List;

/**
 * API接口参数配置服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
public interface ApiParamService extends LimsBaseService<DtoApiParam, String> {

    /**
     * 根据ApiID集合查询关联参数数据
     *
     * @param apiIds ApiID集合
     * @return 关联参数数据
     */
    List<DtoApiParam> findByApiIdIn(Collection<String> apiIds);

    /**
     * 根据接口实体传参保存接口参数数据
     *
     * @param api 接口实体
     * @return 保存后的接口参数数据
     */
    List<DtoApiParam> saveByApi(DtoApi api);

    /**
     * 根据API的ID集合删除参数数据
     *
     * @param apiIds API的ID集合
     */
    void deleteByApiIdIn(Collection<String> apiIds);
}
