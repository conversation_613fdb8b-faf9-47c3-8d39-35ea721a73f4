package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiColumn;

import java.util.List;

/**
 * API接口配置服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
public interface ApiService extends LimsBaseService<DtoApi, String> {

    /**
     * 验证API接口并返回结果列
     *
     * @param api 接口信息
     * @return 执行结果列
     */
    List<DtoApiColumn> verifyApi(DtoApi api);
}
