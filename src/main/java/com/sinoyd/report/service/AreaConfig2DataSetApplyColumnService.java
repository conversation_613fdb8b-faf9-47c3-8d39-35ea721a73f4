package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoAreaConfig2DataSetApplyColumn;

import java.util.Collection;
import java.util.List;

/**
 * 区域配置与数据集列应用映射数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/18
 */
public interface AreaConfig2DataSetApplyColumnService extends LimsBaseService<DtoAreaConfig2DataSetApplyColumn, String> {

    /**
     * 根据区域配置id查询数据列应用数据
     *
     * @param areaConfigIds  区域配置id集合
     * @return  List<DtoAreaConfig2DataSetApplyColumn>
     */
    List<DtoAreaConfig2DataSetApplyColumn> findByAreaConfigIdIn(Collection<String> areaConfigIds);


    /**
     * 根据数据集应用配置id删除数据
     *
     * @param applyIds 应用配置id集合
     */
    void deleteByDataSetApplyColumnIds(Collection<String> applyIds);

}
