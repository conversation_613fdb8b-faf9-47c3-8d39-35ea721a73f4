package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoAreaConfig;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 报表区域参数配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface AreaConfigService extends LimsBaseService<DtoAreaConfig, String> {

    /**
     * 根据报表编码查询所有区域配置数据
     *
     * @param reportCode 报表编码
     * @return 区域配置数据
     */
    List<DtoAreaConfig> findByReportCode(String reportCode);

    /**
     * 根据报表编码删除区域参数配置
     *
     * @param reportCodes 报表编码集合
     * @return 删除的条数
     */
    Integer deleteByReportCodeIn(Collection<String> reportCodes);

    /**
     * 获取区域类型
     *
     * @return 区域类型
     */
    List<String> findAreaTypes();

    /**
     * 获取区域配置列表
     *
     * @param reportCode 模板编码
     * @return Map
     */
    Map<String, Object> getAreaConfigMap(String reportCode);
}
