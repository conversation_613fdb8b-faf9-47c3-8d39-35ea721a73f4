package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoAreaExpandConfig;

import java.util.List;

/**
 * 报表区域扩展配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
public interface AreaExpandConfigService extends LimsBaseService<DtoAreaExpandConfig, String> {

    /**
     * 根据区域id查询
     *
     * @param areaId 区域id
     * @return 区域扩展配置
     */
    DtoAreaExpandConfig findByAreaId(String areaId);

    /**
     * 根据区域id列表查询
     *
     * @param areaIdList 区域id列表
     * @return 区域扩展配置
     */
    List<DtoAreaExpandConfig> findByAreaIds(List<String> areaIdList);

}
