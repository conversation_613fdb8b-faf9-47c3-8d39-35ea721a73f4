package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;

import java.util.List;

/**
 * 报表区域扩展配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
public interface AreaExpandMergeConfigService extends LimsBaseService<DtoAreaExpandMergeConfig, String> {

    /**
     * 根据扩展配置id查询
     *
     * @param expandConfigId 扩展配置id
     * @return 区域扩展合并配置列表
     */
    List<DtoAreaExpandMergeConfig> findByExpandConfigId(String expandConfigId);

    /**
     * 根据扩展配置id列表查询
     *
     * @param expandConfigIdList 扩展配置id列表
     * @return 区域扩展合并配置列表
     */
    List<DtoAreaExpandMergeConfig> findByExpandConfigIds(List<String> expandConfigIdList);

}
