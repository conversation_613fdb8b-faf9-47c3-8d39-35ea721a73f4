package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoBaseConfig2Module;
import com.sinoyd.report.dto.DtoReportModule;

import java.util.List;

/**
 * 报告组件与报表基础配置关联服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
public interface BaseConfig2ModuleService extends LimsBaseService<DtoBaseConfig2Module, String> {

    /**
     * 根据报告配置id查询
     *
     * @param baseConfigId 配置id
     * @return 查询结果
     */
    List<DtoBaseConfig2Module> queryByBaseConfigId(String baseConfigId);

    /**
     * 查询报告关联的组件的详细信息
     *
     * @param id 报告和组件关联关系id
     * @return 查询结果
     */
    DtoReportModule findModuleInfo(String id);

    /**
     * 批量删除报告组件关联信息
     *
     * @param ids 报告和组件关联关系id列表
     * @return 删除的数量
     */
    int deleteConfig2Module(List<String> ids);

}
