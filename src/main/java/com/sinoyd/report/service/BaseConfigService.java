package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoDocument;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 报表基础配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface BaseConfigService extends LimsBaseService<DtoBaseConfig, String> {

    /**
     * 获取附件路径
     *
     * @param configId 配置id
     * @return 路径
     */
    String getDocumentAttachPath(String configId);

    /**
     * 根据报表编码查询报表配置数据
     *
     * @param reportCodes 报表编码
     * @return 报表配置数据
     */
    List<DtoBaseConfig> findByReportCodes(Collection<String> reportCodes);

    /**
     * 根据报表编码查询报表配置数据
     *
     * @param reportCode 报表编码
     * @return 报表配置数据
     */
    DtoBaseConfig findByReportCode(String reportCode);

    /**
     * 文件上传
     *
     * @param request 文件上传对象
     */
    List<DtoDocument> upload(HttpServletRequest request);

    /**
     * 下载报表模板
     *
     * @param configId 报表配置id
     * @param response 响应流
     */
    void downloadReport(String configId, HttpServletResponse response);

    /**
     * 报表模板预览
     *
     * @param configId 报表配置id
     * @param response 响应流
     */
    void preview(String configId, HttpServletResponse response);

    /**
     * 获取模板所有sheet页名称
     *
     * @param configId 基础配置id
     * @return sheet页名称集合
     */
    List<String> getSheetNames(String configId);
}
