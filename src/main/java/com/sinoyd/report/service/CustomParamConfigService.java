package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoCustomParamConfig;

import java.util.List;

/**
 * 报表全局配置自定义参数接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
public interface CustomParamConfigService extends LimsBaseService<DtoCustomParamConfig, String> {

    /**
     * 根据全局配置id查询
     *
     * @param globalConfigId 全局配置id
     * @return 配置信息列表
     */
    List<DtoCustomParamConfig> findByGlobalConfigId(String globalConfigId);

    /**
     * 根据全局配置id查询可选择的配置信息
     *
     * @param globalConfigId 全局配置id
     * @return 可选择的配置信息列表
     */
    List<DtoCustomParamConfig> findAlternativeConfig(String globalConfigId);

    /**
     * 保存被选择的配置信息列表
     *
     * @param configList     被选择的配置信息
     * @param globalConfigId 全局配置id
     * @return 保存后的数据
     */
    List<DtoCustomParamConfig> saveCheckedConfig(List<DtoCustomParamConfig> configList, String globalConfigId);

}
