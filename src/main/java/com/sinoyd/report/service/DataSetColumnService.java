package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetColumn;

import java.util.Collection;
import java.util.List;

/**
 * 数据集列服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
public interface DataSetColumnService extends LimsBaseService<DtoDataSetColumn, String> {

    /**
     * 根据数据集Id集合查询数据列
     *
     * @param dataSetIds 数据集Id
     * @return 数据列
     */
    List<DtoDataSetColumn> findByDataSetIdIn(Collection<String> dataSetIds);

    /**
     * 根据数据集保存数据列
     *
     * @param dataSet 数据集
     * @return 数据列
     */
    List<DtoDataSetColumn> saveByDataSet(DtoDataSet dataSet);

    /**
     * 数据集id集合
     *
     * @param dataSetIds 数据集id集合
     */
    void deleteByDataSetIdIn(Collection<String> dataSetIds);
}
