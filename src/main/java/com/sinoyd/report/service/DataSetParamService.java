package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetParam;

import java.util.Collection;
import java.util.List;

/**
 * 数据集参数服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
public interface DataSetParamService extends LimsBaseService<DtoDataSetParam, String> {

    /**
     * 根据数据集配置id集合查询数据集参数
     *
     * @param dataSetIds 数据集配置id集合
     * @return 数据集参数
     */
    List<DtoDataSetParam> findByDataSetIdIn(Collection<String> dataSetIds);

    /**
     * 根据数据集数据保存数据集参数
     *
     * @param dataSet 数据集实体
     * @return 数据集参数
     */
    List<DtoDataSetParam> saveByDataSet(DtoDataSet dataSet);


    /**
     * 根据数据集id删除关联参数数据
     *
     * @param dataSetIds 数据集id集合
     */
    void deleteByDataSetIdIn(Collection<String> dataSetIds);
}
