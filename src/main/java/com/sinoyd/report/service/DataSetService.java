package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.vo.JdbcExecuteVO;

import java.util.List;
import java.util.Map;

/**
 * 数据集服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/30
 */
public interface DataSetService extends LimsBaseService<DtoDataSet, String> {

    /**
     * 校验Sql,并返回结果列
     *
     * @param dataSet 数据集实体
     * @return Sql执行返回列
     */
    DtoDataSet verifySql(DtoDataSet dataSet);

    /**
     * 获取数据库SQL执行VO
     *
     * @param dataSet   数据集配置数据
     * @param sqlParams sql请求参数
     * @return 数据库SQL执行VO
     */
    JdbcExecuteVO getExecuteVO(DtoDataSet dataSet, Map<String, Object> sqlParams);
}
