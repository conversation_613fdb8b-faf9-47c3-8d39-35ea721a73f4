package com.sinoyd.report.service;

import com.sinoyd.base.vo.DocumentPathVO;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoDocument;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件相关业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/30
 */
public interface DocumentService extends LimsBaseService<DtoDocument, String> {

    /**
     * 下载文件
     *
     * @param documentId 文件id
     * @param response   响应流
     */
    void download(String documentId, HttpServletResponse response);

    /**
     * 文件上传
     *
     * @param request 文件上传对象
     */
    List<DtoDocument> upload(HttpServletRequest request);

    /**
     * 文件上传（可控根目录与是否覆盖操作）
     *
     * @param request  请求体
     * @param rootPath 根目录
     * @param isCover  是否覆盖
     * @return 保存后的文档数据
     */
    List<DtoDocument> uploadFile(HttpServletRequest request, String rootPath, Boolean isCover);

    /**
     * 得到文件上传的路径
     *
     * @param code 通过编码锁定相应的反射类
     * @param map  前端传相应的参数集合
     * @return 返回文件上传的路径vo
     */
    DocumentPathVO getDocumentPathFromXml(String code, Map<String, Object> map);

    /**
     * 根据对象id查询
     *
     * @param objectId 对象id
     * @return 结果
     */
    List<DtoDocument> findByObjectId(String objectId);

    /**
     * 根据对象id集合查询
     *
     * @param objectIds 对象id集合
     * @return 结果
     */
    List<DtoDocument> findByObjectIdIn(Collection<String> objectIds);

    /**
     * 删除对应对象的附件文件和数据
     *
     * @param objectIds 外键id
     */
    void deleteDataAndFileByObjectIds(Collection<String> objectIds);

    /**
     * 文件预览
     *
     * @param vo       文件预览模型
     * @param response 响应流
     */
    void previewDocument(DocumentPreviewVO vo, HttpServletResponse response);
}