package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoGlobalConfig;

import java.util.Collection;

/**
 * 报表全局参数配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
public interface GlobalConfigService extends LimsBaseService<DtoGlobalConfig, String> {

    /**
     * 根据报表编码查询对应的全局参数配置
     *
     * @param reportCode 报表编码
     * @return 全局参数配置
     */
    DtoGlobalConfig findByReportCode(String reportCode);

    /**
     * 根据报表编码集合删除全区参数配置数据
     *
     * @param reportCodes 报表编码集合
     * @return 删除的条数
     */
    Integer deleteByReportCodeIn(Collection<String> reportCodes);
}
