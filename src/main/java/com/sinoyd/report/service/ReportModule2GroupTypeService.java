package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoReportModule2GroupType;
import com.sinoyd.report.dto.customer.DtoModuleGroupTypeTemp;

import java.util.List;

/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
public interface ReportModule2GroupTypeService extends LimsBaseService<DtoReportModule2GroupType, String> {

    /**
     * 新增报告和组件关联关系的分页配置扩展信息
     *
     * @param moduleGroupTypeTemp 请求参数entity
     * @return 查询结果
     */
    List<DtoReportModule2GroupType> saveGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp);

    /**
     * 修改报告和组件关联关系的分页配置扩展信息
     *
     * @param moduleGroupTypeTemp 请求参数entity
     * @return 查询结果
     */
    List<DtoReportModule2GroupType> updateGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp);

    /**
     * 根据报告和组件关联关系id查询分页配置拓展信息
     *
     * @param baseConfigModuleId 告和组件关联关系id
     * @return List<DtoReportModule2GroupType>
     */
    List<DtoReportModule2GroupType> findByBaseConfigModuleId(String baseConfigModuleId);

    /**
     * 根据报告和组件关联关系id查询分页配置拓展信息
     *
     * @param baseConfigModuleIds 告和组件关联关系id
     * @return List<DtoReportModule2GroupType>
     */
    List<DtoReportModule2GroupType> findByBaseConfigModuleIdIn(List<String> baseConfigModuleIds);

}
