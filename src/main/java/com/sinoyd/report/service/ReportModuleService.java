package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoReportModule;

import java.util.List;

/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
public interface ReportModuleService extends LimsBaseService<DtoReportModule, String> {

    /**
     * 批量删除组件
     *
     * @param ids 组件id列表
     * @return 删除个数
     */
    int deleteModule(List<String> ids);

}
