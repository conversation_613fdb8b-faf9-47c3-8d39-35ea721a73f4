package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSamplingConfig;

import java.util.Collection;
import java.util.List;

/**
 * 表单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingConfigService extends LimsBaseService<DtoSamplingConfig, String> {

    /**
     * 根据报告编号查询表单配置
     *
     * @param reportCodes 报告编号
     * @param reportCodes 报告编号
     * @return 表单配置
     */
    List<DtoSamplingConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields);


    /**
     * 复制表单配置
     *
     * @param samplingConfig 表单配置
     * @return 复制后的表单配置
     */
    DtoSamplingConfig copy(DtoSamplingConfig samplingConfig);
}
