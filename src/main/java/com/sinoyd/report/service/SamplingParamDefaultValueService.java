package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;
import com.sinoyd.report.vo.SamplingParamDefaultValueQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * 采样单参数默认值服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingParamDefaultValueService extends LimsBaseService<DtoSamplingParamDefaultValue, String> {

    /**
     * 根据参数id查询默认值
     *
     * @param queryVO 查询条件
     * @return List<DtoWorkSheetParamDefaultValue>
     */
    List<DtoSamplingParamDefaultValue> findList(SamplingParamDefaultValueQueryVO queryVO);

    /**
     * 根据采样单参数id删除参数默认值配置数据
     *
     * @param samplingParamIds 采样单参数id集合
     */
    void deleteBySamplingParamIdIn(Collection<String> samplingParamIds);

    /**
     * 根据采样单参数id查询数据
     *
     * @param samplingParamIds 采样单参数id
     * @return List<DtoSamplingParamDefaultValue>
     */
    List<DtoSamplingParamDefaultValue> findBySamplingParamIdIn(Collection<String> samplingParamIds);

}
