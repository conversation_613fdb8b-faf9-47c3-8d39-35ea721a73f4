package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSamplingParam;

import java.util.Collection;
import java.util.List;

/**
 * 采样单参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingParamService extends LimsBaseService<DtoSamplingParam, String> {

    /**
     * 根据采样单配置id删除数据
     *
     * @param samplingConfigIds 采样单配置id
     */
    void deleteBySamplingConfigIdIn(Collection<String> samplingConfigIds);

    /**
     * 根据采样单配置id查询数据
     *
     * @param samplingConfigId 采样单配置id
     * @param category         参数类别
     * @return 采样单参数数据
     */
    List<DtoSamplingParam> findBySamplingConfigIdAndCategory(String samplingConfigId, Integer category);

    /**
     * 根据采样单配置Ids查询参数集合
     *
     * @param samplingConfigIds 采样单配置Ids
     * @return 参数集合
     */
    List<DtoSamplingParam> findBySamplingConfigIdIn(Collection<String> samplingConfigIds);

    /**
     * 根据ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    List<DtoSamplingParam> findByIds(Collection<String> ids);
}
