package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSamplingTest;

import java.util.Collection;
import java.util.List;

/**
 * 采样单关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface SamplingTestService extends LimsBaseService<DtoSamplingTest, String> {

    /**
     * 根据采样单配置id查询数据
     *
     * @param samplingIds 采样单配置id
     * @return List<DtoSamplingTest>
     */
    List<DtoSamplingTest> findBySamplingIds(Collection<String> samplingIds);

    /**
     * 根据采样单配置id删除数据
     *
     * @param samplingConfigIds 采样单配置id
     */
    void deleteBySamplingConfigIdIn(Collection<String> samplingConfigIds);

}
