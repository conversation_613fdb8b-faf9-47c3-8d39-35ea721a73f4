package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSheetConfig;

import java.util.Collection;
import java.util.List;

/**
 * 报表区sheet页配置接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
public interface SheetConfigService extends LimsBaseService<DtoSheetConfig, String> {

    /**
     * 根据报表编码查询sheet页配置
     *
     * @param reportCode 报表编码
     * @return sheet页配置
     */
    List<DtoSheetConfig> findByReportCode(String reportCode);

    /**
     * 根据多个报表编码查询sheet页配置
     *
     * @param reportCodes           报表编码
     * @param isLoadTransientFields 是否加载非数据库字段
     * @return sheet页配置
     */
    List<DtoSheetConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields);
}
