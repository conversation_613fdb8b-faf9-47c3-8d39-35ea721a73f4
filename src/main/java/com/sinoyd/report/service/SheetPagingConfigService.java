package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoSheetPagingConfig;

import java.util.List;

/**
 * sheet页分页配置明细接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
public interface SheetPagingConfigService extends LimsBaseService<DtoSheetPagingConfig, String> {

    /**
     * 根据sheet页配置id查询分页依据
     *
     * @param sheetConfigIdList sheet页配置id列表
     * @param transientFlag     是否获取非数据库映射字段数据
     * @return 分页依据列表
     */
    List<DtoSheetPagingConfig> findBySheetConfigIdIn(List<String> sheetConfigIdList, boolean transientFlag);

    /**
     * 根据sheet页配置id查询分页依据
     *
     * @param sheetConfigId sheet页配置id
     * @return 分页依据列表
     */
    List<DtoSheetPagingConfig> findBySheetConfigId(String sheetConfigId);

}
