package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormula2Test;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormula2TestService extends LimsBaseService<DtoTestFormula2Test, String> {

    /**
     * 根据测试项目公式id查询关联测试项目
     *
     * @param testFormulaIds 测试项目公式id
     * @param needLoadFields 是否加载关联字段
     * @return  关联测试项目
     */
    List<DtoTestFormula2Test> findByTestFormulaIds(Collection<String> testFormulaIds, Boolean needLoadFields);

    /**
     * 根据测试项目id删除关联测试项目公式
     *
     * @param testFormulaIds 测试项目公式id
     */
    void deleteByTestFormulaIdIn(Collection<String> testFormulaIds);

    /**
     * 根据测试项目ids获取关联集合
     *
     * @param testIds 测试项目ids
     * @return 关联集合
     */
    List<DtoTestFormula2Test> findByTestIds(Collection<String> testIds);

}
