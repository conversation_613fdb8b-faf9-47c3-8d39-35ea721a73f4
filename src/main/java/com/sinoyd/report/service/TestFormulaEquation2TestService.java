package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormulaEquation2Test;

import java.util.Collection;
import java.util.List;

/**
 * 公式方程关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquation2TestService extends LimsBaseService<DtoTestFormulaEquation2Test, String> {

    /**
     * 查询公式方程关联测试项目数据
     *
     * @param formulaEquationId 公式方程ID
     * @param testFormulaId     测试公式ID
     * @return 公式方程关联测试项目数据
     */
    List<DtoTestFormulaEquation2Test> findList(String formulaEquationId, String testFormulaId);


    /**
     * 查询公式下的所有公式类型对应的测试项目数据
     *
     * @param formulaEquationIds    公式方程id集合
     * @param isLoadTransientFields 是否加载冗余字段
     * @return 公式下的所有公式类型对应的测试项目数据
     */
    List<DtoTestFormulaEquation2Test> findByFormulaEquationIdIn(Collection<String> formulaEquationIds, boolean isLoadTransientFields);
}
