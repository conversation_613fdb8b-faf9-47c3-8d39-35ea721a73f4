package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormulaEquationParamDefaultValue;
import com.sinoyd.report.vo.TestFormulaEquationParamDefaultValueQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程参数默认值数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationParamDefaultValueService extends LimsBaseService<DtoTestFormulaEquationParamDefaultValue, String> {

    /**
     * 根据测试项目公式参数id和测试项目公式id查询测试项目公式方程参数默认值列表
     *
     * @param queryVO 查询条件
     * @return 测试项目公式方程参数默认值列表
     */
    List<DtoTestFormulaEquationParamDefaultValue> findList(TestFormulaEquationParamDefaultValueQueryVO queryVO);

    /**
     * 根据公式方程参数id参数数据
     *
     * @param equationParamIds 公式方程参数id集合
     */
    void deleteByEquationParamIdIn(Collection<String> equationParamIds);

    /**
     * 根据参数id获取参数默认值
     *
     * @param paramIds 参数ids
     * @return 默认值集合
     */
    List<DtoTestFormulaEquationParamDefaultValue> findByParamIds(Collection<String> paramIds);

    /**
     * 根据方程参数id集合获取参数默认值
     *
     * @param equationParamIds 方程参数ids
     * @return 默认值集合
     */
    List<DtoTestFormulaEquationParamDefaultValue> findByEquationParamIdIn(Collection<String> equationParamIds);

}
