package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormulaEquationParam;
import com.sinoyd.report.vo.TestFormulaEquationParamQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationParamService extends LimsBaseService<DtoTestFormulaEquationParam, String> {

    /**
     * 根据公式方程ID删除
     *
     * @param testFormulaIds 公式ID
     */
    void deleteByTestFormulaIdIn(Collection<String> testFormulaIds);

    /**
     * 参数列表
     *
     * @param queryVO 查询条件
     * @return 参数列表
     */
    List<DtoTestFormulaEquationParam> paramList(TestFormulaEquationParamQueryVO queryVO);

    /**
     * 根据ids获取公式参数集合
     *
     * @param formulaIds     公式ids
     * @param needLoadFields 是否扩展内容
     * @return 公式参数集合
     */
    List<DtoTestFormulaEquationParam> findByTestFormulaIds(Collection<String> formulaIds, Boolean needLoadFields);
}
