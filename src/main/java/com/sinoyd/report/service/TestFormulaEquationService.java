package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormulaEquation;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式方程数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaEquationService extends LimsBaseService<DtoTestFormulaEquation, String> {

    /**
     * 根据测试项目公式id删除数据
     *
     * @param testFormulaIds 测试项目公式id集合
     */
    void deleteByTestFormulaIdIn(Collection<String> testFormulaIds);

    /**
     * 批量保存测试项目公式方程
     *
     * @param entities       数据载体
     * @param testFormulaId  测试项目公式id
     */
    void batchSave(List<DtoTestFormulaEquation> entities, String testFormulaId);

    /**
     * 查询测试项目公式方程
     *
     * @param testFormulaIds 测试项目公式id集合
     * @return 测试项目公式方程数据
     */
    List<DtoTestFormulaEquation> findByTestFormulaIdIn(Collection<String> testFormulaIds);

}
