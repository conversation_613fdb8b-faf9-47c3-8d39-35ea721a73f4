package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoTestFormula;
import com.sinoyd.report.vo.FormulaQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目公式数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface TestFormulaService extends LimsBaseService<DtoTestFormula, String> {

    /**
     * 设置启用
     *
     * @param ids      id集合
     * @param isEnable 是否启用
     */
    void setEnable(List<String> ids, Boolean isEnable);

    /**
     * 复制测试项目公式
     *
     * @param ids id集合
     */
    void copy(List<String> ids);

    /**
     * 判断采样单配置或者原始记录单配置有没有绑定公式
     *
     * @param objectIds 对象id集合
     * @return 是否绑定公式
     */
    String bindFormulaFlag(List<String> objectIds);

    /**
     * 根据对象id查询
     *
     * @param objectIds             对象id
     * @param isLoadTransientFields 是否扩展内容
     * @return 对象集合
     */
    List<DtoTestFormula> findByObjectIds(Collection<String> objectIds, Boolean isLoadTransientFields);

    /**
     * 根据公式id及测试项目id获取公式集合
     *
     * @param formulaId 公式id
     * @param testId    测试项目id
     * @return 公式集合
     */
    List<DtoTestFormula> findByFormulaIdAndTestId(String formulaId, String testId);

    /**
     * 根据表单配置id及测试项目id获取公式集合
     *
     * @param queryVO 公式查询对象
     * @return 公式集合
     */
    List<DtoTestFormula> findSheetConfigFormula(FormulaQueryVO queryVO);

    /**
     * 根据ids获取公式集合
     *
     * @param ids 公式ids
     * @return 公式集合
     */
    List<DtoTestFormula> findAllByIds(Collection<String> ids);

    /**
     * 根据 测试项目ids 获取公式
     *
     * @param testIds               测试项目ids
     * @param isLoadTransientFields 是否详细信息
     * @return 公式集合
     */
    List<DtoTestFormula> findByTestIds(Collection<String> testIds, Boolean isLoadTransientFields);
}
