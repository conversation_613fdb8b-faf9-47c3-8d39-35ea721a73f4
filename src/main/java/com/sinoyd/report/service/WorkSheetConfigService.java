package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoWorkSheetConfig;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetConfigService extends LimsBaseService<DtoWorkSheetConfig, String> {


    /**
     * 根据测试项目id集合查询所有关联数据
     *
     * @param testIds 测试项目id集合
     * @return 关联数据
     */
    List<DtoWorkSheetConfig> findByTestIds(Collection<String> testIds);

    /**
     * 根据报告编号集合查询所有关联数据
     *
     * @param reportCodes           报告编号集合
     * @param isLoadTransientFields 是否加载非数据库字段
     * @return 关联数据
     */
    List<DtoWorkSheetConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields);
}
