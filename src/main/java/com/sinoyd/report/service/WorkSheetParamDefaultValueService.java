package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoWorkSheetParamDefaultValue;
import com.sinoyd.report.vo.WorkSheetParamDefaultValueQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数默认值数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetParamDefaultValueService extends LimsBaseService<DtoWorkSheetParamDefaultValue, String> {

    /**
     * 根据原始记录单参数ID查询原始记录单参数默认值列表
     *
     * @param queryVO 查询条件
     * @return  原始记录单参数默认值列表
     */
    List<DtoWorkSheetParamDefaultValue> findList(WorkSheetParamDefaultValueQueryVO queryVO);

    /**
     * 根据原始记录单参数ID删除原始记录单参数默认值
     *
     * @param workSheetParamIds 原始记录单参数ID集合
     */
    void deleteByWorkSheetParamIdIn(Collection<String> workSheetParamIds);

    /**
     * 根据原始记录单参数查找默认值信息
     *
     * @param workSheetParamIds 原始记录单参数ID集合
     * @return 原始记录单参数默认值信息集合
     */
    List<DtoWorkSheetParamDefaultValue> findByWorkSheetParamIdIn(Collection<String> workSheetParamIds);

}
