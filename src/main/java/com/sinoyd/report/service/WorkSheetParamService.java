package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoWorkSheetParam;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录单参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetParamService extends LimsBaseService<DtoWorkSheetParam, String> {

    /**
     * 根据原始记录单配置ID删除
     *
     * @param workSheetConfigIds 原始记录单配置ID集合
     */
    void deleteByWorkSheetConfigIdIn(Collection<String> workSheetConfigIds);

    /**
     * 根据原始记录单配置ID和类别查询
     *
     * @param workSheetConfigId 原始记录单配置ID
     * @param category          类别
     * @return 原始记录单参数数据
     */
    List<DtoWorkSheetParam> findByWorkSheetConfigIdAndCategory(String workSheetConfigId, Integer category);

    /**
     * 根据原始记录单配置Ids获取参数数据
     *
     * @param workSheetConfigIds 原始记录单配置ids
     * @return 原始记录单参数数据
     */
    List<DtoWorkSheetParam> findByWorkSheetConfigIds(Collection<String> workSheetConfigIds);

    /**
     * 根据参数ids获取参数集合
     *
     * @param ids 参数ids
     * @return 参数集合
     */
    List<DtoWorkSheetParam> findByIds(Collection<String> ids);
}
