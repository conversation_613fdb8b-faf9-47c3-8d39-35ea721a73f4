package com.sinoyd.report.service;

import com.sinoyd.base.service.LimsBaseService;
import com.sinoyd.report.dto.DtoWorkSheetTest;

import java.util.Collection;
import java.util.List;

/**
 * 原始记录测试项项服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
public interface WorkSheetTestService extends LimsBaseService<DtoWorkSheetTest, String> {

    /**
     * 根据分析记录单id查询数据
     *
     * @param workSheetIds 分析记录单ids
     * @return List<DtoWorkSheetTest>
     */
    List<DtoWorkSheetTest> findByWorkSheetIds(List<String> workSheetIds);

    /**
     * 根据原始记录单配置id删除数据
     *
     * @param workSheetConfigIds 原始记录单配置ids
     */
    void deleteByWorkSheetConfigIdIn(Collection<String> workSheetConfigIds);

    /**
     * 根据测试项目id集合查询所有关联数据
     *
     * @param testIds 测试项目id集合
     * @return 关联数据
     */
    List<DtoWorkSheetTest> findByTestIds(Collection<String> testIds);

}
