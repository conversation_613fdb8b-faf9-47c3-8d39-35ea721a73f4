package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiColumn;
import com.sinoyd.report.repository.ApiColumnRepository;
import com.sinoyd.report.service.ApiColumnService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * API数据列配置服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@Service
public class ApiColumnServiceImpl
        extends LimsBaseServiceImpl<DtoApiColumn, String, ApiColumnRepository>
        implements ApiColumnService {

    /**
     * 根据Api接口ID集合查询关联数据列
     *
     * @param apiIds Api接口ID集合
     * @return 关联数据列
     */
    @Override
    public List<DtoApiColumn> findByApiIdIn(Collection<String> apiIds) {
        if (StringUtils.isNotEmpty(apiIds)){
            return repository.findByApiIdIn(apiIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据API实体传参保存结果列
     *
     * @param api API实体
     * @return 保存后的结果列
     */
    @Override
    @Transactional
    public List<DtoApiColumn> saveByApi(DtoApi api) {
        //删除旧关联数据（真删）
        List<DtoApiColumn> oldApiColumns = repository.findByApiIdIn(Collections.singletonList(api.getId()));
        delete(oldApiColumns);
        //保存新数据
        List<DtoApiColumn> apiColumns = api.getApiColumns();
        if (StringUtils.isNotEmpty(apiColumns)){
            apiColumns.forEach(p->p.setApiId(api.getId()));
        }
        return save(apiColumns);
    }

    /**
     * 根据Api接口ID集合删除关联数据列
     *
     * @param apiIds Api接口ID集合
     */
    @Override
    @Transactional
    public void deleteByApiIdIn(Collection<String> apiIds) {
        if (StringUtils.isNotEmpty(apiIds)){
            //查询关联数据
            List<DtoApiColumn> apiColumns = repository.findByApiIdIn(apiIds);
            //删除数据（假删）
            List<String> apiColumnIds = apiColumns.stream().map(DtoApiColumn::getId).collect(Collectors.toList());
            logicDeleteById(apiColumnIds);
        }
    }
}
