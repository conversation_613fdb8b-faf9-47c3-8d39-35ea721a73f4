package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiParam;
import com.sinoyd.report.repository.ApiParamRepository;
import com.sinoyd.report.service.ApiParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * API接口参数配置服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/04
 */
@Service
public class ApiParamServiceImpl
        extends LimsBaseServiceImpl<DtoApiParam, String, ApiParamRepository>
        implements ApiParamService {


    /**
     * 根据ApiID集合查询关联参数数据
     *
     * @param apiIds ApiID集合
     * @return 关联参数数据
     */
    @Override
    public List<DtoApiParam> findByApiIdIn(Collection<String> apiIds) {
        if (StringUtils.isNotEmpty(apiIds)) {
            return repository.findByApiIdIn(apiIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据接口实体传参保存接口参数数据
     *
     * @param api 接口实体
     * @return 保存后的接口参数数据
     */
    @Override
    @Transactional
    public List<DtoApiParam> saveByApi(DtoApi api) {
        //删除原有参数数据（真删）
        List<DtoApiParam> oldApiParams = repository.findByApiIdIn(Collections.singletonList(api.getId()));
        delete(oldApiParams);
        //保存新的参数数据
        List<DtoApiParam> requestParams = api.getApiParams();
        if (StringUtils.isNotEmpty(requestParams)) {
            requestParams.forEach(p -> p.setApiId(api.getId()));
        }
        return save(requestParams);
    }

    /**
     * 根据API的ID集合删除参数数据
     *
     * @param apiIds API的ID集合
     */
    @Override
    @Transactional
    public void deleteByApiIdIn(Collection<String> apiIds) {
        if (StringUtils.isNotEmpty(apiIds)) {
            //查询到关联参数数据
            List<DtoApiParam> apiParams = repository.findByApiIdIn(apiIds);
            //删除关联数据（假删）
            List<String> apiParamsIds = apiParams.stream().map(DtoApiParam::getId).collect(Collectors.toList());
            logicDeleteById(apiParamsIds);
        }
    }
}
