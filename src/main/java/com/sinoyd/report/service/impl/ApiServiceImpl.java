package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.config.WebParamConfig;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoApiColumn;
import com.sinoyd.report.dto.DtoApiParam;
import com.sinoyd.report.entity.ApiColumn;
import com.sinoyd.report.entity.ApiParam;
import com.sinoyd.report.enums.EnumApiType;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.ApiRepository;
import com.sinoyd.report.service.ApiColumnService;
import com.sinoyd.report.service.ApiParamService;
import com.sinoyd.report.service.ApiService;
import com.sinoyd.report.util.ExcelApiExecuteUtil;
import com.sinoyd.report.vo.ApiExecuteVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * API接口配置服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@Service
public class ApiServiceImpl
        extends LimsBaseServiceImpl<DtoApi, String, ApiRepository>
        implements ApiService {

    private WebParamConfig webParamConfig;

    private ApiParamService apiParamService;

    private ApiColumnService apiColumnService;

    @Override
    public void findByPage(PageBean<DtoApi> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoApi a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public DtoApi save(DtoApi entity) {
        DtoApi save = super.save(entity);
        //关联保存参数数据、
        save.setApiParams(apiParamService.saveByApi(save));
        //关联保存数据列数据
        save.setApiColumns(apiColumnService.saveByApi(save));
        return save;
    }

    @Override
    @Transactional
    public DtoApi update(DtoApi entity) {
        DtoApi update = super.update(entity);
        //关联保存参数数据、
        update.setApiParams(apiParamService.saveByApi(update));
        //关联保存数据列数据
        update.setApiColumns(apiColumnService.saveByApi(update));
        return update;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        //发布删除事件
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.REPORT_API, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    /**
     * 验证API接口并返回结果列
     *
     * @param api 接口信息
     * @return 执行结果列
     */
    @Override
    public List<DtoApiColumn> verifyApi(DtoApi api) {
        List<DtoApiColumn> originColumns = api.getApiColumns();
        //获取验证对象
        ApiExecuteVO verifyVO = getVerifyVO(api);
        //验证接口连通性
        //校验Sql并返回Sql执行列
        List<DtoApiColumn> resultColumns = ExcelApiExecuteUtil.verifyApi(verifyVO);
        if (StringUtils.isNotEmpty(originColumns) && StringUtils.isNotEmpty(resultColumns)) {
            Map<String, List<DtoApiColumn>> columnMap = originColumns.stream().collect(Collectors.groupingBy(DtoApiColumn::getColumnCode));
            for (DtoApiColumn resultColumn : resultColumns) {
                Optional<DtoApiColumn> originCol = columnMap.getOrDefault(resultColumn.getColumnCode(), new ArrayList<>()).stream().findFirst();
                originCol.ifPresent(apiColumn -> BeanUtils.copyProperties(apiColumn, resultColumn));
            }
        }
        return resultColumns;
    }

    /**
     * 加载冗余属性
     *
     * @param collection 记录集合
     */
    @Override
    public void loadTransientFields(Collection<DtoApi> collection) {
        //获取API接口ID集合
        List<String> apiIds = collection.stream().map(DtoApi::getId).collect(Collectors.toList());
        //获取关联参数
        Map<String, List<DtoApiParam>> apiParamsGroup = apiParamService.findByApiIdIn(apiIds)
                .stream().collect(Collectors.groupingBy(ApiParam::getApiId));
        //获取关联数据列
        Map<String, List<DtoApiColumn>> apiColumnsGroup = apiColumnService.findByApiIdIn(apiIds)
                .stream().collect(Collectors.groupingBy(ApiColumn::getApiId));
        //冗余关联数据
        for (DtoApi api : collection) {
            List<DtoApiParam> params = apiParamsGroup.getOrDefault(api.getId(), new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(DtoApiParam::getOrderNum)).collect(Collectors.toList());
            api.setApiParams(params);
            List<DtoApiColumn> columns = apiColumnsGroup.getOrDefault(api.getId(), new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(DtoApiColumn::getOrderNum)).collect(Collectors.toList());
            api.setApiColumns(columns);
            //设置API接口类型
            api.setApiTypeName(EnumApiType.getByValue(api.getApiType()).name());
        }
    }

    /**
     * 获取API接口验证对象
     *
     * @param api 接口数据实体
     * @return 接口验证对象
     */
    private ApiExecuteVO getVerifyVO(DtoApi api) {
        ApiExecuteVO verifyVO = new ApiExecuteVO(webParamConfig.getApiHost(), api , getRequestParams(api.getApiParams()));
        if (StringUtils.isNotEmpty(api.getAuthorizationApiId())) {
            DtoApi tokenApi = findOne(api.getAuthorizationApiId());
            verifyVO.setAuthorizationApi(getVerifyVO(tokenApi));
        }
        return verifyVO;
    }

    /**
     * 获取请求参数数据
     *
     * @param params api接口参数
     * @return 请求参数
     */
    private List<NameValuePair> getRequestParams(List<DtoApiParam> params) {
        List<NameValuePair> requestParams = new ArrayList<>();
        for (DtoApiParam param : params) {
            String value = StringUtils.isNotEmpty(param.getParamValue()) ? param.getParamValue() : "";
            NameValuePair requestParam = new NameValuePair(param.getParamName(), value);
            requestParams.add(requestParam);
        }
        return requestParams;
    }

    @Autowired
    public void setWebParamConfig(WebParamConfig webParamConfig) {
        this.webParamConfig = webParamConfig;
    }

    @Autowired
    @Lazy
    public void setApiParamService(ApiParamService apiParamService) {
        this.apiParamService = apiParamService;
    }

    @Autowired
    @Lazy
    public void setApiColumnService(ApiColumnService apiColumnService) {
        this.apiColumnService = apiColumnService;
    }
}
