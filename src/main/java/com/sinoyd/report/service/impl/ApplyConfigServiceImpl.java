package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.dto.DtoApplyConfig;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.repository.ApplyConfigRepository;
import com.sinoyd.report.service.ApplyConfigService;
import com.sinoyd.report.service.BaseConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础配置应用服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class ApplyConfigServiceImpl extends LimsBaseServiceImpl<DtoApplyConfig, String, ApplyConfigRepository>
        implements ApplyConfigService {

    private BaseConfigService baseConfigService;

    @Override
    public void findByPage(PageBean<DtoApplyConfig> pb, BaseCriteria criteria) {
        pb.setEntityName("DtoApplyConfig a, DtoBaseConfig c");
        pb.setSelect("select a,c.orderNum");
        comRepository.findByPage(pb, criteria);
        List<DtoApplyConfig> dataList = pb.getData();
        List<DtoApplyConfig> newDataList = new ArrayList<>();

        Iterator<DtoApplyConfig> iterator = dataList.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (iterator.hasNext()) {
            Object obj = iterator.next();
            Object[] objData = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoApplyConfig reportApply = (DtoApplyConfig) objData[0];
            reportApply.setOrderNum((Integer) objData[1]);
            newDataList.add(reportApply);
        }
        newDataList = newDataList.stream().sorted(Comparator.comparing(DtoApplyConfig::getOrderNum).reversed()).collect(Collectors.toList());
        loadTransientFields(newDataList);
        pb.setData(newDataList);
    }

    @Override
    public void loadTransientFields(Collection<DtoApplyConfig> dataList) {
        List<String> reportCodes = dataList.stream().map(DtoApplyConfig::getReportCode).distinct().collect(Collectors.toList());
        List<DtoBaseConfig> baseConfigs = StringUtils.isNotEmpty(reportCodes) ? baseConfigService.findByReportCodes(reportCodes) : new ArrayList<>();
        dataList.forEach(data -> {
            baseConfigs.stream().filter(b -> b.getReportCode().equals(data.getReportCode())).findFirst().ifPresent(b -> {
                data.setReportName(b.getTemplateName());
                data.setReportTypeValue(b.getReportTypeValue());
            });
        });
    }

    @Autowired
    @Lazy
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }
}
