package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.report.dto.DtoAreaConfig2DataSetApplyColumn;
import com.sinoyd.report.dto.DtoDataSetApplyColumn;
import com.sinoyd.report.repository.AreaConfig2DataSetApplyColumnRepository;
import com.sinoyd.report.repository.DataSetApplyColumnRepository;
import com.sinoyd.report.service.AreaConfig2DataSetApplyColumnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 区域配置与数据集列应用映射数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/18
 */
@Service
public class AreaConfig2DataSetApplyColumnServiceImpl extends LimsBaseServiceImpl<DtoAreaConfig2DataSetApplyColumn, String, AreaConfig2DataSetApplyColumnRepository>
        implements AreaConfig2DataSetApplyColumnService {

    private DataSetApplyColumnRepository dataSetApplyColumnRepository;

    private CodeService codeService;

    @Override
    public void findByPage(PageBean<DtoAreaConfig2DataSetApplyColumn> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoAreaConfig2DataSetApplyColumn a");
        pageBean.setSelect("DtoAreaConfig2DataSetApplyColumn a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    public List<DtoAreaConfig2DataSetApplyColumn> findByAreaConfigIdIn(Collection<String> areaConfigIds) {
        if (StringUtils.isNotEmpty(areaConfigIds)){
            List<DtoAreaConfig2DataSetApplyColumn> result = repository.findByAreaConfigIdIn(areaConfigIds);
            loadTransientFields(result);
            return result;
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void deleteByDataSetApplyColumnIds(Collection<String> applyIds) {
        List<DtoAreaConfig2DataSetApplyColumn> config2ApplyCol = repository.findByDataSetApplyColumnIdIn(applyIds);
        List<String> deleteIds = config2ApplyCol.stream().map(DtoAreaConfig2DataSetApplyColumn::getId).collect(Collectors.toList());
        logicDeleteById(deleteIds);
    }

    @Override
    public void loadTransientFields(Collection<DtoAreaConfig2DataSetApplyColumn> collection) {
        Set<String> setApplyIds = collection.stream().map(DtoAreaConfig2DataSetApplyColumn::getDataSetApplyColumnId).collect(Collectors.toSet());
        List<DtoDataSetApplyColumn> dataSetApplyColumns = StringUtils.isNotEmpty(setApplyIds) ? dataSetApplyColumnRepository.findAllById(setApplyIds) : new ArrayList<>();
        List<DtoCode> businessTypes = new ArrayList<>();
        try {
            businessTypes = codeService.findCodes("lims_report_dataSetApplyBusinessType");
        } catch (Exception e) {
            e.printStackTrace();
        }
        final List<DtoCode> finalBusinessTypes = businessTypes;
        collection.forEach(data -> dataSetApplyColumns.stream().filter(d -> d.getId().equals(data.getDataSetApplyColumnId())).findFirst().ifPresent(d -> {
            finalBusinessTypes.stream().filter(b -> b.getDictValue().equals(d.getBusinessType())).findFirst().ifPresent(b -> data.setBusinessTypeName(b.getDictName()));
            data.setBusinessType(d.getBusinessType());
            data.setColumnName(d.getColumnName());
            data.setColumnCode(d.getColumnCode());
            data.setPlaceholderName(d.getPlaceholderName());
            data.setRemark(d.getRemark());
        }));
    }

    @Autowired
    public void setDataSetApplyColumnRepository(DataSetApplyColumnRepository dataSetApplyColumnRepository) {
        this.dataSetApplyColumnRepository = dataSetApplyColumnRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
