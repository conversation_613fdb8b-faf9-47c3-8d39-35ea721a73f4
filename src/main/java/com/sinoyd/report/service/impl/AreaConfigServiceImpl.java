package com.sinoyd.report.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.jsoniter.JsonIterator;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.enums.EnumAreaType;
import com.sinoyd.report.enums.EnumSheetPageRules;
import com.sinoyd.report.repository.AreaConfigRepository;
import com.sinoyd.report.repository.SheetConfigRepository;
import com.sinoyd.report.repository.SheetPagingConfigRepository;
import com.sinoyd.report.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表区域参数配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Service
@Slf4j
public class AreaConfigServiceImpl extends LimsBaseServiceImpl<DtoAreaConfig, String, AreaConfigRepository> implements AreaConfigService {

    private AreaConfig2DataSetApplyColumnService areaConfig2DataSetApplyColumnService;
    private AreaExpandConfigService areaExpandConfigService;
    private BaseConfigService baseConfigService;

    private SheetConfigService sheetConfigService;
    private SheetPagingConfigService sheetPagingConfigService;

    /**
     * 保存报表区域配置数据
     *
     * @param entities 报表区域配置
     * @return 已保存的区域配置数据
     */
    @Override
    @Transactional
    public List<DtoAreaConfig> save(Collection<DtoAreaConfig> entities) {
        List<String> reportCodes = entities.stream().map(DtoAreaConfig::getReportCode).distinct().collect(Collectors.toList());
        //先删除报表配置对应的所有区域配置数据(真删)
        List<DtoAreaConfig> deleteAreaConfig = repository.findByReportCodeIn(reportCodes);
        delete(deleteAreaConfig);
        //重新保存区域配置数据
        return super.save(entities);
    }

    @Override
    public DtoAreaConfig findOne(String key) {
        DtoAreaConfig areaConfig = super.findOne(key);
        if (StringUtils.isNotNull(areaConfig)) {
            areaConfig.setDataSetApplyColumns(areaConfig2DataSetApplyColumnService.findByAreaConfigIdIn(Collections.singleton(key)));
            DtoAreaExpandConfig expandConfig = areaExpandConfigService.findByAreaId(areaConfig.getId());
            areaConfig.setAreaExpandConfig(expandConfig);
        }
        return areaConfig;
    }

    @Override
    @Transactional
    public DtoAreaConfig save(DtoAreaConfig entity) {
        List<DtoAreaConfig2DataSetApplyColumn> setApplyColumns = entity.getDataSetApplyColumns();
        if (StringUtils.isNotEmpty(setApplyColumns)) {
            setApplyColumns.forEach(s -> s.setAreaConfigId(entity.getId()));
            areaConfig2DataSetApplyColumnService.save(setApplyColumns);
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAreaConfig update(DtoAreaConfig entity) {
        //删除原有关联数据（真删）
        List<DtoAreaConfig2DataSetApplyColumn> oldData = areaConfig2DataSetApplyColumnService
                .findByAreaConfigIdIn(Collections.singletonList(entity.getId()));
        areaConfig2DataSetApplyColumnService.delete(oldData);
        //保存新数据
        List<DtoAreaConfig2DataSetApplyColumn> setApplyColumns = entity.getDataSetApplyColumns();
        if (StringUtils.isNotEmpty(setApplyColumns)) {
            setApplyColumns.forEach(s -> s.setAreaConfigId(entity.getId()));
            areaConfig2DataSetApplyColumnService.save(setApplyColumns);
        }
        return super.update(entity);
    }

    /**
     * 根据报表编码查询所有区域配置数据
     *
     * @param reportCode 报表编码
     * @return 区域配置数据
     */
    @Override
    public List<DtoAreaConfig> findByReportCode(String reportCode) {
        List<DtoAreaConfig> data = repository.findByReportCode(reportCode);
        loadTransientFields(data);
        if (StringUtils.isNotEmpty(data)) {
            //排序: 排序值倒序->区域类型正序->Sheet页名称正序
            data.sort(Comparator.comparing(DtoAreaConfig::getOrderNum, Comparator.reverseOrder())
                    .thenComparing(DtoAreaConfig::getAreaType)
                    .thenComparing(DtoAreaConfig::getSheetName));
        }
        return data;
    }

    /**
     * 根据报表编码删除区域参数配置
     *
     * @param reportCodes 报表编码集合
     * @return 删除的条数
     */
    @Override
    @Transactional
    public Integer deleteByReportCodeIn(Collection<String> reportCodes) {
        List<DtoAreaConfig> delete = repository.findByReportCodeIn(reportCodes);
        List<String> deleteIds = delete.stream().map(DtoAreaConfig::getId).collect(Collectors.toList());
        return logicDeleteById(deleteIds);
    }

    /**
     * 获取区域类型
     *
     * @return 区域类型
     */
    @Override
    public List<String> findAreaTypes() {
        List<String> resultList = new ArrayList<>();
        for (EnumAreaType enumAreaType : EnumAreaType.values()) {
            resultList.add(enumAreaType.name());
        }
        return resultList;
    }

    @Override
    public Map<String, Object> getAreaConfigMap(String reportCode) {
        Map<String, Object> result = new HashMap<>();
        DtoBaseConfig baseConfig = baseConfigService.findByReportCode(reportCode);
        List<DtoAreaConfig> areaConfigs = repository.findByReportCode(reportCode).stream().sorted(Comparator.comparing(DtoAreaConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
        Set<String> areaConfigIds = areaConfigs.stream().map(DtoAreaConfig::getId).collect(Collectors.toSet());
        List<DtoAreaConfig2DataSetApplyColumn> dataSetApplyColumns = areaConfig2DataSetApplyColumnService.findByAreaConfigIdIn(areaConfigIds);
        List<String> configSheetNames = areaConfigs.stream().map(DtoAreaConfig::getSheetName).distinct().collect(Collectors.toList());
        List<String> sheetNames = StringUtils.isNotNull(baseConfig) ? baseConfigService.getSheetNames(baseConfig.getId()) : new ArrayList<>();
        configSheetNames.forEach(sheetName -> {
            if (!sheetNames.contains(sheetName)) {
                sheetNames.add(sheetName);
            }
        });
        List<Map<String, Object>> sheetList = new ArrayList<>();
        List<DtoSheetConfig> sheetConfigList = sheetConfigService.findByReportCode(reportCode);
        if (StringUtils.isEmpty(sheetConfigList)) {
            //sheet页配置不存在，则重新初始化
            for (int i = 0; i < sheetNames.size(); i++) {
                String sheetName = sheetNames.get(i);
                DtoSheetConfig sheetConfig = new DtoSheetConfig();
                sheetConfig.setOrderNum(i + 1);
                sheetConfig.setSheetName(sheetName);
                sheetConfig.setReportCode(StringUtils.isNotNull(baseConfig) ? baseConfig.getReportCode() : "");
                sheetConfig.setPageRules(EnumSheetPageRules.continuous.getValue());
                sheetConfigList.add(sheetConfig);
            }
        }
        Map<String, List<DtoSheetConfig>> sheetConfigMap = sheetConfigList.stream().collect(Collectors.groupingBy(DtoSheetConfig::getSheetName));
        for (String sheetName : sheetNames) {
            List<DtoAreaConfig> areaConfigs2Sheet = areaConfigs.stream().filter(a -> a.getSheetName().equals(sheetName)).collect(Collectors.toList());
            areaConfigs2Sheet.forEach(a -> a.setDataSetApplyColumns(dataSetApplyColumns.stream().filter(d -> d.getAreaConfigId().equals(a.getId())).collect(Collectors.toList())));
            Map<String, Object> map = new HashMap<>();
            map.put("sheetName", sheetName);
            map.put("areaConfigs", areaConfigs2Sheet);
            DtoSheetConfig sheetConfig = sheetConfigMap.containsKey(sheetName) ? sheetConfigMap.get(sheetName).get(0) : null;
            map.put("sheetConfig", sheetConfig);
            sheetList.add(map);
        }
        result.put(baseConfig.getTemplateName(), sheetList);
        return result;
    }

    /**
     * 加载冗余属性
     *
     * @param collection 记录集合
     */
    @Override
    public void loadTransientFields(Collection<DtoAreaConfig> collection) {
        Set<String> areaConfigIds = collection.stream().map(DtoAreaConfig::getId).collect(Collectors.toSet());
        List<DtoAreaConfig2DataSetApplyColumn> dataSetApplyColumns = areaConfig2DataSetApplyColumnService.findByAreaConfigIdIn(areaConfigIds);
        Map<String, List<DtoAreaConfig2DataSetApplyColumn>> applyColumnGroup = dataSetApplyColumns.stream()
                .collect(Collectors.groupingBy(DtoAreaConfig2DataSetApplyColumn::getAreaConfigId));
        for (DtoAreaConfig areaConfig : collection) {
            areaConfig.setDataSetApplyColumns(applyColumnGroup.getOrDefault(areaConfig.getId(), new ArrayList<>()));
        }
    }

    @Autowired
    @Lazy
    public void setAreaConfig2DataSetApplyColumnService(AreaConfig2DataSetApplyColumnService areaConfig2DataSetApplyColumnService) {
        this.areaConfig2DataSetApplyColumnService = areaConfig2DataSetApplyColumnService;
    }

    @Autowired
    @Lazy
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    @Lazy
    public void setSheetConfigService(SheetConfigService sheetConfigService) {
        this.sheetConfigService = sheetConfigService;
    }

    @Autowired
    @Lazy
    public void setSheetPagingConfigService(SheetPagingConfigService sheetPagingConfigService) {
        this.sheetPagingConfigService = sheetPagingConfigService;
    }

    @Autowired
    @Lazy
    public void setAreaExpandConfigService(AreaExpandConfigService areaExpandConfigService) {
        this.areaExpandConfigService = areaExpandConfigService;
    }


}
