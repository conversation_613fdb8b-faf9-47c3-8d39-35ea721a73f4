package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoAreaExpandConfig;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.repository.AreaExpandConfigRepository;
import com.sinoyd.report.service.AreaConfigService;
import com.sinoyd.report.service.AreaExpandConfigService;
import com.sinoyd.report.service.AreaExpandMergeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报表区域扩展配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@Service
@Slf4j
public class AreaExpandConfigServiceImpl extends LimsBaseServiceImpl<DtoAreaExpandConfig, String, AreaExpandConfigRepository> implements AreaExpandConfigService {

    private AreaExpandMergeConfigService areaExpandMergeConfigService;
    private AreaConfigService areaConfigService;

    /**
     * 保存区域扩展配置
     *
     * @param entity 区域扩展配置实体
     * @return 区域扩展配置
     */
    @Override
    @Transactional
    public DtoAreaExpandConfig save(DtoAreaExpandConfig entity) {
        List<DtoAreaExpandMergeConfig> mergeConfigList = entity.getAreaExpandMergeConfigList();
        if (StringUtils.isNotEmpty(mergeConfigList)) {
            mergeConfigList.forEach(p -> p.setAreaExpandConfigId(entity.getId()));
            areaExpandMergeConfigService.save(mergeConfigList);
        }
        return super.save(entity);
    }

    /**
     * 修改区域扩展配置
     *
     * @param entity 区域扩展配置实体
     * @return 区域扩展配置
     */
    @Override
    @Transactional
    public DtoAreaExpandConfig update(DtoAreaExpandConfig entity) {
        List<DtoAreaExpandMergeConfig> oldMergeConfigList = areaExpandMergeConfigService.findByExpandConfigId(entity.getId());
        if (StringUtils.isNotEmpty(oldMergeConfigList)) {
            List<String> oldMergeConfigIdList = oldMergeConfigList.stream().map(DtoAreaExpandMergeConfig::getId).collect(Collectors.toList());
            areaExpandMergeConfigService.logicDeleteById(oldMergeConfigIdList);
        }
        List<DtoAreaExpandMergeConfig> newMergeConfigList = entity.getAreaExpandMergeConfigList();
        if (StringUtils.isNotEmpty(newMergeConfigList)) {
            for (DtoAreaExpandMergeConfig mergeConfig : newMergeConfigList) {
                mergeConfig.setId(UUIDHelper.newId());
                mergeConfig.setAreaExpandConfigId(entity.getId());
            }
            areaExpandMergeConfigService.save(newMergeConfigList);
        }
        return super.update(entity);
    }

    /**
     * 根据主键获取区域扩展配置详情
     *
     * @param key 区域扩展配置id
     * @return 区域扩展配置对象
     */
    @Override
    public DtoAreaExpandConfig findOne(String key) {
        DtoAreaExpandConfig expandConfig = super.findOne(key);
        if (StringUtils.isNotNull(expandConfig)) {
            List<DtoAreaExpandMergeConfig> mergeConfigList = areaExpandMergeConfigService.findByExpandConfigId(expandConfig.getId());
            expandConfig.setAreaExpandMergeConfigList(mergeConfigList);
        }
        return expandConfig;
    }

    @Autowired
    @Lazy
    public void setAreaExpandMergeConfigService(AreaExpandMergeConfigService areaExpandMergeConfigService) {
        this.areaExpandMergeConfigService = areaExpandMergeConfigService;
    }

    /**
     * 根据区域id查询
     *
     * @param areaId 区域id
     * @return 区域扩展配置
     */
    @Override
    public DtoAreaExpandConfig findByAreaId(String areaId) {
        DtoAreaExpandConfig expandConfig = repository.findByAreaConfigId(areaId);
        if (StringUtils.isNotNull(expandConfig)) {
            List<DtoAreaExpandMergeConfig> mergeConfigList = areaExpandMergeConfigService.findByExpandConfigId(expandConfig.getId());
            expandConfig.setAreaExpandMergeConfigList(mergeConfigList);
        } else {
            expandConfig = new DtoAreaExpandConfig();
            expandConfig.setAreaConfigId(areaId);
        }
        return expandConfig;
    }

    @Override
    public List<DtoAreaExpandConfig> findByAreaIds(List<String> areaIdList) {
        List<DtoAreaExpandConfig> expandConfigList = repository.findByAreaConfigIdIn(areaIdList);
        if (StringUtils.isNotEmpty(expandConfigList)) {
            List<String> expandConfigIdList = expandConfigList.stream().map(DtoAreaExpandConfig::getId).collect(Collectors.toList());
            List<DtoAreaExpandMergeConfig> expandMergeConfigList = areaExpandMergeConfigService.findByExpandConfigIds(expandConfigIdList);
            Map<String, List<DtoAreaExpandMergeConfig>> mergeConfigMap = expandMergeConfigList.stream().collect(Collectors.groupingBy(DtoAreaExpandMergeConfig::getAreaExpandConfigId));
            for (DtoAreaExpandConfig expandConfig : expandConfigList) {
                if (mergeConfigMap.containsKey(expandConfig.getId())) {
                    expandConfig.setAreaExpandMergeConfigList(mergeConfigMap.get(expandConfig.getId()));
                }
            }
        }
        return expandConfigList;
    }

    @Autowired
    @Lazy
    public void setAreaConfigService(AreaConfigService areaConfigService) {
        this.areaConfigService = areaConfigService;
    }
}
