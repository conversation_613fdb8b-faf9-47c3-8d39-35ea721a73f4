package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoAreaExpandMergeConfig;
import com.sinoyd.report.repository.AreaExpandMergeConfigRepository;
import com.sinoyd.report.service.AreaExpandMergeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报表区域扩展合并配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/07
 */
@Service
@Slf4j
public class AreaExpandMergeConfigServiceImpl extends LimsBaseServiceImpl<DtoAreaExpandMergeConfig, String, AreaExpandMergeConfigRepository> implements AreaExpandMergeConfigService {

    /**
     * 根据扩展配置id查询
     *
     * @param expandConfigId 扩展配置id
     * @return 区域扩展合并配置列表
     */
    @Override
    public List<DtoAreaExpandMergeConfig> findByExpandConfigId(String expandConfigId) {
        return repository.findByAreaExpandConfigId(expandConfigId);
    }

    /**
     * 根据扩展配置id列表查询
     *
     * @param expandConfigIdList 扩展配置id列表
     * @return 区域扩展合并配置列表
     */
    @Override
    public List<DtoAreaExpandMergeConfig> findByExpandConfigIds(List<String> expandConfigIdList) {
        return repository.findByAreaExpandConfigIdIn(expandConfigIdList);
    }
}
