package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoBaseConfig2Module;
import com.sinoyd.report.dto.DtoReportModule;
import com.sinoyd.report.dto.DtoReportModule2GroupType;
import com.sinoyd.report.repository.BaseConfig2ModuleRepository;
import com.sinoyd.report.service.BaseConfig2ModuleService;
import com.sinoyd.report.service.ReportModule2GroupTypeService;
import com.sinoyd.report.service.ReportModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报告组件配置关联关系操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Service
public class BaseConfig2ModuleServiceImpl extends LimsBaseServiceImpl<DtoBaseConfig2Module, String, BaseConfig2ModuleRepository> implements BaseConfig2ModuleService {

    private ReportModule2GroupTypeService reportModule2GroupTypeService;
    private ReportModuleService reportModuleService;

    @Override
    public List<DtoBaseConfig2Module> queryByBaseConfigId(String baseConfigId) {
        List<DtoBaseConfig2Module> reportConfig2ModuleList = repository.findByBaseConfigId(baseConfigId);
        if (StringUtils.isNotEmpty(reportConfig2ModuleList)) {
            List<String> moduleIdList = reportConfig2ModuleList.stream().map(DtoBaseConfig2Module::getReportModuleId).distinct().collect(Collectors.toList());
            List<DtoReportModule> moduleList = reportModuleService.findAll(moduleIdList);
            Map<String, DtoReportModule> moduleMap = moduleList.stream().collect(Collectors.toMap(DtoReportModule::getId, dto -> dto));
            for (DtoBaseConfig2Module config2Module : reportConfig2ModuleList) {
                DtoReportModule module = moduleMap.get(config2Module.getReportModuleId());
                if (StringUtils.isNotNull(module)) {
                    config2Module.setModuleCode(module.getModuleCode());
                    config2Module.setModuleName(module.getModuleName());
                    config2Module.setTableName(module.getTableName());
                    config2Module.setSourceTableName(module.getSourceTableName());
                }
            }
            return reportConfig2ModuleList;
        }
        return new ArrayList<>();
    }

    @Override
    public DtoReportModule findModuleInfo(String id) {
        Optional<DtoBaseConfig2Module> baseConfig2ModuleOptional = repository.findById(id);
        if (baseConfig2ModuleOptional.isPresent()) {
            DtoBaseConfig2Module baseConfig2Module = baseConfig2ModuleOptional.get();
            DtoReportModule module = reportModuleService.findOne(baseConfig2Module.getReportModuleId());
            if (module != null) {
                //获取分页方式配置信息
                List<DtoReportModule2GroupType> reportModule2GroupTypeList = reportModule2GroupTypeService.findByBaseConfigModuleId(id);
                module.setReportModule2GroupTypeList(reportModule2GroupTypeList);
                return module;
            }
        }
        return null;
    }

    /**
     * 新增报告和组件的关联关系
     *
     * @param entity 前端传递的实体
     * @return 新增的实体
     */
    @Override
    @Transactional
    public DtoBaseConfig2Module save(DtoBaseConfig2Module entity) {
        DtoBaseConfig2Module config2Module = new DtoBaseConfig2Module();
        config2Module.setBaseConfigId(entity.getBaseConfigId());
        config2Module.setReportModuleId(entity.getReportModuleId());
        //新增报告组件关联关系
        DtoBaseConfig2Module instConfig2Module = repository.save(config2Module);
        //新增分页方式配置
        List<DtoReportModule2GroupType> reportModule2GroupTypeList = entity.getReportModule2GroupTypeList();
        if (StringUtils.isNotEmpty(reportModule2GroupTypeList)) {
            for (DtoReportModule2GroupType reportModule2GroupType : reportModule2GroupTypeList) {
                reportModule2GroupType.setBaseConfigModuleId(instConfig2Module.getId());
            }
            instConfig2Module.setReportModule2GroupTypeList(reportModule2GroupTypeList);
            reportModule2GroupTypeService.save(reportModule2GroupTypeList);
        }
        return instConfig2Module;
    }

    @Override
    @Transactional
    public DtoBaseConfig2Module update(DtoBaseConfig2Module entity) {
        Optional<DtoBaseConfig2Module> oldConfig2ModuleOptional = repository.findById(entity.getId());
        if (!oldConfig2ModuleOptional.isPresent()) {
            throw new BaseException("报告组件关联关系不存在！");
        }
        DtoBaseConfig2Module oldConfig2Module = oldConfig2ModuleOptional.get();
        oldConfig2Module.setReportModuleId(entity.getReportModuleId());
        repository.save(oldConfig2Module);
        //先删除旧的分页方式配置
        List<DtoReportModule2GroupType> oldGroupTypeList = reportModule2GroupTypeService.findByBaseConfigModuleId(entity.getId());
        if (StringUtils.isNotEmpty(oldGroupTypeList)) {
            reportModule2GroupTypeService.delete(oldGroupTypeList);
        }
        //添加新的分页方式配置
        List<DtoReportModule2GroupType> newGroupTypeList = entity.getReportModule2GroupTypeList();
        if (StringUtils.isNotEmpty(newGroupTypeList)) {
            for (DtoReportModule2GroupType newGroupType : newGroupTypeList) {
                newGroupType.setBaseConfigModuleId(entity.getId());
            }
            reportModule2GroupTypeService.save(newGroupTypeList);
            entity.setReportModule2GroupTypeList(newGroupTypeList);
        }
        return entity;
    }

    @Override
    @Transactional
    public int deleteConfig2Module(List<String> ids) {
        List<DtoBaseConfig2Module> config2ModuleList = repository.findAllById(ids);
        if (StringUtils.isNotEmpty(config2ModuleList)) {
            List<String> config2ModuleIdList = config2ModuleList.stream().map(DtoBaseConfig2Module::getId).collect(Collectors.toList());
            //删除分页方式配置
            List<DtoReportModule2GroupType> module2GroupTypeList = reportModule2GroupTypeService.findByBaseConfigModuleIdIn(config2ModuleIdList);
            if (StringUtils.isNotEmpty(module2GroupTypeList)) {
                reportModule2GroupTypeService.delete(module2GroupTypeList);
            }
            //删除报告组件关联关系
            return repository.logicDeleteById(ids);
        }
        return 0;
    }

    @Autowired
    @Lazy
    public void setReportModule2GroupTypeService(ReportModule2GroupTypeService reportModule2GroupTypeService) {
        this.reportModule2GroupTypeService = reportModule2GroupTypeService;
    }

    @Autowired
    @Lazy
    public void setReportModuleService(ReportModuleService reportModuleService) {
        this.reportModuleService = reportModuleService;
    }
}
