package com.sinoyd.report.service.impl;

import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.preview.DocumentPreviewFactory;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.*;
import com.sinoyd.base.enums.EnumReportConfigType;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.BaseConfigRepository;
import com.sinoyd.report.service.*;
import com.sinoyd.report.utils.ReportFileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表基础配置接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Service
public class BaseConfigServiceImpl extends LimsBaseServiceImpl<DtoBaseConfig, String, BaseConfigRepository> implements BaseConfigService {

    private DocumentService documentService;

    private FilePropertyConfig filePropertyConfig;

    private SamplingConfigService samplingConfigService;

    private WorkSheetConfigService workSheetConfigService;

    /**
     * 分页查询报表基础配置
     *
     * @param page     分页参数
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoBaseConfig> page, BaseCriteria criteria) {
        page.setEntityName("DtoBaseConfig bc");
        page.setSelect("select bc");
        super.findByPage(page, criteria);
    }

    /**
     * 新增数据
     *
     * @param entity 报表模板配置实体
     * @return 新增后的数据
     */
    @Override
    @Transactional
    public DtoBaseConfig save(DtoBaseConfig entity) {
        //判断数据是否可以保存
        judgeSave(entity);
        return super.save(entity);
    }

    /**
     * 更新数据
     *
     * @param entity 报表模板配置实体
     * @return 更新后数据
     */
    @Override
    @Transactional
    public DtoBaseConfig update(DtoBaseConfig entity) {
        //判断数据是否可以保存
        judgeSave(entity);
        //发布基础配置更新事件
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(entity, IReportEventName.BASE_CONFIG, IEventAction.UPDATE));
        return super.update(entity);
    }

    /**
     * 批量删除报表基础配置数据
     *
     * @param strings 需要删除的配置id集合
     * @return 删除的条数
     */
    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        //判断数据是否可以删除
        validateDelete(strings);
        //根据id查询到所有的报表编码
        List<DtoBaseConfig> baseConfigList = repository.findAllById(strings);
        List<String> reportCodes = baseConfigList.stream().map(DtoBaseConfig::getReportCode).collect(Collectors.toList());
        //发布事件删除所有子表记录
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(reportCodes, IReportEventName.BASE_CONFIG, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    public void loadTransientFields(Collection<DtoBaseConfig> collection) {
        //获取到所有的附件数据
        Set<String> configIds = collection.stream().map(DtoBaseConfig::getId).collect(Collectors.toSet());
        Map<String, List<DtoDocument>> documentGroupByObjId = documentService.findByObjectIdIn(configIds).stream().collect(Collectors.groupingBy(DtoDocument::getObjectId));
        for (DtoBaseConfig baseConfig : collection) {
            //设置模板附件id
            Optional<DtoDocument> templateDocOp = documentGroupByObjId.getOrDefault(baseConfig.getId(), new ArrayList<>()).stream().findFirst();
            templateDocOp.ifPresent(p -> baseConfig.setDocumentId(p.getId()));
        }
    }

    /**
     * 获取附件路径
     *
     * @param configId 配置id
     * @return 路径
     */
    @Override
    public String getDocumentAttachPath(String configId) {
        String typeCode = "";
        DtoBaseConfig reportConfig = super.findOne(configId);
        if (StringUtils.isNotNull(reportConfig)) {
            typeCode = EnumReportConfigType.getTypeCodeByValue(reportConfig.getReportTypeValue());
        }
        return typeCode;
    }

    /**
     * 根据报表编码查询报表配置数据
     *
     * @param reportCodes 报表编码
     * @return 报表配置数据
     */
    @Override
    public List<DtoBaseConfig> findByReportCodes(Collection<String> reportCodes) {
        if (StringUtils.isNotEmpty(reportCodes)) {
            return repository.findByReportCodeIn(reportCodes);
        }
        return new ArrayList<>();
    }

    /**
     * 根据报表编码查询报表配置数据
     *
     * @param reportCode 报表编码
     * @return 报表配置数据
     */
    @Override
    public DtoBaseConfig findByReportCode(String reportCode) {
        return repository.findByReportCode(reportCode);
    }


    /**
     * 文件上传
     *
     * @param request 文件上传对象
     */
    @Override
    @Transactional
    public List<DtoDocument> upload(HttpServletRequest request) {
        return documentService.uploadFile(request, filePropertyConfig.getTemplatePath(), true);
    }

    @Override
    @Transactional
    public void downloadReport(String configId, HttpServletResponse response) {
        DtoBaseConfig config = findOne(configId);
        if (StringUtils.isNull(config)) {
            throw new BaseException("未找到报表模板配置信息!");
        }
        if (StringUtils.isNotEmpty(config.getTemplatePath())) {
            String path = filePropertyConfig.getTemplatePath() + ReportFileUtil.FILE_SEPARATOR + config.getTemplatePath();
            FileUtil.download(path, config.getTemplateName(), response);
        } else {
            Optional<DtoDocument> document = documentService.findByObjectId(configId).stream()
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (document.isPresent()) {
                documentService.download(document.get().getId(), response);
            } else {
                throw new BaseException("请上传模板或者添加模板文件路径!");
            }
        }
    }

    @Override
    public List<String> getSheetNames(String configId) {
        List<String> sheetNames = new ArrayList<>();
        DtoBaseConfig config = findOne(configId);
        String path = "";
        if (StringUtils.isNotEmpty(config.getTemplatePath())) {
            path = filePropertyConfig.getTemplatePath() + ReportFileUtil.FILE_SEPARATOR + config.getTemplatePath();
        } else {
            Optional<DtoDocument> document = documentService.findByObjectId(configId).stream()
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (document.isPresent()) {
                path = filePropertyConfig.getTemplatePath() + ReportFileUtil.FILE_SEPARATOR + document.get().getFilePath();
            }
        }
        if (StringUtils.isNotEmpty(path)) {
            try {
                Workbook workbook = new Workbook(path);
                // 获取工作簿中所有工作表的数量
                int sheetCount = workbook.getWorksheets().getCount();
                // 遍历每个工作表并打印工作表名称
                for (int i = 0; i < sheetCount; i++) {
                    Worksheet sheet = workbook.getWorksheets().get(i);
                    String sheetName = sheet.getName();
                    sheetNames.add(sheetName);
                }
            } catch (Exception e) {
                return sheetNames;
            }
        }
        return sheetNames;
    }

    /**
     * 报表模板预览
     *
     * @param configId 报表配置id
     * @param response 响应流
     */
    @Override
    public void preview(String configId, HttpServletResponse response) {
        String rootPath = filePropertyConfig.getTemplatePath();
        DtoBaseConfig config = findOne(configId);
        if (StringUtils.isNull(config)) {
            throw new BaseException("未找到报表模板配置信息!");
        }
        DocumentPreviewVO previewVO = new DocumentPreviewVO();
        String fileName = config.getTemplateName();
        String path = config.getTemplatePath();
        if (StringUtils.isEmpty(path)) {
            Optional<DtoDocument> document = documentService.findByObjectId(configId).stream()
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (document.isPresent()) {
                path = document.get().getFilePath();
                fileName = document.get().getFileName();
            } else {
                throw new BaseException("未查询到模板路径, 请上传模板或者添加模板文件路径!");
            }
        }
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String sourcePath = ReportFileUtil.FILE_SEPARATOR + path;
        //判断模板文件是否存在
        File file = new File(rootPath + sourcePath);
        if (!file.exists()) {
            throw new RuntimeException(String.format("文件[%s]不存在!", rootPath + sourcePath));
        }
        previewVO.setSourceDocSuffix(suffix).setSourceFilePath(sourcePath).setSourceFileName(fileName);
        DocumentPreviewFactory.previewAsPDF(rootPath, previewVO, response);
    }

    /**
     * 校验数据是否可以删除
     *
     * @param strings 报表配置id集合
     */
    private void validateDelete(Collection<String> strings) {
        // 获取需要删除的报表配置
        List<DtoBaseConfig> baseConfigs = repository.findAllById(strings);
        if (StringUtils.isEmpty(baseConfigs)) {
            return;
        }

        // 提取报表编码集合
        List<String> reportCodes = baseConfigs.stream()
                .map(DtoBaseConfig::getReportCode)
                .collect(Collectors.toList());
        StringBuilder errorMsg = new StringBuilder();
        // 检查采样单配置关联
        List<DtoSamplingConfig> samplingConfigs = samplingConfigService.findByReportCodeIn(reportCodes, false);
        if (StringUtils.isNotEmpty(samplingConfigs)) {
            for (DtoSamplingConfig config : samplingConfigs) {
                errorMsg.append(String.format("报表配置编码[%s]关联了采样单[%s]，请确认\n",
                        config.getReportCode(), config.getFormName()));
            }
        }

        // 检查原始记录单配置关联
        List<DtoWorkSheetConfig> sheetConfigs = workSheetConfigService.findByReportCodeIn(reportCodes, false);
        if (StringUtils.isNotEmpty(sheetConfigs)) {
            for (DtoWorkSheetConfig config : sheetConfigs) {
                errorMsg.append(String.format("报表配置编码[%s]关联了原始记录单[%s]，请确认\n",
                        config.getReportCode(), config.getFormName()));
            }
        }
        if (StringUtils.isNotEmpty(errorMsg.toString())){
            throw new BaseException(errorMsg.toString());
        }
    }

    /**
     * 判断数据是否可以保存
     *
     * @param entity 报表模板配置实体
     */
    private void judgeSave(DtoBaseConfig entity) {
        Integer countByCode = repository.countByReportCodeAndIdNot(entity.getReportCode(), entity.getId());
        if (countByCode > 0) {
            throw new BaseException("系统中已存在相同报表编码的数据!");
        }
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }

    @Autowired
    @Lazy
    public void setSamplingConfigService(SamplingConfigService samplingConfigService) {
        this.samplingConfigService = samplingConfigService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetConfigService(WorkSheetConfigService workSheetConfigService) {
        this.workSheetConfigService = workSheetConfigService;
    }
}
