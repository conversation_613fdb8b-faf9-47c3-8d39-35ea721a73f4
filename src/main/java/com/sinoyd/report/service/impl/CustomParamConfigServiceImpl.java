package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.base.util.BeanUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.entity.CustomParamConfig;
import com.sinoyd.report.repository.CustomParamConfigRepository;
import com.sinoyd.report.service.CustomParamConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表全局配置自定义参数配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/03
 */
@Service
public class CustomParamConfigServiceImpl extends LimsBaseServiceImpl<DtoCustomParamConfig, String, CustomParamConfigRepository> implements CustomParamConfigService {

    /**
     * 根据全局配置id查询
     *
     * @param globalConfigId 全局配置id
     * @return 配置信息列表
     */
    @Override
    public List<DtoCustomParamConfig> findByGlobalConfigId(String globalConfigId) {
        return repository.findByGlobalConfigIdAndIsDeletedFalse(globalConfigId);
    }

    /**
     * 根据全局配置id查询可选择的配置信息
     *
     * @param globalConfigId 全局配置id
     * @return 可选择的配置信息列表
     */
    @Override
    public List<DtoCustomParamConfig> findAlternativeConfig(String globalConfigId) {
        //先找到已经存在得配置信息
        List<DtoCustomParamConfig> allConfigList = repository.findByGlobalConfigId(globalConfigId);
        //查询全局配置id为空的配置信息
        List<DtoCustomParamConfig> emptyConfigList = repository.findByGlobalConfigIdAndIsDeletedFalse("");
        //查询已删除的配置新
        List<DtoCustomParamConfig> deletedConfigList = allConfigList.stream().filter(CustomParamConfig::getIsDeleted).collect(Collectors.toList());
        List<DtoCustomParamConfig> existConfigList = allConfigList.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
        Set<String> existKeySet = new HashSet<>();
        existConfigList.forEach(p -> existKeySet.add(getConfigKey(p)));
        deletedConfigList = deletedConfigList.stream().filter(p -> !existKeySet.contains(getConfigKey(p))).collect(Collectors.toList());
        emptyConfigList = emptyConfigList.stream().filter(p -> !existKeySet.contains(getConfigKey(p))).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(emptyConfigList)) {
            deletedConfigList.addAll(emptyConfigList);
        }
        List<DtoCustomParamConfig> alternativeConfigList = new ArrayList<>();
        Set<String> dupConfigKeySet = new HashSet<>();
        for (DtoCustomParamConfig config : deletedConfigList) {
            String key = getConfigKey(config);
            if (!dupConfigKeySet.contains(key)) {
                alternativeConfigList.add(config);
                dupConfigKeySet.add(key);
            }
        }
        return alternativeConfigList;
    }

    /**
     * 保存被选择的配置信息列表
     *
     * @param configList     被选择的配置信息
     * @param globalConfigId 全局配置id
     * @return 保存后的数据
     */
    @Transactional
    @Override
    public List<DtoCustomParamConfig> saveCheckedConfig(List<DtoCustomParamConfig> configList, String globalConfigId) {
        List<DtoCustomParamConfig> emptyConfigList = configList.stream().filter(p -> "".equals(p.getGlobalConfigId())).collect(Collectors.toList());
        List<DtoCustomParamConfig> rstConfig = new ArrayList<>();
        if (StringUtils.isNotEmpty(emptyConfigList)) {
            configList.removeAll(emptyConfigList);
            List<DtoCustomParamConfig> instConfigList = new ArrayList<>();
            for (DtoCustomParamConfig emptyConfig : emptyConfigList) {
                DtoCustomParamConfig copyConfig = new DtoCustomParamConfig();
                BeanUtils.copyProperties(emptyConfig, copyConfig, "id");
                copyConfig.setGlobalConfigId(globalConfigId);
                instConfigList.add(copyConfig);
            }
            rstConfig.addAll(repository.saveAll(instConfigList));
        }
        configList.forEach(p -> p.setIsDeleted(false));
        rstConfig.addAll(repository.saveAll(configList));
        return rstConfig;
    }

    /**
     * 获取配置信息重复校验字符串
     *
     * @param config 配置信息对象
     * @return 重复校验字符串
     */
    private String getConfigKey(DtoCustomParamConfig config) {
        String paramCode = StringUtils.isNotEmpty(config.getParamCode()) ? config.getParamCode() : " ",
                paramVal = StringUtils.isNotEmpty(config.getParamValue()) ? config.getParamValue() : " ";
        return paramCode + "-" + paramVal;
    }
}
