package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetApplyColumn;
import com.sinoyd.report.enums.EnumDataSetType;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.DataSetApplyColumnRepository;
import com.sinoyd.report.service.ApiService;
import com.sinoyd.report.service.DataSetApplyColumnService;
import com.sinoyd.report.service.DataSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据集应用列配置服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/05
 */
@Service
public class DataSetApplyColumnServiceImpl
        extends LimsBaseServiceImpl<DtoDataSetApplyColumn, String, DataSetApplyColumnRepository>
        implements DataSetApplyColumnService {

    private DataSetService dataSetService;

    private ApiService apiService;

    private CodeService codeService;

    @Override
    public void findByPage(PageBean<DtoDataSetApplyColumn> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoDataSetApplyColumn dac");
        pageBean.setSelect("select dac");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public DtoDataSetApplyColumn save(DtoDataSetApplyColumn entity) {
        //校验
        judgeSave(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoDataSetApplyColumn update(DtoDataSetApplyColumn entity) {
        //校验
        judgeSave(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.DATA_SET_APPLY_COLUMN, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    /**
     * 加载冗余属性
     *
     * @param collection 记录集合
     */
    @Override
    public void loadTransientFields(Collection<DtoDataSetApplyColumn> collection) {
        Set<String> dataSetIds = collection.stream().map(DtoDataSetApplyColumn::getDataSetId).collect(Collectors.toSet());
        //获取SQL数据集数据
        Map<String, DtoDataSet> dataSetMap = dataSetService.findAllMap(dataSetIds);
        //获取API接口数据
        Map<String, DtoApi> apiMap = apiService.findAllMap(dataSetIds);
        //获取所有业务类型常量
        List<DtoCode> businessTypeList = codeService.findCodes("lims_report_dataSetApplyBusinessType");
        for (DtoDataSetApplyColumn dsApplyColumn : collection) {
            String dataSetName = "";
            if (EnumDataSetType.SQL数据集.getValue().equals(dsApplyColumn.getDataSetType())){
                DtoDataSet dataSet = dataSetMap.get(dsApplyColumn.getDataSetId());
                dataSetName = dataSet != null ? dataSet.getDataSetName() : "";
            }
            if (EnumDataSetType.API接口.getValue().equals(dsApplyColumn.getDataSetType())){
                DtoApi api = apiMap.get(dsApplyColumn.getDataSetId());
                dataSetName = api != null ? api.getApiName() : "";
            }
            Optional<DtoCode> businessTypeOp = businessTypeList.stream().filter(p -> p.getDictValue().equals(dsApplyColumn.getBusinessType())).findFirst();
            businessTypeOp.ifPresent(p->dsApplyColumn.setBusinessTypeName(p.getDictName()));
            dsApplyColumn.setDataSetName(dataSetName);
            dsApplyColumn.setDataSetTypeName(EnumDataSetType.getByValue(dsApplyColumn.getDataSetType()).name());
        }
    }

    /**
     * 校验保存
     *
     * @param entity 实体数据
     */
    private void judgeSave(DtoDataSetApplyColumn entity) {
        Integer count = repository.countByPlaceholderNameAndIdNotAndIsDeletedFalse(entity.getPlaceholderName(), entity.getId());
        if (count > 0) {
            throw new BaseException(String.format("模板占位符[%s]已存在, 请更换占位符!", entity.getPlaceholderName()));
        }
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setDataSetService(DataSetService dataSetService) {
        this.dataSetService = dataSetService;
    }

    @Autowired
    @Lazy
    public void setApiService(ApiService apiService) {
        this.apiService = apiService;
    }
}
