package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.repository.DataSetColumnRepository;
import com.sinoyd.report.service.DataSetColumnService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SQL数据列服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@Service
public class DataSetColumnServiceImpl
        extends LimsBaseServiceImpl<DtoDataSetColumn, String, DataSetColumnRepository>
        implements DataSetColumnService {


    /**
     * 根据数据集Id集合查询数据列
     *
     * @param dataSetIds 数据集Id
     * @return 数据列
     */
    @Override
    public List<DtoDataSetColumn> findByDataSetIdIn(Collection<String> dataSetIds) {
        if (StringUtils.isNotEmpty(dataSetIds)) {
            return repository.findByDataSetIdIn(dataSetIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据数据集保存数据列
     *
     * @param dataSet 数据集
     * @return 数据列
     */
    @Override
    @Transactional
    public List<DtoDataSetColumn> saveByDataSet(DtoDataSet dataSet) {
        //删除原有关联数据（真删）
        List<DtoDataSetColumn> oldDataSetCol = repository.findByDataSetIdIn(Collections.singletonList(dataSet.getId()));
        delete(oldDataSetCol);
        //保存新关联数据
        List<DtoDataSetColumn> dsColumns = dataSet.getDataSetColumns();
        for (DtoDataSetColumn dsColumn : dsColumns) {
            dsColumn.setDataSetId(dataSet.getId());
        }
        return save(dsColumns);
    }

    /**
     * 数据集id集合
     *
     * @param dataSetIds 数据集id集合
     */
    @Override
    @Transactional
    public void deleteByDataSetIdIn(Collection<String> dataSetIds) {
        List<DtoDataSetColumn> dsColumns = repository.findByDataSetIdIn(dataSetIds);
        List<String> dsColumnIds = dsColumns.stream().map(DtoDataSetColumn::getId).collect(Collectors.toList());
        logicDeleteById(dsColumnIds);
    }
}
