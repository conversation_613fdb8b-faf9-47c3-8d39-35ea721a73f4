package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetParam;
import com.sinoyd.report.repository.DataSetParamRepository;
import com.sinoyd.report.service.DataSetParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集参数服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/01
 */
@Service
public class DataSetParamServiceImpl
        extends LimsBaseServiceImpl<DtoDataSetParam, String, DataSetParamRepository>
        implements DataSetParamService {


    /**
     * 根据数据集配置id集合查询数据集参数
     *
     * @param dataSetIds 数据集配置id集合
     * @return 数据集参数
     */
    @Override
    public List<DtoDataSetParam> findByDataSetIdIn(Collection<String> dataSetIds) {
        if (StringUtils.isNotEmpty(dataSetIds)) {
            return repository.findByDataSetIdIn(dataSetIds);
        }
        return new ArrayList<>();
    }

    /**
     * 根据数据集数据保存数据集参数
     *
     * @param dataSet 数据集实体
     * @return 数据集参数
     */
    @Override
    @Transactional
    public List<DtoDataSetParam> saveByDataSet(DtoDataSet dataSet) {
        //先删除原有的参数数据(真删)
        List<DtoDataSetParam> oldDataSetParams = repository.findByDataSetIdIn(Collections.singletonList(dataSet.getId()));
        delete(oldDataSetParams);
        //保存新的参数数据
        List<DtoDataSetParam> save = dataSet.getDataSetParams();
        for (DtoDataSetParam dsParam : save) {
            dsParam.setDataSetId(dataSet.getId());
        }
        return super.save(save);
    }

    /**
     * 根据数据集id删除关联参数数据
     *
     * @param dataSetIds 数据集id集合
     */
    @Override
    @Transactional
    public void deleteByDataSetIdIn(Collection<String> dataSetIds) {
        if (StringUtils.isNotEmpty(dataSetIds)) {
            //查询数据集下的所有参数
            List<DtoDataSetParam> dsParams = repository.findByDataSetIdIn(dataSetIds);
            //删除参数数据
            List<String> deleteIds = dsParams.stream().map(DtoDataSetParam::getId).collect(Collectors.toList());
            logicDeleteById(deleteIds);
        }
    }
}
