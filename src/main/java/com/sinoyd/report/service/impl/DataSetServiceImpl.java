package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.dto.DtoDataSetParam;
import com.sinoyd.report.dto.DtoDataSource;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.DataSetRepository;
import com.sinoyd.report.service.DataSetColumnService;
import com.sinoyd.report.service.DataSetParamService;
import com.sinoyd.report.service.DataSetService;
import com.sinoyd.report.service.DataSourceService;
import com.sinoyd.report.util.ExcelJdbcExecuteUtil;
import com.sinoyd.report.vo.JdbcExecuteVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据集服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/30
 */
@Service
public class DataSetServiceImpl
        extends LimsBaseServiceImpl<DtoDataSet, String, DataSetRepository>
        implements DataSetService {

    private DataSourceService dataSourceService;

    private DataSetParamService dataSetParamService;

    private DataSetColumnService dataSetColumnService;

    @Override
    public void findByPage(PageBean<DtoDataSet> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoDataSet ds");
        pageBean.setSelect("select ds");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public DtoDataSet save(DtoDataSet entity) {
        DtoDataSet save = super.save(entity);
        //关联保存数据集参数数据
        save.setDataSetParams(dataSetParamService.saveByDataSet(save));
        //关联保存数据列
        save.setDataSetColumns(dataSetColumnService.saveByDataSet(save));
        return save;
    }

    @Override
    @Transactional
    public DtoDataSet update(DtoDataSet entity) {
        DtoDataSet update = super.update(entity);
        //关联更新数据集参数数据
        update.setDataSetParams(dataSetParamService.saveByDataSet(update));
        //关联保存数据列
        update.setDataSetColumns(dataSetColumnService.saveByDataSet(update));
        return update;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        //发布删除事件
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.REPORT_DATA_SET, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    /**
     * 校验Sql,并返回结果列
     *
     * @param dataSet 数据集实体
     * @return Sql执行返回列
     */
    @Override
    public DtoDataSet verifySql(DtoDataSet dataSet) {
        List<DtoDataSetColumn> originColumns = dataSet.getDataSetColumns();
        //获取校验实体
        JdbcExecuteVO verifyVO = getVerifyVO(dataSet);
        //校验Sql并返回Sql执行列
        List<DtoDataSetColumn> resultColumns = ExcelJdbcExecuteUtil.verifySQL(verifyVO);
        if (StringUtils.isNotEmpty(originColumns) && StringUtils.isNotEmpty(resultColumns)) {
            Map<String, List<DtoDataSetColumn>> columnMap = originColumns.stream().collect(Collectors.groupingBy(DtoDataSetColumn::getColumnCode));
            for (DtoDataSetColumn resultColumn : resultColumns) {
                Optional<DtoDataSetColumn> originCol = columnMap.getOrDefault(resultColumn.getColumnCode(), new ArrayList<>()).stream().findFirst();
                originCol.ifPresent(dtoDataSetColumn -> BeanUtils.copyProperties(dtoDataSetColumn, resultColumn));
            }
        }
        dataSet.setDataSetColumns(resultColumns);
        return dataSet;
    }

    /**
     * 获取数据库连接校验VO
     *
     * @param dataSet 数据集配置数据
     * @return 数据库连接校验VO
     */
    private JdbcExecuteVO getVerifyVO(DtoDataSet dataSet) {
        if (StringUtils.isNotEmpty(dataSet.getDsId())) {
            //处理Sql参数
            Map<String, Object> sqlParams = new HashMap<>();
            for (DtoDataSetParam dsParam : dataSet.getDataSetParams()) {
                sqlParams.put(dsParam.getParamName(), "1");
            }
            if ((StringUtils.isNull(dataSet.getSaveFlag()) || !dataSet.getSaveFlag()) && StringUtils.isNotEmpty(dataSet.getSqlContent())) {
                List<String> paramNameInSql = ExcelJdbcExecuteUtil.getSqlParamNameList(NamedParameterUtils.parseSqlStatement(dataSet.getSqlContent()));
                for (String paramName : paramNameInSql) {
                    if (!sqlParams.containsKey(paramName)) {
                        sqlParams.put(paramName, "1");
                        DtoDataSetParam paramForSql = new DtoDataSetParam();
                        paramForSql.setParamName(paramName);
                        paramForSql.setId(UUIDHelper.newId());
                        paramForSql.setDataSetId(dataSet.getId());
                        dataSet.getDataSetParams().add(paramForSql);
                    }
                }
            }
            return getExecuteVO(dataSet, sqlParams);
        } else {
            throw new BaseException("必须选择数据源后才可验证SQL!");
        }
    }

    /**
     * 获取数据库SQL执行VO
     *
     * @param dataSet   数据集配置数据
     * @param sqlParams sql请求参数
     * @return 数据库SQL执行VO
     */
    @Override
    public JdbcExecuteVO getExecuteVO(DtoDataSet dataSet, Map<String, Object> sqlParams) {
        if (StringUtils.isNotEmpty(dataSet.getDsId())) {
            DtoDataSource dataSource = dataSourceService.findOne(dataSet.getDsId());
            JdbcExecuteVO verifyVO = new JdbcExecuteVO();
            BeanUtils.copyProperties(dataSource, verifyVO);
            //设置Sql
            verifyVO.setDbType(dataSource.getDbTypeCode())
                    .setDbDrive(dataSource.getDbDriverCode())
                    .setSql(dataSet.getSqlContent())
                    .setIsCollection(dataSet.getIsCollection());
            verifyVO.setValues(sqlParams);
            return verifyVO;
        } else {
            throw new BaseException("必须选择数据源后才可验证SQL!");
        }
    }

    /**
     * 加载冗余属性
     *
     * @param collection 记录集合
     */
    @Override
    public void loadTransientFields(Collection<DtoDataSet> collection) {
        List<String> dataSetIds = collection.stream().map(DtoDataSet::getId).collect(Collectors.toList());
        //查询关联参数数据
        Map<String, List<DtoDataSetParam>> dsParamGroup = dataSetParamService.findByDataSetIdIn(dataSetIds)
                .stream().collect(Collectors.groupingBy(DtoDataSetParam::getDataSetId));
        //查询关联数据列
        Map<String, List<DtoDataSetColumn>> dsColumnsGroup = dataSetColumnService.findByDataSetIdIn(dataSetIds)
                .stream().collect(Collectors.groupingBy(DtoDataSetColumn::getDataSetId));
        //数据源id
        List<String> dataSourceIds = collection.stream().map(DtoDataSet::getDsId).collect(Collectors.toList());
        Map<String, DtoDataSource> dataSourceMap = dataSourceService.findAllMap(dataSourceIds);
        for (DtoDataSet dataSet : collection) {
            DtoDataSource dataSource = dataSourceMap.get(dataSet.getDsId());
            if (dataSource != null) {
                dataSet.setDataSourceName(dataSource.getDsName());
            }
            List<DtoDataSetColumn> columns = dsColumnsGroup.getOrDefault(dataSet.getId(), new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(DtoDataSetColumn::getOrderNum)).collect(Collectors.toList());
            dataSet.setDataSetColumns(columns);
            List<DtoDataSetParam> params = dsParamGroup.getOrDefault(dataSet.getId(), new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(DtoDataSetParam::getOrderNum)).collect(Collectors.toList());
            dataSet.setDataSetParams(params);
        }
    }

    @Autowired
    @Lazy
    public void setDataSourceService(DataSourceService dataSourceService) {
        this.dataSourceService = dataSourceService;
    }

    @Autowired
    @Lazy
    public void setDataSetParamService(DataSetParamService dataSetParamService) {
        this.dataSetParamService = dataSetParamService;
    }

    @Autowired
    @Lazy
    public void setDataSetColumnService(DataSetColumnService dataSetColumnService) {
        this.dataSetColumnService = dataSetColumnService;
    }
}
