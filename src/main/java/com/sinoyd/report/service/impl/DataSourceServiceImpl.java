package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoDataSource;
import com.sinoyd.report.repository.DataSourceRepository;
import com.sinoyd.report.service.DataSourceService;
import com.sinoyd.report.util.ExcelJdbcExecuteUtil;
import com.sinoyd.report.vo.JdbcExecuteVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据源配置服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/29
 */
@Service
@Slf4j
public class DataSourceServiceImpl
        extends LimsBaseServiceImpl<DtoDataSource, String, DataSourceRepository>
        implements DataSourceService {

    @Override
    public void findByPage(PageBean<DtoDataSource> page, BaseCriteria criteria) {
        page.setEntityName("DtoDataSource ds");
        page.setSelect("select ds");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public DtoDataSource save(DtoDataSource entity) {
        validate(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoDataSource update(DtoDataSource entity) {
        validate(entity);
        return super.update(entity);
    }

    /**
     * 验证数据源配置
     *
     * @param ds
     */
    private void validate(DtoDataSource ds) {
        Integer repeatCount = repository.countByDbTypeCodeAndDbDriverCodeAndDbHostAndDbNameAndDbPortAndIdNot
                (ds.getDbTypeCode(), ds.getDbDriverCode(), ds.getDbHost(), ds.getDbName(), ds.getDbPort(), ds.getId());
        if (repeatCount > 0) {
            throw new BaseException("数据源配置已存在!");
        }
        if (repository.countByDsNameAndIdNot(ds.getDsName().trim(), ds.getId()) > 0) {
            throw new BaseException("数据源名称已存在!");
        }
    }

    /**
     * 验证连接
     *
     * @param ds 数据源实体
     */
    @Override
    public void verifyConnection(DtoDataSource ds) {
        try {
            if (!ExcelJdbcExecuteUtil.verifyDBConnect(getVerifyVO(ds))) {
                throw new BaseException("连接失败: 无法获取到连接!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e.getMessage());
        }
    }

    /**
     * 获取数据库连接校验VO
     *
     * @param ds 数据源配置数据
     * @return 数据库连接校验VO
     */
    private JdbcExecuteVO getVerifyVO(DtoDataSource ds) {
        JdbcExecuteVO verifyVO = new JdbcExecuteVO();
        BeanUtils.copyProperties(ds, verifyVO);
        verifyVO.setDbType(ds.getDbTypeCode()).setDbDrive(ds.getDbDriverCode());
        return verifyVO;
    }
}
