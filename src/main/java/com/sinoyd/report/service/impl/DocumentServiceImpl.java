package com.sinoyd.report.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.base.vo.DocumentPathVO;
import com.sinoyd.base.vo.PathConfigVO;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.preview.DocumentPreviewFactory;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoDocument;
import com.sinoyd.report.repository.DocumentRepository;
import com.sinoyd.report.service.DocumentService;
import com.sinoyd.report.utils.ReportFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文档业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/30
 */
@Service
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class DocumentServiceImpl extends LimsBaseServiceImpl<DtoDocument, String, DocumentRepository> implements DocumentService {

    private FilePathConfig filePathConfig;

    private FilePropertyConfig filePropertyConfig;


    @Override
    public void findByPage(PageBean<DtoDocument> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoDocument d");
        pageBean.setSelect("select d");
        super.findByPage(pageBean, criteria);
    }

    /**
     * 下载文件
     *
     * @param documentId 文件id
     * @param response   响应流
     */
    @Transactional
    @Override
    public void download(String documentId, HttpServletResponse response) {
        Optional<DtoDocument> documentOp = repository.findById(documentId);
        documentOp.ifPresent(dtoDocument -> download(dtoDocument, response));
    }

    /**
     * 删除对应对象的附件文件和数据
     *
     * @param objectIds 外键id
     */
    @Override
    @Transactional
    public void deleteDataAndFileByObjectIds(Collection<String> objectIds) {
        List<DtoDocument> documents = repository.findByObjectIdIn(objectIds);
        List<String> deleteIds = documents.stream().map(DtoDocument::getId).collect(Collectors.toList());
        logicDeleteById(deleteIds);
        for (DtoDocument document : documents) {
            FileUtil.delete(filePropertyConfig.getFilePath() + ReportFileUtil.FILE_SEPARATOR + document.getFilePath());
        }
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览模型
     * @param response 响应流
     */
    @Override
    public void previewDocument(DocumentPreviewVO vo, HttpServletResponse response) {
        DocumentPreviewFactory.previewAsPDF(filePropertyConfig.getFilePath(), vo, response);
    }

    /**
     * 文件上传
     *
     * @param request 文件上传对象
     */
    @Override
    @Transactional
    public List<DtoDocument> upload(HttpServletRequest request) {
        return uploadFile(request, filePropertyConfig.getFilePath(), false);
    }

    /**
     * 文件上传（可控根目录与是否覆盖操作）
     *
     * @param request  请求体
     * @param rootPath 根目录
     * @param isCover  是否覆盖
     * @return 保存后的文档数据
     */
    @Override
    public List<DtoDocument> uploadFile(HttpServletRequest request, String rootPath, Boolean isCover) {
        String docTypeId = request.getParameter("docType");
        String docTypeName = request.getParameter("docTypeDesc");
        String folderId = request.getParameter("objectId");
        String folderName = request.getParameter("folderName");
        String uploadPath = request.getParameter("path");
        uploadPath = StringUtils.isNotEmpty(uploadPath) ? ReportFileUtil.FILE_SEPARATOR + uploadPath : "";
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        List<DtoDocument> dtoDocuments = new ArrayList<>();
        Optional<DtoDocument> documentOp = Optional.empty();
        if (isCover) {
            documentOp = repository.findByObjectId(folderId).stream().findFirst();
        }
        //创建文件目录
        String fullPath = rootPath + uploadPath;
        File fileDirectory = new File(fullPath);
        if (!fileDirectory.exists()) {
            boolean mkdirs = fileDirectory.mkdirs();
            if (mkdirs) {
                log.info(String.format("================[%s]文件目录创建成功================", fullPath));
            }
        }
        for (MultipartFile multipartFile : files) {
            //原始文件名
            String fileName = multipartFile.getOriginalFilename();
            //加上时间戳的文件名
            String newFileName = isCover ? fileName : FileUtil.loadFileNameWithTimestamp(fileName);
            //验证文件名称
            if (fileName == null) {
                throw new BaseException("上传的文件，文件名称为空");
            }
            //验证文件类型是否合法
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            verifyFileType(fileType);
            try {
                DtoDocument dtoDocument = new DtoDocument();
                if (isCover && documentOp.isPresent()) {
                    dtoDocument.setId(documentOp.get().getId());
                    dtoDocument.setModifyDate(new Date());
                    dtoDocument.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                }
                dtoDocument.setObjectId(folderId)
                        .setFolderName(folderName)
                        .setFileName(fileName)
                        .setPhysicalName(newFileName)
                        .setFilePath(uploadPath + ReportFileUtil.FILE_SEPARATOR + newFileName)
                        .setDocType(docTypeId)
                        .setDocTypeDesc(docTypeName)
                        .setDocSize(multipartFile.getSize())
                        .setDocSuffix(fileType)
                        .setUploadPerson(PrincipalContextUser.getPrincipal().getUserName())
                        .setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
                File file = new File(new File(fullPath).getAbsoluteFile() + ReportFileUtil.FILE_SEPARATOR + newFileName);
                multipartFile.transferTo(file);
                dtoDocuments.add(dtoDocument);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new BaseException("上传文件失败");
            }
        }
        //考虑到有覆盖数据，使用JPA的保存，可以更新
        return repository.saveAll(dtoDocuments);
    }

    @Override
    public DocumentPathVO getDocumentPathFromXml(String code, Map<String, Object> map) {
        DocumentPathVO vo = new DocumentPathVO();
        PathConfigVO dtoPathConfig = getXmlFilePathConfigByCode(code);
        if (StringUtils.isNull(dtoPathConfig)) {
            throw new BaseException("尚未配置附件归档路径");
        }
        String className = dtoPathConfig.getClassName();
        String methodName = dtoPathConfig.getMethod();
        String placeholder = dtoPathConfig.getPlaceholder();
        if (StringUtils.isEmpty(className) || StringUtils.isEmpty(methodName) || StringUtils.isEmpty(placeholder)) {
            throw new BaseException("编码[" + code + "]对应的附件归档路径配置不正确");
        }

        //处理相应的数据源
        String path = dtoPathConfig.getPath();
        try {
            Class clazz = Class.forName(className);
            Method method = Arrays.stream(clazz.getMethods()).filter(m -> m.getName().equals(methodName))
                    .findFirst().orElseThrow(() -> new BaseException("文件路径配置不正确"));
            Object data = method.invoke(SpringContextAware.getBean(clazz), map.values().toArray());
            //如果返回的直接是字符串，直接替换
            if (data instanceof String) {
                path = path.replace("{" + placeholder + "}", String.valueOf(data));
            } else {
                //根据占位符得出相应的数据字段名称
                String[] fieldNames = placeholder.split(",");
                if (data != null) {
                    Map<String, Object> dataMap = JsonIterator.deserialize(JsonStream.serialize(data), Map.class);
                    for (String fieldName : fieldNames) {
                        String value = String.valueOf(dataMap.get(fieldName));
                        if (StringUtils.isEmpty(value)) {
                            value = "";
                        }
                        path = path.replace("{" + fieldName + "}", value.trim().replace("/", "-"));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("从XML中获取文件路径失败");
        }
        vo.setPath(path);
        return vo;
    }

    /**
     * 根据对象id查询
     *
     * @param objectId 对象id
     * @return 结果
     */
    @Override
    public List<DtoDocument> findByObjectId(String objectId) {
        if (StringUtils.isNotEmpty(objectId)) {
            return repository.findByObjectId(objectId);
        }
        return new ArrayList<>();
    }

    /**
     * 根据对象id集合查询
     *
     * @param objectIds 对象id集合
     * @return 结果
     */
    @Override
    public List<DtoDocument> findByObjectIdIn(Collection<String> objectIds) {
        if (StringUtils.isNotEmpty(objectIds)) {
            return repository.findByObjectIdIn(objectIds);
        }
        return new ArrayList<>();
    }

    /**
     * 下载文档
     *
     * @param document 文档对象
     * @param response 响应流
     */
    private void download(DtoDocument document, HttpServletResponse response) {
        if (StringUtils.isNull(document)) {
            throw new BaseException("文件不存在，请确认");
        }
        //更新下载次数
        Integer downLoadTimes = document.getDownloadTimes();
        document.setDownloadTimes(++downLoadTimes);
        super.update(document);
        String path = filePropertyConfig.getFilePath().concat(document.getFilePath());
        String filename = document.getFileName();
        //下载文件
        FileUtil.download(path, filename, response);
    }

    /**
     * 验证文件类型是否合法
     *
     * @param fileType 文件类型
     */
    private void verifyFileType(String fileType) {
        //设置允许上传文件类型
        String allowedSuffix = filePropertyConfig.getFileSuffix();
        if (StringUtils.isNotEmpty(allowedSuffix)) {
            // 获取文件后缀
            String suffixType = fileType.substring(fileType.lastIndexOf(".") + 1);
            //所有的类型
            List<String> allowSuffixList = Arrays.asList(allowedSuffix.split(","));
            if (!allowSuffixList.contains(suffixType)) {
                throw new BaseException("只允许上传文件类型为[" + allowedSuffix + "]的文件");
            }
        }
    }

    /**
     * 获取指定编号的配置信息
     *
     * @param code 配置编号
     * @return 返回配置信息
     */
    private PathConfigVO getXmlFilePathConfigByCode(String code) {
        List<PathConfigVO> pathConfigs = getAllXmlFilePathConfig();
        Optional<PathConfigVO> optional = pathConfigs.stream().filter(p -> p.getCode().equals(code)).findFirst();
        return optional.orElse(null);
    }

    /**
     * 获取XML中所有的文件配置
     *
     * @return 结果
     */
    private List<PathConfigVO> getAllXmlFilePathConfig() {
        return filePathConfig.getAllConfigs();
    }


    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }

}