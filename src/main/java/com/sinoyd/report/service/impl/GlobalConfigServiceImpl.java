package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoCustomParamConfig;
import com.sinoyd.report.dto.DtoGlobalConfig;
import com.sinoyd.report.repository.CustomParamConfigRepository;
import com.sinoyd.report.repository.GlobalConfigRepository;
import com.sinoyd.report.service.CustomParamConfigService;
import com.sinoyd.report.service.GlobalConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报表配置全局参数配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/17
 */
@Service
public class GlobalConfigServiceImpl extends LimsBaseServiceImpl<DtoGlobalConfig, String, GlobalConfigRepository> implements GlobalConfigService {

    private CustomParamConfigRepository customParamConfigRepository;
    private CustomParamConfigService customParamConfigService;

    /**
     * 根据报表编码查询对应的全局参数配置
     *
     * @param reportCode 报表编码
     * @return 全局参数配置
     */
    @Override
    public DtoGlobalConfig findByReportCode(String reportCode) {
        DtoGlobalConfig globalConfig = repository.findByReportCode(reportCode);
        if (StringUtils.isNotNull(globalConfig)) {
            List<DtoCustomParamConfig> customParamConfigList = customParamConfigRepository.findByGlobalConfigIdAndIsDeletedFalse(globalConfig.getId());
            globalConfig.setCustomParamConfigList(customParamConfigList);
        }
        return globalConfig;
    }

    /**
     * 根据报表编码集合删除全区参数配置数据
     *
     * @param reportCodes 报表编码集合
     * @return 删除的条数
     */
    @Override
    @Transactional
    public Integer deleteByReportCodeIn(Collection<String> reportCodes) {
        List<DtoGlobalConfig> delete = repository.findByReportCodeIn(reportCodes);
        List<String> deleteIds = delete.stream().map(DtoGlobalConfig::getId).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(deleteIds)) {
            //删除全局配置自定义参数配置
            List<DtoCustomParamConfig> paramConfigList = customParamConfigRepository.findByGlobalConfigIdInAndIsDeletedFalse(deleteIds);
            if (StringUtils.isNotEmpty(paramConfigList)) {
                List<String> paramConfigIdList = paramConfigList.stream().map(DtoCustomParamConfig::getId).collect(Collectors.toList());
                customParamConfigRepository.logicDeleteById(paramConfigIdList);
            }
        }
        return logicDeleteById(deleteIds);
    }

    /**
     * 保存数据
     *
     * @param entity 全局参数配置数据
     * @return 保存后的数据
     */
    @Override
    @Transactional
    public DtoGlobalConfig save(DtoGlobalConfig entity) {
        //判断是否可以保存
        judgeSave(entity);
        DtoGlobalConfig globalConfig = super.save(entity);
        entity.setId(globalConfig.getId());
        //新增或修改自定义参数配置
        List<DtoCustomParamConfig> customParamConfigList = saveCustomParamConfig(entity);
        globalConfig.setCustomParamConfigList(customParamConfigList);
        return globalConfig;
    }

    /**
     * 新增/修改自定义参数配置
     *
     * @param entity 全局配置对象
     * @return 自定义参数配置
     */
    private List<DtoCustomParamConfig> saveCustomParamConfig(DtoGlobalConfig entity) {
        List<DtoCustomParamConfig> customParamConfigList = customParamConfigRepository.findByGlobalConfigIdAndIsDeletedFalse(entity.getId());
        if (StringUtils.isNotEmpty(customParamConfigList)) {
            List<String> customParamConfigIdList = customParamConfigList.stream().map(DtoCustomParamConfig::getId).collect(Collectors.toList());
            customParamConfigRepository.logicDeleteById(customParamConfigIdList);
        }
        List<DtoCustomParamConfig> newParamConfigList = entity.getCustomParamConfigList();
        List<DtoCustomParamConfig> copyParamConfigList = new ArrayList<>();
        if (StringUtils.isNotEmpty(newParamConfigList)) {
            for (DtoCustomParamConfig config : newParamConfigList) {
                DtoCustomParamConfig copyConfig = new DtoCustomParamConfig();
                BeanUtils.copyProperties(config, copyConfig);
                copyConfig.setId(UUIDHelper.newId());
                copyConfig.setGlobalConfigId(entity.getId());
                copyConfig.setIsDeleted(false);
                copyParamConfigList.add(copyConfig);
            }
            customParamConfigService.save(copyParamConfigList);
        }
        return copyParamConfigList;
    }

    /**
     * 更新数据
     *
     * @param entity 全局参数配置数据
     * @return 更新后的数据
     */
    @Override
    @Transactional
    public DtoGlobalConfig update(DtoGlobalConfig entity) {
        //判断是否可以保存
        judgeSave(entity);
        //新增或更新自定义参数配置
        List<DtoCustomParamConfig> customParamConfigList = saveCustomParamConfig(entity);
        DtoGlobalConfig globalConfig = super.update(entity);
        globalConfig.setCustomParamConfigList(customParamConfigList);
        return globalConfig;
    }

    /**
     * 判断数据是否可以保存
     *
     * @param entity 报表模板全局参数配置实体
     */
    private void judgeSave(DtoGlobalConfig entity) {
        Integer countByCode = repository.countByReportCodeAndIdNot(entity.getReportCode(), entity.getId());
        if (countByCode > 0) {
            throw new BaseException("此报表编码的基础配置关联的全局参数配置已存在!");
        }
    }

    @Autowired
    public void setCustomParamConfigRepository(CustomParamConfigRepository customParamConfigRepository) {
        this.customParamConfigRepository = customParamConfigRepository;
    }

    @Autowired
    @Lazy
    public void setCustomParamConfigService(CustomParamConfigService customParamConfigService) {
        this.customParamConfigService = customParamConfigService;
    }
}
