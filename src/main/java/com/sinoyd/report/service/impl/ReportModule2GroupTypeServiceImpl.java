package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.report.dto.DtoBaseConfig2Module;
import com.sinoyd.report.dto.DtoReportModule2GroupType;
import com.sinoyd.report.dto.customer.DtoModuleGroupTypeTemp;
import com.sinoyd.report.repository.BaseConfig2ModuleRepository;
import com.sinoyd.report.repository.ReportModule2GroupTypeRepository;
import com.sinoyd.report.service.ReportModule2GroupTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Service
public class ReportModule2GroupTypeServiceImpl extends LimsBaseServiceImpl<DtoReportModule2GroupType, String, ReportModule2GroupTypeRepository> implements ReportModule2GroupTypeService {

    private BaseConfig2ModuleRepository baseConfig2ModuleRepository;

    @Override
    @Transactional
    public List<DtoReportModule2GroupType> saveGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        Optional<DtoBaseConfig2Module> existConfig2ModuleOptional = baseConfig2ModuleRepository.findById(moduleGroupTypeTemp.getBaseConfigModuleId());
        if (!existConfig2ModuleOptional.isPresent()) {
            throw new BaseException("报告和组件的关联关系不存在！");
        }
        List<DtoReportModule2GroupType> module2GroupTypeList = moduleGroupTypeTemp.getGroupTypeList();
        module2GroupTypeList.forEach(p -> p.setBaseConfigModuleId(moduleGroupTypeTemp.getBaseConfigModuleId()));
        if (StringUtils.isNotEmpty(module2GroupTypeList)) {
            repository.saveAll(module2GroupTypeList);
        }
        return module2GroupTypeList;
    }

    @Override
    @Transactional
    public List<DtoReportModule2GroupType> updateGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        Optional<DtoBaseConfig2Module> existConfig2ModuleOptional = baseConfig2ModuleRepository.findById(moduleGroupTypeTemp.getBaseConfigModuleId());
        if (!existConfig2ModuleOptional.isPresent()) {
            throw new BaseException("报告和组件的关联关系不存在！");
        }
        //先删除原有的
        List<DtoReportModule2GroupType> oldGroupTypeList = repository.findByBaseConfigModuleId(moduleGroupTypeTemp.getBaseConfigModuleId());
        if (StringUtils.isNotEmpty(oldGroupTypeList)) {
            repository.deleteAll(oldGroupTypeList);
        }
        //加入现有的
        List<DtoReportModule2GroupType> newGroupTypeList = moduleGroupTypeTemp.getGroupTypeList();
        newGroupTypeList.forEach(p -> p.setBaseConfigModuleId(moduleGroupTypeTemp.getBaseConfigModuleId()));
        return repository.saveAll(newGroupTypeList);
    }

    @Override
    public List<DtoReportModule2GroupType> findByBaseConfigModuleId(String baseConfigModuleId) {
        return repository.findByBaseConfigModuleId(baseConfigModuleId);
    }

    @Override
    public List<DtoReportModule2GroupType> findByBaseConfigModuleIdIn(List<String> baseConfigModuleIds) {
        return repository.findByBaseConfigModuleIdIn(baseConfigModuleIds);
    }

    @Autowired
    public void setReportConfig2ModuleRepository(BaseConfig2ModuleRepository baseConfig2ModuleRepository) {
        this.baseConfig2ModuleRepository = baseConfig2ModuleRepository;
    }

}
