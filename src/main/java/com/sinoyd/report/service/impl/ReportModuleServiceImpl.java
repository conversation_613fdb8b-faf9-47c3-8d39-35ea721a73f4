package com.sinoyd.report.service.impl;

import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoReportModule;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.report.repository.ReportModuleRepository;
import com.sinoyd.report.service.ReportModuleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 报告组件操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/08
 */
@Service
public class ReportModuleServiceImpl extends LimsBaseServiceImpl<DtoReportModule, String, ReportModuleRepository> implements ReportModuleService {

    @Override
    public void findByPage(PageBean<DtoReportModule> pb, BaseCriteria criteria) {
        pb.setEntityName("DtoReportModule a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, criteria);
    }

    @Override
    @Transactional
    public DtoReportModule save(DtoReportModule entity) {
        verify(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoReportModule update(DtoReportModule entity) {
        verify(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public int deleteModule(List<String> ids) {
        if (StringUtils.isNotEmpty(ids)) {
            //删除组件信息
            int cnt = repository.logicDeleteById(ids);
            //发布删除事件
            SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(ids, IReportEventName.REPORT_MODULE, IEventAction.DELETE));
            return cnt;
        }
        return 0;
    }

    /**
     * 校验组件编码是否重复
     *
     * @param entity 实体
     */
    private void verify(DtoReportModule entity) {
        if (repository.countByModuleCodeAndIdNot(entity.getModuleCode(), entity.getId()) > 0) {
            throw new BaseException("组件编码已存在！");
        }
    }

}
