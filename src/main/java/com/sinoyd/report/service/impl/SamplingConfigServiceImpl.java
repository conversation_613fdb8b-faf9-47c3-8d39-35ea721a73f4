package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoSamplingConfig;
import com.sinoyd.report.dto.DtoSamplingTest;
import com.sinoyd.report.repository.SamplingConfigRepository;
import com.sinoyd.report.service.BaseConfigService;
import com.sinoyd.report.service.SamplingConfigService;
import com.sinoyd.report.service.SamplingTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class SamplingConfigServiceImpl extends LimsBaseServiceImpl<DtoSamplingConfig, String, SamplingConfigRepository>
        implements SamplingConfigService {

    private BaseConfigService baseConfigService;

    private SamplingTestService samplingTestService;

    @Override
    public void findByPage(PageBean<DtoSamplingConfig> pb, BaseCriteria recordConfigCriteria) {
        pb.setEntityName("DtoSamplingConfig a");
        pb.setSelect("select a");
        super.findByPage(pb, recordConfigCriteria);
    }

    @Override
    @Transactional
    public DtoSamplingConfig save(DtoSamplingConfig entity) {
        verify(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoSamplingConfig update(DtoSamplingConfig entity) {
        verify(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        //发布删除事件
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.SAMPLING_CONFIG, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }


    @Override
    public List<DtoSamplingConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields) {
        List<DtoSamplingConfig> samplingConfigs = repository.findByReportCodeIn(reportCodes);
        if (isLoadTransientFields) {
            loadTransientFields(samplingConfigs);
        }
        return samplingConfigs;
    }

    @Override
    @Transactional
    public DtoSamplingConfig copy(DtoSamplingConfig samplingConfig) {
        verify(samplingConfig);
        // 保存复制的采样单配置
        DtoSamplingConfig savedConfig = repository.save(samplingConfig);

        // 复制源id映射（key：targetId, val: sourceId）
        Map<String, String> copySourceMap = new HashMap<>();
        copySourceMap.put(savedConfig.getId(), samplingConfig.getSourceRecordConfigId());

        // 发布复制事件，用于复制关联数据
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(copySourceMap, IReportEventName.SAMPLING_CONFIG, IEventAction.COPY));

        return savedConfig;
    }

    /**
     * 校验模版名称是否重复，模版配置是否存在
     *
     * @param entity 实体
     */
    private void verify(DtoSamplingConfig entity) {
        if (repository.countByFormNameAndIdNot(entity.getFormName(), entity.getId()) > 0) {
            throw new BaseException("配置名称已存在!");
        }
        if (repository.countByReportCodeAndIdNot(entity.getReportCode(), entity.getId()) > 0) {
            throw new BaseException("模板配置已存在!");
        }
    }

    @Override
    public void loadTransientFields(Collection<DtoSamplingConfig> collection) {
        List<String> recordCodes = collection.stream().map(DtoSamplingConfig::getReportCode).collect(Collectors.toList());
        List<DtoBaseConfig> configList = StringUtils.isNotEmpty(recordCodes) ? baseConfigService.findByReportCodes(recordCodes) : new ArrayList<>();
        Map<String, DtoBaseConfig> baseConfigMap = configList.stream().collect(Collectors.toMap(DtoBaseConfig::getReportCode, p -> p, (k1, k2) -> k1));
        //关联测试项目配置
        List<String> samplingConfigIds = collection.stream().map(DtoSamplingConfig::getId).collect(Collectors.toList());
        List<DtoSamplingTest> samplingTestList = samplingTestService.findBySamplingIds(samplingConfigIds);
        Map<String, List<DtoSamplingTest>> samplingTestGroup = samplingTestList.stream().collect(Collectors.groupingBy(DtoSamplingTest::getSamplingConfigId));

        collection.forEach(samplingConfig -> {
            Optional<DtoBaseConfig> configOptional = Optional.ofNullable(baseConfigMap.getOrDefault(samplingConfig.getReportCode(), null));
            configOptional.ifPresent(p -> {
                String template = p.getTemplatePath();
                if (!"/".equals(template.substring(0, 1))) {
                    template = String.format("/%s", template);
                }
                samplingConfig.setWorkPath(template);
                samplingConfig.setWorkName(p.getTemplateName());
                samplingConfig.setBaseConfigId(p.getId());
                samplingConfig.setBaseConfigName(p.getTemplateName());
            });
            List<String> testIds = samplingTestGroup.getOrDefault(samplingConfig.getId(), new ArrayList<>())
                    .stream().map(DtoSamplingTest::getTestId).distinct().collect(Collectors.toList());
            samplingConfig.setTestIds(testIds);
        });
    }

    @Autowired
    @Lazy
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    @Lazy
    public void setSamplingTestService(SamplingTestService samplingTestService) {
        this.samplingTestService = samplingTestService;
    }
}
