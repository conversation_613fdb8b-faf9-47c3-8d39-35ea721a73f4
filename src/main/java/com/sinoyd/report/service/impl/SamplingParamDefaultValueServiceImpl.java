package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.dto.DtoSamplingParamDefaultValue;
import com.sinoyd.report.dto.DtoSamplingTest;
import com.sinoyd.report.repository.SamplingParamDefaultValueRepository;
import com.sinoyd.report.service.SamplingParamDefaultValueService;
import com.sinoyd.report.service.SamplingTestService;
import com.sinoyd.report.vo.SamplingParamDefaultValueQueryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采样单参数默认值数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class SamplingParamDefaultValueServiceImpl extends LimsBaseServiceImpl<DtoSamplingParamDefaultValue, String, SamplingParamDefaultValueRepository>
        implements SamplingParamDefaultValueService {

    private SamplingTestService samplingTestService;

    @Override
    public void findByPage(PageBean<DtoSamplingParamDefaultValue> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoSamplingParamDefaultValue a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    public List<DtoSamplingParamDefaultValue> findList(SamplingParamDefaultValueQueryVO queryVO) {
        // 根据samplingConfigId查询采样测试列表
        List<DtoSamplingTest> samplingTests = samplingTestService.findBySamplingIds(Collections.singletonList(queryVO.getSamplingConfigId()));

        //按照查询条件过滤
        if (StringUtils.isNotNull(queryVO.getNeedCma())) {
            samplingTests = samplingTests.stream().filter(samplingTest -> queryVO.getNeedCma().equals(samplingTest.getNeedCma())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNull(queryVO.getNeedCnas())) {
            samplingTests = samplingTests.stream().filter(samplingTest -> queryVO.getNeedCnas().equals(samplingTest.getNeedCnas())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeItemKey())) {
            samplingTests = samplingTests.stream().filter(samplingTest -> samplingTest.getAnalyzeItemName().contains(queryVO.getAnalyzeItemKey()) || samplingTest.getChemicalSymbol().contains(queryVO.getAnalyzeItemKey())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeMethodKey())) {
            samplingTests = samplingTests.stream().filter(samplingTest -> samplingTest.getAnalyzeMethodName().contains(queryVO.getAnalyzeMethodKey()) || samplingTest.getMethodStandardNo().contains(queryVO.getAnalyzeMethodKey())).collect(Collectors.toList());
        }
        // 根据samplingParamId查询采样参数默认值列表
        List<DtoSamplingParamDefaultValue> samplingParamDefaultValues = repository.findBySamplingParamIdIn(Collections.singletonList(queryVO.getSamplingParamId()));

        // 存储结果的列表
        List<DtoSamplingParamDefaultValue> result = new ArrayList<>();

        // 遍历采样测试列表
        samplingTests.forEach(samplingTest -> {
            // 在采样参数默认值列表中查找与当前采样测试匹配的默认值
            Optional<DtoSamplingParamDefaultValue> defaultValueOptional = samplingParamDefaultValues.stream().filter(samplingParamDefaultValue -> samplingTest.getTestId().equals(samplingParamDefaultValue.getTestId())).findFirst();

            // 如果找到匹配的默认值
            if (defaultValueOptional.isPresent()) {
                // 获取匹配的默认值
                DtoSamplingParamDefaultValue samplingParamDefaultValue = defaultValueOptional.get();

                // 更新默认值的属性
                samplingParamDefaultValue.setSampleTypeName(samplingTest.getSampleTypeName());
                samplingParamDefaultValue.setAnalyzeItemName(samplingTest.getAnalyzeItemName());
                samplingParamDefaultValue.setChemicalSymbol(samplingTest.getChemicalSymbol());
                samplingParamDefaultValue.setMethodStandardNo(samplingTest.getMethodStandardNo());
                samplingParamDefaultValue.setAnalyzeMethodName(samplingTest.getAnalyzeMethodName());

                // 将更新后的默认值添加到结果列表中
                result.add(defaultValueOptional.get());
            } else {
                // 如果没有找到匹配的默认值，则创建一个新的默认值
                DtoSamplingParamDefaultValue samplingParamDefaultValue = new DtoSamplingParamDefaultValue();

                // 将采样测试的属性复制到默认值中，除了id属性
                BeanUtils.copyProperties(samplingTest, samplingParamDefaultValue, IBaseConstants.FieldConstant.IGNORE_FIELDS);

                // 设置默认值的samplingParamId属性
                samplingParamDefaultValue.setSamplingParamId(queryVO.getSamplingParamId());

                // 将新创建的默认值添加到结果列表中
                result.add(samplingParamDefaultValue);
            }
        });

        // 返回结果列表
        return result;
    }

    @Override
    @Transactional
    public void deleteBySamplingParamIdIn(Collection<String> samplingParamIds) {
        if (StringUtils.isNotEmpty(samplingParamIds)) {
            List<DtoSamplingParamDefaultValue> samplingParamDefaultValues = repository.findBySamplingParamIdIn(samplingParamIds);
            List<String> ids = samplingParamDefaultValues.stream().map(DtoSamplingParamDefaultValue::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public List<DtoSamplingParamDefaultValue> findBySamplingParamIdIn(Collection<String> samplingParamIds) {
        return repository.findBySamplingParamIdIn(samplingParamIds);
    }

    @Autowired
    @Lazy
    public void setSamplingTestService(SamplingTestService samplingTestService) {
        this.samplingTestService = samplingTestService;
    }

}
