package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.dto.DtoParam;
import com.sinoyd.lims.rms.service.IParamClientService;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoSamplingParam;
import com.sinoyd.report.repository.SamplingParamRepository;
import com.sinoyd.report.service.SamplingParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采样单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
@Slf4j
public class SamplingParamServiceImpl extends LimsBaseServiceImpl<DtoSamplingParam, String, SamplingParamRepository>
        implements SamplingParamService {

    private IParamClientService paramClientService;

    @Override
    public void findByPage(PageBean<DtoSamplingParam> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoSamplingParam a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public DtoSamplingParam save(DtoSamplingParam entity) {
        verifyName(entity);
        saveParam(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoSamplingParam update(DtoSamplingParam entity) {
        verifyName(entity);
        saveParam(entity);
        return super.update(entity);
    }

    /**
     * 保存参数
     *
     * @param entity 实体
     */
    protected void saveParam(DtoSamplingParam entity) {
        DtoParam param = paramClientService.findByParamName(entity.getParamName());
        if (StringUtils.isNull(param)) {
            DtoParam save = new DtoParam();
            save.setId(UUIDHelper.newId());
            save.setParamName(entity.getParamName());
            save.setDimensionId(StringUtils.isNotEmpty(entity.getDimensionId()) ? entity.getDimensionId() : "");
            param = paramClientService.save(save);
        }
        entity.setParamId(StringUtils.isNotNull(param) ? param.getId() : UUIDHelper.guidEmpty());
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.SAMPLING_PARAM, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    @Transactional
    public void deleteBySamplingConfigIdIn(Collection<String> samplingConfigIds) {
        if (StringUtils.isNotEmpty(samplingConfigIds)) {
            List<DtoSamplingParam> samplingParams = repository.findBySamplingConfigIdIn(samplingConfigIds);
            List<String> paramIds = samplingParams.stream().map(DtoSamplingParam::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(paramIds)) {
                logicDeleteById(paramIds);
            }
        }
    }

    @Override
    public List<DtoSamplingParam> findBySamplingConfigIdAndCategory(String samplingConfigId, Integer category) {
        return repository.findBySamplingConfigIdAndParamCategory(samplingConfigId, category);
    }

    @Override
    public List<DtoSamplingParam> findBySamplingConfigIdIn(Collection<String> samplingConfigIds) {
        List<DtoSamplingParam> samplingParams = new ArrayList<>();
        if (StringUtils.isNotEmpty(samplingConfigIds)) {
            samplingParams = repository.findBySamplingConfigIdIn(samplingConfigIds);
        }
        return samplingParams;
    }

    @Override
    public List<DtoSamplingParam> findByIds(Collection<String> ids) {
        return repository.findAllById(ids);
    }

    /**
     * 校验参数名是否存在
     *
     * @param entity 实体
     */
    private void verifyName(DtoSamplingParam entity) {
        if (repository.countByAliasNameAndSamplingConfigIdAndParamCategoryAndIdNot(entity.getAliasName(), entity.getSamplingConfigId(), entity.getParamCategory(), entity.getId()) > 0) {
            throw new BaseException("已存在相同别名的参数！");
        }
    }

    @Autowired
    @Lazy
    public void setParamClientService(IParamClientService paramClientService) {
        this.paramClientService = paramClientService;
    }
}
