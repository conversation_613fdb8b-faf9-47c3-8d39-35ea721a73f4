package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.service.ISampleTypeClientService;
import com.sinoyd.lims.rms.service.ITestClientService;
import com.sinoyd.report.criteria.SamplingTestCriteria;
import com.sinoyd.report.dto.DtoSamplingTest;
import com.sinoyd.report.repository.SamplingTestRepository;
import com.sinoyd.report.service.SamplingTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采样单关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class SamplingTestServiceImpl extends LimsBaseServiceImpl<DtoSamplingTest, String, SamplingTestRepository>
        implements SamplingTestService {

    private ITestClientService testClientService;

    private ISampleTypeClientService sampleTypeClientService;

    @Override
    public void findByPage(PageBean<DtoSamplingTest> pageBean, BaseCriteria criteria) {
        Integer pageNo = pageBean.getPageNo();
        Integer rowsPerPage = pageBean.getRowsPerPage();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        SamplingTestCriteria samplingTestCriteria = resetCriteria(criteria);
        pageBean.setEntityName("DtoSamplingTest a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, samplingTestCriteria);
        pageBean.getData().sort(Comparator.comparing(DtoSamplingTest::getAnalyzeMethodName).thenComparing(DtoSamplingTest::getAnalyzeItemName));
        pageBean.setData(pageBean.getData().stream().skip((pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList()));
        pageBean.setPageNo(pageNo);
        pageBean.setRowsPerPage(rowsPerPage);
    }

    @Override
    @Transactional
    public List<DtoSamplingTest> save(Collection<DtoSamplingTest> entities) {
        List<String> samplingConfigIds = entities.stream().map(DtoSamplingTest::getSamplingConfigId).distinct().collect(Collectors.toList());
        //查找已存在的测试项目配置
        List<DtoSamplingTest> existsTests = StringUtils.isNotNullAndEmpty(samplingConfigIds) ? repository.findBySamplingConfigIdIn(samplingConfigIds) : new ArrayList<>();
        List<DtoSamplingTest> saveList = new ArrayList<>();
        entities.stream().collect(Collectors.groupingBy(DtoSamplingTest::getSamplingConfigId)).forEach((samplingConfigId, testsOfConfig) -> {
            List<DtoSamplingTest> existsTestsOfConfig = existsTests.stream().filter(t -> t.getSamplingConfigId().equals(samplingConfigId)).collect(Collectors.toList());
            //过滤掉已存在的配置
            saveList.addAll(testsOfConfig.stream().filter(t -> existsTestsOfConfig.stream().noneMatch(e -> e.getTestId().equals(t.getTestId()))).collect(Collectors.toList()));
        });
        if (StringUtils.isNotEmpty(saveList)) {
            return super.save(saveList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoSamplingTest> findBySamplingIds(Collection<String> samplingIds) {
        List<DtoSamplingTest> samplingTests = repository.findBySamplingConfigIdIn(samplingIds);
        loadTransientFields(samplingTests);
        return samplingTests;
    }

    @Override
    @Transactional
    public void deleteBySamplingConfigIdIn(Collection<String> samplingConfigIds) {
        if (StringUtils.isNotEmpty(samplingConfigIds)) {
            List<DtoSamplingTest> samplingTests = repository.findBySamplingConfigIdIn(samplingConfigIds);
            List<String> ids = samplingTests.stream().map(DtoSamplingTest::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public void loadTransientFields(Collection<DtoSamplingTest> collection) {
        Set<String> testIds = collection.stream().map(DtoSamplingTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> tests = StringUtils.isNotEmpty(testIds) ? testClientService.findAllByIds(testIds) : new ArrayList<>();
        Set<String> sampleTypeIds = tests.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toSet());
        Map<String, String> sampleTypes = StringUtils.isNotEmpty(sampleTypeIds) ? sampleTypeClientService.findMapByIds(sampleTypeIds) : new HashMap<>();
        collection.forEach(data -> {
            tests.stream().filter(t -> t.getId().equals(data.getTestId())).findFirst().ifPresent(t -> {
                data.initFromTest(t);
                data.setSampleTypeName(sampleTypes.getOrDefault(t.getSampleTypeId(), ""));
            });
        });
    }

    /**
     * 重组查询条件
     *
     * @param criteria 查询条件
     * @return 查询条件
     */
    private SamplingTestCriteria resetCriteria(BaseCriteria criteria) {
        List<DtoTest> tests = testClientService.findValidList();
        SamplingTestCriteria samplingTestCriteria = (SamplingTestCriteria) criteria;
        Set<String> testIds = new HashSet<>();
        if (StringUtils.isNotNullAndEmpty(samplingTestCriteria.getMethodKey())) {
            String key = samplingTestCriteria.getMethodKey();
            List<String> methodKeyTestIds = tests.stream().filter(t -> t.getAnalyzeMethodName().contains(key) || t.getMethodStandardNo().contains(key)).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(methodKeyTestIds);
        }
        if (StringUtils.isNotNullAndEmpty(samplingTestCriteria.getItemKey())) {
            String key = samplingTestCriteria.getItemKey();
            List<String> itemKeyTestIds = tests.stream().filter(t -> t.getAnalyzeItemName().contains(key) || t.getChemicalSymbol().contains(key)).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(itemKeyTestIds);
        }
        samplingTestCriteria.setTestIds(new ArrayList<>(testIds));
        return samplingTestCriteria;
    }



    @Autowired
    @Lazy
    public void setTestClientService(ITestClientService testClientService) {
        this.testClientService = testClientService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeClientService(ISampleTypeClientService sampleTypeClientService) {
        this.sampleTypeClientService = sampleTypeClientService;
    }
}
