package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.dto.DtoSheetPagingConfig;
import com.sinoyd.report.repository.SheetConfigRepository;
import com.sinoyd.report.service.SheetConfigService;
import com.sinoyd.report.service.SheetPagingConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表模板sheet页配置实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
@Service
@Slf4j
public class SheetConfigServiceImpl extends LimsBaseServiceImpl<DtoSheetConfig, String, SheetConfigRepository> implements SheetConfigService {

    private SheetPagingConfigService sheetPagingConfigService;

    /**
     * 保存报表sheet页配置数据
     *
     * @param sheetConfig 报表sheet页配置
     * @return 已保存的sheet页配置数据
     */
    @Override
    @Transactional
    public DtoSheetConfig save(DtoSheetConfig sheetConfig) {
        List<DtoSheetPagingConfig> sheetPagingConfigList = sheetConfig.getSheetPagingConfigList();
        if (StringUtils.isNotEmpty(sheetPagingConfigList)) {
            int priority = sheetPagingConfigList.size();
            for (DtoSheetPagingConfig pagingConfig : sheetPagingConfigList) {
                pagingConfig.setPriority(priority);
                pagingConfig.setSheetConfigId(sheetConfig.getId());
                priority--;
            }
            sheetPagingConfigService.save(sheetPagingConfigList);
        }
        return super.save(sheetConfig);
    }

    /**
     * 修改报表sheet页配置数据
     *
     * @param sheetConfig 报表sheet页配置
     * @return 已保存的sheet页配置数据
     */
    @Override
    @Transactional
    public DtoSheetConfig update(DtoSheetConfig sheetConfig) {
        List<DtoSheetPagingConfig> oldPagingConfigList = sheetPagingConfigService.findBySheetConfigId(sheetConfig.getId());
        if (StringUtils.isNotEmpty(oldPagingConfigList)) {
            //删除旧的分页依据
            List<String> oldPagingConfigIdList = oldPagingConfigList.stream().map(DtoSheetPagingConfig::getId).collect(Collectors.toList());
            sheetPagingConfigService.logicDeleteById(oldPagingConfigIdList);
        }
        List<DtoSheetPagingConfig> sheetPagingConfigList = sheetConfig.getSheetPagingConfigList();
        if (StringUtils.isNotEmpty(sheetPagingConfigList)) {
            int priority = sheetPagingConfigList.size();
            for (DtoSheetPagingConfig pagingConfig : sheetPagingConfigList) {
                pagingConfig.setId(UUIDHelper.newId());
                pagingConfig.setSheetConfigId(sheetConfig.getId());
                pagingConfig.setPriority(priority);
            }
            sheetPagingConfigService.save(sheetPagingConfigList);
        }
        return super.save(sheetConfig);
    }

    /**
     * 批量删除sheet页配置信息
     *
     * @param ids sheet页配置id
     * @return 删除的数量
     */
    @Override
    public Integer logicDeleteById(Collection<String> ids) {
        List<DtoSheetPagingConfig> pagingConfigList = sheetPagingConfigService.findBySheetConfigIdIn(new ArrayList<>(ids), false);
        if (StringUtils.isNotEmpty(pagingConfigList)) {
            List<String> pagingConfigIdList = pagingConfigList.stream().map(DtoSheetPagingConfig::getId).collect(Collectors.toList());
            sheetPagingConfigService.logicDeleteById(pagingConfigIdList);
        }
        return super.logicDeleteById(ids);
    }

    /**
     * 根据报表编码查询sheet页配置
     *
     * @param reportCode 报表编码
     * @return sheet页配置
     */
    @Override
    public List<DtoSheetConfig> findByReportCode(String reportCode) {
        List<DtoSheetConfig> sheetConfigList = repository.findByReportCode(reportCode);
        List<String> sheetConfigIdList = sheetConfigList.stream().map(DtoSheetConfig::getId).collect(Collectors.toList());
        List<DtoSheetPagingConfig> sheetPagingConfigList = StringUtils.isNotEmpty(sheetConfigIdList)
                ? sheetPagingConfigService.findBySheetConfigIdIn(sheetConfigIdList, true) : new ArrayList<>();
        Map<String, List<DtoSheetPagingConfig>> sheetPagingConfigMap = sheetPagingConfigList.stream().collect(Collectors.groupingBy(DtoSheetPagingConfig::getSheetConfigId));
        for (DtoSheetConfig sheetConfig : sheetConfigList) {
            List<DtoSheetPagingConfig> pagingConfigList = sheetPagingConfigMap.getOrDefault(sheetConfig.getId(), new ArrayList<>());
            pagingConfigList.sort(Comparator.comparing(DtoSheetPagingConfig::getPriority).reversed());
            sheetConfig.setSheetPagingConfigList(pagingConfigList);
        }
        return sheetConfigList;
    }

    @Override
    public List<DtoSheetConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields) {
        List<DtoSheetConfig> sheetConfigs = repository.findByReportCodeIn(reportCodes);
        if (isLoadTransientFields) {
            loadTransientFields(sheetConfigs);
        }
        return sheetConfigs;
    }

    @Autowired
    @Lazy
    public void setSheetPagingConfigService(SheetPagingConfigService sheetPagingConfigService) {
        this.sheetPagingConfigService = sheetPagingConfigService;
    }
}
