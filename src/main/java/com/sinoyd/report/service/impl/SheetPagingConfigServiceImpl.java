package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.dto.DtoDataSet;
import com.sinoyd.report.dto.DtoDataSetApplyColumn;
import com.sinoyd.report.dto.DtoSheetConfig;
import com.sinoyd.report.dto.DtoSheetPagingConfig;
import com.sinoyd.report.repository.SheetConfigRepository;
import com.sinoyd.report.repository.SheetPagingConfigRepository;
import com.sinoyd.report.service.DataSetApplyColumnService;
import com.sinoyd.report.service.SheetConfigService;
import com.sinoyd.report.service.SheetPagingConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * sheet页分页配置明细接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/04
 */
@Service
@Slf4j
public class SheetPagingConfigServiceImpl extends LimsBaseServiceImpl<DtoSheetPagingConfig, String, SheetPagingConfigRepository> implements SheetPagingConfigService {

    private DataSetApplyColumnService dataSetApplyColumnService;

    /**
     * 根据sheet页配置id列表查询分页依据
     *
     * @param sheetConfigIdList sheet页配置id列表
     * @return 分页依据列表
     */
    @Override
    public List<DtoSheetPagingConfig> findBySheetConfigIdIn(List<String> sheetConfigIdList, boolean transientFlag) {
        List<DtoSheetPagingConfig> sheetPagingConfigList = repository.findBySheetConfigIdIn(sheetConfigIdList);
        if (transientFlag) {
            List<String> applyColumnIdList = sheetPagingConfigList.stream().map(DtoSheetPagingConfig::getDataSetApplyColumnId).distinct().collect(Collectors.toList());
            if (StringUtils.isNotEmpty(applyColumnIdList)) {
                List<DtoDataSetApplyColumn> applyColumnList = dataSetApplyColumnService.findAll(applyColumnIdList);
                Map<String, DtoDataSetApplyColumn> applyColumnMap = applyColumnList.stream().collect(Collectors.toMap(DtoDataSetApplyColumn::getId, dto -> dto));
                for (DtoSheetPagingConfig sheetPagingConfig : sheetPagingConfigList) {
                    String columnName = applyColumnMap.containsKey(sheetPagingConfig.getDataSetApplyColumnId())
                            ? applyColumnMap.get(sheetPagingConfig.getDataSetApplyColumnId()).getColumnName() : "";
                    sheetPagingConfig.setColumnName(columnName);
                }
            }
        }
        return sheetPagingConfigList;
    }

    /**
     * 根据sheet页配置id查询分页依据
     *
     * @param sheetConfigId sheet页配置id列表
     * @return 分页依据列表
     */
    @Override
    public List<DtoSheetPagingConfig> findBySheetConfigId(String sheetConfigId) {
        return repository.findBySheetConfigId(sheetConfigId);
    }

    /**
     * 保存分页依据
     *
     * @param entity 分页依据对象
     * @return 分页依据列表
     */
    @Override
    public DtoSheetPagingConfig save(DtoSheetPagingConfig entity) {
        return super.save(entity);
    }

    @Autowired
    @Lazy
    public void setDataSetApplyColumnService(DataSetApplyColumnService dataSetApplyColumnService) {
        this.dataSetApplyColumnService = dataSetApplyColumnService;
    }
}
