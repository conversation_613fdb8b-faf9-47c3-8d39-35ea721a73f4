package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.service.ISampleTypeClientService;
import com.sinoyd.lims.rms.service.ITestClientService;
import com.sinoyd.report.dto.DtoSamplingTest;
import com.sinoyd.report.dto.DtoTestFormula2Test;
import com.sinoyd.report.repository.TestFormula2TestRepository;
import com.sinoyd.report.service.TestFormula2TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目公式关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class TestFormula2TestServiceImpl extends LimsBaseServiceImpl<DtoTestFormula2Test, String, TestFormula2TestRepository>
        implements TestFormula2TestService {

    private ITestClientService testClientService;

    private ISampleTypeClientService sampleTypeClientService;

    @Override
    public void findByPage(PageBean<DtoTestFormula2Test> pageBean, BaseCriteria criteria) {
        Integer pageNo = pageBean.getPageNo();
        Integer rowsPerPage = pageBean.getRowsPerPage();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setEntityName("DtoTestFormula2Test a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
        pageBean.getData().sort(Comparator.comparing(DtoTestFormula2Test::getAnalyzeMethodName).thenComparing(DtoTestFormula2Test::getAnalyzeItemName));
        pageBean.setData(pageBean.getData().stream().skip((pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList()));
        pageBean.setPageNo(pageNo);
        pageBean.setRowsPerPage(rowsPerPage);
    }

    @Override
    @Transactional
    public List<DtoTestFormula2Test> save(Collection<DtoTestFormula2Test> entities) {
        List<String> testFormulaIds = entities.stream().map(DtoTestFormula2Test::getTestFormulaId).distinct().collect(Collectors.toList());
        //查找已存在的测试项目配置
        List<DtoTestFormula2Test> existsTests = StringUtils.isNotNullAndEmpty(testFormulaIds) ? repository.findByTestFormulaIdIn(testFormulaIds) : new ArrayList<>();
        List<DtoTestFormula2Test> saveList = new ArrayList<>();
        entities.stream().collect(Collectors.groupingBy(DtoTestFormula2Test::getTestFormulaId)).forEach((testFormulaId, testsOfConfig) -> {
            List<DtoTestFormula2Test> existsTestsOfConfig = existsTests.stream().filter(t -> t.getTestFormulaId().equals(testFormulaId)).collect(Collectors.toList());
            //过滤掉已存在的配置
            saveList.addAll(testsOfConfig.stream().filter(t -> existsTestsOfConfig.stream().noneMatch(e -> e.getTestId().equals(t.getTestId()))).collect(Collectors.toList()));
        });
        if (StringUtils.isNotEmpty(saveList)) {
            return super.save(saveList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoTestFormula2Test> findByTestFormulaIds(Collection<String> testFormulaIds, Boolean needLoadFields) {
        List<DtoTestFormula2Test> tests = repository.findByTestFormulaIdIn(testFormulaIds);
        if (needLoadFields) {
            loadTransientFields(tests);
        }
        return tests;
    }

    /**
     * 根据测试项目ids获取关联集合
     *
     * @param testIds 测试项目ids
     * @return 关联集合
     */
    @Override
    public List<DtoTestFormula2Test> findByTestIds(Collection<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    @Override
    @Transactional
    public void deleteByTestFormulaIdIn(Collection<String> testFormulaIds) {
        if (StringUtils.isNotEmpty(testFormulaIds)) {
            List<DtoTestFormula2Test> testFormula2Tests = repository.findByTestFormulaIdIn(testFormulaIds);
            List<String> ids = testFormula2Tests.stream().map(DtoTestFormula2Test::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public void loadTransientFields(Collection<DtoTestFormula2Test> collection) {
        Set<String> testIds = collection.stream().map(DtoTestFormula2Test::getTestId).collect(Collectors.toSet());
        List<DtoTest> tests = StringUtils.isNotEmpty(testIds) ? testClientService.findAllByIds(testIds) : new ArrayList<>();
        Set<String> sampleTypeIds = tests.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toSet());
        Map<String, String> sampleTypes = StringUtils.isNotEmpty(sampleTypeIds) ? sampleTypeClientService.findMapByIds(sampleTypeIds) : new HashMap<>();
        collection.forEach(data -> {
            tests.stream().filter(t -> t.getId().equals(data.getTestId())).findFirst().ifPresent(t -> {
                data.initFromTest(t);
                data.setSampleTypeName(sampleTypes.getOrDefault(t.getSampleTypeId(), ""));
            });
        });
    }

    @Autowired
    @Lazy
    public void setTestClientService(ITestClientService testClientService) {
        this.testClientService = testClientService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeClientService(ISampleTypeClientService sampleTypeClientService) {
        this.sampleTypeClientService = sampleTypeClientService;
    }
}
