package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.service.ITestClientService;
import com.sinoyd.report.dto.DtoTestFormula2Test;
import com.sinoyd.report.dto.DtoTestFormulaEquation2Test;
import com.sinoyd.report.repository.TestFormulaEquation2TestRepository;
import com.sinoyd.report.service.TestFormula2TestService;
import com.sinoyd.report.service.TestFormulaEquation2TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公式方程关联测试项目数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class TestFormulaEquation2TestServiceImpl extends LimsBaseServiceImpl<DtoTestFormulaEquation2Test, String, TestFormulaEquation2TestRepository>
        implements TestFormulaEquation2TestService {

    private ITestClientService testClientService;

    private TestFormula2TestService testFormula2TestService;

    @Override
    public void findByPage(PageBean<DtoTestFormulaEquation2Test> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoTestFormulaEquation2Test a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    public List<DtoTestFormulaEquation2Test> findList(String formulaEquationId, String testFormulaId) {
        List<DtoTestFormula2Test> testFormula2Tests = testFormula2TestService.findByTestFormulaIds(Collections.singletonList(testFormulaId), true);

        List<DtoTestFormulaEquation2Test> testFormulaEquation2Tests = repository.findByFormulaEquationId(formulaEquationId);

        List<DtoTestFormulaEquation2Test> result = new ArrayList<>();

        testFormula2Tests.forEach(testFormula2Test -> {
            Optional<DtoTestFormulaEquation2Test> formulaEquation2TestOptional = testFormulaEquation2Tests.stream().filter(test -> testFormula2Test.getTestId().equals(test.getTestId())).findFirst();

            // 如果找到匹配的默认值
            if (formulaEquation2TestOptional.isPresent()) {
                // 获取匹配的默认值
                DtoTestFormulaEquation2Test testFormulaEquation2Test = formulaEquation2TestOptional.get();

                // 更新默认值的属性
                testFormulaEquation2Test.setAnalyzeItemName(testFormula2Test.getAnalyzeItemName());
                testFormulaEquation2Test.setChemicalSymbol(testFormula2Test.getChemicalSymbol());
                testFormulaEquation2Test.setMethodStandardNo(testFormula2Test.getMethodStandardNo());
                testFormulaEquation2Test.setAnalyzeMethodName(testFormula2Test.getAnalyzeMethodName());

                // 将更新后的默认值添加到结果列表中
                result.add(testFormulaEquation2Test);
            } else {
                // 如果没有找到匹配的默认值，则创建一个新的默认值
                DtoTestFormulaEquation2Test testFormulaEquation2Test = new DtoTestFormulaEquation2Test();

                BeanUtils.copyProperties(testFormula2Test, testFormulaEquation2Test, IBaseConstants.FieldConstant.IGNORE_FIELDS);

                testFormulaEquation2Test.setFormulaEquationId(formulaEquationId);

                // 将新创建的默认值添加到结果列表中
                result.add(testFormulaEquation2Test);
            }
        });

        // 返回结果列表
        return result;
    }

    @Override
    public List<DtoTestFormulaEquation2Test> findByFormulaEquationIdIn(Collection<String> formulaEquationIds, boolean isLoadTransientFields) {
        List<DtoTestFormulaEquation2Test> collections = repository.findByFormulaEquationIdIn(formulaEquationIds);
        if (isLoadTransientFields) {
            loadTransientFields(collections);
        }
        return collections;
    }

    @Override
    public void loadTransientFields(Collection<DtoTestFormulaEquation2Test> collection) {
        Set<String> testIds = collection.stream().map(DtoTestFormulaEquation2Test::getTestId).collect(Collectors.toSet());
        List<DtoTest> tests = StringUtils.isNotEmpty(testIds) ? testClientService.findAllByIds(testIds) : new ArrayList<>();
        collection.forEach(data -> {
            tests.stream().filter(t -> t.getId().equals(data.getTestId())).findFirst().ifPresent(t -> {
                data.setAnalyzeItemName(t.getAnalyzeItemName());
                data.setAnalyzeMethodName(t.getAnalyzeMethodName());
                data.setMethodStandardNo(t.getMethodStandardNo());
                data.setChemicalSymbol(t.getChemicalSymbol());
            });
        });
    }

    @Autowired
    @Lazy
    public void setTestClientService(ITestClientService testClientService) {
        this.testClientService = testClientService;
    }

    @Autowired
    @Lazy
    public void setTestFormula2TestService(TestFormula2TestService testFormula2TestService) {
        this.testFormula2TestService = testFormula2TestService;
    }
}
