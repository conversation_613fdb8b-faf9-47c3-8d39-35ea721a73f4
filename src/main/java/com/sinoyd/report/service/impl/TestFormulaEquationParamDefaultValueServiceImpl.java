package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.enums.EnumParamCategory;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.enums.EnumObjectType;
import com.sinoyd.report.repository.TestFormulaEquationParamDefaultValueRepository;
import com.sinoyd.report.service.*;
import com.sinoyd.report.vo.TestFormulaEquationParamDefaultValueQueryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目公式方程参数默认值数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class TestFormulaEquationParamDefaultValueServiceImpl extends LimsBaseServiceImpl<DtoTestFormulaEquationParamDefaultValue, String, TestFormulaEquationParamDefaultValueRepository>
        implements TestFormulaEquationParamDefaultValueService {

    private TestFormula2TestService testFormula2TestService;

    private TestFormulaService testFormulaService;

    private SamplingParamService samplingParamService;

    private WorkSheetParamService workSheetParamService;

    private SamplingParamDefaultValueService samplingParamDefaultValueService;

    private WorkSheetParamDefaultValueService workSheetParamDefaultValueService;

    private TestFormulaEquationParamService testFormulaEquationParamService;

    @Override
    public void findByPage(PageBean<DtoTestFormulaEquationParamDefaultValue> pageBean, BaseCriteria criteria, boolean isLoadTransientField) {
        pageBean.setEntityName("DtoTestFormulaEquationParamDefaultValue a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria, isLoadTransientField);
    }

    @Override
    @Transactional
    public List<DtoTestFormulaEquationParamDefaultValue> findList(TestFormulaEquationParamDefaultValueQueryVO queryVO) {
        // 根据测试公式ID查询对应的公式关联测试项目列表
        List<DtoTestFormula2Test> testFormula2Tests = testFormula2TestService.findByTestFormulaIds(Collections.singletonList(queryVO.getTestFormulaId()), true);

        //按照查询条件过滤
        if (StringUtils.isNotNull(queryVO.getNeedCma())) {
            testFormula2Tests = testFormula2Tests.stream().filter(testFormula2Test -> queryVO.getNeedCma().equals(testFormula2Test.getNeedCma())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNull(queryVO.getNeedCnas())) {
            testFormula2Tests = testFormula2Tests.stream().filter(testFormula2Test -> queryVO.getNeedCnas().equals(testFormula2Test.getNeedCnas())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeItemKey())) {
            testFormula2Tests = testFormula2Tests.stream().filter(testFormula2Test -> testFormula2Test.getAnalyzeItemName().contains(queryVO.getAnalyzeItemKey()) || testFormula2Test.getChemicalSymbol().contains(queryVO.getAnalyzeItemKey())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeMethodKey())) {
            testFormula2Tests = testFormula2Tests.stream().filter(testFormula2Test -> testFormula2Test.getAnalyzeMethodName().contains(queryVO.getAnalyzeMethodKey()) || testFormula2Test.getMethodStandardNo().contains(queryVO.getAnalyzeMethodKey())).collect(Collectors.toList());
        }

        List<DtoSamplingParam> samplingParams = new ArrayList<>();
        List<DtoSamplingParamDefaultValue> samplingParamDefaultValues = new ArrayList<>();
        List<DtoWorkSheetParam> workSheetParams = new ArrayList<>();
        List<DtoWorkSheetParamDefaultValue> workSheetParamDefaultValues = new ArrayList<>();

        DtoTestFormula testFormula = testFormulaService.findOne(queryVO.getTestFormulaId());
        if (EnumObjectType.采样单.getValue().equals(testFormula.getObjectType())) {
            samplingParams = samplingParamService.findBySamplingConfigIdAndCategory(testFormula.getObjectId(), EnumParamCategory.数据参数.getValue());
            List<String> samplingParamIds = samplingParams.stream().map(DtoSamplingParam::getId).collect(Collectors.toList());
            samplingParamDefaultValues = StringUtils.isNotEmpty(samplingParamIds) ? samplingParamDefaultValueService.findBySamplingParamIdIn(samplingParamIds) : new ArrayList<>();
        } else if (EnumObjectType.原始记录单.getValue().equals(testFormula.getObjectType())) {
            workSheetParams = workSheetParamService.findByWorkSheetConfigIdAndCategory(testFormula.getObjectId(), EnumParamCategory.数据参数.getValue());
            List<String> workSheetParamIds = workSheetParams.stream().map(DtoWorkSheetParam::getId).collect(Collectors.toList());
            workSheetParamDefaultValues = StringUtils.isNotEmpty(workSheetParamIds) ? workSheetParamDefaultValueService.findByWorkSheetParamIdIn(workSheetParamIds) : new ArrayList<>();
        }

        final List<DtoSamplingParam> finalSamplingParams = samplingParams;
        final List<DtoSamplingParamDefaultValue> finalSamplingParamDefaultValues = samplingParamDefaultValues;
        final List<DtoWorkSheetParam> finalWorkSheetParams = workSheetParams;
        final List<DtoWorkSheetParamDefaultValue> finalWorkSheetParamDefaultValues = workSheetParamDefaultValues;

        // 根据方程参数ID查询对应的公式参数默认值列表
        List<DtoTestFormulaEquationParamDefaultValue> testFormulaEquationParamDefaultValues = repository.findByEquationParamIdIn(Collections.singletonList(queryVO.getEquationParamId()));

        DtoTestFormulaEquationParam testFormulaEquationParam = testFormulaEquationParamService.findOne(queryVO.getEquationParamId());

        // 初始化结果列表
        List<DtoTestFormulaEquationParamDefaultValue> result = new ArrayList<>();

        List<DtoTestFormulaEquationParamDefaultValue> saveList = new ArrayList<>();

        // 遍历DtoTestFormula2Test列表
        testFormula2Tests.forEach(testFormula2Test -> {
            Optional<DtoTestFormulaEquationParamDefaultValue> defaultValueOptional = testFormulaEquationParamDefaultValues.stream().filter(testFormulaEquationParamDefaultValue -> testFormula2Test.getTestId().equals(testFormulaEquationParamDefaultValue.getTestId())).findFirst();

            // 如果找到了对应的公式参数默认值
            if (defaultValueOptional.isPresent()) {
                // 获取该公式参数默认值
                DtoTestFormulaEquationParamDefaultValue testFormulaEquationParamDefaultValue = defaultValueOptional.get();

                // 设置公式参数默认值的属性值
                testFormulaEquationParamDefaultValue.setSampleTypeName(testFormula2Test.getSampleTypeName());
                testFormulaEquationParamDefaultValue.setAnalyzeItemName(testFormula2Test.getAnalyzeItemName());
                testFormulaEquationParamDefaultValue.setChemicalSymbol(testFormula2Test.getChemicalSymbol());
                testFormulaEquationParamDefaultValue.setMethodStandardNo(testFormula2Test.getMethodStandardNo());
                testFormulaEquationParamDefaultValue.setAnalyzeMethodName(testFormula2Test.getAnalyzeMethodName());

                // 将公式参数默认值添加到结果列表中
                result.add(defaultValueOptional.get());
            } else {
                // 如果没有找到对应的公式参数默认值，则创建一个新的公式参数默认值
                DtoTestFormulaEquationParamDefaultValue testFormulaEquationParamDefaultValue = new DtoTestFormulaEquationParamDefaultValue();

                BeanUtils.copyProperties(testFormula2Test, testFormulaEquationParamDefaultValue, IBaseConstants.FieldConstant.IGNORE_FIELDS);

                testFormulaEquationParamDefaultValue.setEquationParamId(queryVO.getEquationParamId());

                if (EnumObjectType.采样单.getValue().equals(testFormula.getObjectType())) {
                    finalSamplingParams.stream().filter(s -> s.getParamName().equals(testFormulaEquationParam.getParamName())).findFirst().ifPresent(s -> {
                        finalSamplingParamDefaultValues.stream().filter(d -> d.getTestId().equals(testFormulaEquationParamDefaultValue.getTestId()) && d.getSamplingParamId().equals(s.getId())).findFirst().ifPresent(d -> {
                            testFormulaEquationParamDefaultValue.setDefaultValue(d.getDefaultValue());
                            saveList.add(testFormulaEquationParamDefaultValue);
                        });
                    });
                } else if (EnumObjectType.原始记录单.getValue().equals(testFormula.getObjectType())) {
                    finalWorkSheetParams.stream().filter(s -> s.getParamName().equals(testFormulaEquationParam.getParamName())).findFirst().ifPresent(s -> {
                        finalWorkSheetParamDefaultValues.stream().filter(d -> d.getTestId().equals(testFormulaEquationParamDefaultValue.getTestId()) && d.getWorkSheetParamId().equals(s.getId())).findFirst().ifPresent(d -> {
                            testFormulaEquationParamDefaultValue.setDefaultValue(d.getDefaultValue());
                            saveList.add(testFormulaEquationParamDefaultValue);
                        });
                    });
                }

                result.add(testFormulaEquationParamDefaultValue);
            }
        });

        if (StringUtils.isNotEmpty(saveList)) {
            repository.saveAll(saveList);
        }

        // 返回结果列表
        return result;
    }

    @Override
    @Transactional
    public void deleteByEquationParamIdIn(Collection<String> equationParamIds) {
        if (StringUtils.isNotEmpty(equationParamIds)) {
            List<DtoTestFormulaEquationParamDefaultValue> testFormulaEquationParamDefaultValues = repository.findByEquationParamIdIn(equationParamIds);
            List<String> ids = testFormulaEquationParamDefaultValues.stream().map(DtoTestFormulaEquationParamDefaultValue::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    /**
     * 根据参数id获取参数默认值
     *
     * @param paramIds 参数ids
     * @return 默认值集合
     */
    @Override
    public List<DtoTestFormulaEquationParamDefaultValue> findByParamIds(Collection<String> paramIds) {
        return repository.findByEquationParamIdIn(paramIds);
    }

    @Override
    public List<DtoTestFormulaEquationParamDefaultValue> findByEquationParamIdIn(Collection<String> equationParamIds) {
        return repository.findByEquationParamIdIn(equationParamIds);
    }

    @Autowired
    @Lazy
    public void setTestFormula2TestService(TestFormula2TestService testFormula2TestService) {
        this.testFormula2TestService = testFormula2TestService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaService(TestFormulaService testFormulaService) {
        this.testFormulaService = testFormulaService;
    }

    @Autowired
    @Lazy
    public void setSamplingParamService(SamplingParamService samplingParamService) {
        this.samplingParamService = samplingParamService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamService(WorkSheetParamService workSheetParamService) {
        this.workSheetParamService = workSheetParamService;
    }

    @Autowired
    @Lazy
    public void setSamplingParamDefaultValueService(SamplingParamDefaultValueService samplingParamDefaultValueService) {
        this.samplingParamDefaultValueService = samplingParamDefaultValueService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamDefaultValueService(WorkSheetParamDefaultValueService workSheetParamDefaultValueService) {
        this.workSheetParamDefaultValueService = workSheetParamDefaultValueService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamService(TestFormulaEquationParamService testFormulaEquationParamService) {
        this.testFormulaEquationParamService = testFormulaEquationParamService;
    }
}
