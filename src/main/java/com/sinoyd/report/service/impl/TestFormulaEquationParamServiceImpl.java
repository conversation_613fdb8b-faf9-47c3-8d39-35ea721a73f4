package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.enums.EnumParamCategory;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.dto.DtoParam;
import com.sinoyd.lims.rms.service.IParamClientService;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.*;
import com.sinoyd.report.enums.EnumObjectType;
import com.sinoyd.report.repository.TestFormulaEquationParamRepository;
import com.sinoyd.report.service.*;
import com.sinoyd.report.vo.TestFormulaEquationParamQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目公式方程参数数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
@Slf4j
public class TestFormulaEquationParamServiceImpl extends LimsBaseServiceImpl<DtoTestFormulaEquationParam, String, TestFormulaEquationParamRepository>
        implements TestFormulaEquationParamService {

    private IParamClientService paramClientService;

    private TestFormulaService testFormulaService;

    private SamplingParamService samplingParamService;

    private WorkSheetParamService workSheetParamService;

    private TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService;

    @Override
    public void findByPage(PageBean<DtoTestFormulaEquationParam> pageBean, BaseCriteria criteria, boolean isLoadTransientField) {
        pageBean.setEntityName("DtoTestFormulaEquationParam a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria, isLoadTransientField);
    }

    @Override
    @Transactional
    public DtoTestFormulaEquationParam update(DtoTestFormulaEquationParam entity) {
        verifyName(entity);
        saveParam(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public DtoTestFormulaEquationParam save(DtoTestFormulaEquationParam entity) {
        verifyName(entity);
        saveParam(entity);
        return super.save(entity);
    }

    /**
     * 根据ids获取公式参数集合
     *
     * @param formulaIds 公式ids
     * @return 公式参数集合
     */
    @Override
    public List<DtoTestFormulaEquationParam> findByTestFormulaIds(Collection<String> formulaIds, Boolean needLoadFields) {
        List<DtoTestFormulaEquationParam> paramList = repository.findByTestFormulaIdIn(formulaIds);
        if (needLoadFields) {
            loadTransientFields(paramList);
        }
        return paramList;
    }

    @Override
    public void loadTransientFields(Collection<DtoTestFormulaEquationParam> collection) {
        List<String> paramIds = collection.stream().map(DtoTestFormulaEquationParam::getId).collect(Collectors.toList());
        List<DtoTestFormulaEquationParamDefaultValue> defaultValueList = testFormulaEquationParamDefaultValueService.findByParamIds(paramIds);
        collection.forEach(p -> {
            List<DtoTestFormulaEquationParamDefaultValue> valueList = defaultValueList.stream()
                    .filter(def -> p.getId().equals(def.getEquationParamId())).collect(Collectors.toList());
            p.setDefaultValueList(valueList);
        });
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.TEST_FORMULA_EQUATION_PARAM, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    @Transactional
    public void deleteByTestFormulaIdIn(Collection<String> testFormulaIds) {
        if (StringUtils.isNotEmpty(testFormulaIds)) {
            List<DtoTestFormulaEquationParam> params = repository.findByTestFormulaIdIn(testFormulaIds);
            List<String> ids = params.stream().map(DtoTestFormulaEquationParam::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    @Transactional
    public List<DtoTestFormulaEquationParam> paramList(TestFormulaEquationParamQueryVO queryVO) {
        DtoTestFormula testFormula = testFormulaService.findOne(queryVO.getTestFormulaId());
        List<DtoTestFormulaEquationParam> params = repository.findByTestFormulaIdIn(Collections.singletonList(queryVO.getTestFormulaId()));
        List<DtoTestFormulaEquationParam> saveParams = new ArrayList<>();
        Set<String> deleteIds = new HashSet<>();
        if (StringUtils.isNotNull(testFormula)) {
            List<DtoTestFormulaEquationParam> existDataParams = params.stream().filter(p -> EnumParamCategory.数据参数.getValue().equals(p.getParamCategory())).collect(Collectors.toList());
            if (EnumObjectType.采样单.getValue().equals(testFormula.getObjectType())) {
                List<DtoSamplingParam> samplingParams = samplingParamService.findBySamplingConfigIdAndCategory(testFormula.getObjectId(), EnumParamCategory.数据参数.getValue());
                samplingParams.stream().filter(p -> existDataParams.stream().noneMatch(e -> e.getParamName().equals(p.getParamName()))).forEach(param -> {
                    dealParam(params, saveParams, param);
                });
                deleteIds.addAll(existDataParams.stream().filter(p -> samplingParams.stream().noneMatch(e -> e.getParamName().equals(p.getParamName()))).map(DtoTestFormulaEquationParam::getId).collect(Collectors.toSet()));
            } else if (EnumObjectType.原始记录单.getValue().equals(testFormula.getObjectType())) {
                List<DtoWorkSheetParam> workSheetParams = workSheetParamService.findByWorkSheetConfigIdAndCategory(testFormula.getObjectId(), EnumParamCategory.数据参数.getValue());
                workSheetParams.stream().filter(p -> existDataParams.stream().noneMatch(e -> e.getParamName().equals(p.getParamName()))).forEach(param -> {
                    dealParam(params, saveParams, param);
                });
                deleteIds.addAll(existDataParams.stream().filter(p -> workSheetParams.stream().noneMatch(e -> e.getParamName().equals(p.getParamName()))).map(DtoTestFormulaEquationParam::getId).collect(Collectors.toSet()));
            }
        }
        if (StringUtils.isNotEmpty(saveParams)) {
            saveParams.forEach(p -> p.setTestFormulaId(queryVO.getTestFormulaId()));
            repository.saveAll(saveParams);
        }
        if (StringUtils.isNotEmpty(deleteIds)) {
            logicDeleteById(deleteIds);
            params.removeIf(p -> deleteIds.contains(p.getId()));
        }
        List<DtoTestFormulaEquationParam> resultParams = params;
        if (StringUtils.isNotNullAndEmpty(queryVO.getParamName())) {
            resultParams = resultParams.stream().filter(p -> p.getParamName().contains(queryVO.getParamName())).collect(Collectors.toList());
        }
        if (StringUtils.isNotNullAndEmpty(queryVO.getParamCategory())) {
            if (EnumParamCategory.公式参数.getValue().equals(queryVO.getParamCategory())) {
                resultParams = resultParams.stream().filter(p -> EnumParamCategory.公式参数.getValue().equals(p.getParamCategory())).collect(Collectors.toList());
            } else {
                resultParams = resultParams.stream().filter(p -> EnumParamCategory.数据参数.getValue().equals(p.getParamCategory())).collect(Collectors.toList());
            }
        }
        resultParams.sort(Comparator.comparing(DtoTestFormulaEquationParam::getOrderNum, Comparator.reverseOrder()));
        resultParams.forEach(p->p.setParamCategoryLabel(EnumParamCategory.getEnumItem(p.getParamCategory()).name()));
        return resultParams;
    }

    /**
     * 添加参数
     *
     * @param params     参数列表
     * @param saveParams 保存参数列表
     * @param param      参数对象
     */
    private void dealParam(List<DtoTestFormulaEquationParam> params, List<DtoTestFormulaEquationParam> saveParams, Object param) {
        DtoTestFormulaEquationParam formulaEquationParam = initParam(param);
        params.add(formulaEquationParam);
        saveParams.add(formulaEquationParam);
    }

    /**
     * 初始化参数对象
     *
     * @param param 参数对象
     * @return 初始化后的参数对象
     */
    private DtoTestFormulaEquationParam initParam(Object param) {
        DtoTestFormulaEquationParam formulaEquationParam = new DtoTestFormulaEquationParam();
        BeanUtils.copyProperties(param, formulaEquationParam, IBaseConstants.FieldConstant.IGNORE_FIELDS);
        return formulaEquationParam;
    }

    /**
     * 校验参数名称重复
     *
     * @param entity 参数对象
     */
    private void verifyName(DtoTestFormulaEquationParam entity) {
        if (repository.countByAliasNameAndTestFormulaIdAndIdNot(entity.getAliasName(), entity.getTestFormulaId(), entity.getId()) > 0) {
            throw new BaseException("已存在相同别名的参数！");
        }
    }

    /**
     * 保存参数
     *
     * @param entity 实体
     */
    protected void saveParam(DtoTestFormulaEquationParam entity) {
        DtoParam param = paramClientService.findByParamName(entity.getParamName());
        if (StringUtils.isNull(param)) {
            DtoParam save = new DtoParam();
            save.setId(UUIDHelper.newId());
            save.setParamName(entity.getParamName());
            save.setDimensionId(UUIDHelper.guidEmpty());
            param = paramClientService.save(save);
        }
        entity.setParamId(StringUtils.isNotNull(param) ? param.getId() : UUIDHelper.guidEmpty());
    }

    @Autowired
    @Lazy
    public void setParamClientService(IParamClientService paramClientService) {
        this.paramClientService = paramClientService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaService(TestFormulaService testFormulaService) {
        this.testFormulaService = testFormulaService;
    }

    @Autowired
    @Lazy
    public void setSamplingParamService(SamplingParamService samplingParamService) {
        this.samplingParamService = samplingParamService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamService(WorkSheetParamService workSheetParamService) {
        this.workSheetParamService = workSheetParamService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamDefaultValueService(TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService) {
        this.testFormulaEquationParamDefaultValueService = testFormulaEquationParamDefaultValueService;
    }
}
