package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.dto.DtoTestFormulaEquation;
import com.sinoyd.report.repository.TestFormulaEquationRepository;
import com.sinoyd.report.service.TestFormulaEquationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目公式方程数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class TestFormulaEquationServiceImpl extends LimsBaseServiceImpl<DtoTestFormulaEquation, String, TestFormulaEquationRepository>
        implements TestFormulaEquationService {

    @Override
    public void findByPage(PageBean<DtoTestFormulaEquation> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoTestFormulaEquation a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public void batchSave(List<DtoTestFormulaEquation> entities, String testFormulaId) {
        List<String> deleteIds = new ArrayList<>();
        List<DtoTestFormulaEquation> existEntities = repository.findByTestFormulaIdIn(Collections.singleton(testFormulaId));
        entities.stream().collect(Collectors.groupingBy(DtoTestFormulaEquation::getEquationCategory)).forEach((equationCategory, testFormulaEquations) -> {
            List<DtoTestFormulaEquation> existEntitiesByCategory = existEntities.stream().filter(entity -> entity.getEquationCategory().equals(equationCategory)).collect(Collectors.toList());
            List<String> delIdsForCategory = existEntitiesByCategory.stream().filter(e -> testFormulaEquations.stream().noneMatch(f -> f.getId().equals(e.getId()))).map(DtoTestFormulaEquation::getId).collect(Collectors.toList());
            deleteIds.addAll(delIdsForCategory);
        });
        if (StringUtils.isNotEmpty(deleteIds)) {
            logicDeleteById(deleteIds);
        }
        if (StringUtils.isNotEmpty(entities)) {
            save(entities);
        }
    }

    @Override
    @Transactional
    public void deleteByTestFormulaIdIn(Collection<String> testFormulaIds) {
        if (StringUtils.isNotEmpty(testFormulaIds)) {
            List<DtoTestFormulaEquation> testFormulaEquations = repository.findByTestFormulaIdIn(testFormulaIds);
            List<String> ids = testFormulaEquations.stream().map(DtoTestFormulaEquation::getId).collect(java.util.stream.Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public List<DtoTestFormulaEquation> findByTestFormulaIdIn(Collection<String> testFormulaIds) {
        return repository.findByTestFormulaIdIn(testFormulaIds);
    }
}
