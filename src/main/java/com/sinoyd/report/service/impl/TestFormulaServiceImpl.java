package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.base.util.BaseUtil;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.service.ITestClientService;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.criteria.TestFormulaCriteria;
import com.sinoyd.report.dto.*;
import com.sinoyd.base.enums.EnumEquationCategory;
import com.sinoyd.report.entity.TestFormula;
import com.sinoyd.report.repository.TestFormulaRepository;
import com.sinoyd.report.service.*;
import com.sinoyd.report.vo.FormulaQueryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目公式数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class TestFormulaServiceImpl extends LimsBaseServiceImpl<DtoTestFormula, String, TestFormulaRepository>
        implements TestFormulaService {

    private SamplingConfigService samplingConfigService;

    private WorkSheetConfigService workSheetConfigService;

    private ITestClientService testClientService;

    private TestFormula2TestService testFormula2TestService;

    private TestFormulaEquationService testFormulaEquationService;

    private TestFormulaEquationParamService testFormulaEquationParamService;

    private TestFormulaEquation2TestService testFormulaEquation2TestService;

    private TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService;


    @Override
    public void findByPage(PageBean<DtoTestFormula> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoTestFormula a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, resetCriteria(criteria));
    }

    /**
     * 根据对象id查询
     *
     * @param objectIds 对象id
     * @return 对象集合
     */
    @Override
    public List<DtoTestFormula> findByObjectIds(Collection<String> objectIds, Boolean isLoadTransientFields) {
        List<DtoTestFormula> testFormulaList = repository.findByObjectIdIn(objectIds);
        if (isLoadTransientFields) {
            loadTransientFields(testFormulaList);
        }
        return testFormulaList;
    }

    /**
     * 根据 测试项目ids 获取公式
     *
     * @param testIds               测试项目ids
     * @param isLoadTransientFields 是否详细信息
     * @return 公式集合
     */
    @Override
    public List<DtoTestFormula> findByTestIds(Collection<String> testIds, Boolean isLoadTransientFields) {
        List<DtoTestFormula2Test> formula2TestList = testFormula2TestService.findByTestIds(testIds);
        Set<String> formulaIds = formula2TestList.stream().map(DtoTestFormula2Test::getTestFormulaId).collect(Collectors.toSet());
        List<DtoTestFormula> testFormulaList = repository.findAllById(formulaIds);
        if (isLoadTransientFields) {
            loadTransientFields(testFormulaList);
        }
        return testFormulaList;
    }

    @Override
    @Transactional
    public DtoTestFormula update(DtoTestFormula entity) {
        DtoTestFormula old = this.findOne(entity.getId());
        if (!entity.getObjectId().equals(old.getObjectId())) {
            SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(entity, IReportEventName.TEST_FORMULA, IEventAction.UPDATE));
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.TEST_FORMULA, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    @Transactional
    public void setEnable(List<String> ids, Boolean isEnable) {
        List<DtoTestFormula> testFormulas = StringUtils.isNotEmpty(ids) ? repository.findAllById(ids) : new ArrayList<>();
        testFormulas.forEach(dtoTestFormula -> dtoTestFormula.setIsEnable(isEnable));
        repository.saveAll(testFormulas);
    }

    @Override
    @Transactional
    public void copy(List<String> ids) {
        List<DtoTestFormula> testFormulas = this.findAll(ids);
        List<DtoTestFormula> saveList = new ArrayList<>();
        List<DtoTestFormula2Test> saveTestList = new ArrayList<>();
        List<DtoTestFormulaEquation> saveEquationList = new ArrayList<>();
        List<DtoTestFormulaEquationParam> saveEqParamsList = new ArrayList<>();
        List<DtoTestFormulaEquation2Test> saveEq2TestList = new ArrayList<>();
        List<DtoTestFormulaEquationParamDefaultValue> saveEqParamDefaultValList = new ArrayList<>();


        List<DtoTestFormula2Test> testFormula2Tests = testFormula2TestService.findByTestFormulaIds(ids, Boolean.FALSE);
        //查询到公式方程
        List<DtoTestFormulaEquation> formulaEquations = testFormulaEquationService.findByTestFormulaIdIn(ids);
        Map<String, List<DtoTestFormulaEquation>> equationGroup = formulaEquations.stream().collect(Collectors.groupingBy(DtoTestFormulaEquation::getTestFormulaId));
        //查询方程参数
        List<DtoTestFormulaEquationParam> equationParams = testFormulaEquationParamService.findByTestFormulaIds(ids, false);
        Map<String, List<DtoTestFormulaEquationParam>> formulaEqParamGroup = equationParams.stream().collect(Collectors.groupingBy(DtoTestFormulaEquationParam::getTestFormulaId));
        List<String> equationIds = formulaEquations.stream().map(DtoTestFormulaEquation::getId).collect(Collectors.toList());
        //查询方程关联测试项目
        List<DtoTestFormulaEquation2Test> equation2Tests = testFormulaEquation2TestService.findByFormulaEquationIdIn(equationIds, false);
        Map<String, List<DtoTestFormulaEquation2Test>> equation2TestGroup = equation2Tests.stream().collect(Collectors.groupingBy(DtoTestFormulaEquation2Test::getFormulaEquationId));
        //查询方程参数默认值
        List<String> equationParamIds = equationParams.stream().map(DtoTestFormulaEquationParam::getId).collect(Collectors.toList());
        List<DtoTestFormulaEquationParamDefaultValue> paramDefaultValues = testFormulaEquationParamDefaultValueService.findByEquationParamIdIn(equationParamIds);
        Map<String, List<DtoTestFormulaEquationParamDefaultValue>> defaultValGroup = paramDefaultValues.stream().collect(Collectors.groupingBy(DtoTestFormulaEquationParamDefaultValue::getEquationParamId));
        testFormulas.forEach(dtoTestFormula -> {
            DtoTestFormula copy = new DtoTestFormula();
            BeanUtils.copyProperties(dtoTestFormula, copy, IBaseConstants.FieldConstant.IGNORE_FIELDS);
            copy.setIsEnable(Boolean.TRUE);
            copy.setFormulaName(dtoTestFormula.getFormulaName() + "【复制】");
            saveList.add(copy);
            testFormula2Tests.stream().filter(t -> t.getTestFormulaId().equals(dtoTestFormula.getId())).forEach(t -> {
                DtoTestFormula2Test copyTest = new DtoTestFormula2Test();
                BeanUtils.copyProperties(t, copyTest, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                copyTest.setTestFormulaId(copy.getId());
                saveTestList.add(copyTest);
            });
            //复制公式方程
            for (DtoTestFormulaEquation oldEquation : equationGroup.getOrDefault(dtoTestFormula.getId(), new ArrayList<>())) {
                DtoTestFormulaEquation copyEquation = new DtoTestFormulaEquation();
                BeanUtils.copyProperties(oldEquation, copyEquation, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                copyEquation.setTestFormulaId(copy.getId());
                //复制方程关联测试项目
                for (DtoTestFormulaEquation2Test oldEquation2Test : equation2TestGroup.getOrDefault(oldEquation.getId(), new ArrayList<>())) {
                    DtoTestFormulaEquation2Test copyEq2Test = new DtoTestFormulaEquation2Test();
                    BeanUtils.copyProperties(oldEquation2Test, copyEq2Test, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                    copyEq2Test.setFormulaEquationId(copyEquation.getId());
                    saveEq2TestList.add(copyEq2Test);
                }
                saveEquationList.add(copyEquation);
            }
            //复制方程参数
            for (DtoTestFormulaEquationParam oldEqParam : formulaEqParamGroup.getOrDefault(dtoTestFormula.getId(), new ArrayList<>())) {
                DtoTestFormulaEquationParam copyParam = new DtoTestFormulaEquationParam();
                BeanUtils.copyProperties(oldEqParam, copyParam, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                copyParam.setTestFormulaId(copy.getId());
                //复制方程参数默认值
                for (DtoTestFormulaEquationParamDefaultValue oldDefaultVal : defaultValGroup.getOrDefault(oldEqParam.getId(), new ArrayList<>())) {
                    DtoTestFormulaEquationParamDefaultValue copyDefaultVal = new DtoTestFormulaEquationParamDefaultValue();
                    BeanUtils.copyProperties(oldDefaultVal, copyDefaultVal, IBaseConstants.FieldConstant.IGNORE_FIELDS);
                    copyDefaultVal.setEquationParamId(copyParam.getId());
                    saveEqParamDefaultValList.add(copyDefaultVal);
                }
                saveEqParamsList.add(copyParam);
            }

        });
        if (StringUtils.isNotEmpty(saveList)) {
            repository.saveAll(saveList);
        }
        if (StringUtils.isNotEmpty(saveTestList)) {
            testFormula2TestService.batchSave(saveTestList);
        }
        if (StringUtils.isNotEmpty(saveEquationList)) {
            testFormulaEquationService.batchSave(saveEquationList);
        }
        if (StringUtils.isNotEmpty(saveEqParamsList)) {
            testFormulaEquationParamService.batchSave(saveEqParamsList);
        }
        if (StringUtils.isNotEmpty(saveEqParamDefaultValList)) {
            testFormulaEquationParamDefaultValueService.batchSave(saveEqParamDefaultValList);
        }
        if (StringUtils.isNotEmpty(saveEq2TestList)) {
            testFormulaEquation2TestService.batchSave(saveEq2TestList);
        }
    }

    @Override
    public String bindFormulaFlag(List<String> objectIds) {
        List<DtoSamplingConfig> samplingConfigs = StringUtils.isNotEmpty(objectIds) ? samplingConfigService.findAll(objectIds) : new ArrayList<>();
        List<DtoWorkSheetConfig> workSheetConfigs = StringUtils.isNotEmpty(objectIds) ? workSheetConfigService.findAll(objectIds) : new ArrayList<>();
        List<DtoTestFormula> testFormulas = StringUtils.isNotEmpty(objectIds) ? repository.findByObjectIdIn(objectIds) : new ArrayList<>();
        List<String> strs = new ArrayList<>();
        if (StringUtils.isNotEmpty(testFormulas)) {
            testFormulas.stream().collect(Collectors.groupingBy(DtoTestFormula::getObjectId)).forEach((objectId, testFormulasPart) -> {
                String objectName = "";
                Optional<DtoSamplingConfig> samplingConfig = samplingConfigs.stream().filter(s -> s.getId().equals(objectId)).findFirst();
                if (samplingConfig.isPresent()) {
                    objectName = samplingConfig.get().getFormName();
                }
                Optional<DtoWorkSheetConfig> workSheetConfig = workSheetConfigs.stream().filter(w -> w.getId().equals(objectId)).findFirst();
                if (workSheetConfig.isPresent()) {
                    objectName = workSheetConfig.get().getFormName();
                }
                String formulaNames = testFormulasPart.stream().map(DtoTestFormula::getFormulaName).collect(Collectors.joining(","));
                strs.add(String.format("%s已经配置测试项目公式【%s】", objectName, formulaNames));
            });
        }
        if (StringUtils.isNotEmpty(strs)) {
            return String.join("；", strs);
        }
        return "";
    }

    @Override
    public void loadTransientFields(Collection<DtoTestFormula> collection) {
        List<String> objectIds = collection.stream().map(DtoTestFormula::getObjectId).collect(Collectors.toList());
        List<String> ids = collection.stream().map(DtoTestFormula::getId).collect(Collectors.toList());
        List<DtoTestFormula2Test> testFormula2Tests = StringUtils.isNotEmpty(ids) ? testFormula2TestService.findByTestFormulaIds(ids, false) : new ArrayList<>();
        List<String> testIds = testFormula2Tests.stream().map(DtoTestFormula2Test::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtils.isNotEmpty(testIds) ? testClientService.findAllByIds(testIds) : new ArrayList<>();
        List<String> parentTestIds = tests.stream().map(DtoTest::getParentId).filter(t -> StringUtils.isNotNullAndEmpty(t) && !UUIDHelper.guidEmpty().equals(t)).distinct().collect(Collectors.toList());
        List<DtoTest> parentTests = StringUtils.isNotEmpty(parentTestIds) ? testClientService.findAllByIds(parentTestIds) : new ArrayList<>();
        List<DtoTestFormulaEquation> testFormulaEquations = StringUtils.isNotEmpty(ids) ? testFormulaEquationService.findByTestFormulaIdIn(ids) : new ArrayList<>();
        List<DtoTestFormulaEquationParam> testFormulaEquationParams = StringUtils.isNotEmpty(ids) ? testFormulaEquationParamService.findByTestFormulaIds(ids, true) : new ArrayList<>();
        List<DtoSamplingConfig> samplingConfigs = StringUtils.isNotEmpty(objectIds) ? samplingConfigService.findAll(objectIds) : new ArrayList<>();
        List<DtoWorkSheetConfig> workSheetConfigs = StringUtils.isNotEmpty(objectIds) ? workSheetConfigService.findAll(objectIds) : new ArrayList<>();
        collection.forEach(dtoTestFormula -> {
            samplingConfigs.stream().filter(dtoSamplingConfig -> dtoSamplingConfig.getId().equals(dtoTestFormula.getObjectId())).findFirst().ifPresent(dtoSamplingConfig -> dtoTestFormula.setObjectName(dtoSamplingConfig.getFormName()));
            workSheetConfigs.stream().filter(dtoWorkSheetConfig -> dtoWorkSheetConfig.getId().equals(dtoTestFormula.getObjectId())).findFirst().ifPresent(dtoWorkSheetConfig -> dtoTestFormula.setObjectName(dtoWorkSheetConfig.getFormName()));
            List<DtoTestFormula2Test> testsOfFormula = testFormula2Tests.stream().filter(dtoTestFormula2Test -> dtoTestFormula2Test.getTestFormulaId().equals(dtoTestFormula.getId())).collect(Collectors.toList());
            dtoTestFormula.setTestNum(testsOfFormula.size());
            List<String> testIdsOfFormula = testsOfFormula.stream().map(DtoTestFormula2Test::getTestId).collect(Collectors.toList());
            List<DtoTest> testsOfDtoFormula = tests.stream().filter(dtoTest -> testIdsOfFormula.contains(dtoTest.getId())).collect(Collectors.toList());
            testsOfDtoFormula.forEach(dtoTest -> {
                if (StringUtils.isNull(dtoTest.getParentId())) {
                    dtoTest.setParentId(UUIDHelper.guidEmpty());
                }
            });
            List<String> testNames = new ArrayList<>();
            testsOfDtoFormula.stream().collect(Collectors.groupingBy(DtoTest::getParentId)).forEach((parentId, childTests) -> {
                Optional<DtoTest> parentTest = parentTests.stream().filter(dtoTest -> dtoTest.getId().equals(parentId)).findFirst();
                if (parentTest.isPresent() && parentTest.get().getIsTotalTest() && parentTest.get().getMergeBase() <= childTests.size()) {
                    testNames.add(parentTest.get().getAnalyzeItemName());
                } else {
                    testNames.addAll(childTests.stream().map(DtoTest::getAnalyzeItemName).collect(Collectors.toList()));
                }
            });
            dtoTestFormula.setTestIds(testIdsOfFormula);
            dtoTestFormula.setTestNames(testNames.stream().distinct().sorted(Comparator.comparing(String::toString)).collect(Collectors.joining(",")));
            Optional<DtoTestFormulaEquation> testFormulaEquation = testFormulaEquations.stream()
                    .filter(dtoTestFormulaEquation -> dtoTestFormulaEquation.getTestFormulaId().equals(dtoTestFormula.getId())
                            && EnumEquationCategory.样品出证.getValue().equals(dtoTestFormulaEquation.getEquationCategory())).findFirst();
            dtoTestFormula.setTestFormulaEquationList(testFormulaEquations);
            testFormulaEquation.ifPresent(dtoTestFormulaEquation -> dtoTestFormula.setSampleFormula(dtoTestFormulaEquation.getCalculateEquation()));
            List<DtoTestFormulaEquationParam> paramList = testFormulaEquationParams.stream().filter(param -> dtoTestFormula.getId().equals(param.getTestFormulaId())).collect(Collectors.toList());
            dtoTestFormula.setTestFormulaEquationParamList(paramList);
        });
    }

    /**
     * 根据公式id及测试项目id获取公式集合
     *
     * @param formulaId 公式id
     * @param testId    测试项目id
     * @return 公式集合
     */
    @Override
    public List<DtoTestFormula> findByFormulaIdAndTestId(String formulaId, String testId) {
        Optional<DtoTestFormula> formula = repository.findById(formulaId);
        List<DtoTestFormula> testFormulaList = new ArrayList<>();
        if (formula.isPresent()) {
            testFormulaList = findByConfigIdAndTestId(formula.get().getObjectId(), testId);
        }
        return testFormulaList;
    }

    /**
     * 查询表单配置公式
     *
     * @param queryVO 公式查询对象
     * @return 公式集合
     */
    @Override
    public List<DtoTestFormula> findSheetConfigFormula(FormulaQueryVO queryVO) {
        List<DtoTestFormula> testFormulaList = new ArrayList<>();
        if (StringUtils.isNotEmpty(queryVO.getSheetConfigIds())) {
            List<DtoTestFormula> formulaList = this.findByObjectIds(queryVO.getSheetConfigIds(), true);
            if (StringUtils.isNotEmpty(queryVO.getTestId()) && !UUIDHelper.guidEmpty().equals(queryVO.getTestId())) {
                testFormulaList = formulaList.stream().filter(p -> p.getTestIds().contains(queryVO.getTestId())).collect(Collectors.toList());
            }
            if (StringUtils.isNotEmpty(queryVO.getTestIds())) {
                for (String testId : queryVO.getTestIds()) {
                    testFormulaList.addAll(formulaList.stream().filter(p -> p.getTestIds().contains(testId)).collect(Collectors.toList()));
                }
            }
        }
        return testFormulaList.stream()
                .filter(BaseUtil.distinctByKey(TestFormula::getId))
                .collect(Collectors.toList());
    }

    /**
     * 根据表单配置id及测试项目id获取公式集合
     *
     * @param configId 表单配置id
     * @param testId   测试项目id
     * @return 公式集合
     */
    private List<DtoTestFormula> findByConfigIdAndTestId(String configId, String testId) {
        List<DtoTestFormula> testFormulaList = new ArrayList<>();
        if (StringUtils.isNotEmpty(configId) && !UUIDHelper.guidEmpty().equals(configId)) {
            List<DtoTestFormula> formulaList = this.findByObjectIds(Collections.singletonList(configId), true);
            testFormulaList = formulaList.stream().filter(p -> p.getTestIds().contains(testId)).collect(Collectors.toList());
        }
        return testFormulaList;
    }

    /**
     * 根据公式ids获取公式集合
     *
     * @param ids 公式ids
     * @return 公式集合
     */
    @Override
    public List<DtoTestFormula> findAllByIds(Collection<String> ids) {
        List<DtoTestFormula> formulaList = repository.findAllById(ids);
        //公式信息
        loadTransientFields(formulaList);
        return formulaList;
    }

    /**
     * 重组查询条件
     *
     * @param criteria 查询条件
     * @return 查询条件
     */
    private TestFormulaCriteria resetCriteria(BaseCriteria criteria) {
        List<DtoTest> tests = testClientService.findValidList();
        TestFormulaCriteria testFormulaCriteria = (TestFormulaCriteria) criteria;
        Set<String> testIds = new HashSet<>();
        boolean keyFlag = false;
        if (StringUtils.isNotNullAndEmpty(testFormulaCriteria.getMethodName())) {
            String key = testFormulaCriteria.getMethodName();
            List<String> methodKeyTestIds = tests.stream().filter(t -> t.getAnalyzeMethodName().toUpperCase().contains(key.toUpperCase())
                    || t.getMethodStandardNo().toUpperCase().contains(key.toUpperCase())).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(methodKeyTestIds);
            keyFlag = true;
        }
        if (StringUtils.isNotNullAndEmpty(testFormulaCriteria.getItemName())) {
            String key = testFormulaCriteria.getItemName();
            List<String> itemKeyTestIds = tests.stream().filter(t -> t.getAnalyzeItemName().toUpperCase().contains(key.toUpperCase())
                    || t.getChemicalSymbol().toUpperCase().contains(key.toUpperCase())).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(itemKeyTestIds);
            keyFlag = true;
        }
        if(keyFlag && StringUtils.isEmpty(testIds)){
            testIds.add(UUIDHelper.guidEmpty());
        }
        testFormulaCriteria.setTestIds(new ArrayList<>(testIds));
        return testFormulaCriteria;
    }

    @Autowired
    @Lazy
    public void setSamplingConfigService(SamplingConfigService samplingConfigService) {
        this.samplingConfigService = samplingConfigService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetConfigService(WorkSheetConfigService workSheetConfigService) {
        this.workSheetConfigService = workSheetConfigService;
    }

    @Autowired
    @Lazy
    public void setTestClientService(ITestClientService testClientService) {
        this.testClientService = testClientService;
    }

    @Autowired
    @Lazy
    public void setTestFormula2TestService(TestFormula2TestService testFormula2TestService) {
        this.testFormula2TestService = testFormula2TestService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationService(TestFormulaEquationService testFormulaEquationService) {
        this.testFormulaEquationService = testFormulaEquationService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamService(TestFormulaEquationParamService testFormulaEquationParamService) {
        this.testFormulaEquationParamService = testFormulaEquationParamService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquation2TestService(TestFormulaEquation2TestService testFormulaEquation2TestService) {
        this.testFormulaEquation2TestService = testFormulaEquation2TestService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaEquationParamDefaultValueService(TestFormulaEquationParamDefaultValueService testFormulaEquationParamDefaultValueService) {
        this.testFormulaEquationParamDefaultValueService = testFormulaEquationParamDefaultValueService;
    }
}
