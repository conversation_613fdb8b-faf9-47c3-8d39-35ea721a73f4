package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoBaseConfig;
import com.sinoyd.report.dto.DtoWorkSheetConfig;
import com.sinoyd.report.dto.DtoWorkSheetParam;
import com.sinoyd.report.dto.DtoWorkSheetTest;
import com.sinoyd.report.repository.WorkSheetConfigRepository;
import com.sinoyd.report.service.BaseConfigService;
import com.sinoyd.report.service.WorkSheetConfigService;
import com.sinoyd.report.service.WorkSheetParamService;
import com.sinoyd.report.service.WorkSheetTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始记录单配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class WorkSheetConfigServiceImpl extends LimsBaseServiceImpl<DtoWorkSheetConfig, String, WorkSheetConfigRepository> implements WorkSheetConfigService {

    private BaseConfigService baseConfigService;

    private WorkSheetTestService workSheetTestService;

    private WorkSheetParamService workSheetParamService;

    @Override
    public void findByPage(PageBean<DtoWorkSheetConfig> pb, BaseCriteria criteria) {
        pb.setEntityName("DtoWorkSheetConfig a");
        pb.setSelect("select a");
        super.findByPage(pb, criteria);
    }

    @Override
    @Transactional
    public DtoWorkSheetConfig save(DtoWorkSheetConfig entity) {
        verify(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoWorkSheetConfig update(DtoWorkSheetConfig entity) {
        verify(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.WORKSHEET_CONFIG, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    public List<DtoWorkSheetConfig> findByTestIds(Collection<String> testIds) {
        List<DtoWorkSheetTest> relationTests = workSheetTestService.findByTestIds(testIds);
        Set<String> workSheetConfigIds = relationTests.stream().map(DtoWorkSheetTest::getWorkSheetConfigId).collect(Collectors.toSet());
        return findAll(workSheetConfigIds);
    }

    @Override
    public List<DtoWorkSheetConfig> findByReportCodeIn(Collection<String> reportCodes, boolean isLoadTransientFields) {
        List<DtoWorkSheetConfig> workSheetConfigs = repository.findByReportCodeIn(reportCodes);
        if (isLoadTransientFields) {
            loadTransientFields(workSheetConfigs);
        }
        return workSheetConfigs;
    }

    @Override
    public void loadTransientFields(Collection<DtoWorkSheetConfig> collection) {
        //ids
        List<String> wsConfigIds = collection.stream().map(DtoWorkSheetConfig::getId).collect(Collectors.toList());
        //基础配置
        List<String> recordCodes = collection.stream().map(DtoWorkSheetConfig::getReportCode).collect(Collectors.toList());
        List<DtoBaseConfig> configList = StringUtils.isNotEmpty(recordCodes) ? baseConfigService.findByReportCodes(recordCodes) : new ArrayList<>();
        Map<String, DtoBaseConfig> baseConfigMap = configList.stream().collect(Collectors.toMap(DtoBaseConfig::getReportCode, p -> p, (k1, k2) -> k1));
        //参数
        List<DtoWorkSheetParam> params = workSheetParamService.findByWorkSheetConfigIds(wsConfigIds);
        Map<String, List<DtoWorkSheetParam>> paramsGroup = params.stream().collect(Collectors.groupingBy(DtoWorkSheetParam::getWorkSheetConfigId));
        collection.forEach(workSheetConfig -> {
            Optional<DtoBaseConfig> configOptional = Optional.ofNullable(baseConfigMap.getOrDefault(workSheetConfig.getReportCode(), null));
            configOptional.ifPresent(p -> {
                String template = p.getTemplatePath();
                if (!"/".equals(template.substring(0, 1))) {
                    template = String.format("/%s", template);
                }
                workSheetConfig.setWorkPath(template);
                workSheetConfig.setWorkName(p.getTemplateName());
                workSheetConfig.setBaseConfigId(p.getId());
                workSheetConfig.setParamList(paramsGroup.getOrDefault(workSheetConfig.getId(), new ArrayList<>()));
            });
        });
    }


    /**
     * 校验模版名称是否重复，模版配置是否存在
     *
     * @param entity 实体
     */
    private void verify(DtoWorkSheetConfig entity) {
        if (repository.countByFormNameAndIdNot(entity.getFormName(), entity.getId()) > 0) {
            throw new BaseException("配置名称已存在!");
        }
        if (repository.countByReportCodeAndAndIdNot(entity.getReportCode(), entity.getId()) > 0) {
            throw new BaseException("模板配置已存在!");
        }
    }

    @Autowired
    @Lazy
    public void setBaseConfigService(BaseConfigService baseConfigService) {
        this.baseConfigService = baseConfigService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetTestService(WorkSheetTestService workSheetTestService) {
        this.workSheetTestService = workSheetTestService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetParamService(WorkSheetParamService workSheetParamService) {
        this.workSheetParamService = workSheetParamService;
    }
}
