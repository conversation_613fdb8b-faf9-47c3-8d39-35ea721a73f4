package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.report.dto.DtoWorkSheetParamDefaultValue;
import com.sinoyd.report.dto.DtoWorkSheetTest;
import com.sinoyd.report.repository.WorkSheetParamDefaultValueRepository;
import com.sinoyd.report.service.WorkSheetParamDefaultValueService;
import com.sinoyd.report.service.WorkSheetTestService;
import com.sinoyd.report.vo.WorkSheetParamDefaultValueQueryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始记录单参数默认值配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class WorkSheetParamDefaultValueServiceImpl extends LimsBaseServiceImpl<DtoWorkSheetParamDefaultValue, String, WorkSheetParamDefaultValueRepository>
        implements WorkSheetParamDefaultValueService {

    private WorkSheetTestService workSheetTestService;

    @Override
    public void findByPage(PageBean<DtoWorkSheetParamDefaultValue> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoWorkSheetParamDefaultValue a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    public List<DtoWorkSheetParamDefaultValue> findList(WorkSheetParamDefaultValueQueryVO queryVO) {
        // 根据工作表配置ID查询工作表测试列表
        List<DtoWorkSheetTest> workSheetTests = workSheetTestService.findByWorkSheetIds(Collections.singletonList(queryVO.getWorkSheetConfigId()));

        //按照查询条件过滤
        if (StringUtils.isNotNull(queryVO.getNeedCma())) {
            workSheetTests = workSheetTests.stream().filter(workSheetTest -> queryVO.getNeedCma().equals(workSheetTest.getNeedCma())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNull(queryVO.getNeedCnas())) {
            workSheetTests = workSheetTests.stream().filter(workSheetTest -> queryVO.getNeedCnas().equals(workSheetTest.getNeedCnas())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeItemKey())) {
            workSheetTests = workSheetTests.stream().filter(workSheetTest -> workSheetTest.getAnalyzeItemName().contains(queryVO.getAnalyzeItemKey()) || workSheetTest.getChemicalSymbol().contains(queryVO.getAnalyzeItemKey())).collect(Collectors.toList());
        }

        if (StringUtils.isNotNullAndEmpty(queryVO.getAnalyzeMethodKey())) {
            workSheetTests = workSheetTests.stream().filter(workSheetTest -> workSheetTest.getAnalyzeMethodName().contains(queryVO.getAnalyzeMethodKey()) || workSheetTest.getMethodStandardNo().contains(queryVO.getAnalyzeMethodKey())).collect(Collectors.toList());
        }

        // 根据工作表参数ID查询工作表参数默认值列表
        List<DtoWorkSheetParamDefaultValue> workSheetParamDefaultValues = repository.findByWorkSheetParamIdIn(Collections.singletonList(queryVO.getWorkSheetParamId()));

        // 存储结果的列表
        List<DtoWorkSheetParamDefaultValue> result = new ArrayList<>();

        // 遍历工作表测试列表
        workSheetTests.forEach(workSheetTest -> {
            // 在工作表参数默认值列表中查找与当前工作表测试匹配的默认值
            Optional<DtoWorkSheetParamDefaultValue> defaultValueOptional = workSheetParamDefaultValues.stream().filter(workSheetParamDefaultValue -> workSheetTest.getTestId().equals(workSheetParamDefaultValue.getTestId())).findFirst();

            // 如果找到匹配的默认值
            if (defaultValueOptional.isPresent()) {
                // 获取匹配的默认值
                DtoWorkSheetParamDefaultValue workSheetParamDefaultValue = defaultValueOptional.get();

                // 更新默认值的属性
                workSheetParamDefaultValue.setSampleTypeName(workSheetTest.getSampleTypeName());
                workSheetParamDefaultValue.setAnalyzeItemName(workSheetTest.getAnalyzeItemName());
                workSheetParamDefaultValue.setChemicalSymbol(workSheetTest.getChemicalSymbol());
                workSheetParamDefaultValue.setMethodStandardNo(workSheetTest.getMethodStandardNo());
                workSheetParamDefaultValue.setAnalyzeMethodName(workSheetTest.getAnalyzeMethodName());

                // 将更新后的默认值添加到结果列表中
                result.add(defaultValueOptional.get());
            } else {
                // 如果没有找到匹配的默认值，则创建一个新的默认值
                DtoWorkSheetParamDefaultValue workSheetParamDefaultValue = new DtoWorkSheetParamDefaultValue();

                // 将工作表测试的属性复制到默认值中，除了id属性
                BeanUtils.copyProperties(workSheetTest, workSheetParamDefaultValue, IBaseConstants.FieldConstant.IGNORE_FIELDS);

                // 设置默认值的workSheetParamId属性
                workSheetParamDefaultValue.setWorkSheetParamId(queryVO.getWorkSheetParamId());

                // 将新创建的默认值添加到结果列表中
                result.add(workSheetParamDefaultValue);
            }
        });

        // 返回结果列表
        return result;
    }

    @Override
    @Transactional
    public void deleteByWorkSheetParamIdIn(Collection<String> workSheetParamIds) {
        if (StringUtils.isNotEmpty(workSheetParamIds)) {
            List<DtoWorkSheetParamDefaultValue> workSheetParamDefaultValues = repository.findByWorkSheetParamIdIn(workSheetParamIds);
            List<String> ids = workSheetParamDefaultValues.stream().map(DtoWorkSheetParamDefaultValue::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public List<DtoWorkSheetParamDefaultValue> findByWorkSheetParamIdIn(Collection<String> workSheetParamIds) {
        return repository.findByWorkSheetParamIdIn(workSheetParamIds);
    }

    @Autowired
    @Lazy
    public void setWorkSheetTestService(WorkSheetTestService workSheetTestService) {
        this.workSheetTestService = workSheetTestService;
    }
}
