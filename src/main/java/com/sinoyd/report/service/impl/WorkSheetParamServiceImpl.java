package com.sinoyd.report.service.impl;

import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.dto.DtoParam;
import com.sinoyd.lims.rms.service.IParamClientService;
import com.sinoyd.report.constants.IReportEventName;
import com.sinoyd.report.dto.DtoWorkSheetParam;
import com.sinoyd.report.repository.WorkSheetParamRepository;
import com.sinoyd.report.service.WorkSheetParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 原始记录单参数配置数据服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
@Slf4j
public class WorkSheetParamServiceImpl extends LimsBaseServiceImpl<DtoWorkSheetParam, String, WorkSheetParamRepository>
        implements WorkSheetParamService {

    private IParamClientService paramClientService;

    @Override
    public void findByPage(PageBean<DtoWorkSheetParam> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoWorkSheetParam a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, criteria);
    }

    @Override
    @Transactional
    public DtoWorkSheetParam save(DtoWorkSheetParam entity) {
        verify(entity);
        saveParam(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoWorkSheetParam update(DtoWorkSheetParam entity) {
        verify(entity);
        saveParam(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings, IReportEventName.WORKSHEET_PARAM, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }

    @Override
    @Transactional
    public void deleteByWorkSheetConfigIdIn(Collection<String> workSheetConfigIds) {
        if (StringUtils.isNotEmpty(workSheetConfigIds)) {
            List<DtoWorkSheetParam> workSheetParams = repository.findByWorkSheetConfigIdIn(workSheetConfigIds);
            List<String> ids = workSheetParams.stream().map(DtoWorkSheetParam::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public List<DtoWorkSheetParam> findByIds(Collection<String> ids) {
        return repository.findAllById(ids);
    }

    @Override
    public List<DtoWorkSheetParam> findByWorkSheetConfigIdAndCategory(String workSheetConfigId, Integer category) {
        return repository.findByWorkSheetConfigIdAndParamCategory(workSheetConfigId, category);
    }

    @Override
    public List<DtoWorkSheetParam> findByWorkSheetConfigIds(Collection<String> workSheetConfigIds) {
        return repository.findByWorkSheetConfigIdIn(workSheetConfigIds);
    }

    /**
     * 看参数是否存在，不存在则新增
     *
     * @param entity 参数实体
     */
    protected void saveParam(DtoWorkSheetParam entity) {
        DtoParam param = paramClientService.findByParamName(entity.getParamName());
        if (StringUtils.isNull(param)) {
            DtoParam save = new DtoParam();
            save.setId(UUIDHelper.newId());
            save.setParamName(entity.getParamName());
            save.setDimensionId(StringUtils.isNotEmpty(entity.getDimensionId()) ? entity.getDimensionId() : "");
            param = paramClientService.save(save);
        }
        entity.setParamId(StringUtils.isNotNull(param) ? param.getId() : UUIDHelper.guidEmpty());
    }

    /**
     * 参数保存校验
     *
     * @param entity 参数实体
     */
    private void verify(DtoWorkSheetParam entity) {
        //校验参数名称是否重复
        if (repository.countByAliasNameAndParamCategoryAndWorkSheetConfigIdAndIdNot(entity.getAliasName(), entity.getParamCategory(), entity.getWorkSheetConfigId(), entity.getId()) > 0) {
            throw new BaseException("已存在相同别名的参数！");
        }
        //是否出证参数只能包含一个
        if (entity.getIsCert() != null
                && entity.getIsCert()
                && repository.countByParamCategoryAndWorkSheetConfigIdAndIsCertTrueAndIdNot(entity.getParamCategory(), entity.getWorkSheetConfigId(), entity.getId()) > 0) {
            throw new BaseException("数据参数中只能包含一个出证参数！");
        }
    }

    @Autowired
    @Lazy
    public void setParamClientService(IParamClientService paramClientService) {
        this.paramClientService = paramClientService;
    }

}
