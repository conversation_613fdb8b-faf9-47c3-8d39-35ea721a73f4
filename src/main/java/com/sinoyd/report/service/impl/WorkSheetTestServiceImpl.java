package com.sinoyd.report.service.impl;

import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.service.ISampleTypeClientService;
import com.sinoyd.lims.rms.service.ITestClientService;
import com.sinoyd.report.criteria.WorkSheetTestCriteria;
import com.sinoyd.report.dto.DtoWorkSheetTest;
import com.sinoyd.report.repository.WorkSheetTestRepository;
import com.sinoyd.report.service.WorkSheetTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始记录测试项项服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Service
public class WorkSheetTestServiceImpl extends LimsBaseServiceImpl<DtoWorkSheetTest, String, WorkSheetTestRepository> implements WorkSheetTestService {

    private ITestClientService testClientService;

    private ISampleTypeClientService sampleTypeClientService;

    @Override
    public void findByPage(PageBean<DtoWorkSheetTest> pageBean, BaseCriteria criteria) {
        Integer pageNo = pageBean.getPageNo();
        Integer rowsPerPage = pageBean.getRowsPerPage();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        WorkSheetTestCriteria workSheetTestCriteria = resetCriteria(criteria);
        pageBean.setEntityName("DtoWorkSheetTest a");
        pageBean.setSelect("select a");
        super.findByPage(pageBean, workSheetTestCriteria);
        pageBean.getData().sort(Comparator.comparing(DtoWorkSheetTest::getAnalyzeMethodName).thenComparing(DtoWorkSheetTest::getAnalyzeItemName));
        pageBean.setData(pageBean.getData().stream().skip((pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList()));
        pageBean.setPageNo(pageNo);
        pageBean.setRowsPerPage(rowsPerPage);
    }

    @Override
    @Transactional
    public List<DtoWorkSheetTest> save(Collection<DtoWorkSheetTest> entities) {
        List<String> workSheetConfigIds = entities.stream().map(DtoWorkSheetTest::getWorkSheetConfigId).distinct().collect(Collectors.toList());
        //查找已存在的测试项目配置
        List<DtoWorkSheetTest> existsTests = StringUtils.isNotNullAndEmpty(workSheetConfigIds) ? repository.findByWorkSheetConfigIdIn(workSheetConfigIds) : new ArrayList<>();
        List<DtoWorkSheetTest> saveList = new ArrayList<>();
        entities.stream().collect(Collectors.groupingBy(DtoWorkSheetTest::getWorkSheetConfigId)).forEach((workSheetConfigId, testsOfConfig) -> {
            List<DtoWorkSheetTest> existsTestsOfConfig = existsTests.stream().filter(t -> t.getWorkSheetConfigId().equals(workSheetConfigId)).collect(Collectors.toList());
            //过滤掉已存在的配置
            saveList.addAll(testsOfConfig.stream().filter(t -> existsTestsOfConfig.stream().noneMatch(e -> e.getTestId().equals(t.getTestId()))).collect(Collectors.toList()));
        });
        if (StringUtils.isNotEmpty(saveList)) {
            return super.save(saveList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoWorkSheetTest> findByWorkSheetIds(List<String> workSheetIds) {
        List<DtoWorkSheetTest> workSheetTests = repository.findByWorkSheetConfigIdIn(workSheetIds);
        loadTransientFields(workSheetTests);
        return workSheetTests;
    }

    @Override
    @Transactional
    public void deleteByWorkSheetConfigIdIn(Collection<String> workSheetConfigIds) {
        if (StringUtils.isNotEmpty(workSheetConfigIds)) {
            List<DtoWorkSheetTest> workSheetTests = repository.findByWorkSheetConfigIdIn(workSheetConfigIds);
            List<String> ids = workSheetTests.stream().map(DtoWorkSheetTest::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(ids)) {
                logicDeleteById(ids);
            }
        }
    }

    @Override
    public List<DtoWorkSheetTest> findByTestIds(Collection<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    @Override
    public void loadTransientFields(Collection<DtoWorkSheetTest> collection) {
        Set<String> testIds = collection.stream().map(DtoWorkSheetTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> tests = StringUtils.isNotEmpty(testIds) ? testClientService.findAllByIds(testIds) : new ArrayList<>();
        Set<String> sampleTypeIds = tests.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toSet());
        Map<String, String> sampleTypes = StringUtils.isNotEmpty(sampleTypeIds) ? sampleTypeClientService.findMapByIds(sampleTypeIds) : new HashMap<>();
        collection.forEach(data -> {
            tests.stream().filter(t -> t.getId().equals(data.getTestId())).findFirst().ifPresent(t -> {
                data.initFromTest(t);
                data.setSampleTypeName(sampleTypes.getOrDefault(t.getSampleTypeId(), ""));
            });
        });
    }

    /**
     * 重组查询条件
     *
     * @param criteria 查询条件
     * @return 查询条件
     */
    private WorkSheetTestCriteria resetCriteria(BaseCriteria criteria) {
        List<DtoTest> tests = testClientService.findValidList();
        WorkSheetTestCriteria workSheetTestCriteria = (WorkSheetTestCriteria) criteria;
        Set<String> testIds = new HashSet<>();
        if (StringUtils.isNotNullAndEmpty(workSheetTestCriteria.getMethodKey())) {
            String key = workSheetTestCriteria.getMethodKey();
            List<String> methodKeyTestIds = tests.stream().filter(t -> t.getAnalyzeMethodName().contains(key) || t.getMethodStandardNo().contains(key)).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(methodKeyTestIds);
        }
        if (StringUtils.isNotNullAndEmpty(workSheetTestCriteria.getItemKey())) {
            String key = workSheetTestCriteria.getItemKey();
            List<String> itemKeyTestIds = tests.stream().filter(t -> t.getAnalyzeItemName().contains(key) || t.getChemicalSymbol().contains(key)).map(DtoTest::getId).collect(Collectors.toList());
            testIds.addAll(itemKeyTestIds);
        }
        workSheetTestCriteria.setTestIds(new ArrayList<>(testIds));
        return workSheetTestCriteria;
    }



    @Autowired
    @Lazy
    public void setTestClientService(ITestClientService testClientService) {
        this.testClientService = testClientService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeClientService(ISampleTypeClientService sampleTypeClientService) {
        this.sampleTypeClientService = sampleTypeClientService;
    }
}
