package com.sinoyd.report.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.report.dto.DtoApiColumn;
import com.sinoyd.report.enums.EnumRequestMethod;
import com.sinoyd.report.vo.ApiExecuteVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * API远程接口请求验证工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/02
 */
@Slf4j
public class ExcelApiExecuteUtil {

    private static final HTTPCaller httpCaller = HTTPCaller.getInstance();


    /**
     * 验证API接口请求
     *
     * @param verifyVO API验证实体
     * @return 接口执行结果列
     */
    public static List<DtoApiColumn> verifyApi(ApiExecuteVO verifyVO) {
        //请求接口
        JSONObject resultObj = execute(verifyVO);
        //结果集列
        return getResultColumns(resultObj, verifyVO);
    }

    /**
     * 执行接口请求，获取请求结果
     *
     * @param executeVO API请求实体
     * @return 请求结果
     */
    private static JSONObject execute(ApiExecuteVO executeVO) {
        //请求Token认证接口，获取Token
        String token = StringUtils.isNotNull(executeVO.getAuthorizationApi())
                ? getToken(executeVO.getAuthorizationApi()) : null;
        //请求接口
        JSONObject resultObj = null;
        if (EnumRequestMethod.GET.name().equals(executeVO.getRequestMethod())) {
            resultObj = httpCaller.get(executeVO.getHost(), executeVO.getUrl(), token, executeVO.getRequestParams());
        } else if (EnumRequestMethod.POST.name().equals(executeVO.getRequestMethod())) {
            resultObj = httpCaller.post(executeVO.getHost(), executeVO.getUrl(), token, executeVO.getRequestParams());
        }
        if (resultObj == null) {
            throw new RuntimeException("接口请求结果为空,url:" + executeVO.getUrl());
        }
        return resultObj;
    }

    /**
     * 验证API接口请求
     *
     * @param executeVO API验证实体
     * @return 接口执行结果列
     */
    public static Object executeApi(ApiExecuteVO executeVO) {
        //请求接口
        JSONObject resultObj = execute(executeVO);
        //结果集列
        if (executeVO.getIsCollection()) {
            List<Map<String, Object>> resultList;
            try {
                JSONArray dataArray = resultObj.getJSONArray(executeVO.getValueKey());
                resultList = JSONObject.parseObject(dataArray.toString(), new TypeReference<List<Map<String, Object>>>() {
                }.getType());
            } catch (Exception e) {
                log.error("接口结果转换为集合失败:" + e.getMessage(), e);
                throw new RuntimeException(String.format("接口[%s]结果转换为集合失败，请确认接口返回类型!", executeVO.getUrl()));
            }
            return resultList;
        } else {
            Map<String, Object> resultMap;
            try {
                JSONObject jsonObject = resultObj.getJSONObject(executeVO.getValueKey());
                resultMap = JSONObject.parseObject(jsonObject.toString(), new TypeReference<List<Map<String, Object>>>() {
                }.getType());
            } catch (Exception e) {
                log.error("接口结果转换为对象失败:" + e.getMessage(), e);
                throw new RuntimeException(String.format("接口[%s]结果转换为对象失败，请确认接口返回类型!", executeVO.getUrl()));
            }
            return resultMap;
        }
    }


    /**
     * 获取接口结果列
     *
     * @param resultObj 接口请求结果
     * @param verifyVO  接口验证实体
     * @return 结果列
     */
    private static List<DtoApiColumn> getResultColumns(JSONObject resultObj, ApiExecuteVO verifyVO) {
        //结果集列
        List<DtoApiColumn> resultColumns = new ArrayList<>();
        //转换结果列
        if (resultObj != null) {
            if (verifyVO.getIsCollection()) {
                try {
                    JSONArray dataArray = resultObj.getJSONArray(verifyVO.getValueKey());
                    if (dataArray != null && !dataArray.isEmpty()) {
                        resultColumns = arrayToColumns(dataArray.getJSONObject(0).keySet());
                    }
                } catch (Exception e) {
                    throw new BaseException("接口结果转换为集合失败，请确认接口返回类型!");
                }
            } else {
                try {
                    JSONObject data = resultObj.getJSONObject(verifyVO.getValueKey());
                    if (data != null) {
                        resultColumns = arrayToColumns(data.keySet());
                    }
                } catch (Exception e) {
                    throw new BaseException("接口结果转换为对象失败，请确认接口返回类型!");
                }
            }
        }
        return resultColumns;
    }

    /**
     * 集合转Api接口返回列
     *
     * @param columnArray 结果列集合
     * @return API接口列
     */
    private static List<DtoApiColumn> arrayToColumns(Collection<String> columnArray) {
        List<DtoApiColumn> apiColumns = new ArrayList<>();
        int orderNum = 0;
        for (String column : columnArray) {
            DtoApiColumn apiColumn = new DtoApiColumn();
            apiColumn.setColumnCode(column);
            apiColumn.setOrderNum(orderNum);
            apiColumns.add(apiColumn);
            orderNum += 10;
        }
        return apiColumns;
    }

    /**
     * 根据认证接口获取Token
     *
     * @param tokenApi Token认证接口API
     * @return Token
     */
    private static String getToken(ApiExecuteVO tokenApi) {
        try {
            //Token请求参数
            JSONObject tokenObj = httpCaller.postOne(tokenApi.getHost(), tokenApi.getUrl(), null, tokenApi.getRequestParams());
            //返回Token
            return tokenObj.getString("token");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("验证Api错误: 无法获取认证接口Token!");
        }
    }
}
