package com.sinoyd.report.util;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.AESUtil;
import com.sinoyd.report.dto.DtoDataSetColumn;
import com.sinoyd.report.vo.JdbcExecuteVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterUtils;
import org.springframework.jdbc.core.namedparam.ParsedSql;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JDBC校验工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/30
 */
@Slf4j
public class ExcelJdbcExecuteUtil {
    /**
     * 数据库连接验证
     *
     * @param executeVO 校验实体
     * @return 是否连接成功
     */
    public static Boolean verifyDBConnect(JdbcExecuteVO executeVO) {
        Connection connection = null;
        try {
            //获取连接对象
            connection = getConnection(executeVO);
            if (connection.isValid(60000)) {
                connection.close();
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("连接失败: " + e.getMessage() + "!");
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.error("数据库连接对象关闭失败: " + e.getMessage(), e);
                }
            }
        }

    }

    /**
     * 校验SQL并返回SQL执行的结果列
     *
     * @param executeVO 校验对象
     * @return 结果列
     */
    public static List<DtoDataSetColumn> verifySQL(JdbcExecuteVO executeVO) {
        List<DtoDataSetColumn> dsColumns = new ArrayList<>();
        //获取JdbcTemplate
        NamedParameterJdbcTemplate namedJdbcTemplate = getNamedJdbcTemplate(executeVO);
        SqlRowSet sqlRowSet;
        //执行Sql查询
        try {
            sqlRowSet = namedJdbcTemplate.queryForRowSet(executeVO.getSql(), executeVO.getValues());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("SQL验证失败: 执行SQL错误[" + e.getMessage() + "]!");
        }
        //转换为数据集列
        for (Map.Entry<String, Object> executeEntry : getColumnMap(sqlRowSet).entrySet()) {
            DtoDataSetColumn dsColumn = new DtoDataSetColumn();
            dsColumn.setColumnCode(executeEntry.getKey());
            dsColumn.setOrderNum(Integer.parseInt(executeEntry.getValue().toString()));
            dsColumns.add(dsColumn);
        }
        return dsColumns.stream().sorted(Comparator.comparing(DtoDataSetColumn::getOrderNum)).collect(Collectors.toList());
    }


    /**
     * 校验SQL并返回SQL执行的结果列
     *
     * @param executeVO 校验对象
     * @return 结果列
     */
    public static Object executeSQL(JdbcExecuteVO executeVO) {
        //获取JdbcTemplate
        NamedParameterJdbcTemplate namedJdbcTemplate = getNamedJdbcTemplate(executeVO);
        //执行Sql查询
        try {
            if (executeVO.getIsCollection()) {
                return namedJdbcTemplate.queryForList(executeVO.getSql(), executeVO.getValues());
            } else {
                return namedJdbcTemplate.queryForMap(executeVO.getSql(), executeVO.getValues());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("SQL执行失败: 执行SQL错误[" + e.getMessage() + "]!");
        }
    }

    /**
     * 获取Sql执行结果列
     *
     * @param sqlRowSet sql执行结果
     * @return 执行结果列
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> getColumnMap(SqlRowSet sqlRowSet) {
        Class<? extends SqlRowSet> sqlRowSelClass = sqlRowSet.getClass();
        for (Field field : sqlRowSelClass.getDeclaredFields()) {
            field.setAccessible(true);
            if ("columnLabelMap".equals(field.getName())) {
                try {
                    return (Map<String, Object>) field.get(sqlRowSet);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new BaseException("获取Sql执行结果列名称数据错误!");
                }
            }
        }
        return new HashMap<>();
    }

    /**
     * 获取Sq参数名称数据
     *
     * @param parsedSql sql执行结果
     * @return 执行结果列
     */
    @SuppressWarnings("unchecked")
    public static List<String> getSqlParamNameList(ParsedSql parsedSql) {
        for (Field field : parsedSql.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            if ("parameterNames".equals(field.getName())) {
                try {
                    return (List<String>) field.get(parsedSql);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new BaseException("获取Sq参数名称数据错误!");
                }
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取JdbcTemplate连接对象
     *
     * @param executeVO 校验实体
     * @return JdbcTemplate连接对象
     */
    private static NamedParameterJdbcTemplate getNamedJdbcTemplate(JdbcExecuteVO executeVO) {
        try {
            DriverManagerDataSource dataSource = new DriverManagerDataSource();
            //获取数据库连接字符串
            String url = getConnectStr(executeVO);
            //解密数据库密码
            String decPassword = AESUtil.decrypt(executeVO.getDbPassword());
            //设置数据源连接信息
            dataSource.setDriverClassName(executeVO.getDbDrive());
            dataSource.setUrl(url);
            dataSource.setUsername(executeVO.getDbUserName());
            dataSource.setPassword(decPassword);
            //创建JdbcTemplate连接对象
            return new NamedParameterJdbcTemplate(dataSource);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("SQL验证失败: 获取JdbcTemplate失败，请验证数据源连通性!");
        }
    }

    /**
     * 获取连接对象
     *
     * @param executeVO 数据源属性
     * @return 连接对象
     */
    private static Connection getConnection(JdbcExecuteVO executeVO) {
        try {
            //获取数据库连接字符串
            String url = getConnectStr(executeVO);
            //解密数据库密码
            String decPassword = AESUtil.decrypt(executeVO.getDbPassword());
            //获取数据库引擎
            Class.forName(executeVO.getDbDrive());
            //获取连接对象
            return DriverManager.getConnection(url, executeVO.getDbUserName(), decPassword);
        } catch (ClassNotFoundException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("连接失败: 未找到数据库驱动" + executeVO.getDbDrive() + "!");
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("连接失败: 无法获取到连接对象 " + e.getMessage() + "!");
        }
    }

    /**
     * 获取数据库连接字符串
     *
     * @param executeVO 校验对象
     * @return 连接字符串
     */
    private static String getConnectStr(JdbcExecuteVO executeVO) {
        StringBuilder url = new StringBuilder();
        url.append("jdbc:").append(executeVO.getDbType()).append("://").append(executeVO.getDbHost());
        switch (executeVO.getDbType()) {
            case "mysql":
                String config = "?useUnicode=true&allowMultiQueries=true&characterEncoding=utf-8&useSSL=false";
                url.append(":").append(executeVO.getDbPort()).append("/").append(executeVO.getDbName()).append(config);
                break;
            case "sqlserver":
                url.append(";DatabaseName=").append(executeVO.getDbName()).append(";encrypt=false");
                break;
            case "dm":
                url.append(":").append(executeVO.getDbPort().toString()).append("?schema=").append(executeVO.getDbName());
        }
        return url.toString();
    }
}
