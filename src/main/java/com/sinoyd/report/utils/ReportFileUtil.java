package com.sinoyd.report.utils;

import com.aspose.cells.WorkbookDesigner;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
@Slf4j
@SuppressWarnings("unused")
public class ReportFileUtil {

    /**
     * 图片类型
     */
    public static final List<String> IMAGE_TYPE_LIST = Stream.of("bmp", "jpeg", "gif", "psd", "png", "tiff", "jpg")
            .collect(Collectors.toList());

    /**
     * 文件分隔符
     */
    public static final String FILE_SEPARATOR = "/";

    /**
     * 时间戳格式
     */
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";


    /**
     * Word的文档对象直接保存到输出流中
     *
     * @param filename 文件名称
     * @param document Word文档对象
     * @param response 响应体
     */
    public static void downloadWord(String filename, Document document, HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            response.setContentType("application/msword");
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(filename, "UTF-8"));
            outputStream = response.getOutputStream();
            document.save(outputStream, SaveFormat.DOCX);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("下载文件失败");
        } finally {
            closeStream(outputStream);
        }
    }

    /**
     * excel的文档对象直接保存到输出流中
     *
     * @param filename 文件名称
     * @param designer excel文档对象
     * @param response 响应体
     */
    public static void downloadExcel(String filename, WorkbookDesigner designer, HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(filename, "UTF-8"));
            outputStream = response.getOutputStream();
            designer.getWorkbook().save(outputStream, com.aspose.cells.SaveFormat.XLSX);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("下载文件失败");
        } finally {
            closeStream(outputStream);
        }
    }

    /**
     * 关闭流
     *
     * @param closeable 流实例
     */
    public static void closeStream(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}