package com.sinoyd.report.utils;

import com.aspose.cells.License;

import java.io.InputStream;
import java.net.URL;

/**
 * Aspose License工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public class ReportLicenseUtil {

    /**
     * 判断是否是 Aspose License(word和cell)
     *
     * @return 结果
     */
    public static boolean isLicense() {
        try {
            InputStream is = getLicense();
            License license = new License();
            license.setLicense(is);
        } catch (Exception e) {
            throw new RuntimeException("验证远大Excel和Word证书失败");
        }
        return true;
    }

    /**
     * 判断是否是PDF License
     *
     * @return 结果
     */
    public static boolean isPdfLicense() {
        try {
            InputStream is = getLicense();
            com.aspose.pdf.License license = new com.aspose.pdf.License();
            license.setLicense(is);
        } catch (Exception e) {
            throw new RuntimeException("验证远大PDF证书失败");
        }
        return true;
    }

    /**
     * 获取Aspose License路径
     *
     * @return Aspose License路径
     */
    public static String getLicensePath() {
        String path = null;
        try {
            String name = "license.xml";
            URL dirs = Thread.currentThread().getContextClassLoader().getResource(name);
            if (dirs != null) {
                String filePath = getRootPath(dirs).replace(name, "");
                String endPath = getEndPath(dirs).replace(name, "");
                if (!filePath.equals(endPath)) {
                    path = endPath.replace(".jar!", "");
                } else {
                    path = filePath.replace(".jar", "");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("获取远大文件体系证书路径失败");
        }
        if (path == null) {
            throw new RuntimeException("获取远大文件体系证书路径失败");
        }
        return path;
    }

    /**
     * 获取尾部路径
     *
     * @param url 路径
     * @return 截取后的路径
     */
    private static String getEndPath(URL url) {
        String fileUrl = url.getPath();
        int pos = fileUrl.lastIndexOf('!');
        if (-1 == pos) {
            return fileUrl;
        }
        if (fileUrl.contains("file:/")) {
            return fileUrl.substring(5, pos);
        }
        return fileUrl.substring(0, pos);
    }

    /**
     * 获取根路径
     *
     * @param url 路径
     * @return 根路径
     */
    private static String getRootPath(URL url) {
        String fileUrl = url.getPath();
        int pos = fileUrl.indexOf('!');
        if (-1 == pos) {
            return fileUrl;
        }
        if (fileUrl.contains("file:/")) {
            return fileUrl.substring(5, pos);
        }
        return fileUrl.substring(0, pos);
    }

    /**
     * 获取license
     *
     * @return license
     */
    private static InputStream getLicense() {
        return Thread.currentThread().getContextClassLoader().getResourceAsStream("license.xml");
    }
}