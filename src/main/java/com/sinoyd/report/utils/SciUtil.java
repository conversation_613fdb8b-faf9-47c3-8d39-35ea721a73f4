package com.sinoyd.report.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 科学计数法工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
@Slf4j
@SuppressWarnings({"unchecked"})
public class SciUtil {

    /**
     * 判断是否需要转换为科学计数法，如有需要则做相应格式化
     *
     * @param value 单元格值
     * @return 格式化后的值
     */
    public static String formatSci(String value) {
        if (value == null || "".equals(value)) {
            return value;
        }
        try {
            Map<String, Object> resMap = changeSciValue(value);
            List<String> resStrList = (List<String>) resMap.get("resStrList");
            List<String> resStrListUp = new ArrayList<>();
            for (String resStr : resStrList) {
                resStr = resStr.contains("10^") ? setTopDopt(resStr) : resStr;
                resStrListUp.add(resStr);
            }
            String midStr = resMap.get("midStr") != null ? resMap.get("midStr").toString() : "";
            String endStr = resMap.get("endStr") != null ? resMap.get("endStr").toString() : "";
            if (resStrListUp.size() == 1) {
                return resStrListUp.get(0) + endStr;
            } else {
                return resStrListUp.get(0) + midStr + resStrListUp.get(1) + endStr;
            }
        } catch (Exception e) {
            return value;
        }
    }

    /**
     * 转换为科学计数法，单元格值不包含E时，不会将次方数转为上标格式
     *
     * @param value 单元格值
     * @return 转换结果
     */
    public static Map<String, Object> changeSciValue(String value) {
        Map<String, Object> resMap = new HashMap<>();
        List<String> resStrList = new ArrayList<>();
        resMap.put("resStrList", resStrList);
        if (value.contains("E")) {
            //单元格值包含字母E的情况下才进行以下逻辑
            if (isESciFormat(value)) {
                //表示value为科学计数法格式，可以直接进行转换，例如：value = 1.5E+3
                resStrList.add(changeSciE(value));
                resMap.put("resStr", resStrList);
                return resMap;
            }
            //此时value不是科学计数法格式，不能直接进行转换，但仍然需要判断value是否有部分子串是科学计数法格式，需要进行部分转换
            String tmp = value;
            //去掉value的最后一个字符,再判断value是否是科学计数法格式，排除value = 1.5e+3L 这种情况
            char lastChar = value.charAt(value.length() - 1);
            tmp = tmp.substring(0, tmp.length() - 1);
            if (isESciFormat(tmp)) {
                //去除最后一个字符后，value为科学计数法格式，则进行转换后再加上value 的最后一个字符返回
                resStrList.add(changeSciE(tmp));
                resMap.put("endStr", lastChar + "");
                return resMap;
            }
            //去除最后一个字符后，value依然不是科学计数法格式
            if (value.contains("（均值：")) {
                // value = -1.23E4（均值:1.5E+3） 类似的格式
                int idx = value.indexOf("（均值：");
                String leftStr = value.substring(0, idx);
                String rightStr = value.substring(idx + 4, value.length() - 1);
                //左边部分的子串为科学计数法格式则将leftStr进行转换,否则leftStr不变
                leftStr = formatSci(leftStr);
                resStrList.add(leftStr);
                resMap.put("midStr", "（均值：");
                //右边部分的子串为科学计数法格式则将rightStr进行转换,否则rightStr不变
                rightStr = formatSci(rightStr);
                resStrList.add(rightStr);
                resMap.put("endStr", "）");
                return resMap;
            }
            if (value.contains("（") && value.contains("）") && value.endsWith("）")) {
                // value = -1.23E4（1.5E+3） 类似的格式
                int idx = value.indexOf("（");
                String leftStr = value.substring(0, idx);
                String rightStr = value.substring(idx + 1, value.length() - 1);
                //左边部分的子串为科学计数法格式则将leftStr进行转换,否则leftStr不变
                leftStr = formatSci(leftStr);
                resStrList.add(leftStr);
                resMap.put("midStr", "（");
                //右边部分的子串为科学计数法格式则将rightStr进行转换,否则rightStr不变
                rightStr = formatSci(rightStr);
                resStrList.add(rightStr);
                resMap.put("endStr", "）");
                return resMap;
            }
            if (value.contains(File.separator)) {
                // value = -1.23E4/1.5E+3 类似的格式
                int idx = value.indexOf(File.separator);
                String leftStr = value.substring(0, idx);
                String rightStr = value.substring(idx + 1);
                //左边部分的子串为科学计数法格式则将leftStr进行转换,否则leftStr不变
                leftStr = formatSci(leftStr);
                resStrList.add(leftStr);
                resMap.put("midStr", File.separator);
                //右边部分的子串为科学计数法格式则将rightStr进行转换,否则rightStr不变
                rightStr = formatSci(rightStr);
                resStrList.add(rightStr);
                resMap.put("endStr", "");
                return resMap;
            }
            if (value.contains("＜") || value.contains("＞")) {
                // value = ＞2.4E4 类似的格式
                String symbol = value.substring(0, 1);
                resStrList.add(symbol + formatSci(value.substring(1)));
                return resMap;
            }
            //已上情况都不满足则直接返回原字符串
            resStrList.add(value);
            return resMap;
        }
        resStrList.add(value);
        return resMap;
    }

    /**
     * 设置科学计数法次方数的上标格式
     *
     * @param value 单元格值
     * @return 格式化完成后的值
     */
    private static String setTopDopt(String value) {
        String[] strArr = value.split("\\^");
        if (strArr.length <= 1) {
            return value;
        }
        //判断当前上标部分是否存在其他数据
        if (strArr[1].contains(" ")) {
            String[] arr = strArr[1].split(" ");
            if (arr.length != 2) {
                return value;
            }
            return (strArr[0] + "^<sup>" + arr[0] + "</sup> " + arr[1]).replace("^", "");
        }
        if (strArr.length != 3) {
            if (strArr[1].contains("x")) {
                return (strArr[0] + "^<sup>" + strArr[1].split("x")[0] + "</sup>x" + strArr[1].split("x")[1]).replace("^", "");
            } else {
                return (strArr[0] + "^<sup>" + strArr[1] + "</sup>").replace("^", "");
            }
        }
        return (strArr[0] + "^<sup>" + strArr[1].split("x")[0] + "</sup>x" + strArr[1].split("x")[1] + "^<sup>" + strArr[2] + "</sup>").replace("^", "");
    }

    /**
     * 判断带字母E的字符串s 是否是带E的科学计数法格式 例如 s = 1.5E+3
     *
     * @param s 带字母E的字符串s
     * @return boolean
     */
    private static boolean isESciFormat(String s) {
        boolean flag = true;
        try {
            new BigDecimal(s);
        } catch (NumberFormatException e) {
            flag = false;
        }
        return flag;
    }

    /**
     * 带字母E的科学计数法核心转换方法
     *
     * @param value 带字母E的字符串s
     * @return 转换后的值
     */
    private static String changeSciE(String value) {
        String[] strings = value.split("E");
        if (strings[0] == null || "".equals(strings[0])) {
            return value;
        }
        //防止出现 1.E3 类似的情况
        strings[0] = strings[0].endsWith(".") ? strings[0] + "0" : strings[0];
        if (strings.length != 3) {
            if (strings[1].contains("x")) {
                String[] valStrings = strings[1].split("x");
                return strings[0] + "×10^" + valStrings[0].replace("+", "") + "x" + valStrings[1];
            } else {
                return strings[0] + "×10^" + strings[1].replace("+", "");
            }
        } else {
            String[] valStrings = strings[1].split("x");
            valStrings[0] = valStrings[0].endsWith(".") ? valStrings[0] + "0" : valStrings[0];
            valStrings[1] = valStrings[1].endsWith(".") ? valStrings[1] + "0" : valStrings[1];
            return strings[0] + "×10^" + valStrings[0].replace("+", "") + "x" + valStrings[1] + "×10^" + strings[2].replace("+", "");
        }
    }

}
