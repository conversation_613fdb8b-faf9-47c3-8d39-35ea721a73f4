package com.sinoyd.report.vo;

import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.report.dto.DtoApi;
import com.sinoyd.report.enums.EnumRequestMethod;
import lombok.Data;

import java.util.List;

/**
 * Api接口验证实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/02
 */
@Data
public class ApiExecuteVO {

    /**
     * 请求地址
     */
    private String host;

    /**
     * Token认证Api接口
     */
    private ApiExecuteVO authorizationApi;

    /**
     * 请求路径
     */
    private String url;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 请求参数
     */
    private List<NameValuePair> requestParams;

    /**
     * 数据集结果Key
     */
    private String valueKey;

    /**
     * 返回数据是否集合
     */
    private Boolean isCollection;

    /**
     *
     * @param host          请求地址
     * @param api           Api接口数据
     * @param requestParams 接口参数
     */
    public ApiExecuteVO(String host, DtoApi api, List<NameValuePair> requestParams){
        this.setHost(host);
        this.setUrl(api.getApiPath());
        this.setRequestMethod(EnumRequestMethod.getByValue(api.getRequestMethod()).name());
        this.setIsCollection(api.getIsCollection());
        this.setValueKey(api.getGatherKey());
        this.setRequestParams(requestParams);
    }
}
