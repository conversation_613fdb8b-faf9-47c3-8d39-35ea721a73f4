package com.sinoyd.report.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 单元格值样式VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/8/2
 */
@Data
@Accessors(chain = true)
public class CellValueStyleVO {

    /**
     * 单元格值
     */
    private String cellValue;

    /**
     * 字体颜色，比如 比如 #FF9B00
     */
    private String color;

    /**
     * 是否转换科学计数法，目前只是把诸如 1E+2 转换成 1*10²的形式
     */
    private Boolean isSci = false;

    /**
     * 科学计数法显示开始位置
     */
    private Integer sciStart;

    /**
     * 科学记数法显示结束位置
     */
    private Integer sciEnd;

    /**
     * 是否上标
     */
    private Boolean isSup = false;

    /**
     * 上标开始位置
     */
    private Integer supStart;

    /**
     * 上标结束位置
     */
    private Integer supEnd;

    /**
     * 是否下标
     */
    private Boolean isSub = false;

    /**
     * 下标开始位置
     */
    private Integer subStart;

    /**
     * 下标结束位置
     */
    private Integer subEnd;

    /**
     * 字体是否加粗
     */
    private Boolean isBlob = false;

    /**
     * 字体大小
     */
    private Short fontSize;

    /**
     * 字体格式，默认宋体
     */
    private String fontFamily = "宋体";

}