package com.sinoyd.report.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * JDBC校验实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/30
 */
@Data
@Accessors(chain = true)
public class JdbcExecuteVO {

    /**
     * 数据库引擎类型
     */
    private String dbType;

    /**
     * 数据库引擎驱动
     */
    private String dbDrive;

    /**
     * 数据库引擎地址
     */
    private String dbHost;

    /**
     * 数据库引擎端口
     */
    private Integer dbPort;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 数据库登录账号
     */
    private String dbUserName;

    /**
     * 数据库登录密码
     */
    private String dbPassword;

    /**
     * 执行的Sql语句
     */
    private String sql;

    /**
     * 返回结果是否为集合
     */
    private Boolean isCollection;

    /**
     * Sql语句参数
     */
    private Map<String, Object> values;
}
