package com.sinoyd.report.vo;

import lombok.Data;

/**
 * SamplingParamDefaultValue应用接口服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
public class SamplingParamDefaultValueQueryVO {

    /**
     * 采样单参数id
     */
    private String samplingParamId;

    /**
     * 采样单配置id
     */
    private String samplingConfigId;

    /**
     * 分析项目key
     */
    private String analyzeItemKey;

    /**
     * 分析方法key
     */
    private String analyzeMethodKey;

    /**
     * 是否cma
     */
    private Boolean needCma;

    /**
     * 是否cns
     */
    private Boolean needCnas;

}
