package com.sinoyd.report.vo;

import lombok.Data;

/**
 * TestFormulaEquationParamDefaultValue查询参数
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@Data
public class TestFormulaEquationParamDefaultValueQueryVO {

    /**
     * 公式方程参数ID
     */
    private String equationParamId;

    /**
     * 公式ID
     */
    private String testFormulaId;

    /**
     * 分析项目key
     */
    private String analyzeItemKey;

    /**
     * 分析方法key
     */
    private String analyzeMethodKey;

    /**
     * 是否cma
     */
    private Boolean needCma;

    /**
     * 是否cns
     */
    private Boolean needCnas;

}
