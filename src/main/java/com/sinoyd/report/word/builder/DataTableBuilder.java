package com.sinoyd.report.word.builder;

import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.MergeAreaVO;

import java.util.List;
import java.util.Map;

/**
 * 数据表实体构造器
 * 此构造器中提供了DataTable对象的数据表的初始化方法
 * 以及List<Map<String, Object>>对象的数据表初始化方法
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/19
 */
public class DataTableBuilder {

    private final DataTableVO dataTable;

    private DataTableBuilder() {
        dataTable = new DataTableVO();
    }

    /**
     * DataTable对象构造方法【无关联表】
     *
     * @param tableName 表名称
     * @param table     表数据
     * @return 构造器实例
     */
    public static DataTableBuilder initTable(String tableName, DataTable table) {
        DataTableBuilder builder = new DataTableBuilder();
        table.setTableName(tableName);
        builder.dataTable.setTable(table);
        builder.dataTable.setTbName(tableName);
        return builder;
    }

    /**
     * DataTable对象构造方法【有关联表】
     *
     * @param tableName       主表名称
     * @param table           主表数据
     * @param mainTBField     主表与次表的关联字段
     * @param relationTBName  次表名称
     * @param relationTable   次表数据
     * @param relationTBField 次表与主表的关联字段
     * @return 构造器实例
     */
    public static DataTableBuilder initTable(String tableName, DataTable table, String mainTBField,
                                             String relationTBName, DataTable relationTable, String relationTBField) {
        DataTableBuilder builder = new DataTableBuilder();
        table.setTableName(tableName);
        relationTable.setTableName(relationTBName);
        builder.dataTable.setTbName(tableName);
        builder.dataTable.setTable(table);
        builder.dataTable.setMainAssociationField(mainTBField);
        builder.dataTable.setSecondaryTable(relationTable);
        builder.dataTable.setSecondaryField(relationTBField);
        return builder;
    }

    /**
     * MapTable对象构造器【无关联表】
     *
     * @param tableName 表名称
     * @param mapTable  表数据
     * @return 构造器实例
     */
    public static DataTableBuilder initMapTable(String tableName, List<Map<String, Object>> mapTable) {
        DataTableBuilder builder = new DataTableBuilder();
        builder.dataTable.setTable(ReportDataUtil.mapTBToDT(tableName, mapTable));
        builder.dataTable.setTbName(tableName);
        return builder;
    }

    /**
     * MapTable对象构造器【有关联表】
     *
     * @param tableName       主表名称
     * @param mainTable       主表数据
     * @param mainTBField     主表与次表关联字段
     * @param relationTbName  次表名称
     * @param relationTable   次表数据
     * @param relationTBField 次表与主表关联字段
     * @return 构造器实例
     */
    public static DataTableBuilder initMapTable(String tableName, List<Map<String, Object>> mainTable, String mainTBField,
                                                String relationTbName, List<Map<String, Object>> relationTable, String relationTBField) {
        DataTableBuilder builder = new DataTableBuilder();
        builder.dataTable.setTbName(tableName);
        builder.dataTable.setTable(ReportDataUtil.mapTBToDT(tableName, mainTable));
        builder.dataTable.setMainAssociationField(mainTBField);
        builder.dataTable.setSecondaryTable(ReportDataUtil.mapTBToDT(relationTbName, relationTable));
        builder.dataTable.setSecondaryField(relationTBField);
        return builder;
    }

    /**
     * 设置是否列拓展【功能暂时未完成】
     *
     * @param isColumnExpend 是否列拓展
     * @return 构造器实例
     */
    public DataTableBuilder setIsColumnExpend(Boolean isColumnExpend) {
        this.dataTable.setIsColumnExpend(isColumnExpend);
        return this;
    }


    /**
     * 设置表格对应书签
     * 书签用处为，当模板文档中添加了分页符，
     * 且当前表格在分页符下方时，则需要设置表格书签，用于合并区域的定位
     *
     * @param bookMark 书签
     * @return 构造器实例
     */
    public DataTableBuilder setBookMark(String bookMark) {
        this.dataTable.setBookMark(bookMark);
        return this;
    }

    /**
     * 设置表格合并区域数据
     *
     * @param mergeAreaList 合并区域数据
     * @return 构造器实例
     */
    public DataTableBuilder setMergeAreaList(List<MergeAreaVO> mergeAreaList) {
        this.dataTable.setMergeAreaList(mergeAreaList);
        return this;
    }

    /**
     * 设置嵌套子表数据（需要同时设置嵌套子表关联字段
     * ）
     *
     * @param subTableMap              嵌套子表数据
     * @param subTableRelationFieldMap 嵌套子表关联字段
     * @return 构造器实例
     */
    public DataTableBuilder setSubTableMap(Map<String, DataTableVO> subTableMap, Map<String, String> subTableRelationFieldMap) {
        this.dataTable.setSubTableMap(subTableMap);
        this.dataTable.setSubTableRelationFieldMap(subTableRelationFieldMap);
        return this;
    }

    /**
     * 设置子表对主表的关联字段
     *
     * @param relationField 子表关联主表字段
     * @return 构造器实例
     */
    public DataTableBuilder setRelationField(String relationField) {
        this.dataTable.setRelationField(relationField);
        return this;
    }

    /**
     * 获取表格数据对象实例
     *
     * @return 表格数据对象实例
     */
    public DataTableVO getVOInstance() {
        return dataTable;
    }
}
