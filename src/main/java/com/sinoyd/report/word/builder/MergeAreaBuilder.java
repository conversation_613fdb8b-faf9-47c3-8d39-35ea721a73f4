package com.sinoyd.report.word.builder;

import com.sinoyd.report.word.vo.MergeAreaVO;

import java.util.List;

/**
 * 区域合并构造器
 * 用于构造合并区域传参实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/10
 */
public class MergeAreaBuilder {

    private final MergeAreaVO mergeArea;

    private MergeAreaBuilder() {
        mergeArea = new MergeAreaVO();
    }

    /**
     * 初始化区域合并构造器
     * 注：当文档中存在分页符时，分页符下方的表索引会重新计算，
     * 所以当次表格在分页符下方时且需要合并时，需要为当前表格添加书签并添加至参数中
     *
     * @param tableIndex 需要合并的表在文档中的索引
     * @param bookMark   表格所在书签
     * @return 区域合并构造器实例
     */
    public static MergeAreaBuilder init(Integer tableIndex, String bookMark) {
        MergeAreaBuilder builder = new MergeAreaBuilder();
        builder.mergeArea.setTableIdx(tableIndex);
        builder.mergeArea.setBookMark(bookMark);
        return builder;
    }

    /**
     * 初始化区域合并构造器
     * 此初始化方法为文档中没有分页符时，可计算数据表索引位置时所使用的方法
     *
     * @param tableIndex 文档中需要合并的表在文档中的索引位置
     * @return 区域合并构造器实例
     */
    public static MergeAreaBuilder init(Integer tableIndex) {
        MergeAreaBuilder builder = new MergeAreaBuilder();
        builder.mergeArea.setTableIdx(tableIndex);
        return builder;
    }

    /**
     * 添加行合并区域
     *
     * @param startRowNum 开始行索引
     * @param endRowNum   结束行索引
     * @param columnIndex 需要做合并的列索引
     * @return 区域合并构造器实例
     */
    public MergeAreaBuilder addRowMergeArea(Integer startRowNum, Integer endRowNum, Integer columnIndex) {
        MergeAreaVO.MergeRowArea rowArea = new MergeAreaVO.MergeRowArea();
        rowArea.setStartRowNum(startRowNum);
        rowArea.setEndRowNum(endRowNum);
        rowArea.setColumnIndex(columnIndex);
        this.mergeArea.getRowMergeAreas().add(rowArea);
        return this;
    }

    /**
     * 添加多列进行行合并的合并区域
     *
     * @param startRowNum  开始行索引
     * @param endRowNum    结束行索引
     * @param colIndexList 需要做合并的列索引集合
     * @return 区域合并构造器实例
     */
    public MergeAreaBuilder addRowMergeArea(Integer startRowNum, Integer endRowNum, List<Integer> colIndexList) {
        MergeAreaVO.MergeRowArea rowArea = new MergeAreaVO.MergeRowArea();
        rowArea.setStartRowNum(startRowNum);
        rowArea.setEndRowNum(endRowNum);
        rowArea.setColIndexList(colIndexList);
        this.mergeArea.getRowMergeAreas().add(rowArea);
        return this;
    }


    /**
     * 添加单行进行列合并的合并区域
     *
     * @param startColNum 开始列索引
     * @param endColNum   结束列索引
     * @param rowIndex    需要做合并的行索引
     * @return 区域合并构造器实例
     */
    public MergeAreaBuilder addColMergeArea(Integer startColNum, Integer endColNum, Integer rowIndex) {
        MergeAreaVO.MergeColumnArea colArea = new MergeAreaVO.MergeColumnArea();
        colArea.setStartColNum(startColNum);
        colArea.setEndColNum(endColNum);
        colArea.setRowIndex(rowIndex);
        this.mergeArea.getColMergeAreas().add(colArea);
        return this;
    }

    /**
     * 添加多行进行列合并的合并区域
     *
     * @param startColNum  开始列索引
     * @param endColNum    结束列索引
     * @param rowIndexList 需要做合并的行索引集合
     * @return 区域合并构造器实例
     */
    public MergeAreaBuilder addColMergeArea(Integer startColNum, Integer endColNum, List<Integer> rowIndexList) {
        MergeAreaVO.MergeColumnArea colArea = new MergeAreaVO.MergeColumnArea();
        colArea.setStartColNum(startColNum);
        colArea.setEndColNum(endColNum);
        colArea.setRowIndexList(rowIndexList);
        this.mergeArea.getColMergeAreas().add(colArea);
        return this;
    }

    /**
     * 获取合并区域传参实体实例
     *
     * @return 合并区域传参实体实例
     */
    public MergeAreaVO getInstance() {
        return this.mergeArea;
    }
}
