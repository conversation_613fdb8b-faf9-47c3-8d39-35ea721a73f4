package com.sinoyd.report.word.builder;

import com.aspose.words.net.System.Data.DataSet;
import com.sinoyd.report.word.vo.ChartVO;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.ImageVO;
import com.sinoyd.report.word.vo.WordParamVO;

import java.util.List;
import java.util.Map;

/**
 * 报告传参构造器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/10
 */
@SuppressWarnings("rawtypes")
public class WordParamBuilder {

    private final WordParamVO wordParam;

    private WordParamBuilder() {
        wordParam = new WordParamVO();
    }

    /**
     * 初始化方法:不包含输出路径的初始化方法
     *
     * @param templatePath 模板路径
     * @param isAbs        是否为绝对路径
     * @return 构造器实例
     */
    public static WordParamBuilder init(String templatePath, Boolean isAbs) {
        WordParamBuilder builder = new WordParamBuilder();
        if (isAbs) {
            builder.wordParam.setTemplateAbsPath(templatePath);
        } else {
            builder.wordParam.setRelativePath(templatePath);
        }
        return builder;
    }

    /**
     * 初始化方法: 包含输出路径的初始化方法
     *
     * @param templatePath 模板路径
     * @param isAbs        模板路径是否为绝对路径
     * @param outPath      文件输出路径
     * @param isOutAbs     文件输出
     * @param fileName     文件输出名称
     * @return 构造器实例
     */
    public static WordParamBuilder init(String templatePath, Boolean isAbs, String outPath, Boolean isOutAbs, String fileName) {
        WordParamBuilder builder = new WordParamBuilder();
        if (isAbs) {
            builder.wordParam.setTemplateAbsPath(templatePath);
        } else {
            builder.wordParam.setRelativePath(templatePath);
        }
        if (isOutAbs) {
            builder.wordParam.setOutPutAbsPath(outPath);
        } else {
            builder.wordParam.setOutRelativePath(outPath);
        }
        builder.wordParam.setFileName(fileName);
        return builder;
    }

    /**
     * 设置基础赋值数据字典
     *
     * @param basicMap 基础赋值数据字典
     * @return 构造器实例
     */
    public WordParamBuilder setBasicMap(Map<String, Object> basicMap) {
        this.wordParam.setBasicMap(basicMap);
        return this;
    }

    /**
     * 设置表格数据表字典数据
     *
     * @param dataMap 表格数据表字典数据
     * @return 构造器实例
     */
    public WordParamBuilder setDataMap(Map<String, DataTableVO> dataMap) {
        this.wordParam.setDataMap(dataMap);
        return this;
    }

    /**
     * 设置处理好的数据集
     *
     * @param dataSet 数据集
     * @return 构造器实例
     */
    public WordParamBuilder setDataSet(DataSet dataSet) {
        this.wordParam.setDataSet(dataSet);
        return this;
    }

    /**
     * 设置是否需要处理单元格格式
     *
     * @param isFormatCell 是否需要处理单元格格式
     * @return 构造器实例
     */
    public WordParamBuilder isFormatCell(Boolean isFormatCell) {
        this.wordParam.setIsFormatCell(isFormatCell);
        return this;
    }

    /**
     * 设置图片数据集合
     *
     * @param imageList 图片数据集合
     * @return 构造器实例
     */
    public WordParamBuilder setImageList(List<ImageVO> imageList) {
        this.wordParam.setImageList(imageList);
        return this;
    }

    /**
     * 设置图表数据集合
     *
     * @param chartList 图表数据集合
     * @return 构造器实例
     */
    public WordParamBuilder setChartList(List<ChartVO> chartList) {
        this.wordParam.setChartList(chartList);
        return this;
    }


    /**
     * 获取构造的参数实例
     *
     * @return 参数实例
     */
    public WordParamVO getInstance() {
        return this.wordParam;
    }
}
