package com.sinoyd.report.word.chart.datatype;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Vector;

/**
 * 柱状统计图数据
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BarChartData extends BaseChartData {

    /**
     * x轴类别
     */
    private Vector<String> category;

    /**
     * Y轴数据以及数据的名称
     */
    private List<ChartData> chartData;

    /**
     * 坐标轴标题
     */
    private String axisLabel;
}
