package com.sinoyd.report.word.chart.datatype;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 饼状统计图数据
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PieChartData extends BaseChartData {

    /**
     * 名称
     */
    private List<String> name = new ArrayList<>();

    /**
     * 数值
     */
    private Map<String,Double> valueMap = new HashMap<>();
}
