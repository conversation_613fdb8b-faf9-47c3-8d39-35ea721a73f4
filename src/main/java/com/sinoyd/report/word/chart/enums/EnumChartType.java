package com.sinoyd.report.word.chart.enums;

import com.sinoyd.report.word.chart.strategy.AbsChartExport;
import com.sinoyd.report.word.chart.strategy.BarChartExport;
import com.sinoyd.report.word.chart.strategy.LineChartExport;
import com.sinoyd.report.word.chart.strategy.PieChartExport;
import lombok.Getter;

/**
 * 图表类型枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/26
 */
@Getter
public enum EnumChartType {
    /**
     * 柱状图
     */
    柱状图("Bar", new BarChartExport()),

    /**
     * 折线图
     */
    折线图("Line",new LineChartExport()),

    /**
     * 饼图
     */
    饼图("Pie",new PieChartExport());

    /**
     * 编码
     */
    private final String code;

    /**
     * 图表生成策略实例
     */
    private final AbsChartExport strategy;

    EnumChartType(String code, AbsChartExport strategy) {
        this.code = code;
        this.strategy = strategy;
    }

    public static EnumChartType findByCode(String code){
        for (EnumChartType e : EnumChartType.values()) {
            if (code.equals(e.code)){
                return e;
            }
        }
        throw new RuntimeException("未找到图表类型相关枚举，请确认图表类型是否正确");
    }
}
