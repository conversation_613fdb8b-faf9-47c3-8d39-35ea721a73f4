package com.sinoyd.report.word.chart.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.report.word.chart.datatype.BaseChartData;
import com.sinoyd.report.word.chart.datatype.ChartData;
import com.sinoyd.report.word.chart.utils.ChartUtil;
import com.sinoyd.report.word.util.ReportImageUtil;
import lombok.extern.slf4j.Slf4j;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.AbstractDataset;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Vector;

/**
 * 图表生成基类
 * 传入图表数据对象与生成路径，生成图表图片到对应的路径
 * 1）根据传入的图表数据对象获取转换的JFreeChart图表对象
 * 2）根据设置的生成路径将图表生成到对应的路径下
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/15
 */
@Slf4j
public abstract class AbsChartExport {


    /**
     * 导出图表
     *
     * @param outPutPath    输出路径(绝对路径)
     * @param baseChartData 图表数据
     * @param outputPicName 导出文件名
     */
    public void exportChart(String outPutPath, BaseChartData baseChartData, String outputPicName) {
        JFreeChart chart = createChart(baseChartData);
        String picAbsPath = outPutPath + File.separator + ReportImageUtil.handlePicName(outputPicName);
        try {
            ChartUtilities.saveChartAsJPEG(new File(picAbsPath), chart, baseChartData.getWidth().intValue(), baseChartData.getHeight().intValue());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("保存图表图片数据时出错: " + picAbsPath);
        }
    }

    /**
     * 转换默认类型图表数据【柱状图，折线图】
     *
     * @param chartData  图表数据
     * @param categories 类别数据
     * @return 需要生成的图表数据
     */
    protected AbstractDataset createDefaultDataSet(List<ChartData> chartData, Vector<String> categories) {
        DefaultCategoryDataset dataSet = new DefaultCategoryDataset();
        //循环处理
        for (ChartData series : chartData) {
            String name = series.getName();
            Map<String, Object> data = series.getDataMap();
            if (data != null && categories != null && data.values().size() == categories.size()) {
                for (String category : categories) {
                    String value = data.get(category) == null ? "" : data.get(category).toString();
                    if (ChartUtil.isPercent(value)) {
                        value = value.substring(0, value.length() - 1);
                    }
                    if (ChartUtil.isNumber(value)) {
                        dataSet.setValue(Double.parseDouble(value), name, category);
                    }
                }
            }
        }
        return dataSet;
    }

    /**
     * 创建图表
     *
     * @param baseChartData 图表数据
     * @return 图表
     */
    public abstract JFreeChart createChart(BaseChartData baseChartData);

    /**
     * 转换图表数据
     *
     * @param baseChartData 自定义图表数据实体
     * @return 图表数据
     */
    public abstract AbstractDataset createCategoryDataSet(BaseChartData baseChartData);
}
