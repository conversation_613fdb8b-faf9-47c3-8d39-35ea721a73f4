package com.sinoyd.report.word.chart.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.report.word.chart.datatype.BarChartData;
import com.sinoyd.report.word.chart.datatype.BaseChartData;
import com.sinoyd.report.word.chart.datatype.ChartData;
import com.sinoyd.report.word.chart.utils.ChartUtil;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.block.BlockBorder;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.AbstractDataset;

import java.awt.*;
import java.util.Vector;

/**
 * 柱状图图表生成策略
 * 生成思路
 * 1）根据所传入的ChartData实体对象去转换为图表数据集
 * 2）根据图表数据集创建JFreeChart图表对象
 * 3）处理图表样式
 * 4）返回JFreeChart图表对象
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/15
 */
public class BarChartExport extends AbsChartExport{
    /**
     * @param baseChartData 图表数据
     * @return 图表
     */
    @Override
    public JFreeChart createChart(BaseChartData baseChartData) {
        BarChartData chartData = (BarChartData) baseChartData;
        Vector<ChartData> series = new Vector<>(chartData.getChartData());
        for (ChartData s : series) {
            if (s.getDataMap().values().size() != chartData.getCategory().size()) {
                throw new BaseException("创建柱状图错误，类别数量需要等于数据值的数量");
            }
        }

        // 1：创建数据集合
        DefaultCategoryDataset dataSet = (DefaultCategoryDataset) this.createCategoryDataSet(chartData);

        // 2：创建Chart
        JFreeChart chart = ChartFactory.createBarChart(chartData.getTitle(), "", chartData.getAxisLabel(), dataSet);

        // 3:设置抗锯齿，防止字体显示不清楚
        ChartUtil.setAntiAlias(chart);

        // 4:对柱子进行渲染
        ChartUtil.setBarRenderer(chart.getCategoryPlot(), false);

        // 5:对其他部分进行渲染
        // X坐标轴渲染
        ChartUtil.setXAxis(chart.getCategoryPlot());
        // Y坐标轴渲染
        ChartUtil.setYAxis(chart.getCategoryPlot());
        // 设置标注无边框
        chart.getLegend().setFrame(new BlockBorder(Color.WHITE));

        return chart;
    }


    /**
     * 转换图表数据
     *
     * @param baseChartData 自定义图表数据实体
     * @return 图表数据
     */
    @Override
    public AbstractDataset createCategoryDataSet(BaseChartData baseChartData) {
        //转换成柱状图数据
        BarChartData barChartData = (BarChartData)baseChartData;
        //处理转换默认图表数据
        return this.createDefaultDataSet(barChartData.getChartData(), barChartData.getCategory());
    }
}
