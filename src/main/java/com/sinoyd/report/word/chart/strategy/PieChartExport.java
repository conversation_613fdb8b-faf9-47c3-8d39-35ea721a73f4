package com.sinoyd.report.word.chart.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.report.word.chart.datatype.BaseChartData;
import com.sinoyd.report.word.chart.datatype.PieChartData;
import com.sinoyd.report.word.chart.utils.ChartUtil;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.data.general.AbstractDataset;
import org.jfree.data.general.DefaultPieDataset;

/**
 * 饼图生成策略
 * 生成思路
 * 1）根据所传入的ChartData实体对象去转换为图表数据集
 * 2）根据图表数据集创建JFreeChart图表对象
 * 3）处理图表样式
 * 4）返回JFreeChart图表对象
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/15
 */
public class PieChartExport extends AbsChartExport{
    /**
     * 创建图表
     *
     * @param baseChartData 图表数据
     * @return 图表
     */
    @Override
    public JFreeChart createChart(BaseChartData baseChartData) {
        PieChartData chartData = (PieChartData) baseChartData;
        if (chartData.getName().size() != chartData.getValueMap().keySet().size()) {
            throw new BaseException("创建柱状图错误，类别数量需要等于数据值的数量");
        }
        DefaultPieDataset dataSet = (DefaultPieDataset) this.createCategoryDataSet(chartData);

        // 2：创建Chart[创建不同图形]
        JFreeChart chart = ChartFactory.createPieChart(chartData.getTitle(), dataSet, false, true, false);
        // 3:设置抗锯齿，防止字体显示不清楚
        ChartUtil.setAntiAlias(chart);
        // 4:对柱子进行渲染[创建不同图形]
        ChartUtil.setPieRender(chart.getPlot());

        return chart;
    }

    /**
     * 转换图表数据
     *
     * @param baseChartData 自定义图表数据实体
     * @return 图表数据
     */
    @Override
    public AbstractDataset createCategoryDataSet(BaseChartData baseChartData) {
        PieChartData pieChartData = (PieChartData) baseChartData;
        DefaultPieDataset dataSet = new DefaultPieDataset();
        for (String category : pieChartData.getName()) {
            Double value = pieChartData.getValueMap().get(category);
            dataSet.setValue(category, value);
        }
        return dataSet;
    }
}
