package com.sinoyd.report.word.service;

import com.aspose.words.Document;
import com.sinoyd.report.word.vo.WordParamVO;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 报告生成接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
public interface WordReportService {


    /**
     * 根据数据传输VO生成文件
     *
     * @param params   处理后的数据VO
     * @param response Http响应体
     * @return 生成报表文件的临时路径
     */
    Object generate(WordParamVO params, HttpServletResponse response);

    /**
     * 生成文档到输出流中
     *
     * @param params       处理后的数据VO
     * @param outputStream 输出流
     */
    void generateStream(WordParamVO params, OutputStream outputStream);

    /**
     * 根据数据传输VO生成Doc对象
     *
     * @param params   处理后的数据VO
     * @return 生成的报表Doc对象
     */
    Document generateDoc(WordParamVO params);

}
