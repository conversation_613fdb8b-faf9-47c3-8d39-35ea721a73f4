package com.sinoyd.report.word.service.impl;

import com.sinoyd.report.word.service.WebOfficeService;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.vo.WebOfficeVO;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * WebOffice接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/16
 */
@Service
@Slf4j
public class WebOfficeServiceImpl implements WebOfficeService {

    private WordReportService wordReportService;

    /**
     * 统一生成webOffice的报告
     *
     * @param params 报告编号
     * @return 在线编辑实例
     */
    @Override
    public WebOfficeVO generateWebOffice(WordParamVO params) {
        Object fileName = wordReportService.generate(params, null);
        return generateWebOffice(fileName.toString());
    }

    /**
     * 统一返回webOffice的结构
     *
     * @param fileName 文件路径
     * @return 返回数据
     */
    private WebOfficeVO generateWebOffice(String fileName) {
        File file = new File(fileName);
        String fileType = file.getName().substring(file.getName().lastIndexOf("."));
        WebOfficeVO dtoWebOffice = new WebOfficeVO();
        dtoWebOffice.setFileType(fileType);
        dtoWebOffice.setFileName(file.getName());
        dtoWebOffice.setWebUrl(file.getPath());
        return dtoWebOffice;
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}