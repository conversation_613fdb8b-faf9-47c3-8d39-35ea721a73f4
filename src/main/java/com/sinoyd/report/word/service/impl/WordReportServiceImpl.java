package com.sinoyd.report.word.service.impl;

import com.aspose.words.Document;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.util.ReportDataVerifyUtils;
import com.sinoyd.report.word.util.ReportUtils;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 报告生成接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
@Service
@Slf4j
public class WordReportServiceImpl implements WordReportService {

    /**
     * 根据数据传输VO生成文件
     *
     * @param params   处理后的数据VO
     * @param response Http响应体
     */
    @Override
    public Object generate(WordParamVO params, HttpServletResponse response) {
        //根据数据集DataSet生成文件
        return generate(params, null, response, true);
    }

    /**
     * 生成文档到输出流中
     *
     * @param params       处理后的数据VO
     * @param outputStream 输出流
     */
    @Override
    public void generateStream(WordParamVO params, OutputStream outputStream) {
        //根据数据集DataSet生成文件
        generate(params, outputStream, null, false);
    }

    /**
     * 根据数据传输VO生成Doc对象
     *
     * @param params 处理后的数据VO
     * @return 生成的报表Doc对象
     */
    @Override
    public Document generateDoc(WordParamVO params) {
        //校验传参
        ReportDataVerifyUtils.verifyParams(params, false);
        //返回文档对象
        return ReportUtils.findDocument(params);
    }


    /**
     * 生成文档
     *
     * @param outputStream 输出流
     * @param response     响应体
     * @param isOut        是否输出文件
     */
    public Object generate(WordParamVO params, OutputStream outputStream, HttpServletResponse response, Boolean isOut) {
        //校验传参
        ReportDataVerifyUtils.verifyParams(params, isOut);
        //获取文档对象
        Document doc = ReportUtils.findDocument(params);
        //返回文档输出路径
        return ReportUtils.download(doc, params.getFileName(), params.getOutPutAbsPath(), params.getOutRelativePath(), response, outputStream, isOut);
    }


}
