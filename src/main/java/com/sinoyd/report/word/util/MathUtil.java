package com.sinoyd.report.word.util;

/**
 * 数学工具
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/06
 */
public class MathUtil {

    private static final char[] cnArr = new char[]{'一', '二', '三', '四', '五', '六', '七', '八', '九'};

    /**
     * 数字转中文
     *
     * @param intInput 数字
     * @return 数字中文
     */
    public static String toChinese(Integer intInput) {
        String si = String.valueOf(intInput);
        String sd = "";
        if (si.length() == 1) {
            if (intInput == 0) {
                return sd;
            } else {
                sd = sd + cnArr[intInput - 1];
                return sd;
            }
        } else {
            if (si.length() == 2) {
                if (si.charAt(0) == '1') {
                    sd = sd + "十";
                    if (intInput % 10 == 0) {
                        return sd;
                    }
                } else {
                    sd = sd + cnArr[intInput / 10 - 1] + "十";
                }

                sd = sd + toChinese(intInput % 10);
            } else if (si.length() == 3) {
                sd = sd + cnArr[intInput / 100 - 1] + "百";
                if (String.valueOf(intInput % 100).length() < 2) {
                    if (intInput % 100 == 0) {
                        return sd;
                    }

                    sd = sd + "零";
                }

                sd = sd + toChinese(intInput % 100);
            } else if (si.length() == 4) {
                sd = sd + cnArr[intInput / 1000 - 1] + "千";
                if (String.valueOf(intInput % 1000).length() < 3) {
                    if (intInput % 1000 == 0) {
                        return sd;
                    }

                    sd = sd + "零";
                }

                sd = sd + toChinese(intInput % 1000);
            } else if (si.length() == 5) {
                sd = sd + cnArr[intInput / 10000 - 1] + "万";
                if (String.valueOf(intInput % 10000).length() < 4) {
                    if (intInput % 10000 == 0) {
                        return sd;
                    }

                    sd = sd + "零";
                }

                sd = sd + toChinese(intInput % 10000);
            }

            return sd;
        }
    }
}
