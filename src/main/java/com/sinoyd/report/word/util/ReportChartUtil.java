package com.sinoyd.report.word.util;

import com.aspose.words.Document;
import com.sinoyd.report.word.chart.enums.EnumChartType;
import com.sinoyd.report.word.vo.ChartVO;
import com.sinoyd.report.word.vo.ImageVO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 图表处理工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/16
 */
@SuppressWarnings({"rawtypes"})
public class ReportChartUtil {

    /**
     * 插入图表数据
     *
     * @param doc         文档对象
     * @param chartVOList 图表数据集合
     */
    public static void insertChartPath(Document doc, List<ChartVO> chartVOList) {
        if (chartVOList == null || chartVOList.isEmpty()) {
            return;
        }
        List<ImageVO> imageVOList = new ArrayList<>();
        for (ChartVO chartVO : chartVOList) {
            imageVOList.add(createChartImage(chartVO));
        }
        ReportImageUtil.insertImage(doc, imageVOList);
    }


    /**
     * 创建图表图片数据
     *
     * @param chartVO 图表数据
     * @return 图片数据
     */
    public static ImageVO createChartImage(ChartVO chartVO){
        //校验图表数据
        ReportDataVerifyUtils.verifyChart(chartVO);
        //新建图片数据对象
        ImageVO chartImage = new ImageVO();
        chartImage.setHeight(chartVO.getChartData().getHeight());
        chartImage.setWidth(chartVO.getChartData().getWidth());
        chartImage.setImageMark(chartVO.getBookMark());
        //获取绝对路径则获取Resource路径
        boolean isAbs = (chartVO.getOutAbsPath() != null && !"".equals(chartVO.getOutAbsPath()));
        String absPath = isAbs ? chartVO.getOutAbsPath() :
                ReportUtils.getResourcePath(chartVO.getOutRelativePath(), "无法获取到Resource下图表临时生成相对路径，请确认!");
        //导出图表数据
        EnumChartType.findByCode(chartVO.getChartType())
                .getStrategy()
                .exportChart(absPath, chartVO.getChartData(), chartVO.getOutPutPicName());
        if (isAbs) {
            chartImage.setImageAbsPath(absPath + File.separator + ReportImageUtil.handlePicName(chartVO.getOutPutPicName()));
        } else {
            chartImage.setImgRelativePath(chartVO.getOutRelativePath() + File.separator + ReportImageUtil.handlePicName(chartVO.getOutPutPicName()));
        }
        return chartImage;
    }

}
