package com.sinoyd.report.word.util;

import com.aspose.words.net.System.Data.*;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 报告数据处理工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/09/22
 */
@Slf4j
public class ReportDataUtil {


    /**
     * 转换生成数据为DataSet
     *
     * @param params 传参VO
     * @return 数据集合
     */
    public static DataSet parseDataSet(WordParamVO params) {
        DataSet ds = new DataSet();
        //处理basic表数据
        if (params.getBasicMap() != null && !params.getBasicMap().isEmpty()) {
            DataTable tbBasic = new DataTable();
            tbBasic.setTableName("basic");
            Set<String> keySet = params.getBasicMap().keySet();
            //初始化列
            for (String key : keySet) {
                addTableColumn(tbBasic, key);
            }
            //初始化行数据
            DataRow row = tbBasic.newRow();
            params.getBasicMap().forEach(row::set);
            tbBasic.getRows().add(row);
            ds.getTables().add(tbBasic);
        }
        Map<String, DataTableVO> dataMap = params.getDataMap();
        if (dataMap != null && !dataMap.isEmpty()) {
            //处理其余表数据
            for (String tableName : dataMap.keySet()) {
                DataTableVO dataTableVO = dataMap.get(tableName);
                DataTable tb = dataTableVO.getTable();
                ds.getTables().add(tb);
                DataTable secondaryTable = dataTableVO.getSecondaryTable();
                if (secondaryTable != null) {
                    ds.getTables().add(secondaryTable);
                }
                //DS添加所有嵌套子表
                insertNestingTable(ds, dataTableVO);
            }
            //处理表与表之前的关系
            handleTableRelation(ds, dataMap);
        }
        return ds;
    }

    /**
     * DS数据集中添加嵌套子表数据
     *
     * @param ds   数据集
     * @param dtVO 当前数据表实体
     */
    public static void insertNestingTable(DataSet ds, DataTableVO dtVO) {
        if (StringUtils.isNotEmpty(dtVO.getSubTableMap())) {
            for (Map.Entry<String, DataTableVO> subTbEntry : dtVO.getSubTableMap().entrySet()) {
                if (subTbEntry.getValue().getTable() != null) {
                    ds.getTables().add(subTbEntry.getValue().getTable());
                }
                //创建嵌套子表
                insertNestingTable(ds, subTbEntry.getValue());
            }
        }
    }

    /**
     * 处理关联表关系
     *
     * @param ds      数据集
     * @param dataMap 数据表
     */
    public static void handleTableRelation(DataSet ds, Map<String, DataTableVO> dataMap) {
        for (String tableName : dataMap.keySet()) {
            DataTableVO dataTableVO = dataMap.get(tableName);
            //设置主次表关系
            writeReportInfo(ds, dataTableVO);
            //循环处理嵌套子表的表字段关联关系
            writeSubTableRelation(ds, dataTableVO);
        }
    }

    /**
     * 表关联方法
     *
     * @param ds   dataSet
     * @param dtVO 表传输VO
     */
    public static void writeReportInfo(DataSet ds, DataTableVO dtVO) {
        if (dtVO.getSecondaryTable() != null) {
            String mainAssFiled = dtVO.getMainAssociationField() != null && !"".equals(dtVO.getMainAssociationField()) ?
                    dtVO.getMainAssociationField() : "Id";
            String secondaryAssFiled = dtVO.getSecondaryField() != null && !"".equals(dtVO.getSecondaryField()) ? dtVO.getSecondaryField() : "Id";
            //设置表关系
            if (dtVO.getSecondaryTable().getColumns().getCount() > 0 && dtVO.getTable().getColumns().getCount() > 0) {
                try {
                    ds.getRelations().add(new DataRelation("result",
                            dtVO.getTable().getColumns().get(mainAssFiled), dtVO.getSecondaryTable().getColumns().get(secondaryAssFiled)));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new RuntimeException("设置关联表关系出错!");
                }
            }
        }
    }

    /**
     * 写入嵌套子表关联关系
     *
     * @param ds   数据表集合
     * @param dtVO 主表数据
     */
    private static void writeSubTableRelation(DataSet ds, DataTableVO dtVO) {
        //子表数据与子表关联字段数据都同时纯在时才会处理关联关系
        if (StringUtils.isNotEmpty(dtVO.getSubTableMap())
                && StringUtils.isNotEmpty(dtVO.getSubTableRelationFieldMap())) {
            Map<String, String> subRelationFieldMap = dtVO.getSubTableRelationFieldMap();
            //未设置关联字段的子表名称
            List<String> noneRelationFieldTb = new ArrayList<>();
            //循环处理子表
            for (Map.Entry<String, DataTableVO> subTbEntry : dtVO.getSubTableMap().entrySet()) {
                //获取主表对子表的关联字段
                String mainToSubField = subRelationFieldMap.getOrDefault(subTbEntry.getKey(), "");
                //获取子表对主表的关联字段
                String subRelationField = subTbEntry.getValue().getRelationField();
                //添加未设置关联字段的子表
                if (StringUtils.isEmpty(mainToSubField) || StringUtils.isEmpty(subRelationField)) {
                    noneRelationFieldTb.add(subTbEntry.getKey());
                    continue;
                }
                ds.getRelations().add(new DataRelation("result",
                        dtVO.getTable().getColumns().get(mainToSubField), subTbEntry.getValue().getTable().getColumns().get(subRelationField)));
                //循环设置嵌套关联关系
                writeSubTableRelation(ds, subTbEntry.getValue());
            }
            if (StringUtils.isNotEmpty(noneRelationFieldTb)) {
                String throwMsg = String.format("关联关系设置失败，主表：[%s]存在子表未设置关联字段：[%s]",
                        dtVO.getTbName(), String.join("，", noneRelationFieldTb));
                log.error(throwMsg);
                throw new RuntimeException(throwMsg);
            }
        }
    }


    /**
     * 给数据表添加列
     *
     * @param dt         数据表
     * @param columnName 列名
     */
    public static void addTableColumn(DataTable dt, String columnName) {
        DataColumn column = new DataColumn();
        column.setColumnName(columnName);
        dt.getColumns().add(column);
    }

    /**
     * 将MapTable转换成DataTable对象
     *
     * @param tableName 表名称
     * @param mapTable  表数据
     * @return DataTable对象
     */
    public static DataTable mapTBToDT(String tableName, List<Map<String, Object>> mapTable) {
        if (mapTable != null && !mapTable.isEmpty()) {
            try {
                DataTable table = new DataTable();
                table.setTableName(tableName);
                //获取列集合,获取所有列中keySet去重
                Set<String> columnSet = new HashSet<>();
                mapTable.forEach(p -> columnSet.addAll(p.keySet()));
                //添加表格列数据
                for (String column : columnSet) {
                    ReportDataUtil.addTableColumn(table, column);
                }
                //添加表格行数据
                for (Map<String, Object> rowMap : mapTable) {
                    DataRow row = table.newRow();
                    for (Map.Entry<String, Object> entry : rowMap.entrySet()) {
                        if (columnSet.contains(entry.getKey())) {
                            row.set(entry.getKey(), entry.getValue());
                        }
                    }
                    table.getRows().add(row);
                }
                return table;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("MapList数据表转换为DataTable对象时出错!");
            }

        }
        return null;
    }


}
