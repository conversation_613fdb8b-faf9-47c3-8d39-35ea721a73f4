package com.sinoyd.report.word.util;

import com.sinoyd.report.word.vo.ChartVO;
import com.sinoyd.report.word.vo.ImageVO;
import com.sinoyd.report.word.vo.WordParamVO;

/**
 * 报告实体数据校验工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/30
 */
@SuppressWarnings({"rawtypes"})
public class ReportDataVerifyUtils {

    /**
     * 校验报告生成传参数据
     *
     * @param params 数据生成传参实体
     * @param isOut  是否导出文件
     */
    public static void verifyParams(WordParamVO params, Boolean isOut) {
        if (params == null) {
            throw new RuntimeException("报告生成传参不能为空");
        }
        if ((params.getRelativePath() == null || "".equals(params.getRelativePath().trim()))
                && (params.getTemplateAbsPath() == null || "".equals(params.getTemplateAbsPath().trim()))) {
            throw new RuntimeException("传参中模板路径不能为空!");
        }
        if (isOut){
            if (params.getFileName() == null || "".equals(params.getFileName().trim())) {
                throw new RuntimeException("传参中文件名称不能为空!");
            }
        }
    }

    /**
     * 校验图表数据
     *
     * @param chartVO 图表数据
     */
    public static void verifyChart(ChartVO chartVO) {
        if (chartVO.getBookMark() == null || "".equals(chartVO.getBookMark())) {
            throw new RuntimeException("图表数据实体需要设置对应模板中的书签，请设置书签");
        }
        if ((chartVO.getOutRelativePath() == null || "".equals(chartVO.getOutRelativePath()))
                && (chartVO.getOutAbsPath() == null || "".equals(chartVO.getOutAbsPath()))) {
            throw new RuntimeException("图表数据实体需要设置临时输出路径");
        }
    }

    /**
     * 校验图片数据
     *
     * @param imageVO 图表数据
     */
    public static void verifyImage(ImageVO imageVO) {
        //查找书签位置
        if (imageVO.getImageMark() == null || "".equals(imageVO.getImageMark())) {
            throw new RuntimeException("插入的图片必须设置书签, 请设置ImageVO中的书签位置");
        }
        //校验图片路径
        if ((imageVO.getImgRelativePath() == null || "".equals(imageVO.getImgRelativePath()))
                && (imageVO.getImageAbsPath() == null || "".equals(imageVO.getImageAbsPath()))) {
            throw new RuntimeException("插入的图片必须设置图片路径, 请设置ImageVO中的图片路径");
        }
    }
}
