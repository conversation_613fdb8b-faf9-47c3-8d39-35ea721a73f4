package com.sinoyd.report.word.util;

import com.aspose.words.Bookmark;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.Shape;
import com.sinoyd.report.utils.ReportFileUtil;
import com.sinoyd.report.word.vo.ImageVO;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;

/**
 * 报告中图片处理
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/09/22
 */
@Slf4j
public class ReportImageUtil {

    /**
     * 处理图片数据插入
     *
     * @param doc         文档对象
     * @param imageVOList 图片数据集合
     */
    public static void insertImage(Document doc, List<ImageVO> imageVOList) {
        if (imageVOList == null || imageVOList.isEmpty()) {
            return;
        }
        // 创建文档构建器
        DocumentBuilder builder = new DocumentBuilder(doc);
        for (ImageVO imageVO : imageVOList) {
            insertSingleImage(doc, builder, imageVO);
        }
    }

    /**
     * 处理单个图片的插入
     *
     * @param doc     文档对象
     * @param builder 文档构造器
     * @param imageVO 图片数据
     */
    public static void insertSingleImage(Document doc, DocumentBuilder builder, ImageVO imageVO) {
        //校验图片数据
        ReportDataVerifyUtils.verifyImage(imageVO);
        // 查找书签
        Bookmark bookmark = doc.getRange().getBookmarks().get(imageVO.getImageMark());
        if (bookmark == null) {
            throw new RuntimeException("图片书签未设置，请在模板中设置图片的对应书签：" + imageVO.getImageMark());
        }
        InputStream imgIS = getPicInputStream(imageVO.getImageAbsPath(), imageVO.getImgRelativePath());
        //插入图片数据
        buildDocImage(builder, imageVO, bookmark, imgIS);
    }

    /**
     * 获取图片流
     *
     * @param absPath      绝对路径
     * @param relativePath 相对路径
     * @return 图片流
     */
    public static InputStream getPicInputStream(String absPath, String relativePath) {
        InputStream inputStream;
        if (absPath != null && !"".equals(absPath)) {
            try {
                inputStream = new FileInputStream(absPath);
            } catch (FileNotFoundException ex) {
                log.error(ex.getMessage(), ex);
                throw new RuntimeException("无法创建图片流,图片路径找不到: " + absPath);
            }
        } else {
            inputStream = ReportImageUtil.class.getResourceAsStream(relativePath);
        }
        return inputStream;
    }

    /**
     * 构建图片数据
     *
     * @param builder  文档构造器
     * @param imageVO  图片数据
     * @param bookmark 图片书签
     * @param picIS    图片流
     */
    public static void buildDocImage(DocumentBuilder builder, ImageVO imageVO, Bookmark bookmark, InputStream picIS) {
        try {
            // 将光标移到书签位置
            builder.moveToBookmark(imageVO.getImageMark(), false, true);
            // 插入图片
            builder.insertImage(picIS);
            //获取书签下的第一个节点
            Shape shape = (Shape) bookmark.getBookmarkEnd().getNextSibling();
            // 设置宽度
            shape.setWidth(imageVO.getWidth());
            // 设置高度
            shape.setHeight(imageVO.getHeight());
            // 设置水平对齐为居中
            shape.setHorizontalAlignment(imageVO.getPosition());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("插入图片异常!");
        } finally {
            ReportFileUtil.closeStream(picIS);
        }
    }


    /**
     * 处理图片名称后缀
     *
     * @param outputPicName 图片名称
     * @return 图片名称
     */
    public static String handlePicName(String outputPicName) {
        if (outputPicName.contains(".png") || outputPicName.contains(".jpg")) {
            outputPicName = outputPicName.replace(".jpg", ".png");
        } else {
            outputPicName = outputPicName + ".png";
        }
        return outputPicName;
    }
}
