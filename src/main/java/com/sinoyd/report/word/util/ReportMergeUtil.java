package com.sinoyd.report.word.util;

import com.aspose.words.CellMerge;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.sinoyd.report.word.vo.MergeAreaVO;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告中单元格合并工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/09/22
 */
@Slf4j
public class ReportMergeUtil {

    /**
     * 获取合并区域Map
     *
     * @param params 生成传参
     * @return 合并区域Map
     */
    public static Map<String, List<MergeAreaVO>> findMergeAreaMap(WordParamVO params) {
        Map<String, List<MergeAreaVO>> mergeAreaMap = new HashMap<>();
        params.getDataMap().forEach((k, v) -> mergeAreaMap.put(k, v.getMergeAreaList()));
        return mergeAreaMap;
    }

    /**
     * 执行合并
     *
     * @param doc          文档对象
     * @param mergeAreaMap 数据集合
     */
    public static void mergeData(Document doc, Map<String, List<MergeAreaVO>> mergeAreaMap) {
        if (mergeAreaMap == null || mergeAreaMap.isEmpty()) {
            return;
        }
        DocumentBuilder builder = new DocumentBuilder(doc);
        mergeAreaMap.forEach((k, v) -> {
            for (MergeAreaVO mergeAreaVO : v) {
                if (mergeAreaVO.getBookMark() != null && !"".equals(mergeAreaVO.getBookMark())) {
                    try {
                        builder.moveToBookmark(mergeAreaVO.getBookMark());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        throw new RuntimeException("移动至质控信息所在表的书签位置出错!");
                    }
                }
                //处理纵向合并的数据
                List<MergeAreaVO.MergeRowArea> rowMergeAreas = mergeAreaVO.getRowMergeAreas();
                for (MergeAreaVO.MergeRowArea rowMergeArea : rowMergeAreas) {
                    if (rowMergeArea.getColumnIndex() != null){
                        verMerge(mergeAreaVO.getTableIdx(), rowMergeArea.getStartRowNum(), rowMergeArea.getEndRowNum(), rowMergeArea.getColumnIndex(), builder);
                    }else{
                        if(rowMergeArea.getColIndexList() != null && !rowMergeArea.getColIndexList().isEmpty()){
                            for (Integer colIndex : rowMergeArea.getColIndexList()) {
                                verMerge(mergeAreaVO.getTableIdx(), rowMergeArea.getStartRowNum(), rowMergeArea.getEndRowNum(), colIndex, builder);
                            }
                        }
                    }

                }
                //处理横向合并的数据
                List<MergeAreaVO.MergeColumnArea> colMergeAreas = mergeAreaVO.getColMergeAreas();
                for (MergeAreaVO.MergeColumnArea colMergeArea : colMergeAreas) {
                    if (colMergeArea.getRowIndex() != null){
                        horMerge(mergeAreaVO.getTableIdx(), colMergeArea.getStartColNum(), colMergeArea.getEndColNum(), colMergeArea.getRowIndex(), builder);
                    }else{
                        if (colMergeArea.getRowIndexList() != null && !colMergeArea.getRowIndexList().isEmpty()){
                            for (Integer rowIndex : colMergeArea.getRowIndexList()) {
                                horMerge(mergeAreaVO.getTableIdx(), colMergeArea.getStartColNum(), colMergeArea.getEndColNum(), rowIndex, builder);
                            }
                        }
                    }

                }
            }
        });
    }


    /**
     * 竖向合并单元格
     *
     * @param tableIdx 实际生成的报表word文档中技术依据信息表的索引
     * @param startRow 开始行索引
     * @param endRow   结束行索引
     * @param col      列索引
     * @param builder  documentBuilder 对象
     */
    public static void verMerge(int tableIdx, int startRow, int endRow, int col, DocumentBuilder builder) {
        try {
            builder.moveToCell(tableIdx, startRow, col, 0);
            builder.getCellFormat().setVerticalMerge(CellMerge.FIRST);
            for (int n = startRow + 1; n <= endRow; n++) {
                builder.moveToCell(tableIdx, n, col, 0);
                builder.getCellFormat().setVerticalMerge(CellMerge.PREVIOUS);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("竖向合并单元格出错");
        }
    }

    /**
     * 横向合并单元格
     *
     * @param tableIdx 实际生成的报表word文档中技术依据信息表的索引
     * @param startCol 开始列索引
     * @param endCol   结束列索引
     * @param row      行索引
     * @param builder  documentBuilder 对象
     */
    public static void horMerge(int tableIdx, int startCol, int endCol, int row, DocumentBuilder builder) {
        try {
            builder.moveToCell(tableIdx, row, startCol, 0);
            builder.getCellFormat().setHorizontalMerge(CellMerge.FIRST);
            for (int n = startCol + 1; n <= endCol; n++) {
                builder.moveToCell(tableIdx, row, n, 0);
                builder.getCellFormat().setHorizontalMerge(CellMerge.PREVIOUS);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("横向合并单元格出错");
        }
    }

}
