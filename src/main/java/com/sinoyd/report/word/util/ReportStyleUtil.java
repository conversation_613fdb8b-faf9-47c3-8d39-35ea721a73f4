package com.sinoyd.report.word.util;

import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 报告生成样式处理工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/09/22
 */
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class ReportStyleUtil {

    /**
     * 文档字体处理
     *
     * @param doc 文档对象
     */
    public static void setDocFontFormat(Document doc) {
        DocumentBuilder builder = new DocumentBuilder(doc);
        // 遍历文档中的所有表格节点
        NodeCollection<Node> tableNodes = doc.getChildNodes(NodeType.TABLE, true);
        try {
            for (Node node : tableNodes) {
                Table table = (Table) node;
                // 遍历表格中的所有行和列
                for (int row = 0; row < table.getRows().getCount(); row++) {
                    CellCollection cells = table.getRows().get(row).getCells();
                    for (int col = 0; col < cells.getCount(); col++) {
                        //处理表格单元格的样式
                        handleCellValue(builder, table, col, cells);
                    }
                }
            }
            // 遍历文档中的所有节点
            NodeCollection<Node> childNodes = doc.getChildNodes(NodeType.ANY, true);
            for (Node node : childNodes) {
                //处理表格外节点数据样式
                handleNodeValue(builder, node);
            }
        } catch (Exception e) {
            log.error("设置单元格格式出错：" + e.getMessage(), e);
            throw new RuntimeException("设置单元格格式出错");
        }
    }

    /**
     * 处理表格外节点数据
     *
     * @param builder 文档操作对象
     * @param node    文档节点对象
     * @throws Exception 文档插入HTML异常抛出
     */
    public static void handleNodeValue(DocumentBuilder builder, Node node) throws Exception {
        if (node.getNodeType() != NodeType.TABLE && node.getNodeType() == NodeType.PARAGRAPH) {
            String text = node.getText();
            if (text.contains("<span")) {
                Paragraph paragraph = (Paragraph) node;
                builder.moveTo(paragraph);
                if (builder.getCurrentParagraph().getRuns().get(0) != null) {
                    //当前单元格字体样式
                    String cellFontFamily = findRunsFount(builder.getCurrentParagraph().getRuns()).getName();
                    for (int j = 0; j < builder.getCurrentParagraph().getRuns().getCount(); j++) {
                        builder.getCurrentParagraph().getRuns().get(j).setText("");
                    }
                    //插入Html标签【添加最外层字体样式，使模板文字字体样式保持原样】
                    builder.insertHtml("<span style = 'font-family:" + cellFontFamily + ";'>" + text + "</span>");
                    //处理勾选框样式
                    handleCheckBox(builder, cellFontFamily, text);
                }
            }
        }
    }

    /**
     * 处理表格节点的单元格样式
     *
     * @param builder 文档操作对象
     * @param table   当前数据表节点对象
     * @param col     列索引
     * @param cells   表格中列数据
     * @throws Exception 文档操作对象插入HTML语句的异常抛出
     */
    public static void handleCellValue(DocumentBuilder builder, Table table, int col, CellCollection cells) throws Exception {
        builder.moveTo(table);
        // 获取单元格位置信息
        Cell cell = cells.get(col);
        //获取到当前指针下的处理前的字体数据
        String cellFontFamily = "宋体";
        Double cellFontSize = 14.0;
        if (builder.getCurrentParagraph().getRuns().get(0) != null) {
            Font currentFont = findRunsFount(builder.getCurrentParagraph().getRuns());
            //当前单元格字体样式
            cellFontFamily = currentFont.getName();
            //当前单元格字体大小
            cellFontSize = currentFont.getSize();
        }
        //循环置空所有节点文本
        NodeCollection cellNodes = cell.getChildNodes();
        for (int i = 0; i < cellNodes.getCount(); i++) {
            Node cellNode = cellNodes.get(i);
            builder.moveTo(cellNode);
            // 将单元格文本保存到对象中
            String text = cellNode.getText();
            if (text.contains("<span")) {
                for (int j = 0; j < builder.getCurrentParagraph().getRuns().getCount(); j++) {
                    builder.getCurrentParagraph().getRuns().get(j).setText("");
                }
                //插入Html标签【添加最外层字体样式，使模板文字字体样式保持原样】
                builder.insertHtml("<span style = 'font-family:" + cellFontFamily + ";'>" + text + "</span>");
                //处理勾选框样式
                handleCheckBox(builder, cellFontFamily, text);
                //设置字体大小与模板字体相同
                setTemplateFontSize(builder, cellFontSize, text);
            }
        }
    }

    /**
     * 设置字体字体大小与模板相同
     *
     * @param builder      文档操作对象
     * @param cellFontSize 当前模板中未赋值的字体对象
     * @param text         光标所在的节点内容
     */
    public static void setTemplateFontSize(DocumentBuilder builder, Double cellFontSize, String text) {
        //如果没有设置字体大小，则使用模板上的字体大小生成
        if (!text.contains("font-size")) {
            //循环当前单元格内的所有段落节点
            for (int i = 0; i < builder.getCurrentParagraph().getRuns().getCount(); i++) {
                //设置字体与模板字体相同
                builder.getCurrentParagraph().getRuns().get(i).getFont().setSize(cellFontSize);
            }
        }
    }

    /**
     * 处理勾选框的字体样式
     *
     * @param builder  文档操作对象
     * @param fontName 字体类型
     * @param text     操作文本
     */
    public static void handleCheckBox(DocumentBuilder builder, String fontName, String text) {
        if (text.contains("font-family")) {
            if (builder.getCurrentParagraph().getRuns().get(0) != null) {
                fontName = findRunsFount(builder.getCurrentParagraph().getRuns()).getName();
            }
        }
        //循环当前单元格内的所有段落节点
        for (int i = 0; i < builder.getCurrentParagraph().getRuns().getCount(); i++) {
            //获取当前段落节点的文本内容
            String currentText = builder.getCurrentParagraph().getRuns().get(i).getText();
            //处理勾选框字体样式，保持与其设置的字体样式相同
            if (currentText.contains("□") || currentText.contains("☑")) {
                builder.getCurrentParagraph().getRuns().get(i).getFont().setName(fontName);
            }
        }
    }

    /**
     * 获取节点内第一个字的字体对象
     *
     * @param runs 节点数据
     * @return 字体对象
     */
    public static Font findRunsFount(RunCollection runs) {
        Font font = runs.get(0).getFont();
        for (int i = 0; i < runs.getCount(); i++) {
            Run run = runs.get(i);
            //获取到所有节点中第一次出现文字的字体样式
            if (run.getText() != null && !"".equals(run.getText())) {
                font = run.getFont();
                break;
            }
        }
        return font;
    }
}
