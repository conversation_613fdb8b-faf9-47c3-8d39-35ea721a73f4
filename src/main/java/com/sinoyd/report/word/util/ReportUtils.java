package com.sinoyd.report.word.util;

import com.aspose.words.Document;
import com.aspose.words.MailMergeCleanupOptions;
import com.aspose.words.SaveFormat;
import com.aspose.words.net.System.Data.DataSet;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.report.utils.ReportFileUtil;
import com.sinoyd.report.word.vo.MergeAreaVO;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * 报告生成工具类
 *
 * <AUTHOR>
 * @version V5.2.1
 * @since 2023/07/26
 */
@Slf4j
public class ReportUtils {


    /**
     * 处理文件的下载
     *
     * @param doc             文档对象
     * @param fileName        文件名称
     * @param outPutAbsPath   输出绝对路径
     * @param outRelationPath 输出相对路径
     * @param response        响应体
     * @param outputStream    输出流
     * @param isOut           是否输出文件
     * @return 输出路径
     */
    public static Object download(Document doc, String fileName, String outPutAbsPath, String outRelationPath, HttpServletResponse response, OutputStream outputStream, Boolean isOut) {
        if (!isOut) {
            try {
                doc.save(outputStream, SaveFormat.DOCX);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("无法保存文档到输出流中!");
            }
            return null;
        }
        //判断是否绝对路径输出文件
        boolean isAbs = (outPutAbsPath != null && !"".equals(outPutAbsPath));
        //实际路径
        String outPath = isAbs ? outPutAbsPath : outRelationPath;
        if (outPath == null || "".equals(outPath)) {
            //直接保存文档对象到输出流中
            ReportFileUtil.downloadWord(fileName, doc, response);
            return null;
        }
        //获取文件输出绝对路径
        String absOutPath = isAbs ? outPath :
                ReportUtils.getResourcePath(outPath, "无法找到对应的输出路径，请检查Resource输出路径（相对路径）是否存在: " + outPath);
        //下载文档
        if (absOutPath != null && !"".equals(absOutPath)) {
            //保存文件到输出路径下
            String outFullPath = saveOutPutFile(doc, absOutPath, fileName);
            //导出文件,为null时为iWebOffice方式生成，只需返回路径即可
            if (response != null) {
                FileUtil.download(outFullPath, fileName, response);
            }
            return outFullPath;
        } else {
            //直接保存文档对象到输出流中
            ReportFileUtil.downloadWord(fileName, doc, response);
            return null;
        }
    }


    /**
     * 获取报告生成数据处理后的文档对象
     *
     * @param params 报告生成参数实体
     * @return 文档对象
     */
    public static Document findDocument(WordParamVO params) {
        Document doc = findDocObject(params);
        //处理文档对象
        handleDoc(doc, params);
        return doc;
    }

    /**
     * 获取报告生成后的文档对象
     *
     * @param params 报告生成参数实体
     * @return 文档对象
     */
    public static Document findDocObject(WordParamVO params) {
        boolean isAbs = (params.getTemplateAbsPath() != null && !"".equals(params.getTemplateAbsPath()));
        String templatePath = isAbs ? params.getTemplateAbsPath() : params.getRelativePath();
        Document doc;
        InputStream inputStream = null;
        try {
            if (isAbs) {
                try {
                    doc = new Document(templatePath);
                } catch (Exception e) {
                    //如果是 unix os，路径前面需要有 /
                    if (!templatePath.startsWith(File.separator)) {
                        templatePath = File.separator + templatePath;
                    }
                    doc = new Document(templatePath);
                }
            } else {
                inputStream = ReportUtils.class.getResourceAsStream(templatePath);
                if (inputStream == null) {
                    throw new RuntimeException("相对路径输入流创建失败，请确认Resource下是否有此路径: " + templatePath);
                }
                doc = new Document(inputStream);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new RuntimeException("新建文档对象失败，请检查模板路径是否正确");
        } finally {
            ReportFileUtil.closeStream(inputStream);
        }
        return doc;
    }

    /**
     * 处理文档对象
     *
     * @param doc    文档对象
     * @param params 报告生成传参
     */
    public static void handleDoc(Document doc, WordParamVO params) {
        //获取数据集
        DataSet ds;
        if (params.getDataSet() != null){
            ds = params.getDataSet();
        }else{
            ds = ReportDataUtil.parseDataSet(params);
        }
        //获取合并区域Map
        Map<String, List<MergeAreaVO>> mergeAreaMap = ReportMergeUtil.findMergeAreaMap(params);
        //处理图表数据路径
        ReportChartUtil.insertChartPath(doc, params.getChartList());
        //处理图片插入
        ReportImageUtil.insertImage(doc, params.getImageList());
        //邮件合并赋值
        execute(doc, ds);
        //处理合并区域
        ReportMergeUtil.mergeData(doc, mergeAreaMap);
        //设置单元格数据格式
        if (params.getIsFormatCell()){
            ReportStyleUtil.setDocFontFormat(doc);
        }
    }


    /**
     * 执行邮件合并
     *
     * @param doc 文档对象
     * @param ds  数据集合
     */
    public static void execute(Document doc, DataSet ds) {
        if (ds.getTables().getCount() > 0) {
            //执行基本数据的邮件合并
            executeBasicMailMerge(doc, ds);
            //执行其他数据表的邮件合并
            executeOtherTablesMailMerge(doc, ds);
        }
    }

    /**
     * 执行基本数据的邮件合并
     *
     * @param doc 文档对象
     * @param ds  数据集
     */
    public static void executeBasicMailMerge(Document doc, DataSet ds) {
        DataTable dt = ds.getTables().get(0);
        if (dt.getTableName().equals("basic") && dt.getRows().getCount() > 0) {
            try {
                doc.getMailMerge().execute(dt.getRows().get(0));
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                throw new RuntimeException("执行basic表的邮件合并失败");
            }
        }
    }

    /**
     * 执行其余数据表的邮件合并
     *
     * @param doc 文档对象
     * @param ds  数据集
     */
    public static void executeOtherTablesMailMerge(Document doc, DataSet ds) {
        try {
            doc.getMailMerge().setCleanupOptions(MailMergeCleanupOptions.REMOVE_UNUSED_REGIONS);
            doc.getMailMerge().executeWithRegions(ds);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new RuntimeException("执行其余表的邮件合并失败");
        }
    }


    /**
     * 保存文件
     *
     * @param doc           文档对象
     * @param outPutAbsPath 输出绝对路径
     * @param fileName      文件名称
     * @return 下载路径
     */
    public static String saveOutPutFile(Document doc, String outPutAbsPath, String fileName) {
        String outFullPath;
        try {
            File outputFile = new File(outPutAbsPath);
            outFullPath = outPutAbsPath + File.separator + fileName;
            if (!outputFile.exists()) {
                boolean mkdirs = outputFile.mkdirs();
                if (mkdirs) {
                    log.info("路径创建成功" + outPutAbsPath);
                }
            }
            doc.save(outFullPath);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("保存输出文件失败，请检查数据路径是否正确");
        }
        return outFullPath;
    }

    /**
     * 获取Resource绝对路径拼接
     *
     * @param relativePath 相对路径
     * @param exceptionMsg 异常信息
     * @return 绝对路径
     */
    public static String getResourcePath(String relativePath, String exceptionMsg) {
        URL resource = ReportUtils.class.getResource(relativePath);
        if (resource == null) {
            throw new RuntimeException(exceptionMsg);
        }
        return resource.getPath();
    }
}
