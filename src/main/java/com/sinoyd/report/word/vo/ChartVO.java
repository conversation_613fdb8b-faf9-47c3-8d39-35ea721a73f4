package com.sinoyd.report.word.vo;

import com.sinoyd.report.word.chart.datatype.BaseChartData;
import lombok.Data;

/**
 * 图表数据VO
 *
 * @param <T> 图表数据类型
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/15
 */
@Data
public class ChartVO<T extends BaseChartData> {

    /**
     * 图表类型
     */
    private String chartType;

    /**
     * 书签【用于图表插入位置】
     */
    private String bookMark;

    /**
     * 图表生成图片的临时存放路径【相对路径】
     */
    private String outRelativePath;

    /**
     * 图表生成图片的临时存放路径的【绝对路径】
     */
    private String outAbsPath;

    /**
     * 图表生成图片的临时文件名称
     */
    private String outPutPicName;

    /**
     * 图表数据
     */
    private T chartData;
}
