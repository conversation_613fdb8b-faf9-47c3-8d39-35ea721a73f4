package com.sinoyd.report.word.vo;

import com.aspose.words.net.System.Data.DataTable;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据表VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
@Data
public class DataTableVO {

    public DataTableVO() {
    }

    public DataTableVO(DataTable table) {
        this.table = table;
    }

    /**
     * 与父表关联的字段
     */
    private String relationField;

    /**
     * 子表字典集合
     * key为子表名称，value为子表VO
     */
    private Map<String, DataTableVO> subTableMap;


    /**
     * 子表关联字段集合
     * key为子表名称，value为子表关联字段
     */
    private Map<String, String> subTableRelationFieldMap;

    /**
     * 表格对应的书签
     */
    private String bookMark;

    /**
     * 是否为列拓展
     */
    private Boolean isColumnExpend;

    /**
     * 表格名称
     */
    private String tbName;

    /**
     * 数据表
     */
    private DataTable table;

    /**
     * 关联次表
     */
    private DataTable secondaryTable;

    /**
     * 主表与此表的关联字段
     */
    private String mainAssociationField;

    /**
     * 此表与主表的关联字段（当前的表关联字段与此表的哪一个字段进行关联）
     */
    private String secondaryField;

    /**
     * 合并区域集合
     */
    private List<MergeAreaVO> mergeAreaList = new ArrayList<>();
}
