package com.sinoyd.report.word.vo;

import com.aspose.words.HorizontalAlignment;
import lombok.Data;

/**
 * 报告图片处理VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/26
 */
@Data
public class ImageVO {

    /**
     * 默认图片宽度
     */
    public static final Double DEFAULT_WIDTH = 480.0;

    /**
     * 默认图片高度
     */
    public static final Double DEFAULT_HEIGHT = 270.0;

    /**
     * 默认位置居中对齐
     */
    public static final Integer POSITION_CENTER = HorizontalAlignment.CENTER;

    /**
     * 位置左对齐
     */
    public static final Integer POSITION_LEFT = HorizontalAlignment.LEFT;

    /**
     * 位置右对齐
     */
    public static final Integer POSITION_RIGHT = HorizontalAlignment.RIGHT;

    /**
     * 位置水平对齐基准之外
     */
    public static final Integer POSITION_INSIDE = HorizontalAlignment.INSIDE;

    /**
     * 位置水平对齐基准内
     */
    public static final Integer POSITION_OUTSIDE = HorizontalAlignment.OUTSIDE;

    /**
     * 图片的相对路径
     */
    private String imgRelativePath;

    /**
     * 图片绝对路径
     */
    private String imageAbsPath;

    /**
     * 需要插入的书签位置
     */
    private String imageMark;

    /**
     * 需要插入的图片宽度
     */
    private Double width = DEFAULT_WIDTH;

    /**
     * 需要插入的图片高度
     */
    private Double height = DEFAULT_HEIGHT;

    /**
     * 图片的位置
     */
    private Integer position = POSITION_CENTER;

}
