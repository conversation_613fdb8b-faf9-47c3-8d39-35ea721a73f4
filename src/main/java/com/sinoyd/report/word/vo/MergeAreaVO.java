package com.sinoyd.report.word.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 合并区域VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
@Data
public class MergeAreaVO {

    /**
     * 当前表在文档中的位置
     */
    Integer tableIdx;

    /**
     * 如果有分页符，则需要告知当前需要合并的表对应的书签位置
     */
    String bookMark;

    /**
     * 纵向合并区域集合
     */
    List<MergeRowArea> rowMergeAreas = new ArrayList<>();

    /**
     * 横向合并区域集合
     */
    List<MergeColumnArea> colMergeAreas = new ArrayList<>();

    /**
     * 按照行合并区域(纵向合并区域)
     */
    @Data
    public static class MergeRowArea {
        /**
         * 开始合并的行索引
         */
        private Integer startRowNum;

        /**
         * 结束合并的行索引
         */
        private Integer endRowNum;

        /**
         * 需要做纵向合并的列索引(哪一行需要做纵向合并)
         */
        private Integer columnIndex;

        /**
         * 需要做纵向合并的列索引集合(批量合并)
         */
        private List<Integer> colIndexList;
    }

    /**
     * 按照列合并区域(横向合并区域)
     */
    @Data
    public static class MergeColumnArea {

        /**
         * 开始合并的列索引
         */
        private Integer startColNum;

        /**
         * 结束合并的列索引
         */
        private Integer endColNum;

        /**
         * 需要做列合并的行索引（哪些行需要做列合并）
         */
        private Integer rowIndex;

        /**
         * 需要批量横向合并的列索引集合
         */
        private List<Integer> rowIndexList;
    }
}
