package com.sinoyd.report.word.vo;

import com.aspose.words.net.System.Data.DataSet;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告生成数据传输VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/21
 */
@Data
@SuppressWarnings({"rawtypes"})
public class WordParamVO {

    /**
     * 整体Word的公共参数字典（key:字段占位符名称，Value:占位符对应的数据）
     */
    private Map<String, Object> basicMap = new HashMap<>();

    /**
     * Word中其他的表格数据（key:表名称, Value:表数据）
     */
    private Map<String, DataTableVO> dataMap = new HashMap<>();

    /**
     * 数据集
     */
    private DataSet dataSet;

    /**
     * 模板绝对路径
     */
    private String templateAbsPath;

    /**
     * 模板文件路径（相对路径Resource的路径）
     */
    private String relativePath;

    /**
     * 输出文件的绝对路径
     */
    private String outPutAbsPath;

    /**
     * 生成文件的路径地址（相对路径Resource的路径）
     */
    private String outRelativePath;

    /**
     * 报告文件名称
     */
    private String fileName;

    /**
     * 是否需要处理单元格格式
     */
    private Boolean isFormatCell = Boolean.TRUE;

    /**
     * 插入的图片集合
     */
    private List<ImageVO> imageList;

    /**
     * 图表数据集合
     */
    private List<ChartVO> chartList;
}
