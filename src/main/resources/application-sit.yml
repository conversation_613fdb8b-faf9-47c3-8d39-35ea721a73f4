server:
  port: ${PORT:6100}

spring:
  cache:
    type: redis
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:sinoyd}
    timeout: 10000
    database: 2
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

  # Mysql DATABASE CONFIG
  datasource:
    # sql连接配置
    driver-class-name: com.mysql.cj.jdbc.Driver
    jdbc-url: jdbc:mysql://${DB_HOST:*************}:${DB_PORT:3306}/${DB_NAME:lims60report}?useUnicode=true&allowMultiQueries=true&nullCatalogMeansCurrent=true
    username: ${DB_USER:user} #oracle #devuser
    password: ${DB_PWD:11111} #manager1 #qweAsd#21 #123qwe!@#
    initialSize: 10
    minIdle: 5
    maxActive: 50
    maxWait: 60000
    testWhileIdle: true

  flyway:
    url: jdbc:mysql://${DB_HOST:*************}:${DB_PORT:3306}/${DB_NAME:lims60report}?useUnicode=true&allowMultiQueries=true
    user: ${DB_USER:user}
    password: ${DB_PWD:11111}
    # 是否启用flyway
    enabled: ${FLYWAY_ENABLED:true}
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 生产务必禁掉。这个默认值是 false 理论上作为默认配置是不科学的。
    clean-disabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: ${FLYWAY_LOCATIONS:classpath:db/migration/mysql}
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true

  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:10MB} #单个文件最大20M
      max-request-size: ${MAX_REQUEST_SIZE:100MB} #请求总大小最大100M

#logging
logging:
  level:
    root: info
  file:
    path: ${LOG_PATH:E:/03-TCD2023/报表升级/03-代码/logs}
    name: ${LOG_NAME:LIMS60-report.log}

limsFile:
  # 文件上传路径
  filePath: E:/03-TCD2023/报表升级/03-代码/proxy/files
  # 临时目录（生成的报表会先放临时目录）
  outputPath: E:/03-TCD2023/报表升级/03-代码/proxy/outputs
  # 报表、采样单、原始记录单等模板
  templatePath: E:/03-TCD2023/报表升级/03-代码/proxy/files/report_templates
  # 允许上传的文件类型
  fileSuffix: jpg,doc,docx,xls,xlsx,jpeg,png,txt,mp3,flac,avi,mp4

