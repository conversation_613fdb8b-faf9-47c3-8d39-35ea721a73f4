CREATE TABLE TB_Report_BaseConfig
(
    id              VARCHAR(50)  NOT NULL COMMENT '报表id',
    reportCode      VARCHAR(50)  NOT NULL COMMENT '报表编码',
    reportTypeValue INT          NOT NULL COMMENT '报表类型编码，枚举维护：原始记录单、采样单、报告、报表、标签',
    templateName    VARCHAR(255) NOT NULL COMMENT '模板名称',
    templatePath    VARCHAR(255) NOT NULL COMMENT '模板位置',
    orderNum        INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    versionNum      VARCHAR(255) COMMENT '版本号',
    controlNum      VARCHAR(255) COMMENT '受控编号',
    definedFileName VARCHAR(255) COMMENT '配置报表名称',
    isDeleted       bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId           VARCHAR(50)  NOT NULL COMMENT '机构id',
    domainId        VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator         VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier        VARCHAR(50)  NOT NULL COMMENT '修改人',
    modifyDate      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '报表基础配置表';

CREATE TABLE TB_Report_GlobalConfig
(
    id          VARCHAR(50) NOT NULL COMMENT '主键',
    reportCode  VARCHAR(50) NOT NULL COMMENT '报表编码',
    isPageable  bit(1)      NOT NULL DEFAULT b'1' COMMENT '是否分页',
    pageRows    INT         NOT NULL COMMENT '每页行数',
    pageColumns INT         NOT NULL COMMENT '每页列数',
    fontFamily  VARCHAR(50) COMMENT '字体',
    fontSize    INT COMMENT '字体大小',
    isDeleted   bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
) COMMENT = '报表全局配置表';

CREATE TABLE TB_Report_AreaConfig
(
    id             VARCHAR(50)  NOT NULL COMMENT '主键',
    reportCode     VARCHAR(50)  NOT NULL COMMENT '报表编码',
    sheetName      VARCHAR(255) NOT NULL COMMENT 'sheet名称',
    areaType       VARCHAR(255) NOT NULL COMMENT '区域类型，枚举（1: Info，2: Data）',
    areaStart      VARCHAR(255) NOT NULL COMMENT '开始位置',
    areaEnd        VARCHAR(255) NOT NULL COMMENT '结束位置',
    expandType     INT          NOT NULL DEFAULT 3 COMMENT '扩展方式，枚举(1：行扩展 2：列扩展 3：不扩展)',
    expandAreaSize VARCHAR(50)  NOT NULL DEFAULT 1 COMMENT '每次扩展的行、列数',
    fontFamily     VARCHAR(50) COMMENT '字体',
    fontSize       INT COMMENT '字体大小',
    maxFontSize    INT COMMENT '最大字体大小，用于缩放',
    mergeStart     VARCHAR(255) COMMENT '合并开始位置',
    mergeEnd       VARCHAR(255) COMMENT '合并结束位置',
    expandPageSize INT COMMENT '每页总行、列数',
    orderNum       INT COMMENT '排序',
    isDeleted      bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
) COMMENT = '报表区域配置表';


