DROP TABLE IF EXISTS TB_Report_BaseConfig2Module;
CREATE TABLE TB_Report_BaseConfig2Module(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    baseConfigId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报表基础配置id' ,
    reportModuleId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告组件id（常量维护）' ,
    isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
)  COMMENT = '报告组件与报表基础配置关联表';

DROP TABLE IF EXISTS TB_Report_ReportModule;
CREATE TABLE TB_Report_ReportModule(
    id VARCHAR(50) NOT NULL   COMMENT '主键id' ,
    moduleCode VARCHAR(100) NOT NULL   COMMENT '组件编码' ,
    moduleName VARCHAR(100) NOT NULL   COMMENT '组件名称' ,
    tableName VARCHAR(100) NOT NULL   COMMENT '组件主表名称' ,
    sourceTableName VARCHAR(100)    COMMENT '组件数据行表名称' ,
    sampleCount INT    COMMENT '组件每页样品数量' ,
    testCount INT    COMMENT '组件每页测试项目数量' ,
    sonTableJson VARCHAR(500)    COMMENT '子组件配置信息（适用于复合组件）' ,
    isCompound bit(1) NOT NULL   COMMENT '是否复合组件' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    creator VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人' ,
    createDate DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    domainId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室' ,
    modifier VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人' ,
    modifyDate DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
    isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
)  COMMENT = '报告组件信息表';

DROP TABLE IF EXISTS TB_Report_ReportModule2GroupType;
CREATE TABLE TB_Report_ReportModule2GroupType(
    id VARCHAR(50) NOT NULL   COMMENT '主键id' ,
    baseConfigModuleId VARCHAR(50) NOT NULL   COMMENT '报告组件配置id' ,
    groupTypeName VARCHAR(255)    COMMENT '分页类型名称（包含数据源，属性名称，分页方式）' ,
    priority INT NOT NULL  DEFAULT -1 COMMENT '优先级（最外层分页的优先级最高）' ,
    isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
)  COMMENT = '报告各个组件配置的分页方式表';

DROP TABLE IF EXISTS TB_Data_Project;
CREATE TABLE TB_Data_Project(
    id VARCHAR(50) NOT NULL   COMMENT '租户号' ,
    projectId VARCHAR(50)    COMMENT '项目id' ,
    projectCode VARCHAR(255)    COMMENT '项目编号' ,
    projectTypeName VARCHAR(255)    COMMENT '项目类型名称' ,
    inceptPersonName VARCHAR(255)    COMMENT '项目登记人' ,
    leader VARCHAR(255)    COMMENT '项目负责人' ,
    inceptTime DATETIME    COMMENT '项目登记时间' ,
    monitorPurp VARCHAR(255)    COMMENT '监测目的' ,
    monitorMethods VARCHAR(1000)    COMMENT '监测方式' ,
    customerRequired VARCHAR(1000)    COMMENT '监测要求及说明' ,
    saveCondition VARCHAR(255)    COMMENT '保存条件' ,
    projectName VARCHAR(255)    COMMENT '项目名称' ,
    customerName VARCHAR(255)    COMMENT '委托单位' ,
    customerAddress VARCHAR(255)    COMMENT '地址' ,
    linkMan VARCHAR(255)    COMMENT '联系人' ,
    linkPhone VARCHAR(255)    COMMENT '电话' ,
    inspectedEnt VARCHAR(255)    COMMENT '受检单位' ,
    inspectedLinkMan VARCHAR(255)    COMMENT '受检方联系人' ,
    inspectedLinkPhone VARCHAR(255)    COMMENT '受检方联系电话' ,
    inspectedAddress VARCHAR(255)    COMMENT '受检方地址' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '项目信息';


DROP TABLE IF EXISTS TB_Data_Sample;
CREATE TABLE TB_Data_Sample(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    projectId VARCHAR(50)    COMMENT '项目id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    receiveId VARCHAR(50)    COMMENT '送样单id' ,
    sampleFolderId VARCHAR(50)    COMMENT '点位id' ,
    fixedPointId VARCHAR(50)    COMMENT '断面id' ,
    cycleOrder INT    COMMENT '频次' ,
    timesOrder INT    COMMENT '周期数' ,
    sampleOrder INT    COMMENT '样品数' ,
    redFolderName VARCHAR(255)    COMMENT '点位' ,
    sampleTypeName VARCHAR(255)    COMMENT '检测类型' ,
    sampleCategory INT    COMMENT '样品类别' ,
    samplingTimeBegin DATETIME    COMMENT '采样开始时间' ,
    samplingTimeEnd DATETIME    COMMENT '采样结束时间' ,
    qcId VARCHAR(50)    COMMENT '质控id' ,
    associateSampleId VARCHAR(50)    COMMENT '质控样的原样id' ,
    pack VARCHAR(255)    COMMENT '包装/规格' ,
    sampleWeight VARCHAR(255)    COMMENT '样品重量' ,
    weightOrQuantity VARCHAR(255)    COMMENT '样品数量' ,
    samColor VARCHAR(255)    COMMENT '样品颜色' ,
    sampleExplain VARCHAR(255)    COMMENT '样品特征' ,
    volume VARCHAR(255)    COMMENT '样品体积' ,
    recordCode VARCHAR(255)    COMMENT '送样单号' ,
    samplingTime DATETIME    COMMENT '采样时间' ,
    sendTime DATETIME    COMMENT '送样时间' ,
    senderName VARCHAR(255)    COMMENT '送样负责人' ,
    receiveSampleDate DATETIME    COMMENT '接样日期' ,
    folderCode VARCHAR(255)    COMMENT '点位号' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '样品信息';

DROP TABLE IF EXISTS TB_Data_QualityControl;
CREATE TABLE TB_Data_QualityControl(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    qcId VARCHAR(50)    COMMENT '质控id' ,
    associateSampleId VARCHAR(50)    COMMENT '关联样品id' ,
    qcGrade INT    COMMENT '质控等级' ,
    qcType INT    COMMENT '质控类型' ,
    qcValue VARCHAR(255)    COMMENT '质控值' ,
    qcVolume VARCHAR(255)    COMMENT '加标体积' ,
    qaId VARCHAR(50)    COMMENT '添加质控人员id' ,
    qcTime DATETIME    COMMENT '添加质控时间' ,
    qcTestValue VARCHAR(255)    COMMENT '测定值' ,
    realSampleTestValue VARCHAR(255)    COMMENT '样值' ,
    qcCode VARCHAR(255)    COMMENT '标样编号' ,
    qcOriginValue VARCHAR(255)    COMMENT '原样的检测结果' ,
    qcValidDate DATETIME    COMMENT '标样的有效期' ,
    qcStandardDate DATETIME    COMMENT '标样的配置日期' ,
    qcConcentration VARCHAR(255)    COMMENT '加标液浓度' ,
    qcVolumeDimension VARCHAR(255)    COMMENT '加标体积量纲' ,
    qcValueDimension VARCHAR(255)    COMMENT '加入标准量量纲' ,
    qcTestValueDimension VARCHAR(255)    COMMENT '测定值量纲' ,
    realSampleTestValueDimension VARCHAR(255)    COMMENT '样值量纲' ,
    qcConcentrationDimension VARCHAR(255)    COMMENT '加标液浓度量纲' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '质控信息';

DROP TABLE IF EXISTS TB_Data_Report;
CREATE TABLE TB_Data_Report(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    projectId VARCHAR(50)    COMMENT '项目id' ,
    code VARCHAR(255)    COMMENT '报告编号' ,
    reportType VARCHAR(255)    COMMENT '报告类型' ,
    reportYear INT    COMMENT '报告年份' ,
    analyseItemSortId VARCHAR(50)    COMMENT '分析项目排序id' ,
    folderSortId VARCHAR(50)    COMMENT '点位排序id' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '报告信息';

DROP TABLE IF EXISTS TB_Data_ReportFolderInfo;
CREATE TABLE TB_Data_ReportFolderInfo(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    reportId VARCHAR(50)    COMMENT '报告id' ,
    folderId VARCHAR(50)    COMMENT '点位id' ,
    folderName VARCHAR(255)    COMMENT '点位名称' ,
    folderCode VARCHAR(255)    COMMENT '点位编码' ,
    folderRemark VARCHAR(1000)    COMMENT '点位备注' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '报告点位信息';

DROP TABLE IF EXISTS TB_Data_ReportSampleInfo;
CREATE TABLE TB_Data_ReportSampleInfo(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    reportId VARCHAR(50)    COMMENT '报告id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    sampleCode VARCHAR(255)    COMMENT '样品编号' ,
    sampleRemark VARCHAR(255)    COMMENT '样品备注' ,
    reportFolderId VARCHAR(50)    COMMENT '报告点位信息' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '报告样品信息';

DROP TABLE IF EXISTS TB_Data_ReportBaseInfo;
CREATE TABLE TB_Data_ReportBaseInfo(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    reportId VARCHAR(50)    COMMENT '报告id' ,
    projectName VARCHAR(255)    COMMENT '项目名称' ,
    systemCode VARCHAR(255)    COMMENT '系统编号' ,
    inspectedEnt VARCHAR(255)    COMMENT '受检单位' ,
    inspectedAddress VARCHAR(255)    COMMENT '受检单位地址' ,
    customerName VARCHAR(255)    COMMENT '委托单位' ,
    customerAddress VARCHAR(255)    COMMENT '委托单位地址' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '报告基本信息';

DROP TABLE IF EXISTS TB_Data_ReportFolderSortInfo;
CREATE TABLE TB_Data_ReportFolderSortInfo(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    reportId VARCHAR(50)    COMMENT '报告id' ,
    folderId VARCHAR(50)    COMMENT '点位id' ,
    orderNum INT    COMMENT '排序值' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '报告点位排序';

DROP TABLE IF EXISTS TB_Data_AnalyseData;
CREATE TABLE TB_Data_AnalyseData(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    analyseDataId VARCHAR(50)    COMMENT '数据id' ,
    workSheetFolderId VARCHAR(50)    COMMENT '工作单id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    testId VARCHAR(50)    COMMENT '测试项目id' ,
    workSheetId VARCHAR(50)    COMMENT '小工作单id' ,
    redAnalyzeItemName VARCHAR(255)    COMMENT '分析项目名称' ,
    redAnalyzeMethodName VARCHAR(255)    COMMENT '分析方法名称' ,
    redCountryStandard VARCHAR(255)    COMMENT '国标名称' ,
    yearSn VARCHAR(255)    COMMENT '年度' ,
    analyzeMethodId VARCHAR(50)    COMMENT '分析方法id' ,
    analyseItemId VARCHAR(50)    COMMENT '分析项目id' ,
    qcId VARCHAR(50)    COMMENT '质控id' ,
    qcType INT    COMMENT '质控类型' ,
    qcGrade INT    COMMENT '质控类别' ,
    mostSignificance INT    COMMENT '有效位数' ,
    mostDecimal INT    COMMENT '小数位数' ,
    examLimitValue VARCHAR(255)    COMMENT '检出限' ,
    dimension VARCHAR(255)    COMMENT '量纲' ,
    testValue VARCHAR(255)    COMMENT '出证结果' ,
    testOrignValue VARCHAR(255)    COMMENT '检测结果' ,
    testValueDstr VARCHAR(255)    COMMENT '检测结果修约值' ,
    analystName VARCHAR(255)    COMMENT '分析人员' ,
    analyzeTime DATETIME    COMMENT '数据分析时间' ,
    finishTime DATETIME    COMMENT '分析完成时间' ,
    isDataEnabled BIT(1)    COMMENT '有效性' ,
    isCompleteField BIT(1)    COMMENT '是否在现场完成' ,
    isOutsourcing BIT(1)    COMMENT '是否分析分包' ,
    isSamplingOut BIT(1)    COMMENT '是否采样分包' ,
    gatherCode VARCHAR(255)    COMMENT '采集编号' ,
    qcInfo VARCHAR(255)    COMMENT '质控信息' ,
    seriesValue VARCHAR(255)    COMMENT '串联中间结果' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '数据信息';

DROP TABLE IF EXISTS TB_Data_QualityControlEvaluate;
CREATE TABLE TB_Data_QualityControlEvaluate(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    objectId VARCHAR(50)    COMMENT '关联id' ,
    qcId VARCHAR(50)    COMMENT '质控id' ,
    checkItem VARCHAR(255)    COMMENT '检查项' ,
    judgingMethod INT    COMMENT '评判方式' ,
    isPass BIT(1)    COMMENT '是否合格' ,
    checkItemValue VARCHAR(255)    COMMENT '检查项值' ,
    allowLimit VARCHAR(255)    COMMENT '允许限值' ,
    dimensionName VARCHAR(255)    COMMENT '量纲' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '质控评价信息';

DROP TABLE IF EXISTS TB_Data_InstrumentUseRecord;
CREATE TABLE TB_Data_InstrumentUseRecord(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    instrumentId VARCHAR(50)    COMMENT '仪器id' ,
    instrumentName VARCHAR(255)    COMMENT '仪器名称' ,
    instrumentModel VARCHAR(255)    COMMENT '仪器型号' ,
    instrumentCode VARCHAR(255)    COMMENT '仪器编号' ,
    instrumentSerialNo VARCHAR(255)    COMMENT '仪器本站编号' ,
    objectId VARCHAR(50)    COMMENT '关联id' ,
    objectType INT    COMMENT '使用类型' ,
    usePersonName VARCHAR(255)    COMMENT '使用人名称' ,
    startTime DATETIME    COMMENT '开始时间' ,
    endTime DATETIME    COMMENT '结束时间' ,
    testIds VARCHAR(255)    COMMENT '测试项目ids' ,
    temperature VARCHAR(255)    COMMENT '温度' ,
    humidity VARCHAR(255)    COMMENT '湿度' ,
    pressure VARCHAR(255)    COMMENT '大气压' ,
    beforeUseSituation VARCHAR(255)    COMMENT '使用前情况' ,
    beforeAfterSituation VARCHAR(255)    COMMENT '使用后情况' ,
    isAssistInstrument BIT(1)    COMMENT '是否辅助仪器' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '仪器信息';

DROP TABLE IF EXISTS TB_Data_SamplePreparation;
CREATE TABLE TB_Data_SamplePreparation(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    analyzeItemNames VARCHAR(255)    COMMENT '制备样品的分析项目名称' ,
    preparationBeginTime DATETIME    COMMENT '制备开始时间' ,
    preparationEndTime DATETIME    COMMENT '制备结束时间' ,
    preparedPersonName VARCHAR(255)    COMMENT '制备人名称' ,
    method VARCHAR(255)    COMMENT '制备方法' ,
    content VARCHAR(1000)    COMMENT '制备内容' ,
    instrumentId VARCHAR(1000)    COMMENT '制备仪器ids' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '样品制备信息';

DROP TABLE IF EXISTS TB_Data_CurveDetail;
CREATE TABLE TB_Data_CurveDetail(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    curveId VARCHAR(50)    COMMENT '测试项目id' ,
    analyseCode VARCHAR(255)    COMMENT '分析编号' ,
    addVolume VARCHAR(255)    COMMENT '标准溶液加入体积' ,
    addAmount VARCHAR(255)    COMMENT '标准物加入量' ,
    absorbance VARCHAR(255)    COMMENT '吸光度A' ,
    lessBlankAbsorbance VARCHAR(255)    COMMENT '减空白吸光度' ,
    absorbanceB VARCHAR(255)    COMMENT '吸光度B' ,
    relativeDeviation VARCHAR(255)    COMMENT '相对偏差' ,
    aValueTTZ VARCHAR(255)    COMMENT '220吸光度' ,
    aValueTSF VARCHAR(255)    COMMENT '275吸光度' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '曲线明细详情';

DROP TABLE IF EXISTS TB_Data_Curve;
CREATE TABLE TB_Data_Curve(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    testId VARCHAR(50)    COMMENT '测试项目id' ,
    workSheetFolderId VARCHAR(50)    COMMENT '工作单id' ,
    checkDate DATETIME    COMMENT '校准日期' ,
    coefficient VARCHAR(255)    COMMENT '相关系数' ,
    configDate DATETIME    COMMENT '配置日期' ,
    zeroPoint VARCHAR(255)    COMMENT '曲线零点' ,
    kValue VARCHAR(255)    COMMENT '斜率a' ,
    bValue VARCHAR(255)    COMMENT '截距b' ,
    cValue VARCHAR(255)    COMMENT '实数c' ,
    isDouble BIT(1)    COMMENT '是否双曲线' ,
    curveType VARCHAR(255)    COMMENT '曲线类型' ,
    curveMode VARCHAR(255)    COMMENT '曲线模型' ,
    curveInfo VARCHAR(255)    COMMENT '曲线信息' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '曲线信息';

DROP TABLE IF EXISTS TB_Data_WorkSheetFolder;
CREATE TABLE TB_Data_WorkSheetFolder(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    workSheetFolderId VARCHAR(50)    COMMENT '工作单id' ,
    workSheetCode VARCHAR(255)    COMMENT '工作单号' ,
    analystName VARCHAR(255)    COMMENT '分析人名称' ,
    analyzeMethodId VARCHAR(50)    COMMENT '分析方法id' ,
    backOpinion VARCHAR(255)    COMMENT '退回意见' ,
    finishTime DATETIME    COMMENT '分析完成日期' ,
    remark VARCHAR(255)    COMMENT '备注' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '工作单明细';

DROP TABLE IF EXISTS TB_Data_EvaluationRecord;
CREATE TABLE TB_Data_EvaluationRecord(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    objectId VARCHAR(50)    COMMENT '关联id' ,
    objectType INT    COMMENT '类型' ,
    evaluationId VARCHAR(50)    COMMENT '评价标准id' ,
    evaluationLevelId VARCHAR(50)    COMMENT '评价等级id' ,
    testId VARCHAR(50)    COMMENT '测试项目id' ,
    upperLimitSymble VARCHAR(255)    COMMENT '上限运算符' ,
    upperLimitValue VARCHAR(255)    COMMENT '上限' ,
    lowerLimitSymble VARCHAR(255)    COMMENT '下限运算符' ,
    lowerLimitValue VARCHAR(255)    COMMENT '下限' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '评价明细';

DROP TABLE IF EXISTS TB_Data_ParamsData;
CREATE TABLE TB_Data_ParamsData(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    objectId VARCHAR(50)    COMMENT '关联id' ,
    paramsConfigName VARCHAR(255)    COMMENT '参数名称' ,
    paramsValue VARCHAR(255)    COMMENT '参数数据值' ,
    dataSource VARCHAR(255)    COMMENT '数据源' ,
    dimension VARCHAR(255)    COMMENT '单位名称' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '参数信息';

DROP TABLE IF EXISTS TB_Data_OutSorceData;
CREATE TABLE TB_Data_OutSorceData(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    analyseDataId VARCHAR(50)    COMMENT '数据id' ,
    analyzeMethodName VARCHAR(255)    COMMENT '分析名称' ,
    testValue VARCHAR(255)    COMMENT '出证结果' ,
    dimensionName VARCHAR(255)    COMMENT '量纲名称' ,
    analyzeTime DATETIME    COMMENT '分析日期' ,
    analyzeEndTime DATETIME    COMMENT '分析结束日期' ,
    subcontractor VARCHAR(255)    COMMENT '分包商' ,
    cmaCode VARCHAR(255)    COMMENT 'CMA编号' ,
    outSourceReportCode VARCHAR(255)    COMMENT '分包报告编号' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '分包信息';

DROP TABLE IF EXISTS TB_Data_SampleGroup;
CREATE TABLE TB_Data_SampleGroup(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    receiveId VARCHAR(50)    COMMENT '送样单id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    sampleTypeGroupName VARCHAR(255)    COMMENT '样品分组名称' ,
    analyseItemNames VARCHAR(255)    COMMENT '分析项目名称' ,
    fixer VARCHAR(255)    COMMENT '固定剂' ,
    containerName VARCHAR(255)    COMMENT '容器名称' ,
    pretreatmentMethod VARCHAR(255)    COMMENT '前处理方式' ,
    sampleVolume VARCHAR(255)    COMMENT '采样体积' ,
    saveCondition VARCHAR(255)    COMMENT '保存条件' ,
    riskDescription VARCHAR(255)    COMMENT '危险性描述' ,
    transportationCondition VARCHAR(255)    COMMENT '运输条件' ,
    isGroup INT    COMMENT '分组标识 1:按分组  2:全因子  3:单因子' ,
    containerStatus INT    COMMENT '采样容器状态 EnumContainerStatus 1.完好无损 2.破损' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '样品分组信息';

DROP TABLE IF EXISTS TB_Data_Sub2Sample;
CREATE TABLE TB_Data_Sub2Sample(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    receiveId VARCHAR(50)    COMMENT '送样单id' ,
    sampleId VARCHAR(50)    COMMENT '样品id' ,
    subId VARCHAR(50)    COMMENT '领样单id' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '领样单和样品关系';

DROP TABLE IF EXISTS TB_Data_WorkSheetReagent;
CREATE TABLE TB_Data_WorkSheetReagent(
    id VARCHAR(50) NOT NULL   COMMENT 'id' ,
    workSheetFolderId VARCHAR(50)    COMMENT '工作单id' ,
    reagentConfigId VARCHAR(50)    COMMENT '试剂配置记录id' ,
    reagent VARCHAR(1000)    COMMENT '配置记录' ,
    context VARCHAR(1000)    COMMENT '需求的配置过程' ,
    reagentName VARCHAR(255)    COMMENT '试剂名称' ,
    reagentSpecification VARCHAR(255)    COMMENT '试剂规格' ,
    configurationSolution VARCHAR(255)    COMMENT '配置溶液' ,
    configDate DATETIME    COMMENT '配置日期' ,
    expiryDate DATETIME    COMMENT '有效期' ,
    course VARCHAR(1000)    COMMENT '稀释过程记录' ,
    diluent VARCHAR(255)    COMMENT '稀释液' ,
    reagentType INT    COMMENT '试剂类型' ,
    suitItem VARCHAR(255)    COMMENT '适用项目' ,
    orgId VARCHAR(50) NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id' ,
    domainId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '所属实验室id' ,
    syncTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '同步时间' ,
    PRIMARY KEY (id)
)  COMMENT = '试剂配置记录信息';

DROP TABLE IF EXISTS TB_Report_BusinessGlobalConfig;
CREATE TABLE TB_Report_BusinessGlobalConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    reportCode VARCHAR(50) NOT NULL   COMMENT '报表编码' ,
    notShowInYYSample VARCHAR(255)    COMMENT '不跟随原样的质控' ,
    showOutParallel VARCHAR(255)    COMMENT '平行质控数据是否显示现场平行' ,
    showAllCurve bit(1)    COMMENT '是否同时显示双曲线' ,
    groupByAnalyzeItem VARCHAR(255)    COMMENT '是否根据因子分页' ,
    qcWithYY VARCHAR(255)    COMMENT '质控数据是否跟随原样' ,
    qcPagingByProject VARCHAR(255)    COMMENT '质控信息页（多个质控样）是否按项目进行分页' ,
    pageSyncWithData VARCHAR(255)    COMMENT '质控页质控样数据页数同步时，是否算上原始记录页data区的page页数' ,
    formatSci VARCHAR(255)    COMMENT '是否转换科学计数法格式' ,
    qcRepeatDisplay VARCHAR(255)    COMMENT '需要跟随项目重复展示的质控样' ,
    originalRecordType VARCHAR(255)    COMMENT '原始记录页样品分页方式' ,
    workSheetDataSourceType VARCHAR(255)    COMMENT '记录单数据源实现类' ,
    PRIMARY KEY (id)
)  COMMENT = '报表业务全局配置表';

DROP TABLE IF EXISTS TB_Report_BusinessAreaConfig;
CREATE TABLE TB_Report_BusinessAreaConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    areaConfigId VARCHAR(50) NOT NULL   COMMENT '区域配置id' ,
    defaultVal VARCHAR(100)    COMMENT '默认值' ,
    dateFormat VARCHAR(100)    COMMENT '日期类型数据默认格式' ,
    blankProperty VARCHAR(255)    COMMENT '空白属性名称' ,
    blankStr VARCHAR(255)    COMMENT '空白默认值' ,
    qcCountPerTest INT(11)    COMMENT '质控样每页每个测试项目的样品数量' ,
    PRIMARY KEY (id)
)  COMMENT = '报表业务区域配置表';

DROP TABLE IF EXISTS TB_Report_SampleGroupConfig;
CREATE TABLE TB_Report_SampleGroupConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    reportCode VARCHAR(255)    COMMENT '报表编码' ,
    groupType INT(11)    COMMENT '分组类型 1:按主要样品及关联样品分组 2：自定义分组' ,
    mainDataExcludeType VARCHAR(255)    COMMENT '主要样品需要排除的样品类型' ,
    relDataType VARCHAR(255)    COMMENT '关联样品类型' ,
    customerGroupTypeName VARCHAR(255)    COMMENT '自定义分组方式' ,
    PRIMARY KEY (id)
)  COMMENT = '原始记录样品分组配置';

DROP TABLE IF EXISTS TB_Report_CellConfig;
CREATE TABLE TB_Report_CellConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    areaConfigId VARCHAR(50)    COMMENT '区域配置id' ,
    placeHolderName VARCHAR(255)    COMMENT '单元格占位符名称' ,
    propertyName VARCHAR(255)    COMMENT '属性名称' ,
    valueFormat INT(11)    COMMENT '单元格值的格式' ,
    dataSource VARCHAR(255)    COMMENT '数据源' ,
    composition INT(11)    COMMENT '单元格数据的组合方式' ,
    associateData VARCHAR(255)    COMMENT '关联数据' ,
    connectString VARCHAR(255)    COMMENT '多个属性组合时使用的连接符' ,
    customerCalculateMode VARCHAR(255)    COMMENT '自定义计算方式类名' ,
    PRIMARY KEY (id)
)  COMMENT = '报表各数据区域的每个单元格的配置';

DROP TABLE IF EXISTS TB_Report_CellConditionConfig;
CREATE TABLE TB_Report_CellConditionConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    cellConfigId VARCHAR(50)    COMMENT '单元格配置id' ,
    conditionType INT(11)    COMMENT '条件类型' ,
    qcGrade INT(11)    COMMENT '质控等级' ,
    qcType INT(11)    COMMENT '质控类型' ,
    analyzeItemName VARCHAR(255)    COMMENT '分析项目名称' ,
    propertyName VARCHAR(255)    COMMENT '属性名称' ,
    valueFormat INT(11)    COMMENT '单元格值的格式' ,
    dataSource VARCHAR(255)    COMMENT '数据来源' ,
    composition INT(11)    COMMENT '单元格数据的组合方式' ,
    associateData VARCHAR(255)    COMMENT '关联数据' ,
    connectString VARCHAR(255)    COMMENT '多个属性组合时使用的连接符' ,
    customerCalculateMode VARCHAR(255)    COMMENT '自定义计算方式类名' ,
    PRIMARY KEY (id)
)  COMMENT = '报表数据区单元格条件配置';

DROP TABLE IF EXISTS TB_Report_QcSampleGroupConfig;
CREATE TABLE TB_Report_QcSampleGroupConfig(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    reportCode VARCHAR(255)    COMMENT '报表编码' ,
    groupPropertyName VARCHAR(255)    COMMENT '分组属性名称' ,
    groupAliasName VARCHAR(255)    COMMENT '分组属性别名' ,
    qcType INT(11)    COMMENT '质控类型' ,
    qcGrade INT(11)    COMMENT '质控等级' ,
    groupRules VARCHAR(500)    COMMENT '分组规则' ,
    PRIMARY KEY (id)
)  COMMENT = '报表原始记录页质控样分组展示配置';
