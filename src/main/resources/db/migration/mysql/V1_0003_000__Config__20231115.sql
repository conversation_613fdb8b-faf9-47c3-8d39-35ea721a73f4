-- -------------------------------------
-- 创建文档附件表
-- -------------------------------------
DROP TABLE IF EXISTS TB_REPORT_Document;
CREATE TABLE TB_REPORT_Document
(
    id             varchar(50)  NOT NULL COMMENT '主键',
    objectId       varchar(50)  NOT NULL COMMENT '关联对象Id（Guid）',
    folderName     varchar(255) NULL DEFAULT NULL COMMENT '文件夹名称',
    fileName       varchar(255) NULL DEFAULT NULL COMMENT '文件名称',
    physicalName   varchar(255) NULL DEFAULT NULL COMMENT '物理文件名',
    filePath       varchar(500) NULL DEFAULT NULL COMMENT '文件路径',
    isDeleted      bit(1)       NOT NULL DEFAULT b'0' COMMENT '假删',
    isTranscript   bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否副本',
    docType        varchar(50)  NOT NULL COMMENT '文件类型',
    docTypeDesc    varchar(255) NOT NULL COMMENT '文件类型描述',
    docSize        int(11) NOT NULL DEFAULT 0 COMMENT '文件大小',
    docSuffix      varchar(10) NULL DEFAULT NULL COMMENT '文件后缀',
    downloadTimes  int(11) NOT NULL DEFAULT 0 COMMENT '下载次数',
    orderNum       int(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    remark         varchar(1000) NULL DEFAULT NULL COMMENT '备注',
    uploadPersonId varchar(50)  NOT NULL COMMENT '上传人Id',
    uploadPerson   varchar(50) NULL DEFAULT NULL COMMENT '上传人姓名',
    isTop          bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否文件置顶',
    orgId          varchar(50)  NOT NULL COMMENT '组织机构id',
    creator        varchar(50)  NOT NULL COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)  NOT NULL COMMENT '所属实验室',
    modifier       varchar(50)  NOT NULL COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
) COMMENT '附件文档表';