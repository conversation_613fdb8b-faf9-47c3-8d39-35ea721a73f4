-- 全局配置表 TB_REPORT_GlobalConfig 修改 --
alter table TB_REPORT_GlobalConfig
    add orgId varchar(50) not null comment '组织机构id';

alter table TB_REPORT_GlobalConfig
    add domainId varchar(50) not null comment '实验室id';

alter table TB_REPORT_GlobalConfig
    add creator varchar(50) not null comment '创建人id';

alter table TB_REPORT_GlobalConfig
    add createDate datetime default CURRENT_TIMESTAMP not null;

alter table TB_REPORT_GlobalConfig
    add modifier varchar(50) not null comment '修改人id';

alter table TB_REPORT_GlobalConfig
    add modifyDate datetime default CURRENT_TIMESTAMP not null comment '修改时间';

-- 数据源表 --
CREATE TABLE TB_Report_DataSource
(
    id           VARCHAR(50) NOT NULL COMMENT '主键',
    dsName       VARCHAR(50) NOT NULL COMMENT '数据源名称',
    dbTypeCode   VARCHAR(50) NOT NULL COMMENT '数据库类型字典编码，字典管理',
    dbDriverCode VARCHAR(50) NOT NULL COMMENT '数据库驱动字典编码，字典管理，和dbTypeCode联动',
    dbHost       VARCHAR(50) NOT NULL COMMENT '数据库地址',
    dbPort       VARCHAR(50) NOT NULL COMMENT '数据库端口，需要根据dbTypeCode给出默认值',
    dbName       VARCHAR(50) NOT NULL COMMENT '数据库Schema名称',
    dbUserName   VARCHAR(50) NOT NULL COMMENT '数据库用户名',
    dbPassword   VARCHAR(50) NOT NULL COMMENT '数据库密码，加密存储',
    remark       VARCHAR(900) COMMENT '备注',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId        VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId     VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator      VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier     VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据源配置表';

-- 数据集表 --
CREATE TABLE TB_Report_DataSet
(
    id           VARCHAR(50) NOT NULL COMMENT '主键',
    dsId         VARCHAR(50) NOT NULL COMMENT '数据源id',
    dataSetName  VARCHAR(50) NOT NULL COMMENT '数据集名称',
    dataSetCode  VARCHAR(50) NOT NULL COMMENT '数据集编码',
    isCollection bit(1)      NOT NULL DEFAULT b'1' COMMENT '是否集合',
    sqlContent   text        NOT NULL COMMENT 'sql内容',
    orderNum     INT         NOT NULL DEFAULT 0 COMMENT '排序值',
    remark       VARCHAR(900) COMMENT '备注',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId        VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId     VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator      VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier     VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据集表';

-- 数据集参数表 --
CREATE TABLE TB_Report_DataSetParam
(
    id         VARCHAR(50) NOT NULL COMMENT '主键',
    dataSetId  VARCHAR(50) NOT NULL COMMENT '数据集id',
    paramName  VARCHAR(50) NOT NULL COMMENT '参数名称',
    paramType  VARCHAR(50) NOT NULL DEFAULT 0 COMMENT '参数类型，枚举 EnumParamType 管理',
    orderNum   INT         NOT NULL COMMENT '排序值',
    remark     VARCHAR(900) COMMENT '说明',
    isDeleted  bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId      VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId   VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator    VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier   VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据集参数表';

-- 数据集的数据列表 --
CREATE TABLE TB_Report_DataSetColumn
(
    id         VARCHAR(50) NOT NULL COMMENT '主键',
    dataSetId  VARCHAR(50) NOT NULL COMMENT '数据集id',
    columnCode VARCHAR(50) NOT NULL COMMENT '数据列编码',
    columnName VARCHAR(50) NOT NULL COMMENT '数据列名称',
    orderNum   INT         NOT NULL DEFAULT 0 COMMENT '排序值',
    remark     VARCHAR(900) COMMENT '说明',
    isDeleted  bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId      VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId   VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator    VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier   VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据集的数据列表';;

-- API接口表 --
CREATE TABLE TB_Report_Api
(
    id                VARCHAR(50)  NOT NULL COMMENT '主键',
    apiCode           VARCHAR(50)  NOT NULL COMMENT 'API编码',
    apiName           VARCHAR(255) NOT NULL COMMENT 'API名称',
    apiPath           VARCHAR(900) NOT NULL COMMENT 'API地址',
    apiType           INT          NOT NULL COMMENT 'API类型，枚举 EnumAPIType 管理',
    isCollection      bit(1)       NOT NULL DEFAULT b'1' COMMENT '是否集合',
    requestMethod     INT          NOT NULL COMMENT '请求方式，枚举 EnumRequestMethod 管理',
    authorizationType INT          NOT NULL COMMENT '认证方式，枚举 EnumAuthorizationType 管理',
    gatherKey         VARCHAR(50) COMMENT '采集key(返回key)',
    orderNum          INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    remark            VARCHAR(900) COMMENT '备注',
    isDeleted         bit(1)       NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId             VARCHAR(50)  NOT NULL COMMENT '机构id',
    domainId          VARCHAR(50)  NOT NULL COMMENT '实验室id',
    creator           VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier          VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = 'API接口表';

-- API接口参数表 --
CREATE TABLE TB_Report_ApiParam
(
    id         VARCHAR(50) NOT NULL COMMENT '主键',
    apiId      VARCHAR(50) NOT NULL COMMENT 'API接口id',
    paramName  VARCHAR(50) NOT NULL COMMENT '参数名称',
    paramType  VARCHAR(50) NOT NULL DEFAULT 0 COMMENT '参数类型，枚举 EnumParamType 管理',
    paramValue VARCHAR(50) COMMENT '参数值',
    orderNum   INT         NOT NULL COMMENT '排序值',
    remark     VARCHAR(900) COMMENT '说明',
    isDeleted  bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId      VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId   VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator    VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier   VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = 'API参数表';

-- API接口数据列表 --
CREATE TABLE TB_Report_ApiColumn
(
    id         VARCHAR(50) NOT NULL COMMENT '主键',
    apiId      VARCHAR(50) NOT NULL COMMENT 'API接口id',
    columnCode VARCHAR(50) NOT NULL COMMENT '数据列编码',
    columnName VARCHAR(50) NOT NULL COMMENT '数据列名称',
    orderNum   INT         NOT NULL DEFAULT 0 COMMENT '排序值',
    remark     VARCHAR(900) COMMENT '说明',
    isDeleted  bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId      VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId   VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator    VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier   VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = 'API数据列表';

-- 数据集应用表 --
CREATE TABLE TB_Report_DataSetApply
(
    id          VARCHAR(50) NOT NULL COMMENT '主键',
    applyType   VARCHAR(50) NOT NULL COMMENT '应用类型，枚举 EnumApplyType 管理',
    applyName   VARCHAR(50) NOT NULL COMMENT '应用名称',
    dataSetType INT         NOT NULL COMMENT '数据集类型，枚举 EnumDataSetType 管理',
    dataSetId   VARCHAR(50) NOT NULL COMMENT '数据集id，如果是接口，存储接口id',
    remark      VARCHAR(900) COMMENT '备注',
    isDeleted   bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId       VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId    VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator     VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier    VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据集应用表';

-- 数据集应用数据列表 --
CREATE TABLE TB_Report_DataSetApplyColumn
(
    id              VARCHAR(50) NOT NULL COMMENT '主键',
    dataSetApplyId  VARCHAR(50) NOT NULL COMMENT '数据集应用id',
    columnCode      VARCHAR(50) NOT NULL COMMENT '数据列编码',
    columnName      VARCHAR(50) NOT NULL COMMENT '数据列名称',
    placeholderName VARCHAR(50) NOT NULL COMMENT '模版占位符',
    orderNum        INT         NOT NULL DEFAULT 0 COMMENT '排序值',
    remark          VARCHAR(900) COMMENT '说明',
    isDeleted       bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId           VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId        VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator         VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier        VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数据集应用数据列表';

