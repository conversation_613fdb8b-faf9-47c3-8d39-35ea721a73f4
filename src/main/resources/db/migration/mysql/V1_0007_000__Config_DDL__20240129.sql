-- 区域配置表 TB_REPORT_AreaConfig 修改 --
alter table TB_REPORT_AreaConfig
    add orgId varchar(50) not null comment '组织机构id';

alter table TB_REPORT_AreaConfig
    add domainId varchar(50) not null comment '实验室id';

alter table TB_REPORT_AreaConfig
    add creator varchar(50) not null comment '创建人id';

alter table TB_REPORT_AreaConfig
    add createDate datetime default CURRENT_TIMESTAMP not null;

alter table TB_REPORT_AreaConfig
    add modifier varchar(50) not null comment '修改人id';

alter table TB_REPORT_AreaConfig
    add modifyDate datetime default CURRENT_TIMESTAMP not null comment '修改时间';

alter table TB_REPORT_AreaConfig
    add remark varchar(900) null comment '备注' after orderNum;

-- 区域数据绑定表 --
CREATE TABLE TB_Report_AreaDataBanding
(
    id            VARCHAR(50) NOT NULL COMMENT '主键',
    areaId        VARCHAR(50) NOT NULL COMMENT '区域id',
    applyColumnId VARCHAR(50) NOT NULL COMMENT '数据集应用数据列id',
    isDeleted     bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标记',
    orgId         VARCHAR(50) NOT NULL COMMENT '机构id',
    domainId      VARCHAR(50) NOT NULL COMMENT '实验室id',
    creator       VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier      VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '区域数据绑定表';

