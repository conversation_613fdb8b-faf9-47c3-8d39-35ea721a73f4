-- 修改API接口配置中的认证类型字段
ALTER TABLE TB_REPORT_Api DROP COLUMN authorizationType;

ALTER TABLE TB_REPORT_Api
    ADD COLUMN authorizationApiId VARCHAR(50) NULL COMMENT '认证接口Id' AFTER requestMethod;

-- 删除数据集应用表
DROP TABLE TB_REPORT_DataSetApply;

-- 数据集应用列中数据集类型与数据集关联Id

ALTER TABLE TB_REPORT_DataSetApplyColumn DROP COLUMN dataSetApplyId;

ALTER TABLE TB_REPORT_DataSetApplyColumn
    ADD COLUMN dataSetId VARCHAR(50) NOT NULL COMMENT '数据集id，如果是接口，存储接口id' AFTER id;

ALTER TABLE TB_REPORT_DataSetApplyColumn
    ADD COLUMN dataSetType INT NOT NULL COMMENT '数据集类型，枚举 EnumDataSetType 管理' AFTER id;

ALTER TABLE TB_REPORT_DataSetApplyColumn
    ADD COLUMN dataSetColumnId VARCHAR(50) NOT NULL COMMENT '数据集/API接口列Id' AFTER dataSetId;

ALTER TABLE TB_REPORT_DataSetApplyColumn
    ADD COLUMN businessType VARCHAR(50) NOT NULL COMMENT '业务类型，常量管理' AFTER id;
