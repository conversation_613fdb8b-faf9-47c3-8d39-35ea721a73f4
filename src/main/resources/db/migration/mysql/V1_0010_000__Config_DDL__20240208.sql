CREATE TABLE TB_REPORT_AreaConfig2DataSetApplyColumn
(
    id                   varchar(50) NOT NULL COMMENT '主键',
    areaConfigId         varchar(50) NOT NULL COMMENT '区域配置id',
    dataSetApplyColumnId varchar(50) NOT NULL COMMENT '应用数据列id',
    isDeleted            bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (id)
);

ALTER TABLE TB_REPORT_AreaConfig
    ADD COLUMN areaName varchar(50) NOT NULL COMMENT '区域名称';