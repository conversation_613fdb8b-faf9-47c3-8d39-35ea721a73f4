DROP TABLE IF EXISTS TB_Report_ApplyConfig;
CREATE TABLE TB_Report_ApplyConfig
(
    id          VARCHAR(50)  NOT NULL COMMENT '主键',
    reportCode  VARCHAR(50)  NOT NULL COMMENT '报表编码',
    webAppId    VARCHAR(50)  NOT NULL COMMENT '所属应用id',
    webAppName  VARCHAR(50)  NOT NULL COMMENT '所属应用名称',
    moduleCode  VARCHAR(50)  NOT NULL COMMENT '所属模块编码',
    moduleName  VARCHAR(50)  NOT NULL COMMENT '所属模块名称',
    controlCode VARCHAR(50)  NOT NULL COMMENT '控件编码',
    controlSite VARCHAR(100) COMMENT '控件位置',
    controlType INT          NOT NULL COMMENT '控件类型，枚举管理',
    isEditable  BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否可编辑',
    isEnabled   BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否启用',
    displayName VARCHAR(100) NOT NULL COMMENT '显示名称',
    isDeleted   bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId       VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId    VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator     VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier    VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '报表应用配置表';

DROP TABLE IF EXISTS TB_Report_SamplingConfig;
CREATE TABLE TB_Report_SamplingConfig
(
    id              VARCHAR(50)  NOT NULL COMMENT '主键',
    reportCode      VARCHAR(50)  NOT NULL COMMENT '报表编码',
    formName        VARCHAR(100) NOT NULL COMMENT '采样单名称',
    sampleTypeId    VARCHAR(900) NOT NULL COMMENT '检测类型id，多个英文逗号拼接',
    sampleTypeName  VARCHAR(200) COMMENT '检测类型名称,逗号拼接',
    bigSampleTypeId VARCHAR(50)  NOT NULL COMMENT '检测类型大类id',
    isRelatedTest   BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否仅关联测试项目显示',
    remark          VARCHAR(900) COMMENT '备注',
    orderNum        INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    isDeleted       bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId           VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId        VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator         VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier        VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '采样单配置表';

DROP TABLE IF EXISTS TB_Report_SamplingParam;
CREATE TABLE TB_Report_SamplingParam
(
    id               VARCHAR(50)  NOT NULL COMMENT '主键',
    samplingConfigId VARCHAR(50)  NOT NULL COMMENT '采样单配置id',
    paramCategory    INT          NOT NULL COMMENT '参数种类，枚举管理，如样品参数、数据参数',
    paramId          VARCHAR(50)  NOT NULL COMMENT '参数id',
    paramType        INT          NOT NULL COMMENT '参数类型，枚举管理',
    paramName        VARCHAR(50)  NOT NULL COMMENT '参数名称',
    aliasName        VARCHAR(100) NOT NULL COMMENT '参数别名',
    controlType      INT          NOT NULL COMMENT '控件类型，枚举管理',
    isExtend         BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否扩展参数',
    isMandatory      BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否必填',
    isShare          BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否共享',
    dataSource       varchar(200) NULL DEFAULT '' COMMENT '数据源',
    defaultValue     VARCHAR(50) COMMENT '默认值',
    dimensionId      VARCHAR(50) COMMENT '计量单位id',
    dimensionName    VARCHAR(50) COMMENT '计量单位名称',
    orderNum         INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    isDisplay        BIT(1)       NOT NULL DEFAULT B'1' COMMENT '是否显示',
    reference        VARCHAR(100) COMMENT '参考文本',
    dsType           INT COMMENT '数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置',
    isDeleted        bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId            VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '采样单参数表';

DROP TABLE IF EXISTS TB_Report_SamplingParamDefaultValue;
CREATE TABLE TB_Report_SamplingParamDefaultValue
(
    id              VARCHAR(50) NOT NULL COMMENT '主键',
    samplingParamId VARCHAR(50) NOT NULL COMMENT '采样单参数id',
    testId          VARCHAR(50) NOT NULL COMMENT '测试项目公式id',
    defaultValue    VARCHAR(50) COMMENT '默认值',
    isDeleted       bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId           VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId        VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator         VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier        VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '采样单参数默认值表(目前只有数据参数有)';

DROP TABLE IF EXISTS TB_Report_SamplingTest;
CREATE TABLE TB_Report_SamplingTest
(
    id               VARCHAR(50) NOT NULL COMMENT '主键',
    samplingConfigId VARCHAR(50) NOT NULL COMMENT '采样单配置id',
    testId           VARCHAR(50) NOT NULL COMMENT '测试项目id',
    isDeleted        bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId            VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId         VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator          VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '采样单关联测试项目表';

DROP TABLE IF EXISTS TB_Report_WorkSheetConfig;
CREATE TABLE TB_Report_WorkSheetConfig
(
    id               VARCHAR(50)  NOT NULL COMMENT '主键',
    reportCode       VARCHAR(50)  NOT NULL COMMENT '报表编码',
    formName         VARCHAR(100) NOT NULL COMMENT '原始记录单名称',
    fillType         INT          NOT NULL COMMENT '录入方式，枚举管理',
    isShareHeadParam BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否共享表头参数',
    remark           VARCHAR(900) COMMENT '备注',
    orderNum         INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    isDeleted        bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId            VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '原始记录单配置表';

DROP TABLE IF EXISTS TB_Report_WorkSheetParam;
CREATE TABLE TB_Report_WorkSheetParam
(
    id                VARCHAR(50)  NOT NULL COMMENT '主键',
    workSheetConfigId VARCHAR(50)  NOT NULL COMMENT '原始记录单配置id',
    paramCategory     INT          NOT NULL COMMENT '参数种类，枚举管理，如表头参数',
    paramId           VARCHAR(50)  NOT NULL COMMENT '参数id',
    paramName         VARCHAR(50)  NOT NULL COMMENT '参数名称',
    aliasName         VARCHAR(100) NOT NULL COMMENT '参数别名',
    controlType       INT          NOT NULL COMMENT '控件类型，枚举管理',
    isMandatory       BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否必填',
    isCert            BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否出证',
    dimensionId       VARCHAR(50) COMMENT '量纲id',
    dimensionName     VARCHAR(50) COMMENT '量纲名称',
    defaultValue      VARCHAR(100) COMMENT '默认值',
    significantDigit  INT COMMENT '有效位数',
    decimalDigit      INT COMMENT '小数位数',
    orderNum          INT          NOT NULL DEFAULT B'0' COMMENT '排序值',
    isDisplay         BIT(1)       NOT NULL DEFAULT B'1' COMMENT '是否显示',
    reference         VARCHAR(100) COMMENT '参考文本',
    dsType            INT COMMENT '数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置',
    dataSource        varchar(200) NULL DEFAULT '' COMMENT '数据源',
    isDeleted         bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId             VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId          VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator           VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier          VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '原始记录单参数表';

DROP TABLE IF EXISTS TB_Report_WorkSheetTest;
CREATE TABLE TB_Report_WorkSheetTest
(
    id                VARCHAR(50) NOT NULL COMMENT '主键',
    workSheetConfigId VARCHAR(50) NOT NULL COMMENT '原始记录单配置id',
    testId            VARCHAR(50) NOT NULL COMMENT '测试项目id',
    isDeleted         bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId             VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId          VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator           VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier          VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '原始记录单测试项目表';

DROP TABLE IF EXISTS TB_Report_WorkSheetParamDefaultValue;
CREATE TABLE TB_Report_WorkSheetParamDefaultValue
(
    id               VARCHAR(50) NOT NULL COMMENT '主键',
    workSheetParamId VARCHAR(50) NOT NULL COMMENT '原始记录单参数id',
    testId           VARCHAR(50) NOT NULL COMMENT '测试项目公式id',
    defaultValue     VARCHAR(50) COMMENT '默认值',
    isDeleted        bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId            VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId         VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator          VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '原始记录单参数默认值表(目前只有数据参数有)';

DROP TABLE IF EXISTS TB_Report_TestFormula;
CREATE TABLE TB_Report_TestFormula
(
    id          VARCHAR(50)  NOT NULL COMMENT '主键',
    formulaName VARCHAR(100) NOT NULL COMMENT '公式名称',
    objectType  INT          NOT NULL COMMENT '对象类型，枚举管理',
    objectId    VARCHAR(50)  NOT NULL COMMENT '对象id',
    isEnable    BIT(1)       NOT NULL DEFAULT B'1' COMMENT '是否启用',
    orderNum    INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    isDeleted   bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId       VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId    VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator     VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier    VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式表';

DROP TABLE IF EXISTS TB_Report_TestFormulaEquation;
CREATE TABLE TB_Report_TestFormulaEquation
(
    id                   VARCHAR(50)  NOT NULL COMMENT '主键',
    testFormulaId        VARCHAR(50)  NOT NULL COMMENT '测试项目公式id',
    equationCategory     INT          NOT NULL COMMENT '方程种类，枚举管理',
    isPreJudge           BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否前置判定',
    preJudgeEquation     VARCHAR(100) COMMENT '前置判定方程',
    calculateEquation    VARCHAR(100) NOT NULL COMMENT '计算方程',
    significantDigit     INT COMMENT '有效位数',
    decimalDigit         INT COMMENT '小数位数',
    detectionValue       VARCHAR(50) COMMENT '检出限',
    detectionDisplayCode VARCHAR(50) COMMENT '小于检出限显示字典编码，字典管理',
    reviseRuleCode       VARCHAR(50) COMMENT '修约规则字典编码，字典管理',
    calculateWay         INT COMMENT '计算方式，枚举管理',
    calculatePriority    INT                   DEFAULT 0 COMMENT '计算优先级',
    calculateParamId     VARCHAR(50) COMMENT '计算参数id',
    isDeleted            bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId                VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId             VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator              VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier             VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate           DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式方程表';

DROP TABLE IF EXISTS TB_Report_TestFormula2Test;
CREATE TABLE TB_Report_TestFormula2Test
(
    id            VARCHAR(50) NOT NULL COMMENT '主键',
    testFormulaId VARCHAR(50) NOT NULL COMMENT '测试项目公式id',
    testId        VARCHAR(50) NOT NULL COMMENT '测试项目id',
    isDeleted     bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId         VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId      VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator       VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier      VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式和测试项目关联表';

DROP TABLE IF EXISTS TB_Report_TestFormulaEquationParam;
CREATE TABLE TB_Report_TestFormulaEquationParam
(
    id               VARCHAR(50)  NOT NULL COMMENT '主键',
    testFormulaId    VARCHAR(50)  NOT NULL COMMENT '测试项目公式id',
    paramCategory    INT          NOT NULL COMMENT '参数种类，枚举管理，如表头参数',
    paramId          VARCHAR(50)  NOT NULL COMMENT '参数id',
    paramName        VARCHAR(50)  NOT NULL COMMENT '参数名称',
    aliasName        VARCHAR(100) NOT NULL COMMENT '参数别名',
    controlType      INT          NOT NULL COMMENT '控件类型，枚举管理',
    isMandatory      BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否必填',
    isCert           BIT(1)       NOT NULL DEFAULT B'0' COMMENT '是否出证',
    dimensionId      VARCHAR(50) COMMENT '量纲id',
    dimensionName    VARCHAR(50) COMMENT '量纲名称',
    defaultValue     VARCHAR(100) COMMENT '默认值',
    significantDigit INT COMMENT '有效位数',
    decimalDigit     INT COMMENT '小数位数',
    orderNum         INT          NOT NULL DEFAULT B'0' COMMENT '排序值',
    isDisplay        BIT(1)       NOT NULL DEFAULT B'1' COMMENT '是否显示',
    reference        VARCHAR(100) COMMENT '参考文本',
    dsType           INT COMMENT '数据源类型，枚举管理，如枚举、常量、接口，当控件是下拉、选择框等需要进行配置',
    apiDsParams      VARCHAR(900) COMMENT '当dsType是接口时，该字段存储接口参数及值',
    dataSource       varchar(200) NULL DEFAULT '' COMMENT '数据源',
    isDeleted        bit(1)       NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId            VARCHAR(50)  NOT NULL COMMENT '组织机构id',
    domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室id',
    creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式方程参数表';

DROP TABLE IF EXISTS TB_Report_TestFormulaEquationParamDefaultValue;
CREATE TABLE TB_Report_TestFormulaEquationParamDefaultValue
(
    id              VARCHAR(50) NOT NULL COMMENT '主键',
    equationParamId VARCHAR(50) NOT NULL COMMENT '测试项目公式方程参数id',
    testId          VARCHAR(50) NOT NULL COMMENT '测试项目公式id',
    defaultValue    VARCHAR(50) COMMENT '默认值',
    isDeleted       bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId           VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId        VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator         VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier        VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式方程参数默认值表';

DROP TABLE IF EXISTS TB_REPORT_TestFormulaEquation2Test;
CREATE TABLE TB_REPORT_TestFormulaEquation2Test
(
    id                varchar(50) NOT NULL COMMENT 'id',
    formulaEquationId varchar(50) NOT NULL COMMENT '公式方程id',
    testId            varchar(50) NOT NULL COMMENT '测试项目id',
    dimensionId       varchar(50) NULL DEFAULT NULL COMMENT '量纲id',
    dimensionName     varchar(50) NULL DEFAULT NULL COMMENT '量纲名称',
    significantDigit  int(11) NULL DEFAULT NULL COMMENT '有效位数',
    decimalDigit      int(11) NULL DEFAULT NULL COMMENT '小数位数',
    examLimitValue    varchar(50) NULL COMMENT '检出限',
    isDeleted         bit(1)      NOT NULL DEFAULT B'0' COMMENT '是否删除',
    orgId             VARCHAR(50) NOT NULL COMMENT '组织机构id',
    domainId          VARCHAR(50) NOT NULL COMMENT '所属实验室id',
    creator           VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier          VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '测试项目公式方程参数关联测试项目配置表';


