-- 报表全局配置表添加命名规则，名称方法，下载方式字段
ALTER TABLE TB_REPORT_GlobalConfig
    ADD COLUMN namingMethod VARCHAR(255) NULL COMMENT '表单命名方法';

ALTER TABLE TB_REPORT_GlobalConfig
    ADD COLUMN nameRules VARCHAR(255) NULL COMMENT '名称规则';

ALTER TABLE TB_REPORT_GlobalConfig
    ADD COLUMN downloadMethod Int(11) NOT NULL DEFAULT 1 COMMENT '下载方式 枚举 EnumDownloadMethod 1：导出文件 2：生成文件';

-- 新增报表全局配置自定义配置表
CREATE TABLE TB_Report_CustomParamConfig
(
    id             varchar(50)  NOT NULL COMMENT '主键',
    globalConfigId varchar(50)  NOT NULL COMMENT '全局配置id',
    paramCode      varchar(255) NOT NULL COMMENT '参数编码',
    paramName      varchar(255) NULL DEFAULT NULL COMMENT '参数名称',
    paramValue     varchar(255) NULL DEFAULT NULL COMMENT '参数值',
    description    varchar(500) NULL DEFAULT NULL COMMENT '参数说明',
    orgId          varchar(50)  NOT NULL COMMENT '组织机构id',
    domainId       varchar(50)  NOT NULL COMMENT '实验室id',
    creator        varchar(50)  NOT NULL COMMENT '创建人id',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier       varchar(50)  NOT NULL COMMENT '修改人id',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

-- 区域配置表新增每页分页属性行、列数字段
ALTER TABLE TB_REPORT_AreaConfig
    ADD COLUMN pageColumnSize Int(11) NOT NULL DEFAULT 0 COMMENT '每页分页属性行、列数';

-- 区域配置表新增空白区域标识符字段
ALTER TABLE TB_REPORT_AreaConfig
    ADD COLUMN blankIdentifier VARCHAR(100) NULL DEFAULT NULL COMMENT '空白区域标识符';

-- 区域配置表新增空白区域占位符字段
ALTER TABLE TB_REPORT_AreaConfig
    ADD COLUMN emptyPlaceHolder VARCHAR(100) NULL DEFAULT NULL COMMENT '空白区域占位符';

-- 区域配置表删除合并开始位置字段
ALTER TABLE TB_REPORT_AreaConfig DROP COLUMN mergeStart;

-- 区域配置表删除合并结束位置字段
ALTER TABLE TB_REPORT_AreaConfig DROP COLUMN mergeEnd;

-- 新增报表模板sheet页配置表
CREATE TABLE TB_REPORT_SheetConfig
(
    id         varchar(50) NOT NULL COMMENT '主键',
    reportCode varchar(50) NOT NULL COMMENT '报表编码',
    sheetName  varchar(50) NULL DEFAULT NULL COMMENT 'sheet页名称',
    pageRules  int(11) NOT NULL DEFAULT 1 COMMENT '页码规则，枚举(1：保持连续 2：独立分页)',
    isDeleted  bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId      varchar(50) NOT NULL COMMENT '组织机构id',
    domainId   varchar(50) NOT NULL COMMENT '实验室id',
    creator    varchar(50) NOT NULL COMMENT '创建人id',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier   varchar(50) NOT NULL COMMENT '修改人id',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

-- 新增sheet页分页方式明细配置
CREATE TABLE TB_REPORT_SheetPagingConfig
(
    id                   varchar(50) NOT NULL COMMENT '主键',
    sheetConfigId        varchar(50) NOT NULL COMMENT 'sheet页配置id',
    dataSetApplyColumnId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '应用数据列id',
    priority             int(11) NOT NULL DEFAULT 0 COMMENT '优先级（值越大，优先级越高）',
    countPerPage         int(11) NOT NULL DEFAULT 0 COMMENT '每页数量（例如按分析项目分页，每页放3个分析项目，countPerPage = 3）',
    isDeleted            bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId                varchar(50) NOT NULL COMMENT '组织机构id',
    domainId             varchar(50) NOT NULL COMMENT '实验室id',
    creator              varchar(50) NOT NULL COMMENT '创建人id',
    createDate           datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier             varchar(50) NOT NULL COMMENT '修改人id',
    modifyDate           datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

-- 新增区域扩展配置表
CREATE TABLE TB_Report_AreaExpandConfig
(
    id           varchar(50) NOT NULL COMMENT '主键',
    areaConfigId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '区域配置id',
    qrCodeLength int(11) NULL DEFAULT NULL COMMENT '二维码长度',
    qrCodeWidth  int(11) NULL DEFAULT NULL COMMENT '二维码宽度',
    qrCodeStart  int(11) NULL DEFAULT NULL COMMENT '二维码开始位置',
    qrCodeEnd    int(11) NULL DEFAULT NULL COMMENT '二维码结束位置',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId        varchar(50) NOT NULL COMMENT '组织机构id',
    domainId     varchar(50) NOT NULL COMMENT '实验室id',
    creator      varchar(50) NOT NULL COMMENT '创建人id',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier     varchar(50) NOT NULL COMMENT '修改人id',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

-- 新增区域扩展合并配置表
CREATE TABLE TB_Report_AreaExpandMergeConfig
(
    id                 varchar(50) NOT NULL COMMENT '主键',
    areaExpandConfigId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '区域配置id',
    mergeRules         varchar(200) NULL DEFAULT NULL COMMENT '合并规则',
    startPosition      int(11) COMMENT '开始位置',
    endPosition        int(11) COMMENT '结束位置',
    isDeleted          bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId              varchar(50) NOT NULL COMMENT '组织机构id',
    domainId           varchar(50) NOT NULL COMMENT '实验室id',
    creator            varchar(50) NOT NULL COMMENT '创建人id',
    createDate         datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier           varchar(50) NOT NULL COMMENT '修改人id',
    modifyDate         datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);