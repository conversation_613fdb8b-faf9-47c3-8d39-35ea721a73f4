-- 报表全局配置自定义参数配置添加配置信息字段
ALTER TABLE TB_Report_CustomParamConfig
    ADD COLUMN configInfo VARCHAR(300) NULL COMMENT '配置信息';

ALTER TABLE TB_Report_CustomParamConfig
    ADD COLUMN isDeleted bit(1) NOT NULL DEFAULT B'0' COMMENT '是否删除' AFTER description;

-- 报表全局配置表修改命名规则，名称方法字段长度
ALTER TABLE TB_REPORT_GlobalConfig
    MODIFY COLUMN namingMethod VARCHAR(100) NULL COMMENT '表单命名方法';

ALTER TABLE TB_REPORT_GlobalConfig
    MODIFY COLUMN nameRules VARCHAR(100) NULL COMMENT '名称规则';

-- 区域配置表新增空白区域模板占位符字段
ALTER TABLE TB_Report_AreaConfig
    ADD COLUMN emptyTemplatePlaceHolder VARCHAR(100) NULL DEFAULT NULL COMMENT '空白区域模板占位符';

-- sheet页配置增加每页行数，每页列数字段
ALTER TABLE TB_REPORT_SheetConfig
    ADD COLUMN sheetRows Int(11) NOT NULL DEFAULT 1 COMMENT 'sheet页行数';

ALTER TABLE TB_REPORT_SheetConfig
    ADD COLUMN sheetColumns Int(11) NOT NULL DEFAULT 1 COMMENT 'sheet页列数';

-- 区域扩展配置增加二维码顶部间距，二维码左间距, 二维码大小字段
ALTER TABLE TB_REPORT_AreaExpandConfig
    ADD COLUMN picTop Int(11) NOT NULL DEFAULT 0 COMMENT '二维码顶部间距';

ALTER TABLE TB_REPORT_AreaExpandConfig
    ADD COLUMN picLeft Int(11) NOT NULL DEFAULT 0 COMMENT '二维码左间距';

ALTER TABLE TB_REPORT_AreaExpandConfig
    ADD COLUMN picSize Int(11) NOT NULL DEFAULT 0 COMMENT '二维码大小';