-- 报表全局配置自定义配置数据初始化
delete
from tb_report_customparamconfig
where id in ('01594cd9-c621-4c4a-a40e-005771b8245d', '2ae5fa4b-5c03-468c-a368-cf6606db93w5',
             '3dc25c66-7861-4659-bf26-7988fa6f521f',
             '84493df8-6c80-48e7-87a3-5160070be94t', 'a3c8c9ab-6bb2-43ab-babf-9a14e1d22d78');

INSERT INTO tb_report_customparamconfig(id, globalConfigId, paramCode, paramName, paramValue, description, isDeleted,
                                        orgId, domainId, creator, createDate, modifier, modifyDate, configInfo)
VALUES ('01594cd9-c621-4c4a-a40e-005771b8245d', '', 'AreaDataMainSubConfig', '区域数据主从配置', 'DefaultMainSubConfig', '',
        b'0', '247f5a8191ee4b57bc8ae53d31cff545', '247f5a8191ee4b57bc8ae53d31cff545',
        '60c6bc21cecd493ca05273f2141f4387', '2024-10-15 08:45:15', '60c6bc21cecd493ca05273f2141f4387',
        '2024-10-15 10:07:05', '');

INSERT INTO tb_report_customparamconfig(id, globalConfigId, paramCode, paramName, paramValue, description, isDeleted,
                                        orgId, domainId, creator, createDate, modifier, modifyDate, configInfo)
VALUES ('2ae5fa4b-5c03-468c-a368-cf6606db93w5', '', 'DataSortRules', '数据排序规则', 'DefaultSort', '', b'0',
        '247f5a8191ee4b57bc8ae53d31cff545', '247f5a8191ee4b57bc8ae53d31cff545', '60c6bc21cecd493ca05273f2141f4387',
        '2024-10-15 08:45:15', '60c6bc21cecd493ca05273f2141f4387', '2024-10-15 10:07:05', '');

INSERT INTO tb_report_customparamconfig(id, globalConfigId, paramCode, paramName, paramValue, description, isDeleted,
                                        orgId, domainId, creator, createDate, modifier, modifyDate, configInfo)
VALUES ('3dc25c66-7861-4659-bf26-7988fa6f521f', '', 'DataSourceInd', '报表数据源个性化', 'AnalyzeItemDataInd',
        '按照不同分析项目个性化处理数据', b'0', '247f5a8191ee4b57bc8ae53d31cff545', '247f5a8191ee4b57bc8ae53d31cff545',
        '60c6bc21cecd493ca05273f2141f4387', '2024-09-29 10:50:47', '60c6bc21cecd493ca05273f2141f4387',
        '2024-10-15 08:34:35',
        ' {\"areaType\":\"Data\", \"analyzeItemName\":\"氨氮\", \"sourcePlaceHolder\":\"定容体积\", \"sourceParamFlag\":true, \"targetPlaceHolder\":\"检出浓度\", \"targetParamFlag\": true}');

INSERT INTO tb_report_customparamconfig(id, globalConfigId, paramCode, paramName, paramValue, description, isDeleted,
                                        orgId, domainId, creator, createDate, modifier, modifyDate, configInfo)
VALUES ('84493df8-6c80-48e7-87a3-5160070be94t', '', 'QCOutParallel', '质控页是否显示现场平行', '0', '', b'0',
        '247f5a8191ee4b57bc8ae53d31cff545', '247f5a8191ee4b57bc8ae53d31cff545', '60c6bc21cecd493ca05273f2141f4387',
        '2024-10-15 11:08:07', '60c6bc21cecd493ca05273f2141f4387', '2024-10-15 11:08:07', '');

INSERT INTO tb_report_customparamconfig(id, globalConfigId, paramCode, paramName, paramValue, description, isDeleted,
                                        orgId, domainId, creator, createDate, modifier, modifyDate, configInfo)
VALUES ('a3c8c9ab-6bb2-43ab-babf-9a14e1d22d78', '', 'DataSourceInd', '报表数据源个性化', 'AreaDataInd', '按照不同质控类型个性化处理数据', b'0',
        '247f5a8191ee4b57bc8ae53d31cff545', '247f5a8191ee4b57bc8ae53d31cff545', '60c6bc21cecd493ca05273f2141f4387',
        '2024-09-29 10:50:47', '60c6bc21cecd493ca05273f2141f4387', '2024-10-15 08:34:35',
        '{\"areaType\":\"Data\", \"placeHolder\":\"!进样浓度\", \"value\":\"//\", \"qcType\":2, \"qcGrade\":2}');
