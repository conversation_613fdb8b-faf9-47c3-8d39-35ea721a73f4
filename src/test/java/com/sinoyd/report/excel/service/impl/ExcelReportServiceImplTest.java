package com.sinoyd.report.excel.service.impl;

import com.sinoyd.TestAppRun;
import com.sinoyd.report.enums.EnumReport;
import com.sinoyd.report.excel.service.ExcelExportService;
import com.sinoyd.report.excel.vo.ExcelFixedConfigVO;
import com.sinoyd.report.excel.vo.ExcelMergeAreaVO;
import com.sinoyd.report.excel.vo.ExcelParamVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = TestAppRun.class)
public class ExcelReportServiceImplTest {

    @Autowired
    private ExcelExportService excelExportService;

    /**
     * 测试所有情况
     */
    @Test
    void test() {
        ExcelParamVO paramVO = new ExcelParamVO();
        paramVO.setIsStatistics(true);
        paramVO.setSheetIndex(0);
        paramVO.setFileName("测试.xls");
        paramVO.setTemplatePath(this.getClass().getResource("/template").getPath() + "/测试模板.xls");
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Map<String, Object>> expandList = new ArrayList<>();
        for (int i = 1; i < 20; i++) {
            Map<String, Object> map = new HashMap<>();
            //测试字体颜色字体大小
//            map.put("waterCode", CellValueStyleBuilder.init("AAA").setColor("#FF9B00").setFontSize(25));
            map.put("waterCode", "<span style='color:#FF9B00;font-size:25pt;'>Hello</span><span style='color:green;font-size:14pt;'> World</span>");
            map.put("waterName", "水体sss" + i);
            map.put("code", i);
            map.put("redFolderName", i);
            map.put("11", 1.12 * i);
            map.put("22", 2.22 / i);
            map.put("33", 3.33 + i);
            mapList.add(map);
        }
        for (int j = 1; j <= 3; j++) {
            Map<String, Object> extend = new HashMap<>();
            extend.put("redAnalyzeItemName", "测试项目" + j);
            extend.put("dim", "cm" + j);
            extend.put("testId", (11 * j) + "");
            expandList.add(extend);
        }
        ExcelFixedConfigVO vo = new ExcelFixedConfigVO();
        vo.setStartRow(0);
        vo.setEndRow(1);
        vo.setStartCol(0);
        vo.setEndCol(5);
        Map<String, Object> map = new HashMap<>();
        map.put("A", 12);
        map.put("B", 12);
        map.put("C", 14);
        map.put("D", 15);
        map.put("E", 16);
        vo.setDataMaps(map);
        List<ExcelMergeAreaVO> mergeAreas = new ArrayList<>();
        mergeAreas.add(new ExcelMergeAreaVO(4, 0,
                ExcelMergeAreaVO.MERGE_CELL_DEFAULT_VAL,
                ExcelMergeAreaVO.MERGE_COLUMNS_DEFAULT_VAL,
                3, EnumReport.EnumMergeModel.Row, true));
        mergeAreas.add(new ExcelMergeAreaVO(5, 2,
                ExcelMergeAreaVO.MERGE_CELL_DEFAULT_VAL,
                2,
                ExcelMergeAreaVO.MERGE_COLUMNS_DEFAULT_VAL, EnumReport.EnumMergeModel.Cell, true));
        mergeAreas.add(new ExcelMergeAreaVO(2, 4,
                ExcelMergeAreaVO.MERGE_CELL_DEFAULT_VAL,
                2,
                ExcelMergeAreaVO.MERGE_COLUMNS_DEFAULT_VAL, EnumReport.EnumMergeModel.Cell, true));
        paramVO.setExcelMergeAreas(mergeAreas);
        paramVO.getExcelFixedConfigList().add(vo);
        paramVO.setMapList(mapList);
        paramVO.setExpandList(expandList);
        OutputStream os = null;
        try {
//            os = new FileOutputStream("E:/LimsSinoydFrame/outputs/testStreamDoc.xlsx");
            URL resource = getClass().getResource("/outputs/testStreamDoc.xlsx");
            if (resource != null){
                os = new FileOutputStream(resource.getPath()) ;
                excelExportService.generateStream(paramVO, os);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 测试表头合并情况
     */
    @Test
    void test2() {
        ExcelParamVO paramVO = new ExcelParamVO();
        paramVO.setSheetIndex(0);
        paramVO.setIsStatistics(true);
        paramVO.setFileName("单站统计报表.xls");
//        paramVO.setTemplatePath(this.getClass().getResource("/template").getPath() + "/单站统计报表.xlsx");
        paramVO.setRelativeTemplatePath("/template/单站统计报表.xlsx");
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Map<String, Object>> expandList = new ArrayList<>();
        for (int i = 1; i < 20; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("time", "2023-10-10");
            map.put("11", 1.12 * i);
            map.put("22", 2.22 / i);
            map.put("33", 3.33 + i);
            map.put("44", 4 + i);
            map.put("55", 5 + i);
            map.put("66", 6 + i);
            map.put("77", 7 + i);
            mapList.add(map);
        }
        for (int j = 1; j <= 7; j++) {
            Map<String, Object> extend = new HashMap<>();
            if (j < 3) {
                extend.put("name", "测试项目111");
            } else if (j == 3) {
                extend.put("name", "测试项目222");
            } else {
                extend.put("name", "测试项目333");
            }
            extend.put("unit", "cm" + j);
            extend.put("data", (11 * j) + "");
            expandList.add(extend);
        }
        ExcelFixedConfigVO vo = new ExcelFixedConfigVO();
        vo.setStartRow(0);
        vo.setEndRow(1);
        vo.setStartCol(0);
        vo.setEndCol(3);
        Map<String, Object> map = new HashMap<>();
        map.put("portName", "废气监测点A01统计报表");
        map.put("date", "2023-10-10");
        vo.setDataMaps(map);
        List<ExcelMergeAreaVO> mergeAreas = new ArrayList<>();
//        mergeAreas.add(new ExcelMergeAreaVO(2, 1,
//                ExcelMergeAreaVO.MERGE_CELL_DEFAULT_VAL,
//                2,
//                ExcelMergeAreaVO.MERGE_COLUMNS_DEFAULT_VAL, EnumReport.EnumMergeModel.Cell, false));
        mergeAreas.add(new ExcelMergeAreaVO(2, 1,
                ExcelMergeAreaVO.MERGE_CELL_DEFAULT_VAL,
                4,
                ExcelMergeAreaVO.MERGE_COLUMNS_DEFAULT_VAL, EnumReport.EnumMergeModel.Cell, false));
        paramVO.setExcelMergeAreas(mergeAreas);
        paramVO.getExcelFixedConfigList().add(vo);
        paramVO.setMapList(mapList);
        paramVO.setExpandList(expandList);
        OutputStream os = null;
        try {
            URL resource = getClass().getResource("/outputs/测试.xls");

            if (resource != null){
                os = new FileOutputStream(URLDecoder.decode(resource.getPath(), "UTF-8")) ;
                excelExportService.generateStream(paramVO, os);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试不同表头情况
     */
    @Test
    void test3() {
        ExcelParamVO paramVO = new ExcelParamVO();
        paramVO.setIsStatistics(false);
        paramVO.setSheetIndex(0);
        paramVO.setFileName("测试3.xlsx");
        paramVO.setTemplatePath(this.getClass().getResource("/template").getPath() + "/测试3.xlsx");
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Map<String, Object>> expandList = new ArrayList<>();
        for (int i = 1; i < 40; i++) {
            Map<String, Object> map = new HashMap<>();
            if (i%10 == 0) {
                map.put("2023年10月", (2023 - i) + "年10月");
                for (int j = 1; j <= 31; j++) {
                    map.put(j + "日", j + "日");
                }
            } else {
                map.put("2023年10月", "地名" + i);
                for (int j = 1; j <= 31; j++) {
                    map.put(j + "日", j * 2);
                }
            }

            mapList.add(map);
        }
        for (int j = 0; j <= 31; j++) {
            Map<String, Object> extend = new HashMap<>();
            if (j == 0) {
                extend.put("head", "2023年10月");
                extend.put("data", "2023年10月");
            } else {
                extend.put("head", j + "日");
                extend.put("data", j + "日");
            }

            expandList.add(extend);
        }
        paramVO.setMapList(mapList);
        paramVO.setExpandList(expandList);
        OutputStream os = null;
        try {
            URL resource = getClass().getResource("/outputs/测试3.xlsx");
            if (resource != null){
                os = new FileOutputStream(resource.getPath()) ;
                excelExportService.generateStream(paramVO, os);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void test4() {
        ExcelParamVO paramVO = new ExcelParamVO();
        paramVO.setSheetIndex(0);
        paramVO.setIsMultiData(true);
        paramVO.setFileName("在线监测数据审核报告.xls");
        paramVO.setRelativeTemplatePath("/template/在线监测数据审核报告.xls");
        List<List<Map<String, Object>>> multiDataList = new ArrayList<>();
        ExcelFixedConfigVO vo = new ExcelFixedConfigVO();
        vo.setStartRow(0);
        vo.setEndRow(3);
        vo.setStartCol(0);
        vo.setEndCol(6);
        Map<String, Object> map = new HashMap<>();
        map.put("ReportCode", "20240131JC");
        map.put("Title", "环境报告");
        map.put("PsCode", "A001");
        map.put("PortCode", "JiangSu");
        map.put("PsName", "张家港");
        vo.setDataMaps(map);
        paramVO.getExcelFixedConfigList().add(vo);

        ExcelFixedConfigVO vo1 = new ExcelFixedConfigVO();
        vo1.setStartRow(25);
        vo1.setEndRow(25);
        vo1.setStartCol(2);
        vo1.setEndCol(5);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("Conclusion", "非常棒");
        vo1.setDataMaps(map1);
        paramVO.getExcelFixedConfigList().add(vo1);

        List<Map<String, Object>> dataList1 = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            Map<String, Object> data = new HashMap<>();
            data.put("tstamp", "2024-" + i + "-" + (i * 5));
            data.put("FactorName", "钢厂" + i);
            data.put("monitorValue", i);
            data.put("monitorMultiple", i * 1.1);
            data.put("OutputName", "监测仪器" + i);
            dataList1.add(data);
        }

        List<Map<String, Object>> dataList2 = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Map<String, Object> data = new HashMap<>();
            data.put("FactorName", "化工厂" + i);
            data.put("OverProofValue", i * 1.2);
            data.put("OverProofMultiple", i + 1);
            data.put("OutputName", "现场仪器" + i);
            dataList2.add(data);
        }


        multiDataList.add(dataList1);
        multiDataList.add(dataList2);
        paramVO.setMultiDataList(multiDataList);

        OutputStream os = null;
        try {
            URL resource = getClass().getResource("/outputs/测试3.xlsx");
            if (resource != null){
                os = new FileOutputStream(resource.getPath()) ;
                excelExportService.generateStream(paramVO, os);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }

    }

}
