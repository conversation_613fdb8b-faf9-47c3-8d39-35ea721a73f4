package com.sinoyd.report.word.service.impl;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.word.chart.datatype.BarChartData;
import com.sinoyd.report.word.chart.datatype.ChartData;
import com.sinoyd.report.word.chart.datatype.LineChartData;
import com.sinoyd.report.word.chart.datatype.PieChartData;
import com.sinoyd.report.word.chart.enums.EnumChartType;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 测试类【全部情况测试】
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/26
 */
@SpringBootTest(classes = TestAppRun.class)
@SuppressWarnings({"rawtypes"})
public class AllSituationTest {

    WordReportService wordReportService;

    /**
     * VO传参测试
     */
    @Test
    void generate() {
        WordParamVO params = getParams();
        wordReportService.generate(params, null);
    }

    /**
     * 处理报告生成数据传输VO
     *
     * @return 报告生成数据传输VO
     */
    private WordParamVO getParams() {
        //添加全文档Basic数据
        WordParamVO paramVO = new WordParamVO();
        writeBasic(paramVO);
        //添加没有拓展的表数据
        Map<String, DataTableVO> dataMap = new HashMap<>();
        //处理没有关联表的测试数据
        writeNoRelationTB(dataMap);
        //处理有关联表的测试数据
        writeRelationTB(dataMap);
        //添加图片
        writeImageData(paramVO);
        //添加图表数据
        writeChartData(paramVO);
        paramVO.setDataMap(dataMap);
        //处理模版以及输出路径
        writePath(paramVO);
        return paramVO;
    }

    /**
     * 测试柱状图数据
     *
     * @param paramVO 报告生成数据传输VO
     */
    private void writeChartData(WordParamVO paramVO){
        //柱状图数据
        List<ChartVO> chartList = new ArrayList<>();
        writeBarChart(chartList);
        writeLineChart(chartList);
        writePieChart(chartList);
        paramVO.setChartList(chartList);
    }

    /**
     * 添加饼图测试数据
     *
     * @param chartList 图表数据集合
     */
    private void writePieChart(List<ChartVO> chartList){
        ChartVO<PieChartData> chartVO = new ChartVO<>();
        PieChartData pieChartData = new PieChartData();
        List<String> categories = new ArrayList<>();
        categories.add("2001");
        categories.add("2002");
        categories.add("2003");
        categories.add("2004");
        Map<String, Double> dataMap = new HashMap<>();
        dataMap.put("2001", 12.0);
        dataMap.put("2002", 34.0);
        dataMap.put("2003", 23.0);
        dataMap.put("2004", 11.0);
        pieChartData.setName(categories);
        pieChartData.setValueMap(dataMap);
        chartVO.setChartData(pieChartData);
        chartVO.setChartType(EnumChartType.饼图.getCode());
        chartVO.setBookMark("pieChart1");
        chartVO.setOutPutPicName("饼图1");
        //设置输出相对路径: 相对Resource路径
        chartVO.setOutRelativePath("/outputs/images");
        //设置输出绝对路径: 根路径，如果不设置则取Resource路径
//        chartVO.setOutAbsPath("E:/LimsSinoydFrame/outputs/images");
        chartList.add(chartVO);
    }

    /**
     * 添加柱状图测试数据
     *
     * @param chartList 图表集合
     */
    private void writeBarChart(List<ChartVO> chartList){
        ChartVO<BarChartData> chartVO = new ChartVO<>();
        BarChartData barChartData = new BarChartData();
        Vector<String> categories = new Vector<>();
        categories.add("2001");
        categories.add("2002");
        categories.add("2003");
        categories.add("2004");
        //柱状图数据1
        ChartData chartData1 = new ChartData();
        chartData1.setName("机构A");
        Map<String, Object> dataMap = new HashMap<>();
        int val = 10;
        for (String category : categories) {
            dataMap.put(category, val);
            val = val * 2 + 1;
        }
        chartData1.setDataMap(dataMap);
        //柱状图数据2
        ChartData chartData2 = new ChartData();
        chartData2.setName("机构B");
        Map<String, Object> dataMap2 = new HashMap<>();
        int val2 = 10;
        for (String category : categories) {
            dataMap2.put(category, val2);
            val2 = val2 * 3 + 1;
        }
        chartData2.setDataMap(dataMap2);
        barChartData.setCategory(categories);
        barChartData.setAxisLabel("测试柱状图标题");
        List<ChartData> chartDataList = new ArrayList<>();
        chartDataList.add(chartData1);
        chartDataList.add(chartData2);
        barChartData.setChartData(chartDataList);
        //返回图表VO
        chartVO.setChartType(EnumChartType.柱状图.getCode());
        chartVO.setBookMark("barChart1");
        chartVO.setChartData(barChartData);
        chartVO.setOutPutPicName("柱状图1");
        //设置输出相对路径: 相对Resource路径
        chartVO.setOutRelativePath("/outputs/images");
        //设置输出绝对路径: 根路径，如果不设置则取Resource路径
//        chartVO.setOutAbsPath("E:/LimsSinoydFrame/outputs/images");
        chartList.add(chartVO);
    }

    /**
     * 添加折线图测试数据
     *
     * @param chartList 图表集合
     */
    private void writeLineChart(List<ChartVO> chartList){
        ChartVO<LineChartData> chartVO = new ChartVO<>();
        LineChartData lineChartData = new LineChartData();
        Vector<String> categories = new Vector<>();
        categories.add("1");
        categories.add("2");
        categories.add("3");
        categories.add("4");
        //柱状图数据1
        ChartData chartData1 = new ChartData();
        chartData1.setName("机构A");
        Map<String, Object> dataMap = new HashMap<>();
        int val = 10;
        for (String category : categories) {
            dataMap.put(category, val);
            val = val * 2 + 1;
        }
        chartData1.setDataMap(dataMap);
        //柱状图数据2
        ChartData chartData2 = new ChartData();
        chartData2.setName("机构B");
        Map<String, Object> dataMap2 = new HashMap<>();
        int val2 = 10;
        for (String category : categories) {
            dataMap2.put(category, val2);
            val2 = val2 * 2 + 6;
        }
        chartData2.setDataMap(dataMap2);
        lineChartData.setCategory(categories);
        List<ChartData> chartDataList = new ArrayList<>();
        chartDataList.add(chartData1);
        chartDataList.add(chartData2);
        lineChartData.setChartData(chartDataList);
        //返回图表VO
        chartVO.setChartType(EnumChartType.折线图.getCode());
        chartVO.setBookMark("lineChart1");
        chartVO.setChartData(lineChartData);
        chartVO.setOutPutPicName("折线图1");
        //设置输出相对路径: 相对Resource路径
        chartVO.setOutRelativePath("/outputs/images");
        //设置输出绝对路径: 根路径，如果不设置则取Resource路径
//        chartVO.setOutAbsPath("E:/LimsSinoydFrame/outputs/images");
        chartList.add(chartVO);
    }

    /**
     * 添加图片数据
     *
     * @param paramVO 报告生成数据传输VO
     */
    private void writeImageData(WordParamVO paramVO) {
        List<ImageVO> imageVOList = new ArrayList<>();
        ImageVO imageVO = new ImageVO();
        imageVO.setPosition(ImageVO.POSITION_CENTER);
        imageVO.setImageMark("imageMark1");
        imageVO.setImgRelativePath("/images/1.png");
        //设置图片根路径，如果不设置值，则取Resource路径
//        imageVO.setImageAbsPath("C:/Users/<USER>/Desktop/img/1.png");
        imageVOList.add(imageVO);
        ImageVO imageVO2 = new ImageVO();
        imageVO2.setPosition(ImageVO.POSITION_CENTER);
        imageVO2.setImageMark("imageMark1");
        imageVO2.setImgRelativePath("/images/2.png");
        //设置图片根路径，如果不设置值，则取Resource路径
//        imageVO2.setImageAbsPath("C:/Users/<USER>/Desktop/img/2.png");
        imageVOList.add(imageVO2);
        paramVO.setImageList(imageVOList);
    }

    /**
     * 处理全文档表的基本数据的测试数据
     *
     * @param paramVO 报告生成输出传输VO
     */
    private void writeBasic(WordParamVO paramVO) {
        Map<String, Object> basicMap = new HashMap<>();
        basicMap.put("ReportCode", "BG0092910");
        basicMap.put("ProjectName", "测试生成项目1");
        basicMap.put("SampleType", "测试检测类型");
        paramVO.setBasicMap(basicMap);
    }

    /**
     * 处理无关联表的测试数据
     *
     * @param dataMap 数据集中的表
     */
    private void writeNoRelationTB(Map<String, DataTableVO> dataMap) {
        DataTableVO tableVO = new DataTableVO();
        DataTable tb = new DataTable();
        tb.setTableName("dtTest");
        //添加第一行数据
        DataRow row = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                ReportDataUtil.addTableColumn(tb, "Test" + i + j);
                row.set("Test" + i + j, CellValueStyleBuilder.init("").appendSci("1.5E+3", "#C62323").getHtmlVal());
            }
        }
        tb.getRows().add(row);
        //添加第二行数据
        DataRow row2 = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                row2.set("Test" + i + j, "第二行数据值"+ i + j);
            }
        }
        tb.getRows().add(row2);
        tableVO.setTable(tb);
        MergeAreaVO mergeArea = new MergeAreaVO();
        mergeArea.setBookMark("dtTestMark");
        mergeArea.setTableIdx(1);
        //测试列合并
        MergeAreaVO.MergeRowArea rowArea = new MergeAreaVO.MergeRowArea();
        rowArea.setStartRowNum(1);
        rowArea.setEndRowNum(2);
        rowArea.setColumnIndex(0);
        mergeArea.setRowMergeAreas(Stream.of(rowArea).collect(Collectors.toList()));
        //测试行合并
        MergeAreaVO.MergeColumnArea colArea = new MergeAreaVO.MergeColumnArea();
        colArea.setStartColNum(1);
        colArea.setEndColNum(2);
        colArea.setRowIndex(1);
        mergeArea.setColMergeAreas(Stream.of(colArea).collect(Collectors.toList()));
        tableVO.setMergeAreaList(Stream.of(mergeArea).collect(Collectors.toList()));
        dataMap.put("dtTest", tableVO);
    }

    /**
     * 处理有关联表的测试数据
     *
     * @param dataMap 数据集中的表
     */
    private void writeRelationTB(Map<String, DataTableVO> dataMap) {
        //关联表主表数据
        DataTableVO mainTable = new DataTableVO(new DataTable());
        mainTable.getTable().setTableName("dtTech");
        ReportDataUtil.addTableColumn(mainTable.getTable(), "Id");
        //关联表次表数据
        DataTableVO secondaryTable = new DataTableVO(new DataTable());
        secondaryTable.getTable().setTableName("dtTestInst");
        List<String> sonColumns = Arrays.asList("Id", "TestName", "AnaMethodName", "ExamLimit", "Dimension", "InstrumentSerialNo");
        sonColumns.forEach(p -> ReportDataUtil.addTableColumn(secondaryTable.getTable(), p));

        //处理关联Id为1的数据
        for (int i = 0; i < 5; i++) {
            DataRow tbRow = secondaryTable.getTable().newRow();
            tbRow.set("Id", 1);
            tbRow.set("TestName", "测试项目名称" + i);
            //颜色设置为橙色
            tbRow.set("AnaMethodName", CellValueStyleBuilder.init("分析方法名称" + i).setColor("#FB6D12").getHtmlVal());
            tbRow.set("ExamLimit", "检出限" + i);
            tbRow.set("Dimension", "量纲" + i);
            tbRow.set("InstrumentSerialNo", "仪器编号" + i);
            secondaryTable.getTable().getRows().add(tbRow);
        }
        //处理关联Id为2的数据
        for (int i = 0; i < 5; i++) {
            DataRow tbRow = secondaryTable.getTable().newRow();
            tbRow.set("Id", 2);
            tbRow.set("TestName", "测试项目名称" + i);
            //测试字体颜色(淡蓝色)
            tbRow.set("AnaMethodName", CellValueStyleBuilder.init("分析方法名称" + i).setColor("#00FFFF").getHtmlVal());
            tbRow.set("ExamLimit", "检出限" + i);
            //测试下标（绿色）
            tbRow.set("Dimension", CellValueStyleBuilder.init("量纲").setColor("#00FF00").appendSub(String.valueOf(i)).getHtmlVal());
            //测试上标
            tbRow.set("InstrumentSerialNo", CellValueStyleBuilder.init("仪器编号")
                    .appendSizeFont("放大", 40)
                    .appendSup(String.valueOf(i),"#00FFFF")
                    .getHtmlVal());
            secondaryTable.getTable().getRows().add(tbRow);
        }
        DataRow tbFRow = mainTable.getTable().newRow();
        tbFRow.set("Id", 1);
        mainTable.getTable().getRows().add(tbFRow);
        DataRow tbFRow2 = mainTable.getTable().newRow();
        tbFRow2.set("Id", 2);
        mainTable.getTable().getRows().add(tbFRow2);
        //设置表关系VO
        secondaryTable.setTable(secondaryTable.getTable());
        mainTable.setSecondaryTable(secondaryTable.getTable());
        mainTable.setSecondaryField("Id");
        mainTable.setMainAssociationField("Id");
        dataMap.put("dtTech", mainTable);
//        dataMap.put("dtTestInst", secondaryTable);
    }


    /**
     * 处理文件路径
     *
     * @param paramVO 报告生成输出传输VO
     */
    private void writePath(WordParamVO paramVO) {
        paramVO.setFileName("所有内容测试.docx");
        paramVO.setRelativePath("/template/所有内容测试模板.docx");
        paramVO.setOutRelativePath("/outputs/");
        //根路径设置
//        paramVO.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
//        paramVO.setTemplateAbsPath("E:/LimsSinoydFrame/template/所有内容测试模板.docx");
    }


    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}