package com.sinoyd.report.word.service.impl;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.word.builder.MergeAreaBuilder;
import com.sinoyd.report.word.builder.WordParamBuilder;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.MergeAreaVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 参数构造器测试类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/10/10
 */
@SpringBootTest(classes = TestAppRun.class)
public class BuilderTest {

    private WordReportService wordReportService;

    @Test
    void paramsVOBuilderTest(){
//        WordParamBuilder builder = WordParamBuilder
//                .init("E:/LimsSinoydFrame/template/基础数据测试模板.docx",
//                        true,
//                        "E:/LimsSinoydFrame/outputs/",
//                        true,"构造器参数基础数据测试.docx");
        WordParamBuilder builder = WordParamBuilder
                .init("/template/基础数据测试模板.docx",
                        false,
                        "/outputs/",
                        false,"构造器参数基础数据测试.docx");
        Map<String,Object> basicMap = new HashMap<>();
        basicMap.put("ReportCode", "RG230504");
        basicMap.put("SampleType", "地表水");
        basicMap.put("ProjectName", "项目1");
        //测试颜色标红
        basicMap.put("val1", CellValueStyleBuilder.init("测试数据1").setColor("#EA2020").getHtmlVal());
        //测试拼接上下标数据
        basicMap.put("val2", CellValueStyleBuilder.init().appendCheckBox("是", true).setFontSize(22).getHtmlVal());
        //测试拼接科学计数法处理
        basicMap.put("val3", "是☑");
        //测试拼接加粗以及特殊大小字体文本
        basicMap.put("val4", "否□");
        builder.setBasicMap(basicMap);
        wordReportService.generate(builder.getInstance(), null);
    }

    /**
     * 测试区域合并构造器使用
     */
    @Test
    void mergeAreaBuilderTest(){
//        WordParamBuilder builder = WordParamBuilder
//                .init("E:/LimsSinoydFrame/template/表格外行拓展测试模板.docx",
//                        true,
//                        "E:/LimsSinoydFrame/outputs/",
//                        true,"构造器测试文件.docx");
        WordParamBuilder builder = WordParamBuilder
                .init("/template/表格外行拓展测试模板.docx",
                        false,
                        "/outputs/",
                        false,"构造器测试文件.docx");
        Map<String, DataTableVO> dataMap = new HashMap<>();
        DataTableVO tableVO = new DataTableVO();
        DataTable tb = new DataTable();
        tb.setTableName("dtTest");
        //添加第一行数据
        DataRow row = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                ReportDataUtil.addTableColumn(tb, "Test" + i + j);
                //标红
                row.set("Test" + i + j, CellValueStyleBuilder.init("").appendSci("1.5E+3", "#C62323").getHtmlVal());
            }
        }
        tb.getRows().add(row);
        //添加第二行数据
        DataRow row2 = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                row2.set("Test" + i + j, "第二行数据值"+ i + j);
            }
        }
        tb.getRows().add(row2);
        tableVO.setTable(tb);
        tableVO.setMergeAreaList(builderMergeArea());
        dataMap.put("dtTest", tableVO);
        builder.setDataMap(dataMap);
        wordReportService.generate(builder.getInstance(), null);
    }

    /**
     * 构造合并区域集合
     *
     * @return 合并区域集合
     */
    private List<MergeAreaVO> builderMergeArea(){
        List<MergeAreaVO> mergeAreaVOList = new ArrayList<>();
        MergeAreaBuilder builder = MergeAreaBuilder.init(1,"dtTestMark");
        builder.addRowMergeArea(1,2,Stream.of(0,1).collect(Collectors.toList()));
        builder.addColMergeArea(0,1,3);
        mergeAreaVOList.add(builder.getInstance());
        return mergeAreaVOList;
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
