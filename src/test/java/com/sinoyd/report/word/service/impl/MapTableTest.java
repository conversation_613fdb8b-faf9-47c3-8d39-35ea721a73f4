package com.sinoyd.report.word.service.impl;

import com.sinoyd.TestAppRun;
import com.sinoyd.report.word.builder.DataTableBuilder;
import com.sinoyd.report.word.builder.WordParamBuilder;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.vo.DataTableVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ListMap形式数据表单生成测试类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/10/19
 */
@SpringBootTest(classes = TestAppRun.class)
public class MapTableTest {

    private WordReportService wordReportService;

    @Test
    public void MapTable() {
        WordParamBuilder builder = WordParamBuilder
                .init("/template/字典集合数据表类型测试模板.docx",
                        false,
                        "/outputs/",
                        false,"字典集合数据表类型测试.docx");

//        WordParamBuilder builder = WordParamBuilder
//                .init("E:/LimsSinoydFrame/template/字典集合数据表类型测试模板.docx",
//                        true,
//                        "E:/LimsSinoydFrame/outputs/",
//                        true, "字典集合数据表类型测试.docx");

        Map<String, DataTableVO> dataMap = new HashMap<>();
        //处理测试数据
        writeDataMap(dataMap);
        builder.setDataMap(dataMap);
        wordReportService.generate(builder.getInstance(), null);
    }

    /**
     * 添加表数据
     *
     * @param dataMap 表数据字典
     */
    private void writeDataMap(Map<String, DataTableVO> dataMap) {
        //测试分页数据表
        List<Map<String, Object>> dtTestMapList = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            Map<String, Object> row = new HashMap<>();
            for (int j = 0; j < 3; j++) {
                String key = "Test" + j;
                row.put(key, "测试值" + j);
            }
            dtTestMapList.add(row);
        }
        DataTableVO dtTest = DataTableBuilder.initMapTable("dtTest", dtTestMapList).getVOInstance();
        dataMap.put("dtTest", dtTest);
        //测试行拓展数据表
        List<Map<String, Object>> dtTestTwoMapList = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            Map<String, Object> row = new HashMap<>();
            for (int j = 0; j < 3; j++) {
                String key = "Test" + j;
                row.put(key, "测试值" + j);
            }
            dtTestTwoMapList.add(row);
        }
        DataTableVO dtTestTwo = DataTableBuilder.initMapTable("dtTestTwo", dtTestTwoMapList).getVOInstance();
        dataMap.put("dtTestTwo", dtTestTwo);
        //测试分组测试表
        List<Map<String, Object>> dtMainMapList = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            Map<String, Object> row = new HashMap<>();
//            row.put("AnaMethodName", "分析方法" + i);
            row.put("Id", i);
            dtMainMapList.add(row);
        }
        List<Map<String, Object>> dtRelationMapList = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            Map<String, Object> row = new HashMap<>();
            row.put("Id",1);
            row.put("TestName", "测试项目名称1");
            row.put("AnaMethodName", "分析方法1");
            row.put("ExamLimit", "检出限1");
            row.put("Dimension", "量纲1");
            row.put("InstrumentSerialNo", "仪器编号1");
            dtRelationMapList.add(row);
        }
        for (int i = 0; i < 2; i++) {
            Map<String, Object> row = new HashMap<>();
            row.put("Id",2);
            row.put("TestName", "测试项目名称2");
            row.put("AnaMethodName", "分析方法2");
            row.put("ExamLimit", "检出限2");
            row.put("Dimension", "量纲2");
            row.put("InstrumentSerialNo", "仪器编号2");
            dtRelationMapList.add(row);
        }
        DataTableVO dtMain = DataTableBuilder.initMapTable("dtMain", dtMainMapList, "Id", "dtRelation", dtRelationMapList, "Id").getVOInstance();
        dataMap.put("dtMain", dtMain);
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
