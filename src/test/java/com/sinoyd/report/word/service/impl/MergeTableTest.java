package com.sinoyd.report.word.service.impl;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.word.builder.MergeAreaBuilder;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.WordParamVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 测试类【全部情况测试】
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/07/26
 */
@SpringBootTest(classes = TestAppRun.class)
public class MergeTableTest {

    WordReportService wordReportService;

    /**
     * VO传参测试
     */
    @Test
    void generate() {
        WordParamVO params = getParams();
        wordReportService.generate(params, null);
    }

    /**
     * 处理报告生成数据传输VO
     *
     * @return 报告生成数据传输VO
     */
    private WordParamVO getParams() {
        //添加全文档Basic数据
        WordParamVO paramVO = new WordParamVO();
        writeBasic(paramVO);
        //添加没有拓展的表数据
        Map<String, DataTableVO> dataMap = new HashMap<>();
        //处理没有关联表的测试数据
        writeMergeTB(dataMap);
        //设置表数据
        paramVO.setDataMap(dataMap);
        //处理模版以及输出路径
        writePath(paramVO);
        return paramVO;
    }

    /**
     * 处理全文档表的基本数据的测试数据
     *
     * @param paramVO 报告生成输出传输VO
     */
    private void writeBasic(WordParamVO paramVO) {
        Map<String, Object> basicMap = new HashMap<>();
        basicMap.put("ReportCode", "BG0092910");
        basicMap.put("ProjectName", "测试生成项目1");
        basicMap.put("SampleType", "测试检测类型");
        paramVO.setBasicMap(basicMap);
    }

    /**
     * 处理无关联表的测试数据
     *
     * @param dataMap 数据集中的表
     */
    private void writeMergeTB(Map<String, DataTableVO> dataMap) {

        //region 表1
        DataTableVO tableVOTest1 = new DataTableVO();
        DataTable tableTest1 = new DataTable();
        tableTest1.setTableName("dtTest1");
        //添加第一行数据
        DataRow row = tableTest1.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                ReportDataUtil.addTableColumn(tableTest1, "Test" + i + j);
                row.set("Test" + i + j, CellValueStyleBuilder.init("").appendSci("1.5E+3", "#C62323").getHtmlVal());
            }
        }
        tableTest1.getRows().add(row);
        tableVOTest1.setTable(tableTest1);
        //合并区域创建
        tableVOTest1.setMergeAreaList(Stream.of(MergeAreaBuilder.init(0,"dtTest1")
                .addRowMergeArea(1,3,0)
                .getInstance()).collect(Collectors.toList()));
        dataMap.put("dtTest1", tableVOTest1);
        //endregion

        //region 表2
        //关联表次表数据
        DataTableVO tableVOTest2 = new DataTableVO(new DataTable());
        tableVOTest2.getTable().setTableName("dtTestInst");
        List<String> tb2Cols = Arrays.asList("Id", "TestName", "AnaMethodName", "ExamLimit", "Dimension", "InstrumentSerialNo");
        tb2Cols.forEach(p -> ReportDataUtil.addTableColumn(tableVOTest2.getTable(), p));

        //处理关联Id为1的数据
        for (int i = 0; i < 2; i++) {
            DataRow tbRow = tableVOTest2.getTable().newRow();
            tbRow.set("Id", 1);
            tbRow.set("TestName", "测试项目名称" + i);
            //颜色设置为橙色
            tbRow.set("AnaMethodName", "方法A");
            tbRow.set("ExamLimit", "检出限" + i);
            tbRow.set("Dimension", "量纲" + i);
            tbRow.set("InstrumentSerialNo", "仪器编号" + i);
            tableVOTest2.getTable().getRows().add(tbRow);
        }
        for (int i = 0; i < 2; i++) {
            DataRow tbRow = tableVOTest2.getTable().newRow();
            tbRow.set("Id", 1);
            tbRow.set("TestName", "测试项目名称" + i);
            //颜色设置为橙色
            tbRow.set("AnaMethodName", "方法B");
            tbRow.set("ExamLimit", "检出限" + i);
            tbRow.set("Dimension", "量纲" + i);
            tbRow.set("InstrumentSerialNo", "仪器编号" + i);
            tableVOTest2.getTable().getRows().add(tbRow);
        }
        //合并区域创建
        tableVOTest2.setMergeAreaList(Stream.of(MergeAreaBuilder.init(0,"dtTest2")
                .addRowMergeArea(1,2,1)
                .addRowMergeArea(3,4,1)
                .getInstance()).collect(Collectors.toList()));
        dataMap.put("dtTest2", tableVOTest2);
        //endregion

        //region 表3
        //关联表次表数据
        DataTableVO tableVOTest3 = new DataTableVO(new DataTable());
        tableVOTest3.getTable().setTableName("dtTest3");
        List<String> tb3Cols = Arrays.asList("Id", "Val1", "Val2", "Val3");
        tb3Cols.forEach(p -> ReportDataUtil.addTableColumn(tableVOTest3.getTable(), p));

        //处理关联Id为1的数据
        for (int i = 0; i < 5; i++) {
            DataRow tbRow = tableVOTest3.getTable().newRow();
            tbRow.set("Id", 1);
            tbRow.set("Val1", "测试项目");
            tbRow.set("Val2", "方法" + i);
            tbRow.set("Val3", "仪器编号" + i);
            tableVOTest3.getTable().getRows().add(tbRow);
        }
        //合并区域创建
        tableVOTest3.setMergeAreaList(Stream.of(MergeAreaBuilder.init(0,"dtTest3")
                .addRowMergeArea(1,5,0)
                .getInstance()).collect(Collectors.toList()));
        dataMap.put("dtTest3", tableVOTest3);
        //endregion
    }

    /**
     * 处理文件路径
     *
     * @param paramVO 报告生成输出传输VO
     */
    private void writePath(WordParamVO paramVO) {
        paramVO.setFileName("分节符合并测试.docx");
        paramVO.setRelativePath("/template/分节符合并测试模板.docx");
        paramVO.setOutRelativePath("/outputs/");
        //根路径设置
//        paramVO.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
//        paramVO.setTemplateAbsPath("E:/LimsSinoydFrame/template/分节符合并测试模板.docx");
    }


    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}