package com.sinoyd.report.word.service.impl;

import com.sinoyd.TestAppRun;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.service.init.NestingTableInit;
import com.sinoyd.report.word.service.init.ParallelTableSingleInit;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.WordParamVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 嵌套表测试类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/17
 */
@SpringBootTest(classes = TestAppRun.class)
public class NestingTableTest {

    WordReportService wordReportService;

    @Test
    public void generate() {
        wordReportService.generate(getParams(), null);
    }

    /**
     * 处理报告生成数据传输VO
     *
     * @return 报告生成数据传输VO
     */
    private WordParamVO getParams() {
        //添加全文档Basic数据
        WordParamVO paramVO = new WordParamVO();
        //添加生成所需的测试项目
        Map<String, DataTableVO> dataMap = new HashMap<>();
        //处理嵌套子表测试数据
        createNestingTB(dataMap);
        //设置测试表格数据
        paramVO.setDataMap(dataMap);
        //处理模版以及输出路径
        writePath(paramVO);
        return paramVO;
    }

    /**
     * 创建嵌套主表数据
     *
     * @param dataMap 表数据Map
     */
    private void createNestingTB(Map<String, DataTableVO> dataMap) {
        //写入并行主表【ParallelTableSingle】的测试数据
        ParallelTableSingleInit.writeParallelTableSingle(dataMap);
        //写入嵌套主表【NestingTable】的测试数据
        NestingTableInit.writeNestingTable(dataMap);
    }


    /**
     * 处理文件路径
     *
     * @param paramVO 报告生成输出传输VO
     */
    private void writePath(WordParamVO paramVO) {
        paramVO.setFileName("嵌套表格测试.docx");
        paramVO.setRelativePath("/template/嵌套表格模板.docx");
        paramVO.setOutRelativePath("/outputs/");
        //根路径设置
//        paramVO.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
//        paramVO.setTemplateAbsPath("E:/LimsSinoydFrame/template/嵌套表格模板.docx");
    }


    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
