package com.sinoyd.report.word.service.impl;

import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.vo.WordParamVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试类【只有基础数据赋值的报告】
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/23
 */
@SpringBootTest(classes = TestAppRun.class)
public class OnlyBasicReportTest {

    WordReportService wordReportService;

    @Test
    void onlyBasic(){
        WordParamVO param = new WordParamVO();
        Map<String,Object> basicMap = new HashMap<>();
        basicMap.put("ReportCode", "RG230504");
        basicMap.put("SampleType", "地表水");
        basicMap.put("ProjectName", "项目1");
        //测试颜色标红
        basicMap.put("val1", CellValueStyleBuilder.init("测试数据1").setColor("#8CBEF3").getHtmlVal());
        //测试拼接上下标数据
        basicMap.put("val2", CellValueStyleBuilder.init().appendCheckBox("是", true).setFontSize(22).getHtmlVal());
        //测试拼接科学计数法处理
//        basicMap.put("val3", CellValueStyleBuilder.init("科学计数法：").appendSci("＞1.5E+3", new Color(255, 181, 0)).getHtmlVal());
        basicMap.put("val3", "是☑");
        //测试拼接加粗以及特殊大小字体文本
        basicMap.put("val4", "否□");
        param.setBasicMap(basicMap);
        //设置输出文件名称
        param.setFileName("基础数据测试.docx");
        //设置模板路径以及临时文件数据路径【绝对路径】
//        param.setTemplateAbsPath("E:/LimsSinoydFrame/template/基础数据测试模板.docx");
//        param.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
        param.setRelativePath("/template/基础数据测试模板.docx");
        param.setOutRelativePath("/outputs");
        wordReportService.generate(param,null);

    }

    @Test
    void onlyBasicTest(){
        WordParamVO param = new WordParamVO();
        Map<String,Object> basicMap = new HashMap<>();
        //浅蓝色
        basicMap.put("tableName", CellValueStyleBuilder.init("排污单位自行监测检查内容基本表").setColor("#C62323").getHtmlVal());
        basicMap.put("psName", "单位1");
        basicMap.put("permitCode", "RG23847813");
        basicMap.put("industryTypeName", "测试行业类别");
        basicMap.put("cValue", CellValueStyleBuilder.init("对照证管理信息平台公开数据与监测报告数据进行检查判定。","楷体").setFontSize(22).getHtmlVal());
        basicMap.put("streetName", "项目1");
        basicMap.put("permitTypeId_7544b04271f36039385d9d8394d52036", CellValueStyleBuilder.init().appendCheckBox("管理类别好多好多字的这种歌管理类别1",true, "楷体").getHtmlVal());
        basicMap.put("permitTypeId_fb080daa744accfbbacd474a0573d11d", CellValueStyleBuilder.init().appendCheckBox("管理类别好多好多字的这种歌管理类别2",false, "仿宋").getHtmlVal());
        basicMap.put("permitTypeId_893af44ac10db28fefd25fdbb16d0109", CellValueStyleBuilder.init().appendCheckBox("管理类别好多好多字的这种歌管理类别3",false, "宋体").getHtmlVal());
        //玫红色
        basicMap.put("currentYeKeynote_1", CellValueStyleBuilder.init().appendCheckBox("是",true).setColor("#E500F7").getHtmlVal());
        basicMap.put("currentYeKeynote_0", CellValueStyleBuilder.init().appendCheckBox("否",false).getHtmlVal());
        basicMap.put("isOutInUserVideo_1", CellValueStyleBuilder.init().appendCheckBox("是",false).getHtmlVal());
        basicMap.put("isOutInUserVideo_0", CellValueStyleBuilder.init().appendCheckBox("否",true).getHtmlVal());
        basicMap.put("isPortUserVideo_1", CellValueStyleBuilder.init().appendCheckBox("是",false).getHtmlVal());
        basicMap.put("isPortUserVideo_0", CellValueStyleBuilder.init().appendCheckBox("否",true).getHtmlVal());
        basicMap.put("isStationUserVideo_1", CellValueStyleBuilder.init().appendCheckBox("是",false).getHtmlVal());
        basicMap.put("isStationUserVideo_0", CellValueStyleBuilder.init().appendCheckBox("否",false).getHtmlVal());
        basicMap.put("isSelfCheck_1", CellValueStyleBuilder.init().appendCheckBox("是",true).getHtmlVal());
        basicMap.put("isSelfCheck_0", CellValueStyleBuilder.init().appendCheckBox("否",false).getHtmlVal());
        basicMap.put("isEntrustThird_1", CellValueStyleBuilder.init().appendCheckBox("是",false).getHtmlVal());
        basicMap.put("isEntrustThird_0", CellValueStyleBuilder.init().appendCheckBox("否",true).getHtmlVal());

        basicMap.put("isMakePlan_1", CellValueStyleBuilder.init()
                .appendCheckBox("是", true, "仿宋").getHtmlVal());
        //可设置字体大小与字体样式，字体样式无法控制勾选框样式，【勾选框生成默认样式为】
        basicMap.put("isMakePlan_0", CellValueStyleBuilder.init()
                .appendCheckBox("否", false, "仿宋").getHtmlVal());

        basicMap.put("isContainMainPort_1", CellValueStyleBuilder.init()
                .appendCheckBox("是", true, "仿宋").getHtmlVal());
        //可设置字体大小与字体样式
        basicMap.put("isContainMainPort_0", CellValueStyleBuilder.init()
                .appendCheckBox("否", false, "仿宋").getHtmlVal());
        basicMap.put("isContainPermitPollutant_1", CellValueStyleBuilder.init()
                .appendCheckBox("是", false, "仿宋").getHtmlVal());
        basicMap.put("isContainPermitPollutant_0", CellValueStyleBuilder.init()
                .appendCheckBox("否", true, "仿宋").getHtmlVal());
        param.setBasicMap(basicMap);
        //设置输出文件名称
        param.setFileName("复杂基础数据测试.docx");
        //设置模板路径以及临时文件数据路径【相对路径路径】
        param.setRelativePath("/template/复杂基础数据测试模板.docx");
        param.setOutRelativePath("/outputs/");
        //设置模板文件以及输出文件的根路径，如果不设置，则取项目下的Resource路径
//        param.setTemplateAbsPath("E:/LimsSinoydFrame/template/复杂基础数据测试模板.docx");
//        param.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
        wordReportService.generate(param,null);

    }

    @Test
    void OnlyCheckBox(){
        WordParamVO param = new WordParamVO();
        Map<String,Object> basicMap = new HashMap<>();
        basicMap.put("isMakePlan_1", CellValueStyleBuilder.init()
                .appendCheckBox("是", false, "仿宋").getHtmlVal());
        basicMap.put("isMakePlan_0", CellValueStyleBuilder.init()
                .appendCheckBox("否", false, "仿宋").getHtmlVal());
        param.setBasicMap(basicMap);
        //设置输出文件名称
        param.setFileName("勾选框测试.docx");
        //设置模板路径以及临时文件数据路径【相对路径路径】
        param.setRelativePath("/template/勾选框测试模板.docx");
        param.setOutRelativePath("/outputs/");
        //设置模板文件以及输出文件的根路径，如果不设置，则取项目下的Resource路径
//        param.setTemplateAbsPath("E:/LimsSinoydFrame/template/勾选框测试模板.docx");
//        param.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
        wordReportService.generate(param,null);
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
