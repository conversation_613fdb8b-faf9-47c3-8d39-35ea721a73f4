package com.sinoyd.report.word.service.impl;

import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.utils.ReportFileUtil;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.vo.WordParamVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试类【测试输出流生成】
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/08/23
 */
@SpringBootTest(classes = TestAppRun.class)
@Slf4j
public class OutPutStreamTest {

    WordReportService wordReportService;

    @Test
    void testOutStream(){
        WordParamVO param = new WordParamVO();
        Map<String,Object> basicMap = new HashMap<>();
        basicMap.put("ReportCode", "RG230504");
        basicMap.put("SampleType", "地表水");
        basicMap.put("ProjectName", "项目1");
        //测试颜色标红
        basicMap.put("val1", CellValueStyleBuilder.init("测试数据1").setColor("#EA2020").getHtmlVal());
        //测试拼接上下标数据
        basicMap.put("val2", CellValueStyleBuilder.init().appendCheckBox("是", true).setFontSize(22).getHtmlVal());
        //测试拼接科学计数法处理
//        basicMap.put("val3", CellValueStyleBuilder.init("科学计数法：").appendSci("＞1.5E+3", new Color(255, 181, 0)).getHtmlVal());
        basicMap.put("val3", "是☑");
        //测试拼接加粗以及特殊大小字体文本
        basicMap.put("val4", "否□");
        param.setBasicMap(basicMap);
        //设置模板绝对路径
//        param.setTemplateAbsPath("E:/LimsSinoydFrame/template/基础数据测试模板.docx");
        //设置模板相对路径
        param.setRelativePath("/template/基础数据测试模板.docx");
        OutputStream os = null;
        try {
//            os = new FileOutputStream("E:/LimsSinoydFrame/outputs/testStreamDoc.docx");
            URL resource = getClass().getResource("/outputs/testStreamDoc.docx");
            if (resource != null){
                os = new FileOutputStream(resource.getPath()) ;
                wordReportService.generateStream(param,os);
            }
            ReportFileUtil.closeStream(os);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }finally {
            ReportFileUtil.closeStream(os);
        }
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
