package com.sinoyd.report.word.service.impl;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.TestAppRun;
import com.sinoyd.report.builder.CellValueStyleBuilder;
import com.sinoyd.report.word.service.WordReportService;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;
import com.sinoyd.report.word.vo.WordParamVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * 测试类【行拓展测试】
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/10/10
 */
@SpringBootTest(classes = TestAppRun.class)
public class RowExtendTest {

    WordReportService wordReportService;

    /**
     * 表格外行拓展测试
     */
    @Test
    void outTableRow(){
        //创建传参实体
        WordParamVO paramVO = new WordParamVO();
        Map<String, DataTableVO> dataMap = paramVO.getDataMap();
        DataTableVO tableVO = new DataTableVO();
        DataTable tb = new DataTable();
        tb.setTableName("dtTest");
        //添加第一行数据
        DataRow row = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                ReportDataUtil.addTableColumn(tb, "Test" + i + j);
                row.set("Test" + i + j, "第一行数据值" + i + j);
            }
        }
        tb.getRows().add(row);
        //添加第二行数据
        DataRow row2 = tb.newRow();
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                row2.set("Test" + i + j, "第二行数据值"+ i + j);
            }
        }
        tb.getRows().add(row2);
        tableVO.setTable(tb);
        dataMap.put("dtTest", tableVO);
        paramVO.setDataMap(dataMap);
        paramVO.setFileName("表格外行拓展测试.docx");
        paramVO.setRelativePath("/template/表格外行拓展测试模板.docx");
        paramVO.setOutRelativePath("/outputs/");
        //根路径设置
//        paramVO.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
//        paramVO.setTemplateAbsPath("E:/LimsSinoydFrame/template/表格外行拓展测试模板.docx");
        wordReportService.generate(paramVO, null);
    }

    /**
     * 表格内行拓展测试
     */
    @Test
    void inTableRow(){
        //创建传参实体
        WordParamVO paramVO = new WordParamVO();
        Map<String, DataTableVO> dataMap = paramVO.getDataMap();
        DataTableVO tableVO = new DataTableVO();
        DataTable tb = new DataTable();
        tb.setTableName("dtTest");
        //添加列数据
        ReportDataUtil.addTableColumn(tb, "Test0");
        ReportDataUtil.addTableColumn(tb, "Test1");
        ReportDataUtil.addTableColumn(tb, "Test2");
        //添加第一行数据
        DataRow row = tb.newRow();
        for (int i = 0; i < 3; i++) {
            //标红显示
            row.set("Test" + i, CellValueStyleBuilder.init("第一行数据值" + i).setColor("#FF0000").getHtmlVal());
        }
        tb.getRows().add(row);
        //添加第二行数据
        DataRow row2 = tb.newRow();
        for (int i = 0; i < 3; i++) {
            row2.set("Test" + i, "第二行数据值"+ i);
        }
        tb.getRows().add(row2);
        tableVO.setTable(tb);
        dataMap.put("dtTest", tableVO);
        paramVO.setDataMap(dataMap);
        paramVO.setFileName("表格内行拓展测试.docx");
        paramVO.setRelativePath("/template/表格内行拓展测试模板.docx");
        paramVO.setOutRelativePath("/outputs/");
        //根路径设置
//        paramVO.setOutPutAbsPath("E:/LimsSinoydFrame/outputs/");
//        paramVO.setTemplateAbsPath("E:/LimsSinoydFrame/template/表格内行拓展测试模板.docx");
        wordReportService.generate(paramVO, null);
    }

    @Autowired
    public void setWordReportService(WordReportService wordReportService) {
        this.wordReportService = wordReportService;
    }
}
