package com.sinoyd.report.word.service.init;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.report.word.builder.DataTableBuilder;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 嵌套表测试：并行表单【NestingTable】测试数据初始化类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/17
 */
public class NestingTableInit {

    /**
     * 写入嵌套表单测试数据
     *
     * @param dataMap 生成所需的表单集合
     */
    public static void writeNestingTable(Map<String, DataTableVO> dataMap) {
        String mainTableName = "NestingTable";
        //创建主表一，并行主表
        DataTableVO mainTable = DataTableBuilder.initTable(mainTableName, new DataTable()).getVOInstance();

        //子表数据
        Map<String, DataTableVO> subTable = new HashMap<>();
        //子表关联字段数据
        Map<String, String> subRelationField = new HashMap<>();

        //主表关联字段
        String mainRelationField = "主表NestingTable关联字段";
        //添加主表关联列
        ReportDataUtil.addTableColumn(mainTable.getTable(), mainRelationField);

        //设置主表关系值  主表套表1
        String mainRelationValue1 = "主表NestingTable套表1关联值";
        DataRow relationRow1 = mainTable.getTable().newRow();
        relationRow1.set(mainRelationField, mainRelationValue1);
        mainTable.getTable().getRows().add(relationRow1);

        //设置主表关系值  主表套表2
        String mainRelationValue2 = "主表NestingTable套表2关联值";
        DataRow relationRow2 = mainTable.getTable().newRow();
        relationRow2.set(mainRelationField, mainRelationValue2);
        mainTable.getTable().getRows().add(relationRow2);


        //添加【嵌套子表1（嵌套行循环生成）】
        putSubNestingTbFirst(mainRelationField, subTable, subRelationField);

        //添加【嵌套子表2（循环行生成）】
        putSubNestingTbSecond(mainRelationField, subTable, subRelationField);

        //设置嵌套子表数据
        mainTable.setSubTableMap(subTable);
        mainTable.setSubTableRelationFieldMap(subRelationField);
        //放置并行主表数据
        dataMap.put(mainTableName, mainTable);
    }


    /**
     * 写入【嵌套子表1（嵌套行循环生成）】 的测试数据
     *
     * @param mainRelationField 主表关系字段
     * @param subTable          子表数据Map
     * @param subRelationField  子表关联字段数据Map
     */
    private static void putSubNestingTbFirst(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "SubNestingTbFirst";
        //构建【嵌套子表1（嵌套行循环生成）】表数据
        DataTableVO subNestingTb1 = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();


        //嵌套子表的子表数据
        Map<String, DataTableVO> nestingSubTable = new HashMap<>();
        //嵌套子表的子表关联字段数据
        Map<String, String> nestingSubRelationField = new HashMap<>();

        //设置子嵌套表SubNestingTbFirst的表关联字段
        String nestingFistSubRelationField = "子嵌套表1SubNestingTbFirst关联字段";
        //添加主表关联列
        ReportDataUtil.addTableColumn(subNestingTb1.getTable(), mainRelationField);
        //添加子嵌套表关联列
        ReportDataUtil.addTableColumn(subNestingTb1.getTable(), nestingFistSubRelationField);

        //添加子嵌套添加行数据，增加两个关系值，代表此表中有两个套表可以下面的子表关联到
        // 固定关联主表的【主表NestingTable套表1关联值】关联值
        insertSubFistRelationRow(subNestingTb1, mainRelationField, nestingFistSubRelationField, "主表套表1子嵌套表1SubNestingTbFirst套表1关联值");

        insertSubFistRelationRow(subNestingTb1, mainRelationField, nestingFistSubRelationField, "主表套表1子嵌套表1SubNestingTbFirst套表2关联值");

        //添加子嵌套添加行数据，不增加子表关系值，固定使用子表关系值【子嵌套表1SubNestingTbFirst套表2关联值】，代表子表中关联了【主表套表2子嵌套表1SubNestingTbFirst套表1关联值】的可以生成在此表下面
        // 固定关联主表的【主表NestingTable套表2关联值】关联值
        insertSubFistRelationRow2(subNestingTb1, mainRelationField, nestingFistSubRelationField);


        //传递的关联字段是【子嵌套表1SubNestingTbFirst关联字段】，下面的嵌套子表都要设置这个关联字段的值去确定在那个子表套表里
        //添加【嵌套子表1（循环行生成）】 - 嵌套子表【SubNestingTBFirstRows1】的测试数据
        putSubNestingTBFirstRows1(nestingFistSubRelationField, nestingSubTable, nestingSubRelationField);
        //添加【嵌套子表1（循环行生成）】 - 嵌套子表【SubNestingTBFirstRows2】的测试数据
        putSubNestingTBFirstRows2(nestingFistSubRelationField, nestingSubTable, nestingSubRelationField);


        //设置嵌套子表的嵌套子数据
        subNestingTb1.setSubTableMap(nestingSubTable);
        subNestingTb1.setSubTableRelationFieldMap(nestingSubRelationField);

        //设置主表的嵌套子表
        subTable.put(tableName, subNestingTb1);
        subRelationField.put(tableName, mainRelationField);
    }

    /**
     * 添加子嵌套表的关联字段行数据
     * 此方法固定关联主表的【主表NestingTable套表1关联值】关联值
     *
     * @param subNestingTb1               嵌套子表【SubNestingTbFirst】
     * @param mainRelationField           主表【NestingTable】的关系字段
     * @param nestingFistSubRelationField 嵌套子表【SubNestingTbFirst】的关系字段
     * @param nestingFistSubRelationVal   嵌套子表【SubNestingTbFirst】的关系值
     */
    private static void insertSubFistRelationRow(DataTableVO subNestingTb1, String mainRelationField, String nestingFistSubRelationField, String nestingFistSubRelationVal) {
        //嵌套表添加行数据
        DataRow subRelationRow = subNestingTb1.getTable().newRow();
        //设置嵌套子表对应主表【NestingTable】的关联字段值，此处表示当前这个表是  主表套表1的嵌套数据
        subRelationRow.set(mainRelationField, "主表NestingTable套表1关联值");

        //设置嵌套子表【SubNestingTbFirst】对应下面嵌套表的关联字段值 表示当前表【SubNestingTbFirst】作为主表关联值的嵌套数据、
        // 下方嵌套表可放置这个值的对于关联关系，生成到这个表的下面
        subRelationRow.set(nestingFistSubRelationField, nestingFistSubRelationVal);
        subNestingTb1.getTable().getRows().add(subRelationRow);
    }

    /**
     * 添加子嵌套表的关联字段行数据
     * </p>
     * 此方法固定关联主表的【主表NestingTable套表2关联值】关联值 <br/>
     * 此方法设置后，表示，在主表NestingTable下的第二套关系表中包含子嵌套表【SubNestingTbFirst】 <br/>
     * 对应的子嵌套表【SubNestingTbFirst】下只会生成关系为【主表套表2子嵌套表1SubNestingTbFirst套表1关联值】的子数据  <br/>
     *
     * @param subNestingTb1               嵌套子表【SubNestingTbFirst】
     * @param mainRelationField           主表【NestingTable】的关系字段
     * @param nestingFistSubRelationField 嵌套子表【SubNestingTbFirst】的关系字段
     */
    private static void insertSubFistRelationRow2(DataTableVO subNestingTb1, String mainRelationField, String nestingFistSubRelationField) {
        //嵌套表添加行数据
        DataRow subRelationRow = subNestingTb1.getTable().newRow();
        //设置嵌套子表对应主表【NestingTable】的关联字段值，此处表示当前这个表是  主表套表1的嵌套数据
        subRelationRow.set(mainRelationField, "主表NestingTable套表2关联值");

        //设置嵌套子表【SubNestingTbFirst】对应下面嵌套表的关联字段值 表示当前表【SubNestingTbFirst】作为主表关联值的嵌套数据、
        // 下方嵌套表可放置这个值的对于关联关系，生成到这个表的下面
        subRelationRow.set(nestingFistSubRelationField, "主表套表2子嵌套表1SubNestingTbFirst套表1关联值");
        subNestingTb1.getTable().getRows().add(subRelationRow);
    }


    /**
     * 写入【嵌套子表1（嵌套行循环生成）】 - 嵌套子表【SubNestingTbFirst】 - 嵌套行子表【SubNestingTBFirstRows1】的测试数据
     *
     * @param mainRelationField 上层主表的关系字段，此处对应的是【SubNestingTbFirst】的关系字段
     * @param subTable          上层主表的子表数据
     * @param subRelationField  上层主表的子表关联字段数据
     */
    private static void putSubNestingTBFirstRows1(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "SubNestingTBFirstRows1";
        //构建嵌套行子表【SubNestingTBFirstRows1】的表数据、设置上层表关联字段
        DataTableVO subFistRowTb1 = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();

        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(subFistRowTb1.getTable(), p));
        //添加嵌套子表SubNestingTbFirst 套表1的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subFistRowTb1.getTable().newRow();
            row.set(mainRelationField, "主表套表1子嵌套表1SubNestingTbFirst套表1关联值");
            row.set("Test", "主套1-子套1-嵌套表First行1测试项目" + i);
            row.set("Method", "主套1-子套1-嵌套表First行1方法" + i);
            row.set("Limit", "主套1-子套1-嵌套表First行1限值" + i);
            row.set("Dim", "主套1-子套1-嵌套表First行1量纲" + i);
            row.set("InsNo", "主套1-子套1-嵌套表First行1仪器编号" + i);
            subFistRowTb1.getTable().getRows().add(row);
        }

        //添加嵌套子表SubNestingTbFirst 套表2的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subFistRowTb1.getTable().newRow();
            row.set(mainRelationField, "主表套表1子嵌套表1SubNestingTbFirst套表2关联值");
            row.set("Test", "主套1-子套2-嵌套表First行1测试项目" + i);
            row.set("Method", "主套1-子套2-嵌套表First行1方法" + i);
            row.set("Limit", "主套1-子套2-嵌套表First行1限值" + i);
            row.set("Dim", "主套1-子套2-嵌套表First行1量纲" + i);
            row.set("InsNo", "主套1-子套2-嵌套表First行1仪器编号" + i);
            subFistRowTb1.getTable().getRows().add(row);
        }

        //添加 主套表2-嵌套子表SubNestingTbFirst 套表1的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subFistRowTb1.getTable().newRow();
            row.set(mainRelationField, "主表套表2子嵌套表1SubNestingTbFirst套表1关联值");
            row.set("Test", "主套2-子套1-嵌套表First行1测试项目" + i);
            row.set("Method", "主套2-子套1-嵌套表First行1方法" + i);
            row.set("Limit", "主套2-子套1-嵌套表First行1限值" + i);
            row.set("Dim", "主套2-子套1-嵌套表First行1量纲" + i);
            row.set("InsNo", "主套2-子套1-嵌套表First行1仪器编号" + i);
            subFistRowTb1.getTable().getRows().add(row);
        }
        subTable.put(tableName, subFistRowTb1);
        subRelationField.put(tableName, mainRelationField);
    }

    /**
     * 写入【嵌套子表1（嵌套行循环生成）】 - 嵌套子表【SubNestingTbFirst】 - 嵌套行子表【SubNestingTBFirstRows2】的测试数据
     *
     * @param mainRelationField 上层主表的关系字段，此处对应的是【SubNestingTbFirst】的关系字段
     * @param subTable          上层主表的子表数据
     * @param subRelationField  上层主表的子表关联字段数据
     */
    private static void putSubNestingTBFirstRows2(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "SubNestingTBFirstRows2";
        //构建嵌套行子表【SubNestingTBFirstRows1】的表数据、设置上层表关联字段
        DataTableVO subFistRowTb2 = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();

        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(subFistRowTb2.getTable(), p));

        //添加 主套表1-嵌套子表SubNestingTbFirst 套表2的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subFistRowTb2.getTable().newRow();
            row.set(mainRelationField, "主表套表1子嵌套表1SubNestingTbFirst套表2关联值");
            row.set("Test", "主套1-子套2-嵌套表First行2测试项目" + i);
            row.set("Method", "主套1-子套2-嵌套表First行2方法" + i);
            row.set("Limit", "主套1-子套2-嵌套表First行2限值" + i);
            row.set("Dim", "主套1-子套2-嵌套表First行2量纲" + i);
            row.set("InsNo", "主套1-子套2-嵌套表First行2仪器编号" + i);
            subFistRowTb2.getTable().getRows().add(row);
        }

        //添加 主套表2-嵌套子表SubNestingTbFirst 套表1的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subFistRowTb2.getTable().newRow();
            row.set(mainRelationField, "主表套表2子嵌套表1SubNestingTbFirst套表1关联值");
            row.set("Test", "主套2-子套1-嵌套表First行2测试项目" + i);
            row.set("Method", "主套2-子套1-嵌套表First行2方法" + i);
            row.set("Limit", "主套2-子套1-嵌套表First行2限值" + i);
            row.set("Dim", "主套2-子套1-嵌套表First行2量纲" + i);
            row.set("InsNo", "主套2-子套1-嵌套表First行2仪器编号" + i);
            subFistRowTb2.getTable().getRows().add(row);
        }
        subTable.put(tableName, subFistRowTb2);
        subRelationField.put(tableName, mainRelationField);
    }


    /**
     * 写入【嵌套子表2（循环行生成）】 的测试数据
     *
     * @param mainRelationField 主表关系字段
     * @param subTable          子表数据Map
     * @param subRelationField  子表关联字段数据Map
     */
    private static void putSubNestingTbSecond(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "SubNestingTbSecond";
        //构建【嵌套子表2（循环行生成）】的行循环数据
        DataTableVO subNestingTb2 = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();


        //嵌套子表的子表数据
        Map<String, DataTableVO> nestingSubTable = new HashMap<>();
        //嵌套子表的子表关联字段数据
        Map<String, String> nestingSubRelationField = new HashMap<>();

        //设置子嵌套表SubNestingTbSecond的表关联字段
        String nestingSecondSubRelationField = "子嵌套表2SubNestingTbSecond关联字段";
        //添加主表关联列
        ReportDataUtil.addTableColumn(subNestingTb2.getTable(), mainRelationField);
        //添加子嵌套表关联列
        ReportDataUtil.addTableColumn(subNestingTb2.getTable(), nestingSecondSubRelationField);

        //添加子嵌套添加行数据，增加两个关系值，代表此表中有两个套表可以下面的子表关联到
        // 固定关联主表的【主表NestingTable套表1关联值】关联值
        insertSubFistRelationRow(subNestingTb2, mainRelationField, nestingSecondSubRelationField, "主表套表1子嵌套表2SubNestingTbSecond套表1关联值");


        //传递的关联字段是【子嵌套表2SubNestingTbSecond关联字段】，下面的嵌套子表都要设置这个关联字段的值去确定在那个子表套表里
        //添加【嵌套子表2（循环行生成）】 - 嵌套子表【SubNestingTBSecondRows1】的测试数据
        putSubNestingTBSecondRows1(nestingSecondSubRelationField, nestingSubTable, nestingSubRelationField);


        //设置嵌套子表的嵌套子数据
        subNestingTb2.setSubTableMap(nestingSubTable);
        subNestingTb2.setSubTableRelationFieldMap(nestingSubRelationField);

        //设置主表的嵌套子表
        subTable.put(tableName, subNestingTb2);
        subRelationField.put(tableName, mainRelationField);
    }

    /**
     * 写入【嵌套子表2（循环行生成）】 - 嵌套子表【SubNestingTbSecond】 - 嵌套行子表【SubNestingTBSecondRows1】的测试数据
     *
     * @param mainRelationField 上层主表的关系字段，此处对应的是【SubNestingTbSecond】的关系字段
     * @param subTable          上层主表的子表数据
     * @param subRelationField  上层主表的子表关联字段数据
     */
    private static void putSubNestingTBSecondRows1(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "SubNestingTBSecondRows1";
        //构建嵌套行子表【SubNestingTBSecondRows1】的表数据、设置上层表关联字段
        DataTableVO subSecondRowTb = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();

        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(subSecondRowTb.getTable(), p));
        //添加嵌套子表SubNestingTbSecond 套表1的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = subSecondRowTb.getTable().newRow();
            row.set(mainRelationField, "主表套表1子嵌套表2SubNestingTbSecond套表1关联值");
            row.set("Test", "主套1-子套1-嵌套表Second行测试项目" + i);
            row.set("Method", "主套1-子套1-嵌套表Second行方法" + i);
            row.set("Limit", "主套1-子套1-嵌套表Second行限值" + i);
            row.set("Dim", "主套1-子套1-嵌套表Second行量纲" + i);
            row.set("InsNo", "主套1-子套1-嵌套表Second行仪器编号" + i);
            subSecondRowTb.getTable().getRows().add(row);
        }
        subTable.put(tableName, subSecondRowTb);
        subRelationField.put(tableName, mainRelationField);
    }

}
