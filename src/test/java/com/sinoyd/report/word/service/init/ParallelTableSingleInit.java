package com.sinoyd.report.word.service.init;

import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.sinoyd.report.word.builder.DataTableBuilder;
import com.sinoyd.report.word.util.ReportDataUtil;
import com.sinoyd.report.word.vo.DataTableVO;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 嵌套表测试：并行表单【ParallelTableSingle】测试数据初始化类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/17
 */
public class ParallelTableSingleInit {

    /**
     * 写入并行表单数据
     */
    public static void writeParallelTableSingle(Map<String, DataTableVO> dataMap) {
        String mainTableName = "ParallelTableSingle";
        //创建主表一，并行主表
        DataTableVO mainTable = DataTableBuilder.initTable(mainTableName, new DataTable()).getVOInstance();

        //子表数据
        Map<String, DataTableVO> subTable = new HashMap<>();
        //子表关联字段数据
        Map<String, String> subRelationField = new HashMap<>();

        //主表关联字段
        String mainRelationField = "MainRelationField";
        //添加主表关联列
        ReportDataUtil.addTableColumn(mainTable.getTable(), mainRelationField);

        //设置主表关系值  主表套表1FistMain
        String mainRelationValue1 = "FirstMain";
        DataRow relationRow1 = mainTable.getTable().newRow();
        relationRow1.set(mainRelationField, mainRelationValue1);
        mainTable.getTable().getRows().add(relationRow1);

        //设置主表关系值  主表套表2SecondMain
        String mainRelationValue2 = "SecondMain";
        DataRow relationRow2 = mainTable.getTable().newRow();
        relationRow2.set(mainRelationField, mainRelationValue2);
        mainTable.getTable().getRows().add(relationRow2);

        //添加【并行单子表1（循环行）】- 嵌套子表【ParallelInsideRow1】
        putParallelInsideRow1(mainRelationField, subTable, subRelationField);
        //写入【并行单子表2（嵌套循环行）】-表1【InsideNestingRow1】
        putInsideNestingRow1(mainRelationField, subTable, subRelationField);
        //写入【并行单子表2（嵌套循环行）】-表2【InsideNestingRow2】
        putInsideNestingRow2(mainRelationField, subTable, subRelationField);


        //设置嵌套子表数据
        mainTable.setSubTableMap(subTable);
        mainTable.setSubTableRelationFieldMap(subRelationField);
        //放置并行主表数据
        dataMap.put(mainTableName, mainTable);
    }


    /**
     * 写入【并行单子表1（循环行）】- 嵌套子表【ParallelInsideRow1】 测试数据
     *
     * @param mainRelationField 主表关系字段
     * @param subTable          子表数据Map
     * @param subRelationField  子表关联字段数据Map
     */
    private static void putParallelInsideRow1(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "ParallelInsideRow1";
        //构建【并行单子表1（循环行）】的行循环数据
        DataTableVO tbInsideRow = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();
        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(tbInsideRow.getTable(), p));
        //添加主表套表1的行数据
        for (int i = 0; i < 3; i++) {
            DataRow row = tbInsideRow.getTable().newRow();
            row.set(mainRelationField, "FirstMain");
            row.set("Test", "套表1-并行表测试项目" + i);
            row.set("Method", "套表1-并行表方法" + i);
            row.set("Limit", "套表1-并行表限值" + i);
            row.set("Dim", "套表1-并行表量纲" + i);
            row.set("InsNo", "套表1-并行表仪器编号" + i);
            tbInsideRow.getTable().getRows().add(row);
        }

        //添加主表套表2的行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = tbInsideRow.getTable().newRow();
            row.set(mainRelationField, "SecondMain");
            row.set("Test", "套表2-并行表测试项目" + i);
            row.set("Method", "套表2-并行表方法" + i);
            row.set("Limit", "套表2-并行表限值" + i);
            row.set("Dim", "套表2-并行表量纲" + i);
            row.set("InsNo", "套表2-并行表仪器编号" + i);
            tbInsideRow.getTable().getRows().add(row);
        }
        subTable.put(tableName, tbInsideRow);
        subRelationField.put(tableName, mainRelationField);
    }

    /**
     * 写入【并行单子表2（嵌套循环行）】-表1【InsideNestingRow1】 测试数据
     *
     * @param mainRelationField 主表关系字段
     * @param subTable          子表数据Map
     * @param subRelationField  子表关联字段数据Map
     */
    private static void putInsideNestingRow1(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "InsideNestingRow1";
        //构建【并行单子表2（嵌套循环行）】的行1【InsideNestingRow1】循环数据
        DataTableVO tbInsideNestingRow = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();
        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(tbInsideNestingRow.getTable(), p));
        //添加套表1的表1行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = tbInsideNestingRow.getTable().newRow();
            row.set(mainRelationField, "FirstMain");
            row.set("Test", "套表1-嵌套表-行1测试项目" + i);
            row.set("Method", "套表1-嵌套表-行1方法" + i);
            row.set("Limit", "套表1-嵌套表-行1限值" + i);
            row.set("Dim", "套表1-嵌套表-行1量纲" + i);
            row.set("InsNo", "套表1-嵌套表-行1仪器编号" + i);
            tbInsideNestingRow.getTable().getRows().add(row);
        }
        subTable.put(tableName, tbInsideNestingRow);
        subRelationField.put(tableName, mainRelationField);
    }


    /**
     * 写入【并行单子表2（嵌套循环行）】-表2【InsideNestingRow2】 测试数据
     *
     * @param mainRelationField 主表关系
     * @param subTable          子表数据Map
     * @param subRelationField  子表关联字段数据Map
     */
    private static void putInsideNestingRow2(String mainRelationField, Map<String, DataTableVO> subTable, Map<String, String> subRelationField) {
        String tableName = "InsideNestingRow2";
        //构建【并行单子表2（嵌套循环行）】的行2【InsideNestingRow2】循环数据
        DataTableVO tbInsideNestingRow = DataTableBuilder.initTable(tableName, new DataTable()).setRelationField(mainRelationField).getVOInstance();
        //添加列
        List<String> columnList = Arrays.asList(mainRelationField, "Test", "Method", "Limit", "Dim", "InsNo");
        columnList.forEach(p -> ReportDataUtil.addTableColumn(tbInsideNestingRow.getTable(), p));
        //添加套表1的表2行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = tbInsideNestingRow.getTable().newRow();
            row.set(mainRelationField, "FirstMain");
            row.set("Test", "套表1-嵌套表-行2测试项目" + i);
            row.set("Method", "套表1-嵌套表-行2方法" + i);
            row.set("Limit", "套表1-嵌套表-行2限值" + i);
            row.set("Dim", "套表1-嵌套表-行2量纲" + i);
            row.set("InsNo", "套表1-嵌套表-行2仪器编号" + i);
            tbInsideNestingRow.getTable().getRows().add(row);
        }

        //添加套表2的表2行数据
        for (int i = 0; i < 2; i++) {
            DataRow row = tbInsideNestingRow.getTable().newRow();
            row.set(mainRelationField, "SecondMain");
            row.set("Test", "套表2-嵌套表-行2测试项目" + i);
            row.set("Method", "套表2-嵌套表-行2方法" + i);
            row.set("Limit", "套表2-嵌套表-行2限值" + i);
            row.set("Dim", "套表2-嵌套表-行2量纲" + i);
            row.set("InsNo", "套表2-嵌套表-行2仪器编号" + i);
            tbInsideNestingRow.getTable().getRows().add(row);
        }
        subTable.put(tableName, tbInsideNestingRow);
        subRelationField.put(tableName, mainRelationField);
    }

}
