package com.sinoyd.base.util;

import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.util.StringUtils;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Cache工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/2
 */
public class CacheUtil {

    /**
     * 获取缓存方法
     *
     * @param cacheName 缓存名称
     * @return 缓存对象
     */
    public static Cache getCache(String cacheName) {
        CacheManager cacheManager = SpringContextAware.getBean(CacheManager.class);
        return cacheManager.getCache(cacheName);
    }


    /**
     * 查询缓存字段
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @return 缓存字段值列表
     */
    @SuppressWarnings("unchecked")
    public static List<String> queryCacheFieldValues(String cacheName, String key) {
        Cache cache = getCache(cacheName);
        assert cache != null;
        return cache.get(key, List.class);
    }

    /**
     * 添加缓存字段值
     *
     * @param cacheName      缓存名称
     * @param key            缓存key
     * @param valueContainer 要添加的缓存字段值
     */
    public static void addAllCacheFieldValues(String cacheName, String key, Collection<?> valueContainer) {
        //获取已经存在的缓存字段值
        List<?> values = CacheUtil.queryCacheFieldValues(cacheName, key);
        //如果已经存在缓存字段值，则进行去重添加，否则直接保存
        if (StringUtils.isNotEmpty(values)) {
            Set<Object> tempSet = new HashSet<>(values.size() + valueContainer.size());
            tempSet.addAll(values);
            tempSet.addAll(valueContainer);
            values = new ArrayList<>(tempSet);
        } else {
            values = new ArrayList<>(new HashSet<>(valueContainer));
        }
        if (StringUtils.isNotEmpty(values)) {
            values = values.stream().filter(p -> p != null && StringUtils.isNotEmpty(p.toString())).collect(Collectors.toList());
            //获取缓存，将组装完成后的容器直接存储
            Cache cache = getCache(cacheName);
            assert cache != null;
            cache.put(key, values);
        }
    }

    /**
     * 移除缓存字段的值
     *
     * @param cacheName      缓存名称
     * @param key            缓存keu
     * @param valueContainer 需要移除的值容器
     */
    public static void removeAllCacheFieldValues(String cacheName, String key, Collection<String> valueContainer) {
        //获取已经存在的缓存
        List<?> existList = CacheUtil.queryCacheFieldValues(cacheName, key);
        if (StringUtils.isNotEmpty(existList)) {
            //移除
            existList.removeAll(valueContainer);
            Cache cache = getCache(cacheName);
            assert cache != null;
            //如果移除后已缓存的容器为空则直接删除此key下的容器，否则将更新后的数据覆盖
            if (existList.size() == 0) {
                cache.evict(key);
            } else {
                cache.put(key, existList);
            }
        }
    }

    /**
     * 清除缓存:
     * 如果keys值为空，则删除缓存名称下全部缓存；否则按key进行删除
     *
     * @param cacheName 缓存名称
     * @param keys      缓存key集合
     */
    public static void clearCache(String cacheName, Collection<?> keys) {
        Cache cache = getCache(cacheName);
        if (StringUtils.isEmpty(keys)) {
            cache.clear();
        } else {
            for (Object key : keys) {
                cache.evictIfPresent(key);
            }
        }
    }

    /**
     * 删除缓存名称下全部缓存
     *
     * @param cacheName 缓存名称
     */
    public static void clearCache(String cacheName) {
        clearCache(cacheName, null);
    }

    /**
     * 删除缓存名称下全部缓存
     *
     * @param cacheNames 缓存名称数组
     */
    public static void clearCache(String... cacheNames) {
        if(cacheNames != null && cacheNames.length > 0){
            for (String cacheName : cacheNames){
                clearCache(cacheName, null);
            }
        }
    }

    /**
     * 刷新缓存
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param objects   待缓存的数据
     */
    public static void putCache(String cacheName, String key, Collection<?> objects) {
        Cache cache = getCache(cacheName);
        cache.put(key, objects);
    }

    /**
     * 刷新缓存
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param t         待缓存的数据
     * @param <T>       缓存数据类型
     */
    public static <T> void putCache(String cacheName, String key, T t) {
        Cache cache = getCache(cacheName);
        cache.put(key, t);
    }

    /**
     * 往已有缓存中追加记录
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param objects   本次待缓存的数据
     * @param <T>       泛型
     */
    public static <T> void appendCache(String cacheName, String key, Collection objects) {
        Cache cache = getCache(cacheName);
        Collection<T> originCacheList = loadCacheValues(cacheName, Collections.singletonList(key));
        if (StringUtils.isEmpty(originCacheList)) {
            originCacheList = objects;
        } else {
            originCacheList.addAll(objects);
        }
        cache.put(key, originCacheList);
    }

    /**
     * 往已有缓存中追加记录
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param t         本次待缓存的数据
     * @param <T>       泛型
     */
    public static <T> void appendCache(String cacheName, String key, T t) {
        Cache cache = getCache(cacheName);
        Collection<T> originCacheList = loadCacheValues(cacheName, Collections.singletonList(key));
        if (StringUtils.isEmpty(originCacheList)) {
            originCacheList = Collections.singletonList(t);
        } else {
            originCacheList.add(t);
        }
        cache.put(key, originCacheList);
    }


    /**
     * 从缓存中加载key
     *
     * @param cacheName 缓存名称
     * @param keys      key集合
     * @param <T>       对象类型
     * @return 结果
     */
    public static <T> List<T> loadCacheValues(String cacheName, Collection<String> keys) {
        Cache cache = getCache(cacheName);
        List<T> list = new ArrayList<>();
        keys.forEach(k -> {
            Cache.ValueWrapper wrapper = cache.get(k);
            if (wrapper != null && wrapper.get() != null) {
                if (wrapper.get() instanceof List) {
                    list.addAll((List<T>) wrapper.get());
                } else {
                    list.add((T) wrapper.get());
                }
            }
        });
        return list;

    }

    /**
     * 根据单个key获取缓存数据
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param clazz     泛型类型
     * @param <T>
     * @return
     */
    public static <T> T loadCacheValues(String cacheName, String key, Class<T> clazz) {
        Cache cache = getCache(cacheName);
        return cache.get(key, clazz);
    }


    /**
     * 返回缓存中的子key
     *
     * @param cacheName 缓存名称
     * @param keys      key集合
     * @return 子key集合
     */
    public static List<String> loadCachedChildKeys(String cacheName, Collection<String> keys) {
        List<String> result = new ArrayList<>();
        Cache cache = getCache(cacheName);
        for (String key : keys) {
            Cache.ValueWrapper wrapper = cache.get(key);
            if (wrapper == null || wrapper.get() == null) {
                break;
            } else {
                result.addAll((List) wrapper.get());
            }
        }
        return result;
    }
}