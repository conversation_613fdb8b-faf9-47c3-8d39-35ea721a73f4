package com.sinoyd.base.service;

import java.util.List;
import java.util.Map;

/**
 * 计算相关接口
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
public interface CalculateService {

    /**
     * 批量进行公式计算
     *
     * @param gateUrl  网关地址
     * @param formulas 公式集合
     * @param params   对应的参数数据
     * @return 返回计算后的值
     */
    Object calculationExpression(String gateUrl, List<String> formulas, Map<String, Object> params);

    /**
     * 批量进行公式计算
     *
     * @param gateUrl  网关地址
     * @param formulas 公式集合
     * @param params   对应的参数数据
     * @return 返回计算后的值
     */
    String calculateRule(String gateUrl, List<String> formulas, Map<String, Object> params);

    /**
     * 公式判断计算
     *
     * @param gateUrl  网关地址
     * @param formulas 公式集合
     * @param params   参数数据
     * @return 返回判断条件
     */
    Boolean calculateJudge(String gateUrl, List<String> formulas, Map<String, Object> params);
}
