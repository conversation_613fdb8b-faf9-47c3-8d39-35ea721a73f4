package com.sinoyd.base.status.state;

import java.util.Collection;
import java.util.Collections;

/**
 * 通用状态接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/14
 */
public interface IState<T> {

    /**
     * 业务状态处理(单个)
     *
     * @param t 业务实体
     */
    default void handle(T t) {
        handle(Collections.singletonList(t));
    }

    /**
     * 业务状态处理(多个)
     *
     * @param entities 业务实体集合
     */
    void handle(Collection<T> entities);

    /**
     * 获取当前状态
     *
     * @return 状态值
     */
    Integer getCurrentState();
}
