package com.sinoyd.base.service;

import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 扩展的LIMS Base Service接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/9
 */
public interface LimsBaseService<T, ID extends Serializable> extends IBaseJpaService<T, ID> {

    /**
     * 分页查询
     *
     * @param pageBean             分页信息
     * @param criteria             查询条件
     * @param isLoadTransientField 是否加载冗余属性
     */
    void findByPage(PageBean<T> pageBean, BaseCriteria criteria, boolean isLoadTransientField);

    /**
     * 给冗余字段赋值
     *
     * @param entities 实体集合
     */
    void loadTransientFields(Collection<T> entities);

    /**
     * 获取附件上传路径
     *
     * @param id 主键
     * @return 实体
     */
    T findAttachmentPath(ID id);

    /**
     * 根据id查询
     *
     * @param id                   主键
     * @param isLoadUnDBFiledValue true: 对冗余属性赋值; false: 对冗余属性不赋值
     * @return 结果
     */
    T findOne(ID id, boolean isLoadUnDBFiledValue);

    /**
     * 根据id查询
     *
     * @param ids                  主键
     * @param isLoadUnDBFiledValue true: 对冗余属性赋值; false: 对冗余属性不赋值
     * @return 结果集合
     */
    List<T> findAll(Collection<ID> ids, boolean isLoadUnDBFiledValue);

    /**
     * 根据id查询，并将结果组装成id -> entity的Map形式
     *
     * @param ids                  主键
     * @param isLoadUnDBFiledValue true: 对冗余属性赋值; false: 对冗余属性不赋值
     * @return 结果集合
     */
    Map<String, T> findAllMap(Collection<ID> ids, boolean isLoadUnDBFiledValue);

    /**
     * (拖拽)批量更新排序值
     *
     * @param entities 数据传输对象
     * @return 完成更新后的检测类型对象
     */
    List<T> dragOrderNo(Collection<T> entities);

    /**
     * 缓存实体中单个字段值
     *
     * @param cacheName 缓存名
     * @param key       缓存key
     * @param fieldName 缓存属性名
     * @return 缓存的数据
     */
    List<?> loadCacheField(String cacheName, String key, String fieldName);

    /**
     * 根据给定的ID集合删除所有记录。
     *
     * @param ids 包含待删除记录ID的集合
     */
    void deleteAllByIds(Collection<ID> ids);

    /**
     * 将实体变为游离态
     *
     * @param t 要分离的实体
     */
    void detach(T t);

    /**
     * 将实体变为游离态
     *
     * @param tList 实体集合
     */
    void detach(Collection<T> tList);

    /**
     * 批量新增，该方法只会新增，不会更新
     *
     * @param tList 实体集合
     * @return 返回新增的实体集合
     */
    List<T> batchSave(Collection<T> tList);

    /**
     * 批量更新，该方法只会更新，不会新增
     *
     * @param tList 实体集合
     * @return 返回更新的实体集合
     */
    List<T> batchUpdate(Collection<T> tList);
}
