package com.sinoyd.base.vo;

import com.sinoyd.frame.base.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * 树VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
@Data
@Accessors(chain = true)
public class TreeNodeVO implements BaseEntity {

    /**
     * 主键
     */
    private String id;

    /**
     * 父节点主键
     */
    private String parentId;

    /**
     * 显示名
     */
    private String label;

    /**
     * 种类
     */
    private Integer category;

    /**
     * 类型
     */
    private String type;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 是否叶子节点
     */
    private Boolean isLeaf;

    /**
     * 子节点集合
     */
    private List<TreeNodeVO> children;

    /**
     * 预留字段1
     */
    private String extent1;

    /**
     * 预留字段2
     */
    private String extent2;

    /**
     * 预留字段3
     */
    private String extent3;

    /**
     * 预留字段4
     */
    private String extent4;

    /**
     * 预留字段5
     */
    private String extent5;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TreeNodeVO that = (TreeNodeVO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}