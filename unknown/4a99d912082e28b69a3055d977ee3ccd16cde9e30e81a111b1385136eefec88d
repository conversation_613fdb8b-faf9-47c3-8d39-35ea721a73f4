package com.sinoyd.base.service;

import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * LIMS表格处理基础接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/7/19
 */
public interface LimsExcelBaseService <T, V extends LimsExcelBaseVO, ID extends Serializable, S extends IBaseJpaService<T, ID>>{

    /**
     * 查询导出数据
     *
     * @param criteria 查询条件
     */
    List<T> findExportData(BaseCriteria criteria);

    /**
     * 数据导出
     *
     * @param response 响应流
     * @param criteria 查询条件
     */
    void exportData(HttpServletResponse response, BaseCriteria criteria);

    /**
     * 数据导入
     *
     * @param response 响应流
     * @param file     导入文件
     */
    void importData(HttpServletResponse response, MultipartFile file);

    /**
     * 下载导入模板
     *
     * @param response 响应流
     */
    void downloadImportTemplate(HttpServletResponse response);
}
