package com.sinoyd.base.service;

import com.sinoyd.base.enums.EnumTableStrategy;

/**
 * 分表接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
public interface ISplitTableService {

    /**
     * 自动建表
     *
     * @param templateTable 模板表
     * @param tableStrategy 建表策略
     * @return 表名
     */
    String autoCreateTable(ITemplateTable templateTable, EnumTableStrategy tableStrategy);

}
