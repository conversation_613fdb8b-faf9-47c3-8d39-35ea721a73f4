package com.sinoyd.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计算方式
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/04/08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumCalculateWay {
    原始值(0),
    检出限一半(1),
    取零(2),
    检出限(3);

    /**
     * 枚举值
     */
    private Integer value;

    /**
     * 根据名称获取值
     *
     * @param name 名称
     * @return 直接返回名称
     */
    public static EnumCalculateWay getValueByName(String name) {
        for (EnumCalculateWay enumCalculateWay : EnumCalculateWay.values()) {
            if (name.equals(enumCalculateWay.name())) {
                return enumCalculateWay;
            }
        }
        return null;
    }

    /**
     * 根据类型获取值
     *
     * @param value 类型
     * @return 直接返回名称
     */
    public static EnumCalculateWay getValueByInteger(Integer value) {
        for (EnumCalculateWay enumCalculateWay : EnumCalculateWay.values()) {
            if (value.equals(enumCalculateWay.getValue())) {
                return enumCalculateWay;
            }
        }
        return null;
    }
}
