package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * 业务检查参数VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/30
 */
@Data
@Accessors(chain = true)
public class BizCheckParamVO {

    /**
     * 待检查的业务操作类型, 参考{@link com.sinoyd.base.enums.EnumBizCheckItem} 枚举值
     */
    private String bizCheckType;

    /**
     * 待检查的业务数据id集合
     */
    private Collection<String> bizIds;
}