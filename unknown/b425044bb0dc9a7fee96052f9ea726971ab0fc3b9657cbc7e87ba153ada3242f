package com.sinoyd.base.util;

import com.sinoyd.base.enums.EnumCalculateWay;
import com.sinoyd.base.enums.EnumDateStatus;
import com.sinoyd.base.enums.EnumDetectionDisplayCode;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.utils.MathUtil;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基础工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/14
 */
public class BaseUtil {

    /**
     * 计算过期状态
     *
     * @param expiryDate 有效期
     * @param alertDays  警告天数
     * @return 状态
     */
    public static String computeDateStatus(Date expiryDate, int alertDays) {
        Date alertTime = DateUtil.dateAddDays(DateUtil.getCurrentDate(), alertDays);
        String result = "";
        if (expiryDate != null) {
            if (DateUtil.dateCompare(expiryDate, DateUtil.getCurrentDate()) < 0) {
                result = EnumDateStatus.已过期.name();
            } else if (expiryDate.compareTo(alertTime) <= 0) {
                result = EnumDateStatus.即将过期.name();
            } else if (expiryDate.compareTo(alertTime) > 0) {
                result = EnumDateStatus.正常.name();
            }
        }
        return result;
    }

    /**
     * 如果字符串结尾是数字，自动将数字加1
     *
     * @param str 原字符串
     * @return 处理后的新字符串
     */
    public static String autoIncreaseStringNumber(String str) {
        String[] ptNames = splitStringIntoNumberAndString(str);
        String strPart = ptNames[0];
        String numPart = ptNames[1];
        if (!"".equals(numPart)) {
            StringBuilder newNumPart = new StringBuilder(String.valueOf(Integer.parseInt(numPart) + 1));
            while (newNumPart.length() < numPart.length()) {
                newNumPart.insert(0, "0");
            }
            return strPart + newNumPart;
        }
        return str;
    }

    /**
     * 将字符串拆分成尾部数字部分和非数字部分
     *
     * @param str 原字符串
     * @return 拆分后的数组，第一个元素为非数字部分，第二个元素为数字部分
     */
    public static String[] splitStringIntoNumberAndString(String str) {
        StringBuilder sb = new StringBuilder(str);
        StringBuilder newSb = sb.reverse();
        StringBuilder partNum = new StringBuilder();
        StringBuilder partStr = new StringBuilder();
        boolean isLast = true;
        for (char c : newSb.toString().toCharArray()) {
            if (Character.isDigit(c) && isLast) {
                partNum.append(c);
            } else {
                partStr.append(c);
                isLast = false;
            }
        }
        return new String[]{partStr.reverse().toString(), partNum.reverse().toString()};
    }

    /**
     * 与检出限比较
     *
     * @param testValue      检测结果
     * @param examLimitValue 检出限
     * @param displayCode    检出限结果
     * @return 比较结果
     */
    public static String examValue(String testValue, String examLimitValue, EnumDetectionDisplayCode displayCode) {
        String result = testValue;

        if (StringUtils.isNotEmpty(testValue) && MathUtil.isNumber(testValue)
                && StringUtils.isNotEmpty(examLimitValue) && MathUtil.isNumber(examLimitValue)) {
            if (Double.parseDouble(testValue) < Double.parseDouble(examLimitValue)) {
                switch (displayCode) {
                    case 小于DL:
                        result = "＜DL";
                        break;
                    case 检出限L:
                        result = examLimitValue + "L";
                        break;
                    case 小于检出限:
                        result = "＜" + examLimitValue;
                        break;
                    default:
                        result = "ND";
                        break;
                }
            }
        }
        return result;
    }

    /**
     * 检出限一半显示
     *
     * @param value          数据
     * @param examLimitValue 检出限
     * @param calculateWay   显示类型
     * @return 结果
     */
    public static BigDecimal limitValue(String value, String examLimitValue, EnumCalculateWay calculateWay) {
        //根据参数值处理小于检出限的数值
        String result = value;
        if (StringUtils.isNotEmpty(value)
                && StringUtils.isNotEmpty(examLimitValue)
                && new BigDecimal(examLimitValue).compareTo(new BigDecimal(value)) > 0) {
            switch (calculateWay) {
                case 检出限一半:
                    result = ((new BigDecimal(examLimitValue)).divide(new BigDecimal(2))).toString();
                    break;
                case 检出限:
                    result = examLimitValue;
                    break;
                case 取零:
                    result = "0";
                    break;
                default:
                    result = value;
                    break;
            }
        }
        return new BigDecimal(result);
    }

    /**
     * 判断数值是否为小于检出限显示
     *
     * @param value 数据值
     * @return true/false
     */
    public static Boolean isLessDetectionShow(String value) {
        return StringUtils.isNotEmpty(value) &&
                (value.contains("＜") || value.contains("<")
                || value.contains("ND") || value.contains("L"));
    }

    /**
     * 根据属性中单一字段去重
     *
     * @param keyExtractor 提取字段的函数
     * @param <T>          泛型
     * @return 过滤后的流
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        //putIfAbsent方法添加键值对，如果map集合中没有该key对应的值，则直接添加，并返回null，如果已经存在对应的值，则依旧为原来的值。
        //如果返回null表示添加数据成功(不重复)，不重复(null==null :TRUE)
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 提取范围边界值
     * 格式：[x] >= 10 and [x] <= 20，返回[10, 20]，不满足规则返回[0, 0]
     *
     * @param input 范围字符串
     * @return 边界值数组 [lower, upper]
     */
    public static Integer[] extractBounds(String input) {
        int lower = 0;
        int upper = 0;

        // 分割两个条件
        String[] conditions = input.split("\\s+and\\s+");
        Pattern pattern = Pattern.compile("\\[x\\]\\s*(>=|<=)\\s*(-?\\d+)");

        for (String condition : conditions) {
            Matcher matcher = pattern.matcher(condition);
            if (matcher.find()) {
                int value = Integer.parseInt(matcher.group(2));
                switch (matcher.group(1)) {
                    case ">=":
                        lower = value;
                        break;
                    case "<=":
                        upper = value;
                        break;
                }
            }
        }
        return new Integer[]{lower, upper};
    }
}