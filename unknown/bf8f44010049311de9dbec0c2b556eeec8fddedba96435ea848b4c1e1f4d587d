<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 此xml在spring-boot-version.RELEASE.jar里 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <!-- 开启后可以通过jmx动态控制日志级别(Springboot Admin的功能) -->
    <jmxConfigurator/>

    <!-- 日志文件 -->
    <property  name="LOG_PATH"  source="logging.file.path"  value="${LOG_PATH:-${java.io.tmpdir:-/tmp}}"/>
    <springProperty scope="context"  name="LOG_NAME"  source="logging.file.name"/>

    <!-- 日志文件编码 -->
    <property name="LOG_CHARSET" value="UTF-8" />

    <!-- 文件输出 -->
    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <file>${LOG_PATH}/${LOG_NAME}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}.%i</fileNamePattern>
        </rollingPolicy>
        <triggeringPolicy
                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>10MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>

</configuration>