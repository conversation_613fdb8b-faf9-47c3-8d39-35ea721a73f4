package com.sinoyd.base.vo;

import javax.xml.bind.annotation.XmlElement;

/**
 * 附件配置VO
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
public class PathConfigVO {

    /**
     * 编号
     */
    private String code;

    /**
     * 类名
     */
    private String className;


    /**
     * 方法
     */
    private String method;

    /**
     * 占位符（多个按顺序隔开）
     */
    private String placeholder;

    /**
     * 路径
     */
    private String path;

    @XmlElement(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @XmlElement(name = "className")
    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    @XmlElement(name = "method")
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    @XmlElement(name = "placeholder")
    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    @XmlElement(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
