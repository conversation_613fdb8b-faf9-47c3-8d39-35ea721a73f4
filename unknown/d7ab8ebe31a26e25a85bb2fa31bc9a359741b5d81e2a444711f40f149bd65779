package com.sinoyd.base.vo;

import lombok.Data;

/**
 * 通用复制参数VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/20
 */
@Data
public class GenericCopyParamVO<T> {

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 业务id，比如编制方案时，业务id为项目id，主要用于日志收集时，归类属于哪个项目
     */
    private String bizId;

    /**
     * 源数据id
     */
    private String sourceId;

    /**
     * 源数据实体
     */
    private T sourceEntity;

    /**
     * 复制次数
     */
    private int copyTimes;

}