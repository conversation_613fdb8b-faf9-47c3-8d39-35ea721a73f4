package com.sinoyd.base.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 角色VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/18
 */
@Data
@Accessors(chain = true)
public class RoleVO {

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;
}