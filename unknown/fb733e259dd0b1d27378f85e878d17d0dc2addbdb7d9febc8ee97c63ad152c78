package com.sinoyd.base.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 试剂配置记录类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/02
 */
@Getter
@AllArgsConstructor
public enum EnumReagentType {

    一般试剂(0),

    标准溶液(1);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 根据值返回枚举项
     *
     * @param value 值
     * @return 枚举项
     */
    public static EnumReagentType getEnumItem(Integer value) {
        for (EnumReagentType em : EnumReagentType.values()) {
            if (value.equals(em.getValue())) {
                return em;
            }
        }
        throw new BaseException(String.format("非法的试剂配置记录类型枚举值[%d]", value));
    }
}
